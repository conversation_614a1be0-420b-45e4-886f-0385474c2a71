<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-06-20 16:39:07
 * @LastEditTime: 2024-02-06 10:36:30
 * @LastEditors: thb
-->
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :disabled="allDisabled"
    :rules="rules"
    label-position="top"
    :hide-required-asterisk="isDisabled"
  >
    <el-row :gutter="24" v-if="isShow">
      <el-col :span="12">
        <el-form-item :label="translateDisabled ? '正式合同附件' : '合同附件'" prop="file">
          <FileUpload class="file-width" :isShowTip="false" v-model="formData.file" @on-load-success="validateLoadSuccess" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="关联企业" prop="customerName">
          <!-- 点击弹窗出现客户列表  -->
          <div @click="handleShow" style="width: 100%">
            <el-input
              v-model="formData.customerName"
              readonly
              maxlength="20"
              :disabled="translateDisabled || isDisabled || isAssociated || changeDisabled"
              placeholder="请输入"
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="客户编码" prop="customerNo">
          <el-input v-model="formData.customerNo" maxlength="20" placeholder="请输入" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="甲方联系人" prop="companyPerson">
          <el-input
            v-model="formData.companyPerson"
            maxlength="20"
            :disabled="translateDisabled || isDisabled || changeDisabled"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="甲方联系电话" prop="companyPhone">
          <el-input
            v-model="formData.companyPhone"
            maxlength="20"
            :disabled="translateDisabled || isDisabled || changeDisabled"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="服务产品" prop="productName">
          <template v-if="changeDisabled">
            <el-tree-select
              v-model="formData.productId"
              ref="treeSelectRef"
              filterable
              :data="productTreeData"
              @current-change="treeSelectChange"
              :props="defaultPopsFunction(row)"
              :render-after-expand="false"
            />
          </template>
          <el-input v-else v-model="formData.productName" maxlength="20" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="网上申报" prop="declare">
          <el-radio-group @change="handleChangeDeclare" v-model="formData.declare" :disabled="translateDisabled || isDisabled">
            <el-radio label="1" size="large">非零申报</el-radio>
            <el-radio label="0" size="large">零申报</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="纳税人类型" prop="taxpayerType">
          <el-select
            v-model="formData.taxpayerType"
            :disabled="translateDisabled || isDisabled"
            :placeholder="translateDisabled || isDisabled ? '' : '请选择'"
            clearable
            @change="handleChangeTaxpayerType"
          >
            <el-option
              v-for="(option, index) in taxpayerTypes"
              :key="index"
              :label="option.label"
              :value="option.value"
              :disabled="option.label === '进出口企业' && formData.declare === '0'"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="合同起始时间" prop="startTime">
          <el-date-picker
            v-model="formData.startTime"
            format="YYYY-MM"
            value-format="YYYY-MM"
            type="month"
            placeholder="请选择"
            :disabled="isDisabled"
            @change="handleDateChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="服务月份(月)" prop="monthNum">
          <!--  必须是12个月的倍数 -->
          <!-- <el-input v-model.number="formData.monthNum" @input="handleValidateInput" maxlength="2" placeholder="请输入">
            <template #suffix>
              <div>月</div>
            </template>
          </el-input> -->
          <el-input-number
            step-strictly
            v-model="formData.monthNum"
            :step="formData.rowData.feeType === '1' ? 12 : 1"
            :min="formData.rowData.feeType === '1' ? 12 : 1"
            :disabled="translateDisabled || isDisabled"
            @change="handleInputChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="合同结束时间" prop="endTime">
          <el-date-picker
            v-model="formData.endTime"
            format="YYYY-MM"
            value-format="YYYY-MM"
            type="month"
            placeholder=""
            disabled
          />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6" v-if="formData.declare === '1'">
        <el-form-item label="销售收入" prop="salesRevenue">
          <template v-if="!isDisabled">
            <el-select
              v-model="formData.salesRevenue"
              :disabled="translateDisabled || isDisabled || changeDisabled"
              placeholder="请选择"
              clearable
              @change="handleSelectChange"
            >
              <el-option v-for="(option, index) in salesRevenues" :key="index" :label="option.label" :value="option.value" />
            </el-select>

            <template v-if="formData.salesRevenue === '其他'">
              <el-input
                class="m-t"
                v-model="formData.otherSalesText"
                :disabled="translateDisabled || isDisabled || changeDisabled"
                maxlength="100"
              />
            </template>
          </template>
          <template v-else>
            <el-input disabled v-model="formData.salesRevenue" maxlength="100" />
          </template>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6" v-if="formData.declare === '0'">
        <el-form-item label="人事薪酬服务" prop="payrollService">
          <el-radio-group v-model="formData.payrollService" :disabled="translateDisabled || isDisabled">
            <el-radio label="有" size="large">有</el-radio>
            <el-radio label="无" size="large">无</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="rowChange ? 24 : 12">
        <el-form-item label="所属公司" prop="branchOffice">
          <el-select
            style="width: 100%"
            v-model="formData.branchOffice"
            :disabled="translateDisabled || isDisabled"
            :placeholder="translateDisabled || isDisabled ? ' ' : '请选择'"
            clearable
            @change="handleSelectBranchOffice"
          >
            <el-option v-for="(option, index) in branch_office" :key="index" :label="option.name" :value="option.name" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="乙方联系人" prop="contactPerson">
          <el-input v-model="formData.contactPerson" maxlength="20" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="乙方地址" prop="contactAddress">
          <el-input v-model="formData.contactAddress" maxlength="255" disabled />
        </el-form-item>
      </el-col>
      <!-- 新增乙方账号信息 -->
      <template v-if="!accNumExist">
        <el-col :span="rowChange ? 12 : 12">
          <el-form-item label="乙方账号信息" prop="accountNumber">
            <!-- allDisabled -->
            <template v-if="allDisabled">
              <el-input v-model="formData.accountNumber" maxlength="255" disabled />
            </template>
            <template v-else>
              <el-select
                style="width: 100%"
                v-model="formData.accountNumber"
                placeholder="请选择"
                clearable
                :disabled="translateDisabled || isDisabled"
              >
                <el-option :label="item.label" :value="item.value" v-for="(item, index) in receipt_method" :key="index" />
              </el-select>
            </template>
          </el-form-item>
        </el-col>
      </template>
      <template v-else>
        <el-col :span="rowChange ? 12 : 12">
          <el-form-item label="乙方账号信息" prop="accountNumber">
            <el-input v-model="formData.accountNumber" disabled maxlength="20" />
          </el-form-item>
        </el-col>
      </template>

      <el-col :span="rowChange ? 12 : 6" v-if="formData.rowData.feeType !== '0'">
        <el-form-item label="活动优惠" prop="activityTxt">
          <div style="width: 100%">
            <!-- 设置readonly会导致clearable不可用 -->
            <el-input
              v-model="formData.activityTxt"
              clearable
              maxlength="20"
              :disabled="translateDisabled || isDisabled"
              placeholder="请输入"
              @click="handleShowActivity"
              @clear="handleClearActivity"
            />
          </div>
        </el-form-item>
      </el-col>
      <!-- <el-col :span="8" v-if="formData.salesRevenue === '其他'">
        <el-form-item label="其他" prop="otherSalesText">
          <el-input v-model="formData.otherSalesText" maxlength="100" />
        </el-form-item>
      </el-col> -->
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            :disabled="translateDisabled || isDisabled || formData.activityId"
            :autosize="{ minRows: 2, maxRows: 4 }"
            :placeholder="translateDisabled || isDisabled ? '' : '请输入'"
            type="textarea"
            maxlength="1000"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="服务费" prop="serviceCost">
          <!-- 新增 标准价 和 价格变动的选项 -->
          <el-radio-group v-model="formData.priceChangeFlag" @change="radioChange" :disabled="translateDisabled || isDisabled">
            <el-radio :label="false">标准价</el-radio>
            <el-radio :label="true" :disabled="formData.rowData?.isInContract === '1' || isPriceChange">价格变动</el-radio>
          </el-radio-group>
          <!-- formData.rowData?.isInContract === '1' 为在合同中定义 需要自己输入 -->
          <NumberInput
            v-if="!formData.priceChangeFlag"
            v-model="formData.serviceCost"
            maxlength="20"
            :regFormat="/^(0+)|[^\d]+/g"
            :disabled="translateDisabled || isDisabled || formData.rowData?.isInContract !== '1'"
            placeholder="请输入"
          >
            <!-- 如果是一次性收费 -->
            <template #suffix>
              <!-- 一次性收费 -->
              <div v-if="formData.rowData.feeType === '0'">元</div>
              <!-- 每年收费 -->
              <div v-if="formData.rowData.feeType === '1'">元/年</div>
              <!-- 每月收费 -->
              <div v-if="formData.rowData.feeType === '2'">元/月</div>
            </template>
          </NumberInput>
          <NumberInput
            v-else
            v-model="formData.serviceCost"
            :disabled="translateDisabled || isDisabled"
            maxlength="20"
            :regFormat="/^(0+)|[^\d]+/g"
            placeholder="请输入"
          >
            <!-- 如果是一次性收费 -->
            <template #suffix>
              <!-- 一次性收费 -->
              <div v-if="formData.rowData.feeType === '0'">元</div>
              <!-- 每年收费 -->
              <div v-if="formData.rowData.feeType === '1'">元/年</div>
              <!-- 每月收费 -->
              <div v-if="formData.rowData.feeType === '2'">元/月</div>
            </template>
          </NumberInput>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <!-- totalCost-->
        <el-form-item label="合同总金额" prop="totalCostCn">
          <el-input v-model="formData.totalCost" disabled> </el-input>
          <div>大写：{{ formData.totalCostCn }}</div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24" v-if="isChange">
      <el-col :span="24">
        <el-form-item label="变更原因" prop="changeReason">
          <el-input v-model="formData.changeReason" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"> </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <!-- <customerList v-if="listSelectShow" @on-close="listSelectShow = false" /> -->

  <tableModal
    v-if="listSelectShow"
    :init-param="{ discard: 0 }"
    rowKey="customerId"
    title="关联客户"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
  <tableModal
    v-if="listSelectShowActivity"
    :init-param="{ productId: formData.productId }"
    rowKey="activityId"
    title="活动优惠"
    :columns="columnsActivity"
    :request-api="getActivityListByProductId"
    @on-close="listSelectShowActivity = false"
    @on-select="handleSelectActivity"
  />
</template>
<script setup lang="tsx">
import { ref, computed, watch } from 'vue'
import FileUpload from '@/components/FileUpload'
import { FormValidators } from '@/utils/validate'
// import customerList from './customer-list.vue'
import tableModal from '@/components/tableModal'
import { getCustomers } from '@/api/customer/file'
import { ColumnProps } from '@/components/ProTable/interface'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import NumberInput from '@/components/NumberInput'
import { changeNumMoneyToChinese } from '@/utils/index.js'
import { useDic } from '@/hooks/useDic'
import { getCompanyTreeList } from '@/api/basicData/basicData'
import { getActivityListByProductId } from '@/api/business/business'
import { useDict } from '@/utils/dict'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const { getDic } = useDic()

// const formData = ref({
//   contractName: '',
//   file: undefined,
//   customerName: '',
//   ciId: undefined, // 客户id,
//   customerNo: '',
//   contactPerson: ''
// })
//

const { proxy } = getCurrentInstance()
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  },
  isShow: {
    type: Boolean,
    default: true
  },
  isDisabled: {
    type: Boolean,
    default: false
  },
  changeDisabled: {
    type: Boolean,
    default: false
  },
  translateDisabled: {
    type: Boolean,
    default: false
  },
  isChange: {
    type: Boolean,
    default: false
  },
  isAssociated: {
    type: Boolean,
    default: false
  },
  allDisabled: {
    type: Boolean,
    default: false
  },
  productTreeData: {
    type: Array,
    default: () => {
      return []
    }
  },
  rowChange: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['update:modelValue'])

const isNull = (value: string | null | undefined) => {
  return value === undefined || value === '' || value === null
}
const totalCost = computed(() => {
  // 如果是在合同中定义或者是一次性收费，合同总金额的计算方式就是服务费 + 其他费用(其他费用概念在20231102废弃)
  console.log('monthNum', formData.value.monthNum)
  if (formData.value?.rowData.feeType === '0') {
    // feeType 为零 为一次性收费
    return isNull(formData.value.serviceCost) ? '' : Number(formData.value.serviceCost || 0)
  } else {
    // 每年收费
    if (formData.value?.rowData.feeType === '1') {
      return isNull(formData.value.serviceCost) || isNull(formData.value.monthNum)
        ? ''
        : Number(formData.value.serviceCost || 0) * (Number(formData.value.monthNum || 0) / 12)
    } else {
      return isNull(formData.value.serviceCost) || isNull(formData.value.monthNum)
        ? ''
        : Number(formData.value.serviceCost || 0) * Number(formData.value.monthNum || 0)
    }
  }
})
const accNumExist = ref(false)
const handleSelectBranchOffice = (value: string) => {
  if (!value) {
    accNumEixst.value = false
    formData.value.accountNumber = ''
    return
  }
  const item = branch_office.value.find((item: any) => item.name === value)
  console.log('handleSelectBranchOffice', item)
  // formData.value.companyPerson = item.contacts
  formData.value.contactAddress = item.address // 所属公司联动的应该是乙方联系人而不是甲方联系人
  formRef.value.validateField('contactAddress')
  // formRef.value.validateField('companyPerson')

  if (item.account) {
    accNumExist.value = true
    formData.value.accountNumber = item.account
  } else {
    accNumExist.value = false
    formData.value.accountNumber = ''
  }
}
const formData = computed({
  get: () => {
    console.log('modelValue', props.modelValue, userStore.user.nickName)
    // 默认记账合同下的乙方联系人为当前账号使用人姓名

    if (!props.isDisabled && !props.isChange) {
      Object.assign(props.modelValue, {
        contactPerson: userStore.user.nickName
      })
    }

    // if (props.modelValue.branchOffice) {
    //   handleSelectBranchOffice(props.modelValue.branchOffice)
    // }

    return props.modelValue
  },
  set: newVal => {
    emits('update:modelValue', newVal)
  }
})

watch(
  totalCost,
  () => {
    console.log('totalCost', totalCost.value)
    // const num = formData.value.rowData?.isInContract === '1' ? totalCost.value || '' : totalCost.value
    const moneyCN = changeNumMoneyToChinese(totalCost.value)
    formData.value.totalCostCn = moneyCN
    formData.value.totalCost = totalCost.value
    // formData.rowData?.isInContract === '1'
    console.log('totalCostCn', formData.value.totalCostCn)
  },
  {
    immediate: true
  }
)
const nodeSearchById = (list, id) => {
  for (const node of list) {
    if (node.id === id) {
      return node
    }
    if (node.children && node.children?.length) {
      const nodeSearch = nodeSearchById(node.children, id)
      if (nodeSearch) {
        return nodeSearch
      }
    }
  }
}

// 监听 变更 存储服务产品下的服务费
watch(
  () => props.productTreeData,
  () => {
    // 查出 当前服务产品下的服务费 存储至serviceCostCopy
    const node = nodeSearchById(props.productTreeData, props.modelValue.productId)
    if (props.modelValue.priceChangeFlag) {
      console.log('quotation', node?.quotation)
      formData.value.serviceCostCopy = node?.quotation
    }
  }
)
const radioChange = value => {
  if (value) {
    // serviceCost 缓存

    formData.value.serviceCostCopy = formData.value.serviceCost
    formData.value.serviceCost = ''
  } else {
    if (formData.value.serviceCostCopy === undefined) {
      formData.value.serviceCostCopy = ''
    }
    formData.value.serviceCost = formData.value.serviceCostCopy + '' || formData.value.serviceCost
    formData.value.serviceCostCopy = ''
  }
}
watch(
  formData,
  () => {
    console.log('serviceCostCopy', formData.value.serviceCostCopy)
    console.log('serviceCost', formData.value.serviceCost)
    if (formData.value.priceChangeFlag && formData.value.serviceCostCopy === Number(formData.value.serviceCost)) {
      formData.value.priceChangeFlag = false
      formData.value.serviceCostCopy = ''
    }
  },
  {
    deep: true
  }
)
const rules = {
  customerName: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  contactPerson: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  contactPhone: [
    {
      required: true,
      message: '请输入正确的联系电话',
      validator: (rules: any, value: any, callback: any) => {
        if (value === '' || value === undefined) {
          callback('请输入')
        } else {
          FormValidators.allPhone(rules, value, callback)
        }
      },
      trigger: ['blur']
    }
  ],
  // 切换为零申报类型时。纳税人类型不可能为“进出口企业”，故此时清空纳税人类型，并且不触发change校验，写为手动触发校验
  // taxpayerType: [
  //   {
  //     required: true,
  //     message: '请选择纳税人类型',
  //     trigger: ['change']
  //   }
  // ],
  branchOffice: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  companyPerson: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  companyAddress: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  accountNumber: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  startTime: [
    {
      required: true,
      message: '请选择协议开始时间',
      trigger: ['change']
    }
  ],
  monthNum: [
    {
      required: true,
      message: '请输入服务月份(月)',
      trigger: ['blur']
    }
  ],
  salesRevenue: [
    {
      required: true,
      message: '请选择销售收入',
      trigger: ['change']
    }
  ],
  file: [
    {
      required: true,
      message: '请上传',
      trigger: ['change']
    }
  ],
  changeReason: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  serviceCost: [
    {
      required: true,
      message: '请输入服务费',
      trigger: ['blur']
    }
  ],
  declare: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
// 纳税人字典
const taxpayerTypes = [
  {
    label: '小规模纳税人',
    value: '小规模纳税人'
  },
  {
    label: '一般纳税人',
    value: '一般纳税人'
  },
  {
    label: '进出口企业',
    value: '进出口企业'
  }
]

// 销售收入字典
const salesRevenues = [
  {
    label: '年销售额100万以下',
    value: '年销售额100万以下'
  },
  {
    label: '年销售额100-500万',
    value: '年销售额100-500万'
  },
  {
    label: '年销售额500-2000万',
    value: '年销售额500-2000万'
  },
  {
    label: '年销售额2000万以上',
    value: '年销售额2000万以上'
  }
  // "其他"选项作废
  // {
  //   label: '其他',
  //   value: '其他'
  // }
]

// 乙方账号信息选择项
const { receipt_method } = proxy.useDict('receipt_method')
// 客户列表弹窗显示
const listSelectShow = ref(false)
// 选择单个客户后
const handleSelect = (data: any) => {
  console.log('data', data)
  // 关联客户的数据有 客户名称  客户id(ciId/customerId) 客户编号 联系人 联系电话 所属公司
  const {
    customerName,
    customerId,
    customerNo,
    contactPerson,
    contactPhone,
    branchOffice,
    companyPerson,
    companyPhone,
    companyAddress
  } = data
  // 填充记账合同表单
  formData.value = Object.assign(formData.value, {
    customerName,
    customerId,
    customerNo,
    contactPerson,
    contactPhone,
    branchOffice,
    companyPerson,
    companyPhone,
    companyAddress
  })

  // 自动校验联系人和联系电话
  formRef.value.validateField('contactPerson')
  formRef.value.validateField('contactPhone')
  formRef.value.validateField('branchOffice')
  formRef.value.validateField('companyPerson')
  formRef.value.validateField('companyPhone')
  formRef.value.validateField('companyAddress')

  //
  if (branchOffice) {
    handleSelectBranchOffice(branchOffice)
  }
}
// 服务月份 改变触发事件
const handleInputChange = (pre: number | undefined) => {
  // const num = (pre as number) / 12
  // const yearGap = num < 1 ? 0 : num
  const startTime = formData.value.startTime
  if (startTime) {
    calculateEndTime(startTime, pre)
  }
}

// 计算合同结束时间
const calculateEndTime = (startTime: string, monthNum: number | undefined) => {
  const arr = startTime.split('-')
  // 起始时间的年份
  const year = Number(arr[0])
  // 起始时间的月份加上monthNum
  const total = Number(arr[1]) + (monthNum as number) - 1
  // 间隔年份
  let yearGap = total <= 12 ? 0 : Math.floor(total / 12)
  // 月份取余
  const mod = total <= 12 ? total : total % 12
  const endMonth = mod === 0 ? 12 : mod
  if (mod === 0) {
    yearGap = yearGap - 1
  }
  const endMonth1 = endMonth < 10 ? '0' + endMonth : endMonth
  formData.value.endTime = year + yearGap + '-' + endMonth1
}

// 合同起始时间改变触发事件
const handleDateChange = (value: string) => {
  const monthNum = formData.value.monthNum
  if (monthNum) {
    // const yearGap = monthNum / 12
    calculateEndTime(value, monthNum)
  }
}

//handleSelectChange
const handleSelectChange = (value: string) => {
  console.log('value', value)
  if (value !== '其他') {
    formData.value.otherSalesText = ''
  }
}

const handleChangeDeclare = () => {
  if (formData.value.declare === '0' && formData.value.taxpayerType === '进出口企业') {
    formData.value.taxpayerType = undefined
  }
}

const handleChangeTaxpayerType = () => {
  formRef.value.validateField('taxpayerType')
}

// 处理所属公司下拉框
const branch_office = ref<any>([])
function onGetBasicData() {
  getCompanyTreeList({ enable: 1 }).then(res => {
    branch_office.value = res.data.records
    //
    if (formData.value.branchOffice && !props.allDisabled) {
      handleSelectBranchOffice(formData.value.branchOffice)
    }
  })
}
onGetBasicData()

// 表单校验
const formRef = ref()
const validateForm = async () => {
  console.log('validateForm')
  console.log('formRef', formRef.value)
  return await formRef.value.validate((valid: any) => {
    if (valid) {
    } else {
    }
  })
}
// 关联客户弹窗显示
const handleShow = () => {
  // 如果是详情状态 不需要显示弹窗
  if (props.isDisabled) return
  if (props.isAssociated) return
  if (props.changeDisabled) return
  if (props.translateDisabled) return
  listSelectShow.value = true
}

/** 活动优惠---start  */
const columnsActivity: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'activityQuotation',
    label: '活动报价',
    width: '100',
    render: scope => {
      return <span>{scope.row.activityQuotation || '--'}元</span>
    }
  },
  {
    prop: 'discountTime',
    label: '时长',
    width: '100',
    render: scope => {
      return <span>{scope.row.discountTime || '--'}月</span>
    }
  },
  {
    prop: 'remark',
    label: '备注',
    minWidth: '300'
  }
]
const listSelectShowActivity = ref(false)
const handleShowActivity = () => {
  // 如果是详情状态 不需要显示弹窗
  if (props.isDisabled) return
  if (props.translateDisabled) return
  if (!formData.value.productId) {
    return proxy.$modal.msgWarning('请先选择服务产品')
  }
  formRef.value.validateField('startTime')
  if (!formData.value.startTime) {
    return proxy.$modal.msgWarning('请先选择合同起始时间')
  }
  listSelectShowActivity.value = true
}

const isPriceChange = ref(false)
const setPrice = () => {
  formData.value.priceChangeFlag = false
  radioChange(false)
  isPriceChange.value = true
}
const handleSelectActivity = (data: any) => {
  console.log('data', data)
  const { activityId, activityQuotation, discountTime } = data
  const activityTxt = discountTime ? `${activityQuotation}元，${discountTime}月` : `${activityQuotation}元`
  Object.assign(formData.value, {
    activityId,
    activityTxt,
    activityQuotation, // 临时挂载到formData中用于后续处理数据
    discountTime // 临时挂载到formData中用于后续处理数据
  })
  // 选择了活动价之后 , 服务费定位标准价，不能修改
  setPrice()

  formData.value.remark = getRemarkTxt()
}
const handleClearActivity = () => {
  formData.value.activityId = undefined
  formData.value.activityTxt = undefined
  formData.value.remark = undefined

  isPriceChange.value = false
}
const getRemarkTxt = () => {
  const t1 = formData.value.activityQuotation
    ? `活动价 <${formData.value.activityQuotation}元>（人民币：<${changeNumMoneyToChinese(formData.value.activityQuotation)}>）`
    : ``
  const t2 = formData.value.discountTime
    ? `时间<${formData.value.startTime}>至<${dayjs(formData.value.startTime)
        .add(formData.value.discountTime, 'month')
        .add(-1, 'day')
        .format('YYYY-MM')}>`
    : formData.value.monthNum
    ? `时间<${formData.value.startTime}>至<${dayjs(formData.value.startTime)
        .add(formData.value.monthNum, 'month')
        .add(-1, 'day')
        .format('YYYY-MM')}>`
    : ``
  const remark = formData.value.startTime && formData.value.activityId ? (t1 && t2 ? `${t1}，${t2}` : t1 ? t1 : t2 ? t2 : ``) : ``
  return remark
}
/** 活动优惠---end  */
watch(
  () => [formData.value.activityId, formData.value.startTime, formData.value.monthNum],
  () => {
    console.log('getRemarkTxt---watching', getRemarkTxt())
    formData.value.remark = getRemarkTxt() || formData.value.remark
    console.log('getRemarkTxt', formData.value.remark)
    if (formData.value.remark) {
      isPriceChange.value = true
    }
  },
  {
    immediate: true
  }
)
// 校验上传文件
const validateLoadSuccess = () => {
  formRef.value.validateField('file')
}
const defaultPopsFunction = (row: any) => {
  return {
    value: 'id',
    label: 'name',
    disabled: (data: { type: string; children: string | any[] }) => {
      return data?.type === '业务类型' && !data?.children.length
    }
  }
}
// 树节点选中事件触发
const treeSelectChange = (node: { type: string; isInContract: any; feeType: any; quotation: string }) => {
  console.log('treeSelectChange', node)
  if (node.type === '产品类型') {
    formData.value.rowData.isInContract = node.isInContract
    formData.value.rowData.feeType = node.feeType
    formData.value.serviceCost = node.quotation || ''
    formData.value.activityId = undefined
    formData.value.activityTxt = undefined
    formData.value.remark = undefined
  }
}
defineExpose({
  validateForm
})
</script>
<style lang="scss" scoped>
.m-t {
  margin-top: 12px;
}
.el-select.el-select--default {
  width: 300px;
}
:deep(.el-date-editor) {
  width: 300px;
}

.file-width {
  width: 100%;
}

.el-input-number.el-input-number--default {
  width: 100%;
}
</style>
