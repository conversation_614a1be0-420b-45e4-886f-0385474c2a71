<template>
  <ProTable ref="proTable" title="地址供应商" :columns="columns" :request-api="getAddressProviderList">
    <template #supplier="{ row }">
      <span class="blue-text" @click="handleCheckDetail(row)">{{ row.supplier }}</span>
    </template>

    <template #validTo="{ row }">
      <span :class="[row.isExpired === '1' ? 'danger-text' : '']">{{ row.validTo || '--' }}</span>
    </template>
    <template #addressCost="{ row }">
      <span>{{ row.addressCost ? `${row.addressCost}元` : '--' }}</span>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
    </template>
    <!-- 表格操作 -->
    <template #operation="{ row }">
      <!-- 编辑业务 -->
      <el-button type="primary" link @click="handleUpdate(row)">编辑</el-button>
      <!-- 删除业务 -->
      <el-button type="danger" link @click="handleRemove(row)">删除</el-button>
    </template>
  </ProTable>
</template>
<script setup lang="tsx">
import {
  getAddressProviderList,
  saveUpdateAddressProvider,
  getAddressProviderDetailById,
  removeAddressProvider,
  changeProviderStatus
} from '@/api/address-provider'
import { ColumnProps } from '@/components/ProTable/interface'
import providerForm from './components/provider-form'
import { useDialog } from '@/hooks/useDialog'
import { useHandleData } from '@/hooks/useHandleData'
import { useDic } from '@/hooks/useDic'
const { getDic } = useDic()
const { showDialog } = useDialog()
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    width: 100,
    label: '序号',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'supplier',
    label: '供应商名称',
    width: 200,
    search: { el: 'input' }
  },
  {
    prop: 'addressCost',
    width: 100,
    label: '地址成本'
  },
  {
    prop: 'phone',
    width: 200,
    label: '电话号码'
  },
  {
    prop: 'bankAccount',
    width: 350,
    label: '银行账户'
  },
  {
    prop: 'feeType',
    label: '收费性质',
    width: 100,
    enum: getDic('fee_type'),
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    }
  },
  {
    prop: 'validTo',
    width: 150,
    label: '有效期至'
  },
  {
    prop: 'remark',
    label: '备注'
  },
  {
    prop: 'enable',
    width: 150,
    label: '状态',
    render: scope => {
      return (
        <>
          {
            <el-switch
              model-value={scope.row.enable}
              active-text={scope.row.enable === '1' ? '正常' : '停用'}
              active-value={'1'}
              inactive-value={'0'}
              onClick={() => changeStatus(scope.row)}
            />
          }
        </>
      )
    }
  },
  {
    prop: 'operation',
    width: 150,
    fixed: 'right',
    label: '操作'
  }
]

// 修改状态
const changeStatus = async (row: any) => {
  await useHandleData(changeProviderStatus, row.id, `切换【${row.supplier}】状态`)
  proTable.value?.getTableList()
}

// 新增供应商
const handleAdd = () => {
  showDialog({
    title: '新增地址供应商',
    customClass: 'address-provider-modal',
    cancelButtonText: '关闭',
    confirmButtonText: '保存',
    component: providerForm,
    submitApi: saveUpdateAddressProvider,
    submitCallback: () => {
      getList()
    }
    // handleRevertParams: handleRevertProductParams
  })
}

// 查看地址供应商详情
const handleCheckDetail = row => {
  showDialog({
    title: '详情',
    customClass: 'address-provider-modal',
    cancelButtonText: '关闭',
    showConfirmButton: false,
    requestParams: row.id,
    handleConvertParams: data => {
      data.isDisabled = true
    },
    getApi: getAddressProviderDetailById,
    component: providerForm
  })
}

// handleUpdate
const handleUpdate = row => {
  showDialog({
    title: '编辑地址供应商',
    customClass: 'address-provider-modal',
    cancelButtonText: '关闭',
    confirmButtonText: '保存',
    component: providerForm,
    requestParams: row.id,
    getApi: getAddressProviderDetailById,
    submitApi: saveUpdateAddressProvider,
    submitCallback: () => {
      getList()
    }
  })
}
const handleRemove = async (row: any) => {
  await useHandleData(removeAddressProvider, row.id, `删除所选地址供应商 ${row.supplier} 信息`)
  proTable.value?.getTableList()
}
const proTable = ref()
const getList = () => {
  proTable.value.getTableList()
}
</script>
<style lang="scss" scoped>
.danger-text {
  color: #f56c6c;
}
</style>
