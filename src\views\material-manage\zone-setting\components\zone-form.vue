<!--
 * @Description: 公海配置表单
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-11-16 08:58:39
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="isDisabled">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="公海类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择" :disabled="formData.id">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in zone_setting" :key="index" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="公海名称" prop="name">
          <el-input v-model="formData.name" maxlength="20" placeholder="请输入" :disabled="isDisabled" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="公海管理员" prop="manageId">
          <SelectTree v-model="formData.manageId" placeholder="请选择" :disabled="isDisabled" clearable />
        </el-form-item>
      </el-col>
      <!-- 新增公海成员(多部门) -->
      <el-col :span="12">
        <el-form-item label="公海成员" prop="deptIds">
          <el-tree-select
            v-model="formData.deptIds"
            :data="deptOptions"
            :disabled="isDisabled"
            value-key="id"
            multiple
            check-strictly
            :render-after-expand="false"
            :props="{ label: 'label', children: 'children' }"
            show-checkbox
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="资源回收规则" prop="duration">
          超过
          <NumberInput
            class="input-w"
            v-model="formData.duration"
            :regFormat="/^(0+)|[^\d]+/g"
            :placeholder="isDisabled ? '' : '请输入'"
            :disabled="isDisabled"
          >
            <template #suffix> 天 </template>
          </NumberInput>

          <span> 未产生跟进、转化，资源回收至公海</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="资源分配规则" prop="recovery">
          提前
          <NumberInput
            class="input-w"
            v-model="formData.recovery"
            :regFormat="/^(0+)|[^\d]+/g"
            :placeholder="isDisabled ? '' : '请输入'"
            :disabled="isDisabled"
          >
            <template #suffix> 天 </template>
          </NumberInput>

          <span> 提醒{{ formData.type === '0' ? '线索' : '客户' }}跟进人员资源即将被回收</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            :disabled="isDisabled"
            maxlength="1000"
            type="textarea"
            :placeholder="isDisabled ? '' : '请输入'"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="资源分配规则" prop="rule">
          <el-radio-group v-model="formData.rule" :disabled="isDisabled">
            <el-radio label="0">员工领取</el-radio>
            <el-radio label="1">仅管理员分配</el-radio>
            <el-radio label="2">员工领取+管理员分配</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { deptTreeSelect } from '@/api/system/user'
import { reactive } from 'vue'
import NumberInput from '@/components/NumberInput'

import SelectTree from '@/components/SelectTree'
const { proxy } = getCurrentInstance()

const { zone_setting } = proxy.useDict('zone_setting')
const formData = reactive({
  name: '',
  type: '',
  manageId: '',
  duration: '',
  recovery: '',
  remark: '',
  deptIds: [],
  rule: '0',
  id: undefined
})

const isDisabled = computed(() => {
  return formData.zoneType === 'detail'
})

const rules = {
  type: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  name: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  manageId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

const deptOptions = ref(undefined)

/** 查询部门下拉树结构 */
const getDeptTree = () => {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data
  })
}
getDeptTree()

defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
.input-w {
  width: 100px;
  margin-right: 8px;
  margin-left: 8px;
}
</style>
