<template>
  <dataWrap
    title="业绩金额"
    class="bottom"
    ref="wrapRef"
    :selectOptions="selectOptions"
    :request-api="getBusinessMoney"
    @on-select="drawChart"
    :charts="charts"
  >
    <template #default>
      <div class="left" ref="leftChart"></div>
      <div class="right" ref="rightChart"></div>
    </template>
  </dataWrap>
</template>
<script setup>
import { onMounted } from 'vue'
import dataWrap from '../../collecting-panel/components/data-wrap'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { getBusinessMoneyPie, getBusinessMoneyLine } from '@/api/panel-data/sale'
import { getBusinessList } from '@/api/business/business'
import { getRandomColorHex } from '@/utils/index'
const defaultValue = ref()

let callback = null
const promiseResolve = new Promise((resolve, reject) => {
  callback = resolve
})
const getProduct = () => {
  return new Promise(async (resolve, reject) => {
    try {
      const { data } = await getBusinessList({
        pageSize: 1000,
        pageNum: 1
      })

      // 将后端传回的数据结构进行转换
      const revertData = []
      data.forEach(item => {
        const obj = {
          name: item.typeName,
          id: item.id,
          type: '业务类型',
          children: []
        }
        revertData.push(obj)
        if (Array.isArray(item.child) && item.child.length) {
          item.child.forEach(child => {
            obj.children.push({
              name: child.productName,
              type: '产品类型',
              id: child.id // 产品类型id
            })
          })
        }
      })
      defaultValue.value = revertData[0].id
      resolve({ data: revertData })
      callback({ data: revertData })
    } catch (error) {
      reject(error)
    }
  })
}
const selectOptions = [
  {
    prop: 'yearMonth',
    type: 'date-picker',
    defaultValue: dayjs().format('YYYY-MM'),
    props: { type: 'month', value: 'YYYY-MM', valueFormat: 'YYYY-MM' }
  },
  {
    prop: 'productId',
    type: 'tree-select',
    defaultValue: defaultValue,
    enums: getProduct
  }
]

const getBusinessMoney = async data => {
  const result = await promiseResolve

  // 默认第一次搜索的时候
  if (!data.productId) {
    data.productId = result.data[0].id
  }
  const pieData = await getBusinessMoneyPie({
    yearMonth: data.yearMonth
  })
  const lineData = await getBusinessMoneyLine(data)

  return {
    data: {
      pieData: pieData.data,
      lineData: lineData.data
    }
  }
}

const wrapRef = ref()
const leftChart = ref()
const charts = ref([])
const drawLeftChart = () => {
  let myChart = echarts.init(leftChart.value)
  charts.value[0] = myChart
  const panelData = wrapRef.value.panelData
  console.log('panelData', panelData.pieData)
  const totalNum = panelData.pieData.reduce(function (prev, curr) {
    return Number(prev) + Number(curr.value)
  }, 0)
  console.log('totalNum', totalNum)
  // const colors = panelData.pieData.map(item => getRandomColorHex())
  const option = {
    tooltip: {
      trigger: 'item'
    },
    // color: colors,
    legend: {
      orient: 'vertical',
      right: '8%',
      top: '25%',
      type: 'scroll'
    },

    series: [
      {
        type: 'pie',
        radius: '70%',
        center: ['35%', '50%'],
        data: totalNum === 0 ? [] : (panelData.pieData || []).filter(item => item.value !== '0' && item.value),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        itemStyle: {
          normal: {
            label: {
              show: true,
              formatter: '{d}%'
              // formatter: dec => {
              //   console.log('totalNum', totalNum)
              //   if (totalNum === 0) {
              //     return '0%'
              //   } else {
              //     return Number(((Number(dec.data.value) / totalNum) * 100).toFixed(1)) + '%'
              //   }
              // }
            },
            labelLine: {
              shoe: true
            }
          }
        }
      }
    ]
  }

  myChart.setOption(option)
}

const rightChart = ref()
const drawRightChart = () => {
  console.log('wrapRef', wrapRef.value.panelData)
  let myChart = echarts.init(rightChart.value)

  const lineData = wrapRef.value.panelData.lineData
  console.log('lineData', lineData)
  charts.value[1] = myChart
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['上一年度', '本年度']
    },
    grid: {
      left: '2%',
      top: '12%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },

    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '上一年度',
        type: 'line',

        // data: [20, 42, 60, 20, 20, 50, 90]
        data: lineData.map(item => item.value.lastAmount)
      },
      {
        name: '本年度',
        type: 'line',

        // data: [20, 42, 60, 20, 20, 50, 90],
        data: lineData.map(item => item.value.nowAmount)
        // data: wrapRef.value.panelData.map(item => item.value2)
      }
    ]
  }
  myChart.setOption(option)
}

const drawChart = () => {
  drawLeftChart()
  drawRightChart()
}
onMounted(async () => {
  await wrapRef.value.requestResult
  drawChart()
})
</script>
<style lang="scss" scoped>
.bottom {
  flex: 1;
  .left {
    border-right: 1px solid #e8e8e8ff;
  }
  .left,
  .right {
    flex: 1;
  }
}
</style>
