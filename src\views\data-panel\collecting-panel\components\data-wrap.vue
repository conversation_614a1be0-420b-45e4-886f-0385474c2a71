<!--
 * @Description: 数据title 
 * @Author: thb
 * @Date: 2023-09-04 09:41:03
 * @LastEditTime: 2023-11-07 09:17:28
 * @LastEditors: thb
-->
<template>
  <div class="container">
    <div class="header-title">
      <span class="title">{{ title }}</span>
      <div class="right">
        <!-- 动态组件渲染 -->
        <template v-for="(select, index) in selectOptions" :key="index">
          <component
            :is="selectMap[select.type]"
            v-model="searchOptions[select.prop]"
            :options="enumMap.get(select.prop)"
            v-bind="select['props']"
            :type="select['props'] ? select['props']['type'] : ''"
            @change="handleSelectChange"
            :clearable="false"
          />
        </template>
      </div>
    </div>
    <!-- 数据展示区域 -->
    <div class="content"><slot name="default" :data="panelData"> </slot></div>
  </div>
</template>
<script setup>
import rightSelect from './right-select'
import rightDate from './right-date'
import rightTree from './right-tree'
const selectMap = {
  select: rightSelect,
  'date-picker': rightDate,
  'tree-select': rightTree
}
const props = defineProps({
  title: {
    type: String,
    default: '标题'
  },
  selectOptions: {
    type: Array,
    default: () => {
      return []
    }
  },
  requestApi: {
    type: Function,
    default: () => {}
  },
  handleRequestParams: {
    type: Function,
    default: () => {}
  },
  charts: {
    type: Array,
    default: () => {
      return []
    }
  }
})

const searchOptions = ref({})
const addSearchOptions = options => {
  options.forEach(option => {
    const { prop } = option
    if (prop) {
      searchOptions.value[prop] = ''
    }
  })
}
// 添加searchOptions参数
addSearchOptions(props.selectOptions)

// 处理选择器下拉选择项集合
const enumMap = ref(new Map())
provide('enumMap', enumMap)
const setEnumMap = async option => {
  console.log('enumMap', enumMap.value, option)
  if (!option.enums) return
  // // 如果当前 enum 为后台数据需要请求数据，则调用该请求接口，并存储到 enumMap
  if (typeof option.enums !== 'function') return enumMap.value.set(option.prop, option.enums)
  const { data } = await option.enums()
  enumMap.value.set(option.prop, data)
}

// 初始化接口传递参数
const initParam = {}
const addInitParam = options => {
  options.forEach(option => {
    const { prop, defaultValue } = option
    if (prop && defaultValue) {
      initParam[prop] = defaultValue
      searchOptions.value[prop] = defaultValue
    }

    setEnumMap(option)
  })
}
// 添加默认的初始化参数
addInitParam(props.selectOptions)
// 初始化请求
let resolveCallback = null
let rejectCallback = null
let requestResult = new Promise((resolve, reject) => {
  resolveCallback = resolve
  rejectCallback = reject
})

const panelData = ref({})
const getData = async () => {
  try {
    const requestParams = {
      ...initParam,
      ...searchOptions.value
      // selectOptions 的选择参数
    }
    props.handleRequestParams && props.handleRequestParams(requestParams)
    const { data } = await props.requestApi(requestParams)
    panelData.value = data
    resolveCallback()
  } catch (error) {
    rejectCallback(error)
  }
}

// 右侧选择框change事件触发
const emits = defineEmits(['on-select'])
const handleSelectChange = async value => {
  console.log('handleSelectChange', value)
  await getData()
  emits('on-select')
}
const registerEventListener = () => {
  window.addEventListener('resize', () => {
    console.log('resize')
    console.log('charts', props.charts)
    props.charts.forEach(chart => {
      chart.resize()
    })
  })
}
onMounted(() => {
  props.requestApi && getData()
  registerEventListener()
})
defineExpose({
  panelData,
  requestResult
})
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  background: #fff;
  padding: 15px 20px;
  border-radius: 4px;
}
.content {
  height: calc(100% - 44px);
  display: flex;
}
.header-title {
  width: 100%;
  display: flex;
  height: 32px;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.title {
  font-size: 18px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  color: #333333;
  display: flex;
  align-items: center;
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 20px;
    background: #2383e7;
    margin-right: 12px;
  }
}
.right {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}
</style>
