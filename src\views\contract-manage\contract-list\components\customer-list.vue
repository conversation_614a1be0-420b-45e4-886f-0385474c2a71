<template>
  <el-dialog
    align-center
    width="1200"
    class="contract-add-dialog"
    title="关联客户"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <ProTable ref="proTable" :columns="columns" :request-api="getCustomers" @select="handleSingleSelect" />

    <template #footer>
      <el-button type="primary" @click="confirmSelect">确认选择</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="tsx">
import { ref } from 'vue'
import { getCustomers } from '@/api/customer/file'
import { ColumnProps } from '@/components/ProTable/interface'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import { useDict } from '@/utils/dict'
import { useDic } from '@/hooks/useDic'
const { getDic } = useDic()
const { proxy } = getCurrentInstance()
const { customer_status } = proxy.useDict('customer_status')
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-select'])
const handleClose = () => {
  emits('on-close')
}
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: customer_status.value.concat([
      {
        label: '废弃客户',
        value: '废弃客户'
      }
    ]),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    enum: getDic('customer_property', [
      {
        label: '废弃客户',
        value: '废弃客户'
      }
    ]),
    // enum: customerProperty.concat([
    //   {
    //     label: '废弃客户',
    //     value: '废弃客户'
    //   }
    // ]),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

// 单选
let selectRow: any = null
const proTable = ref()
const handleSingleSelect = (selection: any[], row: any) => {
  selectRow = row
  proTable.value?.clearSelection()
  if (selection.length === 0) return
  proTable.value?.toggleRowSelection(row, true)
}
// 确认选择

const confirmSelect = () => {
  console.log('selectRow', selectRow)
  if (!selectRow) {
    // 没有选择客户, 提示应该选择客户
    proxy.$modal.msgWarning('请选择某一行!')
  } else {
    // 说明选择了某一个客户
    handleClose()
    emits('on-select', selectRow)
  }
}
</script>
<style lang="scss" scoped></style>
