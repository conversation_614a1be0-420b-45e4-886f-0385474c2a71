<template>
  <el-form ref="formRef" :hide-required-asterisk="disabled" :model="data" :rules="rules" label-width="120px" label-position="top">
    <el-row :gutter="24">
      <rowCheck
        v-for="(value, key) in formItemMap"
        :data="data"
        :key="key"
        :label="value"
        :labelKey="key"
        @on-load-success="validateFormFiled"
        @on-preview="previewFile"
      ></rowCheck>
    </el-row>
  </el-form>
</template>
<script setup>
import { FormValidators } from '@/utils/validate'
import rowCheck from './row-check'
defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const formItemMap = {
  copyOfLicenseFileList: '执照正副本',
  legalIdentityFileList: '法人身份证',
  officialSealFileList: '公章',
  financialSealFileList: '财务章',
  corporateSealFileList: '法人章',
  basicDepositAccountFileList: '基本存款账户信息表',
  passwordPaperFileList: '验资密码纸'
}
// 劳务派遣表单检验规则
const rules = {
  copyOfLicenseFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  officialSealFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  legalIdentityFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  financialSealFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  corporateSealFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  basicDepositAccountFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  passwordPaperFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
// 预览文件
const emits = defineEmits('on-preview')
const previewFile = file => {
  emits('on-preview', file)
}
const formRef = ref()

// 文件上传后检验
const validateFormFiled = field => {
  formRef.value.validateField(field)
}
defineExpose({
  formRef,
  rules
})
</script>
<style lang="scss" scoped>
.el-checkbox {
  margin-right: 8px;
}
:deep(.el-col.el-col-12.is-guttered) {
  display: flex;
  align-items: baseline;
  .el-form-item {
    flex: 1;
  }
}
</style>
