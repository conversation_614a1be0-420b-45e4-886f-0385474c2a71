<template>
  <div class="my-container">
    <div class="left">
      <!-- 如果加载速度慢，就在切换到这步前提前加载 -->
      <!-- div/v-html -->
      <div id="printBox" class="my-office-docx" v-html="formData.htmlStr"></div>
      <!-- vue-office-docx -->
      <!-- <vue-office-docx ref="docxRef" :src="docx" style="height: 800px" @rendered="renderedHandler" @error="errorHandler" /> -->
    </div>
    <div class="right my-office-form">
      <el-form ref="formTemplateRef" :model="formData" :rules="rules" label-position="top">
        <!-- <template v-if="currentMode === 'development'">
          <div>formData.contractType-{{ formData?.contractType }}-(0 记账合同 1 一次性合同 2 地址服务协议合同)</div>
          <div>formData.feeType-{{ formData?.feeType }}-(0 一次性收费 1 每年收费 2 每月收费)</div>
          <div>formData.isInContract-{{ formData?.isInContract }}</div></template
        > -->
        <template v-for="(formItem, index) in fieldFormArr" :key="index">
          <el-form-item
            v-if="
              !formItem.hide &&
              (!formItem.whenShow || (formItem.whenShow && formData[formItem.whenShow[0]] === formItem.whenShow[1]))
            "
            :label="formItem.fieldName"
            :prop="formItem.fieldCode"
          >
            <!-- 目前只有productId -->
            <template v-if="formItem.type === 'treeSelect'">
              <el-tree-select
                :disabled="formItem.disabled"
                style="width: 100%"
                v-model="formData.productId"
                :data="productTreeData"
                :props="defaultPopsFunction()"
                node-key="id"
                :render-after-expand="false"
                default-expand-all
              />
            </template>
            <el-select
              v-else-if="formItem.type === 'select'"
              :disabled="!formData[`${formItem.fieldCode}_empty`] && formItem.disabled"
              @change="e => handleChange(e, formItem)"
              v-bind="formItem.props"
              :clearable="formItem.clearable"
              v-model="formData[formItem.fieldCode]"
              style="width: 100%"
              placeholder=" "
            >
              <el-option v-for="item in formItem.option" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-date-picker
              v-else-if="formItem.type === 'date'"
              :disabled="formItem.disabled"
              @change="e => handleChange(e, formItem)"
              v-model="formData[formItem.fieldCode]"
              style="width: 100%"
              placeholder=" "
              :type="formData?.contractType === '2' ? 'date' : 'month'"
              :format="formData?.contractType === '2' ? 'YYYY-MM-DD' : 'YYYY-MM'"
              :value-format="formData?.contractType === '2' ? 'YYYY-MM-DD' : 'YYYY-MM'"
              :disabledDate="disabledDate"
            />
            <el-input-number
              v-else-if="formItem.type === 'inputNumber'"
              :disabled="formItem.disabled"
              @change="e => handleChange(e, formItem)"
              v-model="formData[formItem.fieldCode]"
              style="width: 100%"
              step-strictly
              :step="formItem.step ? formItem.step : formData.feeType === '1' && formData.contractType !== '2' ? 12 : 1"
              :min="formItem.step ? formItem.step : formData.feeType === '1' && formData.contractType !== '2' ? 12 : 1"
              placeholder=" "
              controls-position="right"
              ><template #suffix v-if="formItem.unit">
                <div>{{ formItem.unit }}</div>
              </template></el-input-number
            >
            <!-- 目前只有totalCostCn -->
            <template v-else-if="formItem.type === 'extraText'">
              <el-input
                :disabled="!formData[`${formItem.fieldCode}_empty`] && formItem.disabled"
                @change="e => handleChange(e, formItem)"
                v-model="formData.totalCost"
                placeholder=" "
                ><template #suffix v-if="formItem.unit">
                  <div>{{ formItem.unit }}</div>
                </template></el-input
              >
              <div>大写：{{ formData.totalCostCn }}</div>
            </template>
            <!-- 目前只有activityTxt -->
            <template v-else-if="formItem.type === 'inputSearch'">
              <el-input
                :disabled="!formData[`${formItem.fieldCode}_empty`] && formItem.disabled"
                clearable
                @click="handleShowActivity"
                @clear="handleClearActivity"
                v-model="formData.activityTxt"
                placeholder=" "
              ></el-input>
            </template>
            <el-input
              v-else-if="formItem.type === 'textarea'"
              type="textarea"
              :disabled="!formData[`${formItem.fieldCode}_empty`] && formItem.disabled"
              @change="e => handleChange(e, formItem)"
              v-model="formData[formItem.fieldCode]"
              placeholder=" "
            ></el-input>
            <template v-else>
              <template v-if="formItem.fieldCode === 'serviceCost'">
                <el-radio-group v-model="formData.priceChangeFlag" @change="radioChange" :disabled="formData.isIntention === '1'">
                  <el-radio :label="false">标准价</el-radio>
                  <el-radio :label="true" :disabled="formData.isInContract === '1' || isPriceChange">价格变动</el-radio>
                </el-radio-group>
                <el-input
                  v-if="!formData.priceChangeFlag"
                  :disabled="(!formData[`${formItem.fieldCode}_empty`] && formItem.disabled) || formData.isIntention === '1'"
                  :maxlength="formItem.maxlength || 256"
                  @change="e => handleChange(e, formItem)"
                  v-model="formData[formItem.fieldCode]"
                  placeholder=" "
                  ><template #suffix v-if="formItem.unit">
                    <div>{{ formItem.unit }}</div>
                  </template></el-input
                >
                <el-input
                  v-if="formData.priceChangeFlag"
                  :maxlength="formItem.maxlength || 256"
                  @change="e => handleChange(e, formItem)"
                  :disabled="formData.isIntention === '1'"
                  v-model="formData[formItem.fieldCode]"
                  placeholder=" "
                  ><template #suffix v-if="formItem.unit">
                    <div>{{ formItem.unit }}</div>
                  </template></el-input
                >
              </template>
              <template v-else>
                <el-input
                  :disabled="!formData[`${formItem.fieldCode}_empty`] && formItem.disabled"
                  :maxlength="formItem.maxlength || 256"
                  @change="e => handleChange(e, formItem)"
                  v-model="formData[formItem.fieldCode]"
                  placeholder=" "
                  ><template #suffix v-if="formItem.unit">
                    <div>{{ formItem.unit }}</div>
                  </template></el-input
                >
              </template>
            </template>

            <div v-if="currentMode === 'development'">
              <!-- <span>【{{ formItem.whenShow && formData[formItem.whenShow[0]] === formItem.whenShow[1] }}】</span> -->
              <span>【{{ formItem.fieldCode }}】</span>
              <!-- <span>【{{ formData[formItem.fieldCode] }}-{{ formData[`${formItem.fieldCode}_empty`] }}】</span> -->
            </div>
          </el-form-item>
        </template>
      </el-form>
    </div>
  </div>
  <tableModal
    v-if="listSelectShowActivity"
    :init-param="{ productId: formData.productId }"
    rowKey="activityId"
    title="活动优惠"
    :columns="columnsActivity"
    :request-api="getActivityListByProductId"
    @on-close="listSelectShowActivity = false"
    @on-select="handleSelectActivity"
  />
</template>
<script setup lang="jsx">
import {
  getContractTempList,
  getContractTempGetFieldList,
  getContractTempGetById
} from '@/api/contract-template/contract-template.js'
import { checkContractChangeIsPermission, saveContract, customerContractChangeToFormal } from '@/api/contract/contract'
import { getBusinessList } from '@/api/business/business'
import { getCompanyTreeList } from '@/api/basicData/basicData'
import { nextTick, reactive, watch } from 'vue'
import { customerProperty, customerIndustry } from '@/utils/constants'
import { contractTypeArr } from '@/utils/constants.js'
import { changeNumMoneyToChinese } from '@/utils/index.js'
import dayjs from 'dayjs'
import tableModal from '@/components/tableModal'
import { getActivityListByProductId } from '@/api/business/business'
import { ElMessageBox } from 'element-plus'
import { FormValidators } from '@/utils/validate'
import printJS from 'print-js'
import { useSetDic } from '@/hooks/useSetDic'

import useUserStore from '@/store/modules/user'

const disabledDate = time => {
  if (formData.contractId && !props.isRenewal) {
    return !(
      time.getTime() > new Date(formData.startTime).getTime() - 8.64e7 &&
      time.getTime() < new Date(formData.endTime).getTime() + 8.64e7
    )
  }
}

const userStore = useUserStore()
const { setDic } = useSetDic()
// import VueOfficeDocx from '@vue-office/docx'
// import '@vue-office/docx/lib/index.css'
// import { asBlob } from 'html-docx-js-typescript'
// import { saveAs } from 'file-saver'

const currentMode = import.meta.env.MODE
const props = defineProps({
  id: Number,
  formDataPreStep: {
    type: Object,
    default: () => {
      return {}
    }
  },
  isRenewal: {
    type: Boolean,
    default: false
  }
})

const { proxy } = getCurrentInstance()
// const { branch_office } = proxy.useBasicDict('branch_office')
const branch_office = ref([])
function onGetBasicData() {
  getCompanyTreeList({ enable: 1 }).then(res => {
    branch_office.value = res.data.records // 已验证，新增下拉有对应数据
  })
}
onGetBasicData()

// 乙方账户信息下拉框数据
const { receipt_method } = proxy.useDict('receipt_method')

// 获取变更科目的选择项集合
const { change_project } = proxy.useDict('change_project')

const emits = defineEmits(['on-success'])

const active = ref(0)

const formData = reactive({
  type: 1, // type = 1 表示模板创建合同
  priceChangeFlag: false,
  changeSubjectList: [] // 默认changeSubjectList为空
})
const fieldFormArr = reactive([])

const rules = {
  customerName: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  productId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  contractType: [{ required: true, message: '请选择', trigger: ['blur'] }], // 不能写change，额外手动触发校验
  tempId: [{ required: true, message: '请选择', trigger: ['blur'] }], // 不能写change，额外手动触发校验
  changeReason: [{ required: true, message: '请输入', trigger: ['blur'] }],
  taxpayerType: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  branchOffice: [{ required: true, message: '请输入', trigger: ['blur'] }],
  identityNumber: [
    {
      message: '请输入正确的身份证号',
      trigger: 'blur',
      validator: FormValidators.idCardDif
    }
  ],
  legalPhone: [
    {
      message: '请输入正确的联系电话',
      trigger: 'blur',
      validator: FormValidators.allPhone
    }
  ],
  contactPhone: [
    {
      message: '请输入正确的联系电话',
      trigger: 'blur',
      validator: FormValidators.allPhone
    }
  ],
  companyPhone: [
    {
      message: '请输入正确的联系电话',
      trigger: 'blur',
      validator: FormValidators.allPhone
    }
  ],
  serviceCost: [
    {
      message: '请输入正确的金额',
      trigger: 'blur',
      validator: FormValidators.numberOrPoint
    }
  ],
  everyYear: [
    {
      message: '请输入正确的金额',
      trigger: 'blur',
      validator: FormValidators.numberOrPoint
    }
  ],
  accountNumber: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  changeSubjectList: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  discountTime: [
    {
      required: true,
      message: '请输入',
      trigger: ['change', 'blur']
    }
  ],
  activityId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

const fieldCodeChangeFun = () => {
  return [
    {
      fieldCode: 'ciId',
      disabled: true,
      type: 'text',
      hide: true
    },
    {
      fieldCode: 'customerNo',
      disabled: true,
      type: 'text'
    },
    {
      fieldCode: 'address',
      disabled: true
    },
    {
      fieldCode: 'monthNum',
      unit: '月',
      type: 'inputNumber'
    },
    {
      fieldCode: 'endTime',
      disabled: true
    },
    {
      fieldCode: 'productId',
      type: 'treeSelect',
      disabled: true
    },
    {
      fieldCode: 'serviceCost',
      disabled: formData?.isInContract == '1' ? false : true,
      unit: formData.feeType === '0' ? '元' : formData.feeType === '1' ? '元/年' : '元/月',
      maxlength: 10
    },
    // {
    //   fieldCode: 'softwareFee',
    //   unit: formData.feeType === '0' ? '元' : formData.feeType === '1' ? '元/年' : '元/月'
    // },
    {
      fieldCode: 'everyYear',
      unit: '元'
    },
    // {
    //   fieldName: '其他费用',
    //   fieldCode: 'otherCost',
    //   unit: '元'
    // },
    {
      fieldCode: 'totalCostCn',
      type: 'extraText',
      unit: '元',
      disabled: true
    },
    /*会计公司方---start---*/
    {
      fieldCode: 'branchOffice',
      // disabled: true,
      type: 'select',
      option:
        branch_office.value &&
        branch_office.value.length &&
        branch_office.value.map(item => {
          return { label: item.name, value: item.name }
        })
    },
    {
      fieldCode: 'companyPerson',
      disabled: true
    },
    {
      fieldCode: 'companyAddress', // 甲方地址
      disabled: false
    },
    {
      fieldCode: 'companyPhone',
      disabled: true
    },
    // 新增乙方账户信息
    // accountNumber
    {
      fieldCode: 'accountNumber',
      type: 'select',
      // multiple: true,
      required: false,
      option:
        receipt_method.value &&
        receipt_method.value.length &&
        receipt_method.value.map(item => {
          return { label: item.label, value: item.value }
        })
    },
    // 如果后端传回来的field存在变更科目字段
    {
      fieldCode: 'changeSubjectList',
      type: 'select',
      required: true,
      props: {
        multiple: true,
        filterable: true,
        allowCreate: true,
        defaultFirstOption: true,
        clearable: true
      }, // el元素的props的属性配置
      isRemote: true,
      option: change_project
    },
    /*会计公司方---end---*/
    {
      fieldCode: 'productionCost',
      option: [
        {
          label: '根据单据由甲方支付',
          value: '根据单据由甲方支付'
        },
        {
          label: '由乙方全包费用',
          value: '由乙方全包费用'
        }
      ]
    },
    // {
    //   fieldCode: 'isEstablish',
    //   option: [
    //     {
    //       label: '是',
    //       value: '1'
    //     },
    //     {
    //       label: '否',
    //       value: '0'
    //     }
    //   ],
    //   type: 'select'
    // },
    // 根据模板来确定是什么申报类型
    {
      fieldCode: 'declare',
      option: [
        {
          label: '非零申报',
          value: '1'
        },
        {
          label: '零申报',
          value: '0'
        }
      ],
      type: 'select',
      disabled: true
    },
    {
      fieldCode: 'taxpayerType',
      option: [
        {
          label: '小规模纳税人',
          value: '小规模纳税人'
        },
        {
          label: '一般纳税人',
          value: '一般纳税人'
        },
        {
          label: '进出口企业',
          value: '进出口企业'
        }
      ],
      type: 'select'
    },
    {
      whenShow: ['declare', '1'],
      fieldCode: 'salesRevenue',
      option: [
        {
          label: '年销售额100万以下',
          value: '年销售额100万以下'
        },
        {
          label: '年销售额100-500万',
          value: '年销售额100-500万'
        },
        {
          label: '年销售额500-2000万',
          value: '年销售额500-2000万'
        },
        {
          label: '年销售额2000万以上',
          value: '年销售额2000万以上'
        }
        // {
        //   label: '其他',
        //   value: '其他',
        //   fieldCode: 'otherSalesText'
        // }
      ],
      type: 'select'
    },
    {
      whenShow: ['declare', '0'],
      fieldCode: 'payrollService',
      option: [
        {
          label: '有',
          value: '有'
        },
        {
          label: '无',
          value: '无'
        }
      ],
      type: 'select',
      required: false
    },
    {
      fieldCode: 'activityId',
      type: 'inputSearch'
    },
    {
      fieldCode: 'remark',
      disabled: false,
      type: 'textarea'
      // disabledFunc: () => {
      //   formData.activityId
      // }
    },
    {
      fieldCode: 'contactPhone', // 乙方联系电话
      required: false
    },
    {
      fieldCode: 'contactPerson', // 乙方联系电话
      disabled: true
    }
    // {
    //   fieldCode: 'identityNumber'
    // }
  ]
}
const fieldCodeExtra = new Map()
fieldCodeExtra.set('customerName', {
  fieldName: '关联客户',
  fieldCode: 'customerName',
  disabled: true
})
fieldCodeExtra.set('totalCost', {
  fieldName: '合同总金额',
  fieldCode: 'totalCost',
  unit: '元',
  hide: true
})
fieldCodeExtra.set('serviceCostUnit', {
  fieldName: '服务费单位',
  fieldCode: 'serviceCostUnit',
  hide: true
})
// 记账合同里已有乙方地址
fieldCodeExtra.set('contactAddress', {
  fieldName: '办理人联系地址',
  fieldCode: 'contactAddress',
  hide: true
})

function overwriteArrayAWithArrayB(arrayA, arrayB) {
  const mapB = new Map(arrayB.map(item => [item.fieldCode, item]))
  return arrayA.map(itemA => {
    if (mapB.has(itemA.fieldCode)) {
      return { ...itemA, ...mapB.get(itemA.fieldCode) }
    }
    return itemA
  })
}

// 处理模板对应fieldlist里的字段，以符合需求
const handleChangeContractTemplate = async id => {
  formData.tempId = id
  const res = await getContractTempGetById({ id })
  formData.htmlStr = res.data.htmlStr // 一直使用模板接口的htmlStr，而不是上一次提交模板合同的htmlStr
  formData.contractType = res.data.contractType
  const resFieldList = await getContractTempGetFieldList({ contractType: formData.contractType })
  // 客户信息 服务信息
  // resFieldList.data.find(item => item.fieldCode === 'address').disabled = true
  const fieldCodeChange = fieldCodeChangeFun()
  if (formData.contractType == '2') {
    fieldCodeChange.find(item => item.fieldCode === 'monthNum').fieldName = '服务期限(年)'
  }

  // 处理 如果是一次性合同 且 产品名称里面包含"变更" 才需要"变更科目"字段
  if (formData.contractType === '1') {
    // 合同名称不存在变更二字 需要删除"变更科目"字段
    if (!formData?.productName?.includes('变更')) {
      const fieldIndex = resFieldList.data.findIndex(field => field.fieldCode === 'changeSubjectList')
      if (fieldIndex > 0) {
        resFieldList.data.splice(fieldIndex, 1)
      }
    }
  }

  resFieldList.data = overwriteArrayAWithArrayB(resFieldList.data, fieldCodeChange)

  // 对于变更合同(排除转为正式合同的情况)时，增加一个变更开始时间

  if (formData.isIntention !== '1' && formData.contractId && !props.isRenewal) {
    // 地址协议合同
    if (formData.contractType === '2') {
      // 先找到"协议开始时间"
      const index = resFieldList.data.findIndex(item => item.fieldCode === 'startTime')
      resFieldList.data.splice(index, 0, {
        fieldCode: 'changeStartTime',
        fieldName: '变更开始时间',
        type: 'date',
        required: true
      })
      // 找到相关的协议开始时间 和 服务期限 使其disabled 为 true
      const startTimeFiled = resFieldList.data.filter(item => item.fieldCode === 'startTime')[0]
      startTimeFiled.disabled = true
      const monthNumFiled = resFieldList.data.filter(item => item.fieldCode === 'monthNum')[0]
      monthNumFiled.disabled = true
    }

    // 记账合同
    if (formData.contractType === '0') {
      const index = resFieldList.data.findIndex(item => item.fieldCode === 'startTime')
      resFieldList.data.splice(index, 0, {
        fieldCode: 'changeStartTime',
        fieldName: '变更开始时间',
        type: 'date',
        required: true
      })
      const startTimeFiled = resFieldList.data.filter(item => item.fieldCode === 'startTime')[0]
      startTimeFiled.disabled = true
      const monthNumFiled = resFieldList.data.filter(item => item.fieldCode === 'monthNum')[0]
      monthNumFiled.disabled = true
    }
  }

  //记账合同新增一个"优惠"

  if (formData.contractType === '0') {
    // 活动优惠
    const index = resFieldList.data.findIndex(item => item.fieldCode === 'activityId')
    let option = [
      {
        label: '时长优惠',
        value: '时长优惠'
      },
      {
        label: '活动优惠',
        value: '活动优惠'
      }
    ]
    if (formData.feeType === '0') {
      option = [
        {
          label: '时长优惠',
          value: '时长优惠'
        }
      ]
    }
    // 默认删除活动优惠,新增优惠
    resFieldList.data.splice(index, 1, {
      fieldCode: 'discount',
      fieldName: '优惠',
      type: 'select',
      clearable: true,
      option
    })
  }
  if (formData.isIntention == '1') {
    resFieldList.data.forEach(item => {
      if (item.fieldCode !== 'startTime') {
        item.disabled = true
      }
    })
  }
  // del accountNumber
  // const index_accountNumber = resFieldList.data.findIndex(item => item.fieldCode === 'accountNumber')
  // if (index_accountNumber !== -1) {
  //   resFieldList.data.splice(index_accountNumber, 1)
  // }
  const index_totalCost = resFieldList.data.findIndex(item => item.fieldCode === 'totalCostCn')
  if (index_totalCost !== -1) {
    resFieldList.data.splice(index_totalCost, 0, fieldCodeExtra.get('totalCost'))
  }
  const index_customerName = resFieldList.data.findIndex(item => item.fieldCode === 'ciId')
  if (index_customerName !== -1) {
    resFieldList.data.splice(index_customerName, 0, fieldCodeExtra.get('customerName'))
  }
  // contractType -- 0 记账合同 1 一次性合同 2 地址服务协议合同
  if (['0'].includes(formData.contractType)) {
    const index_serviceCost = resFieldList.data.findIndex(item => item.fieldCode === 'serviceCost')
    if (index_serviceCost !== -1) {
      resFieldList.data.splice(index_serviceCost + 1, 0, fieldCodeExtra.get('serviceCostUnit'))
    }
  }
  if (['2'].includes(formData.contractType)) {
    const index_branchOffice = resFieldList.data.findIndex(item => item.fieldCode === 'branchOffice')
    if (index_branchOffice !== -1) {
      resFieldList.data.splice(index_branchOffice + 1, 0, fieldCodeExtra.get('contactAddress'))
    }
  }
  fieldFormArr.splice(0, fieldFormArr.length, ...resFieldList.data) // fieldFormArr.splice(0, fieldFormArr.length)
  fieldFormArr.forEach(item => {
    if (item.required) {
      if (rules[item.fieldCode]) {
        rules[item.fieldCode].push({
          required: true,
          message: item.type === 'select' ? '请选择' : '请输入',
          trigger: ['blur', 'change']
        })
      } else {
        rules[item.fieldCode] = [
          { required: true, message: item.type === 'select' ? '请选择' : '请输入', trigger: ['blur', 'change'] }
        ]
      }
    }
  })
  // console.log('fieldFormArr.length', fieldFormArr.length)
  // console.log('fieldFormArr', fieldFormArr, formData.isInContract)
}

const formTemplateRef = ref(null)

const onHandleChangeBatch = () => {
  // console.log('onHandleChangeBatch', fieldFormArr)
  Object.keys(formData).forEach(item => {
    const type = fieldFormArr.find(field => field.fieldCode === item)?.type || 'input'
    handleChange(formData[item], { fieldCode: item, type: type })
  })
  // 特殊处理
  handleChange(formData.branchOffice, { fieldCode: 'branchOffice', type: 'input' }) // 虽然表单里是select，但填值是input
  handleChange(formData.customerName, { fieldCode: 'ciId' })
  handleChange(formData.feeType === '0' ? '元' : formData.feeType === '1' ? '元/年' : '元/月', { fieldCode: 'serviceCostUnit' })
  // 对新增的乙方账户信息表单类型进行处理
  handleChange(formData.accountNumber, { fieldCode: 'accountNumber', type: 'input' }) // 虽然表单里是select，但填值是input
}

const deleteField = fieldCode => {
  const index = fieldFormArr.findIndex(item => item.fieldCode === fieldCode)
  if (index > 0) {
    fieldFormArr.splice(index, 1)
  }
}

const addField = (fieldCode, fieldOption) => {
  const fieldIndex = fieldFormArr.findIndex(item => item.fieldCode === fieldOption.fieldCode)
  if (fieldIndex < 0) {
    const index = fieldFormArr.findIndex(item => item.fieldCode === fieldCode)
    fieldFormArr.splice(index + 1, 0, fieldOption)
  }
}

// □✔√☑
// 把表单内容回显到预览的合同中
const handleChange = (e, formItem) => {
  let fieldCode = formItem.fieldCode
  let optionIndex
  let optionObj
  let optionObjHasFieldCode
  let fieldCodeOptionArr = []
  let fieldItem
  if (formItem?.specialFlow === 'productOneOff_flow') {
    fieldItem = formItem
  } else {
    fieldItem = fieldFormArr.find(item => item.fieldCode === fieldCode)
  }
  // console.log('handleChange-e', fieldCode)
  // console.log('handleChange-e', e)
  // console.log('handleChange-formItem', formItem)
  // console.log('handleChange-fieldItem', fieldItem)
  const type = formItem.type || 'text'
  // 如果是变更科目的表单项 需要
  if (formItem.isRemote) {
    setDic('change_project', e[e.length - 1], e[e.length - 1], change_project)
  }
  if (type === 'select' && !formItem?.specialFlow) {
    optionIndex = fieldItem.option.findIndex(item => item.value === e) // 在option中的index
    optionObj = fieldItem.option.find(item => item.value === e) // 在option中的对象
    optionObjHasFieldCode = fieldItem.option.find((item, index) => item.fieldCode) // eg:“销售收入-其他”
    const optionTemp = fieldItem.option.filter((item, index) => !item.fieldCode) // 不含“销售收入-其他”
    fieldCodeOptionArr = optionTemp.map((item, index) => index) // eg:[0,1,2,3]
    fieldCode = `${fieldCode}_${optionIndex + 1}`
  }
  // console.log('handleChange-fieldCode', fieldCode)
  if (optionObj?.fieldCode) {
    fieldCode = optionObj.fieldCode
  }

  // 获取当前产品下的html元素
  const wrapDom = document.getElementById(`pane-${formData.productId}`)
  const spanList = wrapDom?.querySelectorAll(`[fieldcode="${fieldCode}"]`) // 记账合同需要标记两次联系人
  // console.log('handleChange-spanList', spanList)
  spanList?.forEach(span => {
    // console.log('handleChange-span', span)
    if (span) {
      if (e != null && fieldItem) {
        fieldItem.originDiv = span.innerText
        if (type === 'select') {
          if (formItem?.specialFlow === 'productOneOff_flow') {
            span.innerText = '☑'
            span.style = 'color:black'
          } else {
            // 置空其他的选项
            fieldCodeOptionArr.forEach(index => {
              // 销售收入的optionArr遍历时不含“销售收入-其他”
              fieldCode = `${formItem.fieldCode}_${index + 1}`
              const optionItemSpan = document.querySelector(`[fieldcode="${fieldCode}"]`)
              optionItemSpan.innerText = '□'
              optionItemSpan.style = 'color:black'
            })
            // 置空联动的输入框
            if (optionObjHasFieldCode?.fieldCode) {
              const optionObjHasFieldCodeSpan = document.querySelector(`[fieldcode="${optionObjHasFieldCode.fieldCode}"]`)
              optionObjHasFieldCodeSpan.innerText = '_________'
              optionObjHasFieldCodeSpan.style = 'color:black'
            }
            if (optionObj?.fieldCode) {
              if (formData[fieldCode]) {
                span.innerText = formData[fieldCode]
                span.style = 'color:black'
              }
            } else {
              span.innerText = '☑'
            }
          }
        } else {
          span.innerText = e || formData[fieldCode]
          span.style = 'color:black'
          // span.style = 'text-decoration:underline'
        }
      } else if (!e && fieldItem?.originDiv) {
        span.innerText = fieldItem.originDiv
        span.style.backgroundColor = 'lightblue'
        span.style.color = 'black'
      }
    }
  })

  // 针对是记账合同的优惠选择项的处理逻辑
  if (formData.contractType === '0' && formItem.fieldCode === 'discount') {
    if (e === '时长优惠') {
      // 如果存在活动优惠 则删除活动优惠
      deleteField('activityId')
      handleClearActivity()
      // 添加"时长优惠"选择项
      addField('discount', {
        fieldCode: 'discountTime',
        fieldName: '时长优惠',
        type: 'inputNumber',
        step: 1,
        required: true,
        unit: '月'
      })
    }
    if (e === '活动优惠') {
      // 删除"时长优惠"
      deleteField('discountTime')
      formData.discountTime = undefined
      // 添加"时长优惠"选择项
      addField('discount', {
        fieldCode: 'activityId',
        fieldName: '活动优惠',
        type: 'inputSearch',
        required: true
      })
    }

    if (!e) {
      // 同时删除活动优惠和时长优惠
      deleteField('activityId')
      deleteField('discountTime')
      handleClearActivity()
      formData.discountTime = undefined
    }
  }

  // 如果是合同起始时间startTime 或者 monthNum
  if (formData.contractType === '0' && formItem.fieldCode === 'startTime') {
    if (formData.startTime && formData.monthNum && formData.discountTime) {
      setDiscountTimeRemark(formData.discountTime)
    }
  }

  if (formData.contractType === '0' && formItem.fieldCode === 'monthNum') {
    if (formData.startTime && formData.monthNum && formData.discountTime) {
      setDiscountTimeRemark(formData.discountTime)
    }
  }

  if (formData.contractType === '0' && formItem.fieldCode === 'discountTime') {
    if (formData.startTime && formData.monthNum && formData.discountTime) {
      setDiscountTimeRemark(formData.discountTime)
    }
  }
}

// 优惠时长discountTime-startTime-monthNum 设置默认备注
// value 为 discountTime
const setDiscountTimeRemark = value => {
  let endTime = new Date(formData.endTime)
  endTime = endTime.setMonth(endTime.getMonth() + Number(value))
  const year = new Date(endTime).getFullYear()
  const month = new Date(endTime).getMonth() + 1
  const endDate = `${year}-${month < 10 ? '0' + month : month}`
  formData.remark = `赠送${value}月,时间<${formData.startTime}>-<${endDate}>`

  // 合同备注填充
  //
  // handleChange()
  handleChange(formData.remark, { fieldCode: 'remark', type: 'textarea' })
}

const loading = ref(false) // 子组件如何给父组件的button上锁
const submit = async () => {
  await formTemplateRef.value.validate()
  // changeStartTime 分yyyy-mm 或者yyyy-mm-dd
  let length = 0

  if (formData.changeStartTime) {
    length = formData.changeStartTime.split('-').length
  }
  const formDataTemp = Object.assign({}, formData, {
    changeStartTime: formData.changeStartTime ? (length === 2 ? formData.changeStartTime + '-01' : formData.changeStartTime) : ''
  })
  console.log('formDataTemp.salesRevenue', formDataTemp.salesRevenue)

  // 如果是续签 需要将 formData的信息修改保证是新增合同
  if (props.isRenewal) {
    // 删除合同的id
    delete formDataTemp.contractId
    formDataTemp.bizType = undefined
    formDataTemp.originId = undefined
  }
  if (formDataTemp?.isIntention === '1') {
    if (loading.value) return
    loading.value = true
    // 除了地址合同里，其他合同里后端返回的时间格式目前都是'YYYY-DD'，但后端接收要求'YYYY-MM-DD'
    if (formDataTemp.startTime) {
      formDataTemp.startTime = dayjs(formDataTemp.startTime).format('YYYY-MM-DD')
    }
    if (formDataTemp.endTime) {
      formDataTemp.endTime = dayjs(formDataTemp.endTime).format('YYYY-MM-DD')
    }
    customerContractChangeToFormal(formDataTemp)
      .then(res => {
        if (res.code === 200) {
          proxy.$message.success('保存成功')
          emits('on-success')
        }
      })
      .finally(() => {
        loading.value = false
      })
  } else if (formDataTemp.contractId) {
    ElMessageBox.confirm(
      `
    <p>请注意!</p>
    <p>提交变更合同后，原合同将在变更合同审批通过后自动终止，与原合同关联的账单将被关闭并生成新的账单</p>
    `,
      '提示',
      {
        confirmButtonText: '继续提交',
        cancelButtonText: '返回修改',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }
    ).then(() => {
      if (loading.value) return
      loading.value = true
      formDataTemp.bizType = '1'
      formDataTemp.originId = formDataTemp.contractId
      formDataTemp.contractId = undefined
      // 除了地址合同里，其他合同里后端返回的时间格式目前都是'YYYY-DD'，但后端接收要求'YYYY-MM-DD'
      if (formDataTemp.startTime) {
        formDataTemp.startTime = dayjs(formDataTemp.startTime).format('YYYY-MM-DD')
      }
      if (formDataTemp.endTime) {
        formDataTemp.endTime = dayjs(formDataTemp.endTime).format('YYYY-MM-DD')
      }
      saveContract(formDataTemp)
        .then(res => {
          if (res.code === 200) {
            proxy.$message.success('变更成功')
            emits('on-success')
          }
        })
        .finally(() => {
          loading.value = false
        })
    })
  } else {
    if (loading.value) return
    loading.value = true
    // 除了地址合同里，其他合同里后端返回的时间格式目前都是'YYYY-DD'，但后端接收要求'YYYY-MM-DD'
    if (formDataTemp.startTime) {
      formDataTemp.startTime = dayjs(formDataTemp.startTime).format('YYYY-MM-DD')
    }
    if (formDataTemp.endTime) {
      formDataTemp.endTime = dayjs(formDataTemp.endTime).format('YYYY-MM-DD')
    }
    saveContract(formDataTemp)
      .then(res => {
        if (res.code === 200) {
          proxy.$message.success('保存成功')
          emits('on-success')
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}

// 获取所有的产品名称
const productTreeData = ref()
const defaultPopsFunction = () => {
  return {
    value: 'id',
    label: 'name',
    disabled: data => {
      // console.log('data', data)
      return (
        data?.type === '产品类型' &&
        (data.types.findIndex(item => item.value === formData.contractType) === -1 || data.feeType !== formData.feeType) &&
        formData.id
      ) // 变更时候只能选择收费类型包含当前合同模板收费类型的产品
    }
  }
}

const getAllProducts = async () => {
  const { data } = await getBusinessList({
    pageNum: 1,
    pageSize: 10000
  })
  // 将后端传回的数据结构进行转换
  const revertData = []
  data.forEach(item => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      children: []
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach(child => {
        obj.children.push({
          name: child.productName,
          types:
            item.contractType?.split(',')?.map(item1 => {
              // console.log('item1', item1)
              const itemValue = contractTypeArr.find(item0 => item0.label === item1).value
              // console.log('itemValue', itemValue)
              return {
                label: item1,
                value: itemValue
              }
            }) || [],
          type: '产品类型',
          quotation: child.quotation, // 合同报价
          isInContract: child.isInContract, // 是否在合同中定义
          feeType: child.feeType, //收费类型
          id: child.id // 产品类型id
        })
      })
    }
  })
  // console.log('revertData', revertData)
  productTreeData.value = revertData || []
}

/** 活动优惠---start  */
const columnsActivity = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'activityQuotation',
    label: '活动报价',
    width: '100',
    render: scope => {
      return <span>{scope.row.activityQuotation || '--'}元</span>
    }
  },
  {
    prop: 'activityDiscountTime',
    label: '时长',
    width: '100',
    render: scope => {
      return <span>{scope.row.activityDiscountTime || '--'}月</span>
    }
  },
  {
    prop: 'remark',
    label: '备注',
    minWidth: '300'
  }
]
const listSelectShowActivity = ref(false)
const handleShowActivity = () => {
  if (!formData.productId) {
    return proxy.$modal.msgWarning('请先选择服务产品')
  }
  formTemplateRef.value.validateField('startTime')
  formTemplateRef.value.scrollToField('startTime')
  if (!formData.startTime) {
    return proxy.$modal.msgWarning('请先选择合同起始时间')
  }
  listSelectShowActivity.value = true
}
const isPriceChange = ref(false)
const setPrice = () => {
  formData.priceChangeFlag = false
  radioChange(false)
  isPriceChange.value = true
}
const handleSelectActivity = data => {
  console.log('data', data)
  const { activityId, activityQuotation, activityDiscountTime } = data
  const activityTxt = activityDiscountTime ? `${activityQuotation}元，${activityDiscountTime}月` : `${activityQuotation}元`
  Object.assign(formData, {
    activityId,
    activityTxt,
    activityQuotation, // 临时挂载到formData中用于后续处理数据
    activityDiscountTime // 临时挂载到formData中用于后续处理数据
  })
  setPrice()
  formData.remark = getRemarkTxt()
  handleChange(formData.remark, { fieldCode: 'remark', type: 'textarea' })
}
const handleClearActivity = () => {
  formData.activityId = undefined
  formData.activityTxt = undefined
  formData.remark = undefined

  isPriceChange.value = false
  fieldFormArr.find(item => item.fieldCode === 'remark').disabled = false
}
watch(
  () => [formData.activityId, formData.startTime, formData.monthNum],
  () => {
    console.log('watching startTime')
    formData.remark = getRemarkTxt()
    handleChange(formData.remark, { fieldCode: 'remark', type: 'textarea' })
    if (formData.startTime && formData.monthNum && formData.discountTime) {
      setDiscountTimeRemark(formData.discountTime)
    }
  }
)
const getRemarkTxt = () => {
  if (!fieldFormArr.length) return ``
  const t1 = formData.activityQuotation
    ? `活动价 <${formData.activityQuotation}元>（人民币：<${changeNumMoneyToChinese(formData.activityQuotation)}>）`
    : ``
  const t2 = formData.activityDiscountTime
    ? `时间<${formData.startTime}>至<${dayjs(formData.startTime)
        .add(formData.activityDiscountTime, 'month')
        .add(-1, 'day')
        .format('YYYY-MM')}>`
    : formData.monthNum
    ? `时间<${formData.startTime}>至<${dayjs(formData.startTime)
        .add(formData.monthNum, 'month')
        .add(-1, 'day')
        .format('YYYY-MM')}>`
    : ``
  const remark = formData.startTime && formData.activityId ? (t1 && t2 ? `${t1}，${t2}` : t1 ? t1 : t2 ? t2 : ``) : ``
  if (remark) {
    fieldFormArr.find(item => item.fieldCode === 'remark').disabled = true
  }
  return remark
}
/** 活动优惠---end  */

onMounted(async () => {
  console.log('onMounted-template-instance')
  await getAllProducts()
})

// const isFirst = ref(false)
const changeRenewalData = () => {
  console.log('props.formDataPreStep.isFirst', props.formDataPreStep.isFirst)
  if (props.isRenewal && !props.formDataPreStep.isFirst) {
    // 如果是续签合同 修改data中的数据
    // props.formDataPreStep.startTime = props.formDataPreStep.endTime
    // 如果是地址合同
    if (props.formDataPreStep.contractType === '2') {
      //单位是年
      props.formDataPreStep.monthNum = 1
      // 合同的起始时间修改逻辑：合同里的结束之间是到最后一天的，所以如果没有优惠的话续签要从结束时间的后一天开始，
      props.formDataPreStep.startTime = dayjs(props.formDataPreStep.endTime).add(1, 'day').format('YYYY-MM-DD')
      // 合同的起始时间修改逻辑：合同里的结束之间是到最后一天的，所以如果没有优惠的话续签要从结束时间的后一天开始，
      props.formDataPreStep.endTime = dayjs(props.formDataPreStep.endTime)
        .add(1, 'year')
        .add(1, 'day')
        .add(-1, 'day')
        .format('YYYY-MM-DD')
    }
    // 如果是记账合同
    if (props.formDataPreStep.contractType === '0') {
      // 单位是月
      props.formDataPreStep.monthNum = 12
      const { discount } = props.formDataPreStep
      if (discount) {
        if (discount === '时长优惠') {
          // endTime加上时长优惠
          props.formDataPreStep.startTime = dayjs(props.formDataPreStep.endTime)
            .add(props.formDataPreStep.discountTime, 'month')
            .add(1, 'month')
            .format('YYYY-MM')
        }

        if (discount === '活动优惠') {
          if (props.formDataPreStep.activityDiscountTime) {
            props.formDataPreStep.startTime = dayjs(props.formDataPreStep.startTime)
              .add(props.formDataPreStep.activityDiscountTime, 'month')
              .add(-1, 'day')
              .add(1, 'month')
              .format('YYYY-MM')
          } else {
            props.formDataPreStep.startTime = dayjs(props.formDataPreStep.endTime).add(1, 'month').format('YYYY-MM')
          }
        }
      } else {
        // 若没有
        props.formDataPreStep.startTime = dayjs(props.formDataPreStep.endTime).add(1, 'month').format('YYYY-MM')
      }

      props.formDataPreStep.endTime = dayjs(props.formDataPreStep.startTime).add(12, 'month').add(-1, 'month').format('YYYY-MM')
    }
    // // 活动优惠相关数据置空
    Object.assign(props.formDataPreStep, {
      activityId: '',
      activityTxt: '',
      activityQuotation: '',
      activityDiscountTime: '',
      remark: '',
      discount: '',
      discountTime: ''
    })
    // isFirst.value = true
    props.formDataPreStep.isFirst = true

    console.log('props.formDataPreStep.isFirst====after', props.formDataPreStep.isFirst)
  }
}
async function showThisStep() {
  changeRenewalData()
  Object.assign(formData, props.formDataPreStep)
  console.log('function-props.formDataPreStep-formData', JSON.parse(JSON.stringify(formData)))
  await handleChangeContractTemplate(formData.tempId)
  // 非续签
  if (!props.isRenewal) {
    if (props.formDataPreStep.discount === '时长优惠' && props.formDataPreStep.discountTime) {
      const discountFiled = fieldFormArr.filter(item => item.fieldCode === 'discount')[0]
      discountFiled.disabled = true

      addField('discount', {
        fieldCode: 'discountTime',
        fieldName: '时长优惠',
        type: 'inputNumber',
        step: 1,
        required: true,
        unit: '月'
      })
      const discountTimeFiled = fieldFormArr.filter(item => item.fieldCode === 'discountTime')[0]
      discountTimeFiled.disabled = true
    } else {
    }
  }
  // 如果是新增的记账合同，甲乙方对调【2/2】
  if (formData.contractType === '0' && !formData.contractId && !props.isRenewal) {
    const temp = Object.assign({}, formData)
    formData.contactPerson = temp.companyPerson
    formData.contactAddress = temp.companyAddress
    formData.contactPhone = temp.companyPhone
    // formData.address =
    formData.companyPerson = temp.contactPerson
    formData.companyAddress = temp.contactAddress
    formData.companyPhone = temp.contactPhone
  }
  // 如果是新增，按模板区分申报类型
  if (!formData.id) {
    // todo id？
    if (formData.htmlStr.includes('销售收入')) {
      formData.declare = '1'
    } else if (formData.htmlStr.includes('人事薪酬服务')) {
      formData.declare = '0'
      fieldFormArr.find(field => field.fieldCode === 'taxpayerType')?.option?.splice(-1)
    } else {
      formData.declare = '1'
    }
  }
  formData.remark = getRemarkTxt()
  formData.totalCost = computeTotalCost()
  const moneyCN = changeNumMoneyToChinese(formData.totalCost)
  formData.totalCostCn = moneyCN
  handleChange(formData.totalCostCn, { fieldCode: 'totalCostCn' })
  handleChange(formData.totalCost, { fieldCode: 'totalCost' })
  onHandleChangeBatch()
  //
  console.log('productTreeData', productTreeData.value, formData.productId)
  // 查出 当前服务产品下的服务费 存储至serviceCostCopy
  const node = nodeSearchById(productTreeData.value, formData.productId)
  if (formData.priceChangeFlag) {
    formData.serviceCostCopy = node?.quotation
  }
  console.log('活动优惠', formData)
  if (formData.remark) {
    isPriceChange.value = true
  }
  // 前端：【功能】新建合同 - 一次性合同：选择业务创建合同时，在合同中打勾办理的业务并填写备注
  // 方法一【不可行】node-click可以获取父级的名称，但是无法储存，下次变更或续签时候不会触发node-click，获取不到
  // 方法二【不可行】nodeSearchById只获取当前级别，且多绕一层接口
  // 方法三【可行】在某处获取时候把父级名称混入子级待用
  if (formData.contractType === '1') {
    console.log('formData', formData)
    if (formData.productParent.name.includes('许可')) {
      handleChange(true, { fieldCode: 'productOneOffOption_licence', type: 'select', specialFlow: 'productOneOff_flow' })
      handleChange(formData.productName, { fieldCode: 'productOneOffOption_remark_licence', specialFlow: 'productOneOff_flow' })
    } else if (formData.productName.includes('银行')) {
      handleChange(true, { fieldCode: 'productOneOffOption_bank', type: 'select', specialFlow: 'productOneOff_flow' })
      handleChange(formData.productName, { fieldCode: 'productOneOffOption_remark_bank', specialFlow: 'productOneOff_flow' })
    } else if (formData.productName.includes('注册')) {
      handleChange(true, { fieldCode: 'productOneOffOption_register', type: 'select', specialFlow: 'productOneOff_flow' })
      handleChange(formData.productName, { fieldCode: 'productOneOffOption_remark_register', specialFlow: 'productOneOff_flow' })
    } else if (formData.productName.includes('变更')) {
      handleChange(true, { fieldCode: 'productOneOffOption_change', type: 'select', specialFlow: 'productOneOff_flow' })
      handleChange(formData.productName, { fieldCode: 'productOneOffOption_remark_change', specialFlow: 'productOneOff_flow' })
    } else if (formData.productName.includes('注销')) {
      handleChange(true, { fieldCode: 'productOneOffOption_cancel', type: 'select', specialFlow: 'productOneOff_flow' })
      handleChange(formData.productName, { fieldCode: 'productOneOffOption_remark_cancel', specialFlow: 'productOneOff_flow' })
    } else {
      handleChange(true, { fieldCode: 'productOneOffOption_other', type: 'select', specialFlow: 'productOneOff_flow' })
      handleChange(formData.productName, { fieldCode: 'productOneOffOption_remark_other', specialFlow: 'productOneOff_flow' })
    }
  }
  //默认给记账合同的乙方联系人填充当前账号使用人姓名
  // 如果是记账合同

  if (formData.contractType === '0') {
    if (!formData.contractId) {
      formData.companyPerson = userStore.user.nickName
    }

    formData.companyPerson_empty = false
    handleChange(formData.companyPerson, { fieldCode: 'companyPerson' })
  }

  // 如果是地址服务合同
  if (formData.contractType === '2') {
    // formData.companyPerson = formData.contactPerson
    // formData.companyPerson_empty = true
    // handleChange(formData.companyPerson, { fieldCode: 'companyPerson' })
    // if (!props.isRenewal && !formData.contractId) {
    // formData.companyPhone = formData.contactPhone
    // }

    // formData.companyPhone_empty = true
    handleChange(formData.companyPhone, { fieldCode: 'companyPhone' })
    // 联系人（具体办理人）填充为当前账号操作人姓名，不可修改
    // console.log('companyAddress before', formData, formData.companyAddress)
    console.log('formData.contactPerson', formData.contactPerson)
    if (!formData.contractId) {
      console.log('新增合同')
      formData.contactPerson = userStore.user.nickName
      // formData.companyAddress = ''
      // formData.contactPhone = ''
    }
    console.log('formData.contactPerson', formData.contactPerson)
    formData.contactPerson_empty = false
    handleChange(formData.contactPerson, { fieldCode: 'contactPerson' })
    //companyAddress
    // console.log('formData--companyAddress', formData.companyAddress)
    // handleChange(formData.companyAddress, { fieldCode: 'companyAddress' })
    // handleChange(formData.contactPhone, { fieldCode: 'contactPhone' })
  }

  //  如果存在branchoffice
  if (formData.branchOffice) {
    handleSelectBranchOffice(formData.branchOffice, true)
  }
}
const nodeSearchById = (list, id) => {
  for (const node of list) {
    if (node.id === id) {
      return node
    }
    if (node.children && node.children?.length) {
      const nodeSearch = nodeSearchById(node.children, id)
      if (nodeSearch) {
        return nodeSearch
      }
    }
  }
}

// 回到上一步->回到下一步，此时内部依赖值没变化，如何触发computed => 改为普通函数
// const totalCost = computed(() => {})
function computeTotalCost() {
  switch (formData?.contractType) {
    // 记账合同
    case '0':
      if (formData.feeType === '0') {
        // feeType 为零 为一次性收费
        return Number(formData.serviceCost || 0)
      } else {
        // 每年收费
        if (formData.feeType === '1') {
          return Number(formData.serviceCost || 0) * (Number(formData.monthNum || 0) / 12 || 0)
        } else {
          return Number(formData.serviceCost || 0) * (Number(formData.monthNum || 0) || 0)
        }
      }
    // 一次性合同
    case '1':
      if (formData.feeType === '0') {
        // feeType 为零 为一次性收费
        // console.log('formData.serviceCost', formData.serviceCost)
        return Number(formData.serviceCost || 0)
      } else {
        return ''
      }
    // 地址服务协议合同
    // 地址合同都是月付 没有这个说法
    // 地址时间精确到日 ok
    // 地址服务期限的单位只能是年 ok
    case '2':
      if (formData.feeType === '0') {
        // feeType 为零 为一次性收费
        return Number(formData.serviceCost || 0)
      } else {
        // 每年收费
        if (formData.feeType === '1') {
          return Number(formData.serviceCost || 0) * Number(formData.monthNum || 0)
        } else {
          return Number(formData.serviceCost || 0) * (Number(formData.monthNum || 0) * 12 || 0)
        }
      }
  }
}

watch(
  () => [formData?.contractType, formData?.feeType, formData?.serviceCost, formData?.monthNum],
  () => {
    formData.totalCost = computeTotalCost()
    const moneyCN = changeNumMoneyToChinese(formData.totalCost)
    formData.totalCostCn = moneyCN
    handleChange(formData.totalCostCn, { fieldCode: 'totalCostCn' })
    handleChange(formData.totalCost, { fieldCode: 'totalCost' })
  }
)

// watch(
//   formData.totalCost,
//   () => {
//     console.log('formData.totalCost')
//     const moneyCN = changeNumMoneyToChinese(formData.totalCost)
//     formData.totalCostCn = moneyCN
//     handleChange(formData.totalCostCn, { fieldCode: 'totalCostCn' })
//     handleChange(formData.totalCost, { fieldCode: 'totalCost' })
//   }
//   // {
//   //   immediate: true
//   // }
// )

watch(
  () => formData.discountTime,
  () => {
    if (formData.startTime && formData.monthNum && formData.discountTime) {
      setDiscountTimeRemark(formData.discountTime)
    }
  }
)
watch(
  () => [active.value, formData.startTime, formData.monthNum],
  () => {
    console.log('watch-----------------', formData.startTime, formData.monthNum, formData.discountTime)

    if (formData.startTime && formData.monthNum) {
      if (formData.contractType === '2') {
        formData.endTime = dayjs(formData.startTime).add(formData.monthNum, 'year').add(-1, 'day').format('YYYY-MM-DD')
      } else {
        formData.endTime = dayjs(formData.startTime).add(formData.monthNum, 'month').add(-1, 'day').format('YYYY-MM')
      }
      handleChange(formData.endTime, { fieldCode: 'endTime' })
      formTemplateRef.value.validateField('endTime')
    } else {
      if (formData.endTime) {
        formData.endTime = undefined
        handleChange(formData.endTime, { fieldCode: 'endTime' }) // todo 诡异的undefined <span fieldcode="endTime" style="color: black;">2024-07-01</span>
      }
    }

    if (formData.startTime && formData.monthNum && formData.discountTime) {
      setDiscountTimeRemark(formData.discountTime)
    }
  }
  // {
  //   immediate: true
  // }
)

const handleSelectBranchOffice = (value, flag) => {
  const item = branch_office.value.find(item => item.name === value)
  console.log('handleSelectBranchOffice', item)
  // 如果记账合同
  if (formData.contractType === '0') {
    // formData.companyPerson = item.contacts
    formData.companyAddress = item.address
    formData.companyPhone = item.phone

    if (item.account) {
      formData['accountNumber_empty'] = false
      const searched = fieldFormArr.filter(item => item.fieldCode === 'accountNumber')[0]
      searched.disabled = true
      formData.accountNumber = item.account
    } else {
      formData['accountNumber_empty'] = true
      if (!flag) {
        formData.accountNumber = ''
      }
    }
  }
  if (formData.contractType === '2') {
    formData.contactAddress = item.address
    formData.contactPhone = item.phone
    handleChange(formData.contactAddress, { fieldCode: 'contactAddress' })
    handleChange(formData.contactPhone, { fieldCode: 'contactPhone' })
  }
  // const item = branch_office.value.find(item => item.name === value)
}

watch(
  () => formData.branchOffice,
  (newValue, oldValue) => {
    if (!oldValue) return // 因为@change已被占用所以使用watch，替代@change用，要求使下方逻辑不要在第一次getdetail的时候执行
    // console.log('formData.branchOffice')
    handleSelectBranchOffice(formData.branchOffice)
    handleChange(formData.branchOffice, { fieldCode: 'branchOffice' })
    handleChange(formData.contactPerson, { fieldCode: 'contactPerson' })
    handleChange(formData.contactAddress, { fieldCode: 'contactAddress' })
    // console.log('formData.contactPhone', formData.contactPhone)
    handleChange(formData.contactPhone, { fieldCode: 'contactPhone' })
  }
)

watch(
  () => formData.accountNumber,
  (newValue, oldValue) => {
    handleChange(formData.accountNumber, { fieldCode: 'accountNumber' })
  }
)

const radioChange = value => {
  if (value) {
    // serviceCost 缓存
    formData.serviceCostCopy = formData.serviceCost
    formData.serviceCost = ''
  } else {
    if (formData.serviceCostCopy === undefined) {
      formData.serviceCostCopy = ''
    }
    formData.serviceCost = formData.serviceCostCopy + '' || formData.serviceCost
    formData.serviceCostCopy = ''
  }
}
watch(
  formData,
  () => {
    if (formData.priceChangeFlag && formData.serviceCostCopy === Number(formData.serviceCost)) {
      formData.priceChangeFlag = false
      formData.serviceCostCopy = ''
    }
  },
  {
    deep: true
  }
)

function handlePrint() {
  console.log('handlePrint')
  // 在页面显示需打印区域来获取dom
  document.querySelector('#printBox').style.display = 'block'
  document.querySelector('#printBox').style.height = 'auto'
  document.querySelector('#printBox').style['overflow-y'] = 'auto'
  const style = '@page {margin: 30px};' // 打印时去掉眉页眉尾
  // 打印为什么要去掉眉页眉尾？因为眉页title时打印当前页面的title，相当于是获取html中title标签里面的内容，但是比如我打印的内容只是一个弹框里面的内容，是没有title的，这时候就会出现undefined，为了避免出现这种情况，就可以隐藏眉页眉尾
  printJS({
    printable: 'printBox', // 标签元素id
    style,
    type: 'html',
    // header: '干部档案',
    // headerStyle: 'font-weight:500;text-align:center;margin-left:20mm',
    targetStyles: ['*']
  })
  // 获取打印内容后隐藏dom
  // document.querySelector('#printBox').style.display = 'none'
  // 各个配置项
  // printable:要打印的id。
  // type:可以是 html 、pdf、 json 等。
  // properties:是打印json时所需要的数据属性。
  // gridHeaderStyle和gridStyle都是打印json时可选的样式。
  // repeatTableHeader:在打印JSON数据时使用。设置为时false，数据表标题将仅在第一页显示。
  // scanStyles:设置为false时，库将不处理应用于正在打印的html的样式。使用css参数时很有用，此时自己设置的原来想要打印的样式就会失效，在打印预览时可以看到效果
  // targetStyles: [’*’],这样设置继承了页面要打印元素原有的css属性。
  // style:传入自定义样式的字符串，使用在要打印的html页面 也就是纸上的样子。
  // ignoreElements：传入要打印的div中的子元素id，使其不打印。非常好用
}

// const docxRef = ref(null)
// word变成html字符后，如何再次使用？
// 可能还是丢给后端去html2doc比较可行，但是是否前面的doc2html也要后端去处理才比较还原呢？
// case ：字符先组装成word文件，再生成文件伪地址在vue-office中预览(必须是这种，不然合同模板在编辑修改的时候就已经实现不了功能了)
//  - npm找一个转换html为docx的插件
// case : 模板时候就用【docx-html:mammoth】插件转换，字符直接v-html
// case : 后期如果要下载docx或者打印出来，前端做还是后端做，前端做的话怎么实现？
// let docx = 'https://501351981.github.io/vue-office/examples/dist/static/test-files/test.docx'
// let docx = ''
// const htmlString = JSON.parse(localStorage.getItem('contractTemplateInnerHTML'))
// const htmlString = '<body style="background-color: blue;"><h1>Hello, world!!</h1></body>'
// const htmlString =  '<div>Your HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string hereYour HTML string here</div>'
// const blob = new Blob([htmlString], { type: 'text/html' })
// // 如何转码并渲染出来
// const saveDocx = async () => {
//   // const opt = { orientation: 'landscape', margins: { top: 800 } }
//   const data = await asBlob(formData.htmlStr).then(data => {
//     console.log('data-1', data)
//     saveAs(data, 'file.docx') // 保存为docx文件

//     // docx = URL.createObjectURL(data)

//     // let reader = new FileReader()
//     // reader.readAsArrayBuffer(data)
//     // reader.onload = function () {
//     //   docx = reader.result
//     // }

//     console.log('docx', docx)
//   })
//   console.log('data-2', data)
// }

defineExpose({
  submit,
  showThisStep
})
</script>
<style lang="scss" scoped>
.my-step {
  .my-container {
    margin: 10px auto 0;
    display: flex;
    justify-content: center;
    .left {
      .my-office-docx {
        height: 580px;
        // padding: 10px;
        overflow-y: scroll;
        width: 860px;
        padding-bottom: 80px;
      }
    }

    .right {
      margin-left: 15px;
      flex: 1;
    }

    .my-office-form {
      height: 580px;
      padding: 10px;
      overflow-y: scroll;
    }
  }
}

.wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

p {
  font-size: 18px;
}
</style>
