<template>
  <div class="middle-right">
    <dataWrap
      title="当月回款率分析"
      ref="wrapRef"
      :selectOptions="selectOptions"
      :request-api="getCurrentMonthRate"
      @on-select="drawPie"
      :charts="charts"
    >
      <template #default="{ data }">
        <div class="left">
          <div class="left-item">
            <span>账款金额总计：</span>
            <div>
              <span class="number">{{ numberWithCommas(Number(data.total).toFixed(2)) }}</span> <span class="unit">元</span>
            </div>
          </div>
          <div class="left-item">
            <span>实收金额：</span>
            <div>
              <span class="number">{{ numberWithCommas(Number(data.received).toFixed(2)) }}</span> <span class="unit">元</span>
            </div>
          </div>
          <div class="left-item">
            <span> 当月回款率：</span>
            <div>
              <span class="number">{{ data.total === 0 ? '--' : ((data.received / data.total) * 100).toFixed(1) }}</span>
              <span class="unit">%</span>
            </div>
          </div>
        </div>
        <div class="right" ref="pieChart"></div>
      </template>
    </dataWrap>
  </div>
</template>
<script setup>
import dataWrap from './data-wrap'
import dayjs from 'dayjs'
import { getCurrentMonthRate } from '@/api/panel-data/collecting'
import * as echarts from 'echarts'
import { getRandomColorHex, numberWithCommas } from '@/utils/index'
const selectOptions = [
  {
    prop: 'yearMonth',
    type: 'date-picker',
    defaultValue: dayjs().format('YYYY-MM'),
    props: { type: 'month', value: 'YYYY-MM', valueFormat: 'YYYY-MM' }
  }
]

const pieChart = ref()
const wrapRef = ref()
const charts = ref([])
// 饼图
const drawPie = () => {
  let myChart = echarts.init(pieChart.value)
  charts.value[0] = myChart
  const panelData = wrapRef.value.panelData
  const option = {
    tooltip: {
      trigger: 'item'
    },

    legend: {
      // orient: 'vertical',
      bottom: '1%',
      icon: 'circle',
      type: 'scroll'
    },

    series: [
      {
        type: 'pie',
        radius: '50%',
        center: ['50%', '50%'],
        data: (panelData.outstanding || []).filter(item => item.value),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        itemStyle: {
          normal: {
            label: {
              show: true,
              formatter: '{d}%'
            },
            labelLine: {
              shoe: true
            }
          }
        }
      }
    ]
  }
  myChart.setOption(option)
}
onMounted(async () => {
  await wrapRef.value.requestResult
  drawPie()
})
</script>
<style lang="scss" scoped>
.middle-right {
  flex: 1.5;
}

.left {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 16px;
  border-right: 1px solid #e8e8e8ff;
  .left-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 16px;
    padding-right: 16px;
    width: 100%;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    font-size: 16px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    color: #333333;
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }
    .unit {
      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #7d8592;
    }
  }
}
.number {
  font-size: 24px;
  font-family: AlibabaPuHuiTi_2_85_Bold;
  color: #45a0ff;
}
.right {
  flex: 1.5;
}
</style>
