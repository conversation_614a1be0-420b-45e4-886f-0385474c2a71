<!--
 * @Description: 废弃客户弹窗
 * @Author: thb
 * @Date: 2023-05-30 09:56:01
 * @LastEditTime: 2023-06-02 16:02:21
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    class="abandon-dialog"
    title="废弃客户"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-form-item label="废弃原因" prop="discardReason">
        <el-input v-model="formData.discardReason" type="textarea" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit(formRef)">确认废弃</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { abandonCustomer } from '@/api/customer/file'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number
})

const formData = ref({})

const rules = {
  discardReason: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ]
}

const formRef = ref()
const { proxy } = getCurrentInstance()
const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      const { data } = await abandonCustomer({
        ...formData.value,
        discard: 1,
        ciId: props.id
      })
      if (data > 0) {
        proxy.$modal.msgSuccess(`废弃成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`废弃失败!`)
      }
    } else {
    }
  })
}
</script>
<style lang="scss" scoped></style>
