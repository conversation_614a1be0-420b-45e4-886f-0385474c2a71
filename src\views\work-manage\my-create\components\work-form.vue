<!--
 * @Description: 工单表单
 * @Author: thb
 * @Date: 2023-07-17 16:00:03
 * @LastEditTime: 2023-12-15 08:36:41
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    width="1200"
    :title="isDisabled ? '工单详情' : '新增工单'"
    class="work-dialog"
    :close-on-click-modal="false"
    v-model="visible"
    :class="[isDisabled ? 'order-dialog' : '']"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="isDisabled">
      <!-- 详情中增加工单编号 -->
      <el-row :gutter="24" v-if="isDisabled">
        <el-col :span="8">
          <el-form-item label="工单编号" prop="orderNo">
            <el-input v-model="formData.orderNo" maxlength="20" :disabled="isDisabled" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="关联客户" prop="customerName">
            <div @click="handleShow" style="width: 100%" v-if="!isDisabled">
              <el-input
                v-model="formData.customerName"
                :disabled="isDisabled"
                readonly
                maxlength="20"
                :placeholder="isDisabled ? '' : '请输入'"
              />
            </div>
            <el-popover v-else placement="top-start" trigger="hover" :content="formData.customerName">
              <template #reference>
                <!-- 点击弹窗出现客户列表  -->
                <div @click="handleShow" style="width: 100%">
                  <el-input
                    v-model="formData.customerName"
                    :disabled="isDisabled"
                    readonly
                    maxlength="20"
                    :placeholder="isDisabled ? '' : '请输入'"
                  />
                </div>
              </template>
            </el-popover>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="紧急状态" prop="isUrgent">
            <el-radio-group v-model="formData.isUrgent" :disabled="isDisabled">
              <el-radio label="0">一般</el-radio>
              <el-radio label="1">紧急</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="期望完成时间">
            <el-date-picker
              v-model="formData.expectTime"
              type="date"
              :disabled="isDisabled"
              format="YYYY-MM-DD"
              :disabled-date="disabledDate"
              value-format="YYYY-MM-DD"
              :placeholder="isDisabled ? ' ' : '请选择'"
          /></el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="工单类型" prop="orderTypeId">
            <el-tree-select
              v-model="formData.orderTypeId"
              :data="orderList"
              :placeholder="isDisabled ? ' ' : '请选择'"
              :disabled="isDisabled"
              clearable
              :props="{ value: 'id', label: 'typeName', children: 'child' }"
              @node-click="handleNodeClick"
              @change="handleChange"
            >
            </el-tree-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="工单标题" prop="orderTitle">
            <el-input
              v-model="formData.orderTitle"
              maxlength="20"
              :placeholder="isDisabled ? '' : '请输入'"
              :disabled="isDisabled"
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="地区">
            <el-cascader
              v-if="!isDisabled"
              v-model="formData.address"
              filterable
              :props="{ label: 'name', value: 'name', children: 'child' }"
              :options="cascaderOptions"
            />
            <el-popover v-else placement="top-start" trigger="hover" :content="formData.address">
              <template #reference>
                <el-input
                  v-model="formData.address"
                  maxlength="100"
                  :placeholder="isDisabled ? ' ' : '请输入'"
                  :disabled="isDisabled"
                />
              </template>
            </el-popover>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="工单内容" prop="content">
            <el-input
              v-model="formData.content"
              type="textarea"
              :disabled="isDisabled"
              :autosize="{ minRows: 2, maxRows: 6 }"
              maxlength="1000"
              :placeholder="isDisabled ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :disabled="isDisabled"
              :autosize="{ minRows: 2, maxRows: 6 }"
              maxlength="1000"
              :placeholder="isDisabled ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 补充说明 -->
      <!-- 选择的工单类型下如果有补充说明才需要展示 -->
      <el-row :gutter="24" v-if="formData.supplementExplain">
        <el-col :span="24">
          <el-form-item label="补充说明">
            <el-input
              disabled
              v-model="formData.supplementExplain"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              maxlength="1000"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24" v-if="type === 'add'">
        <el-col :span="8">
          <el-form-item label="指派给" prop="executor">
            <SelectTree v-model="formData.executor" clearable :disabled="isDisabled" :placeholder="isDisabled ? ' ' : '请选择'" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 已完成的详情增加完成反馈内容 -->
      <template v-if="formData.orderStatus === '1'">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="完成反馈">
              <el-input
                disabled
                v-model="formData.feedbackOrDescription"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                maxlength="1000"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-if="formData.fileList?.length">
          <el-col :span="24">
            <el-image
              v-for="(item, index) in formData.files"
              :key="index"
              style="width: 200px; height: 200px"
              :src="item"
              :preview-src-list="[item]"
              fit="cover"
            />
          </el-col>
        </el-row>
      </template>

      <!-- 增加回退说明 -->
      <template v-if="formData.orderStatus === '2'">
        <el-row :gutter="24" v-if="formData.feedbackOrDescription">
          <el-col :span="24">
            <el-form-item label="回退说明">
              <el-input
                disabled
                v-model="formData.feedbackOrDescription"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                maxlength="1000"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 清税证明 -->
      <el-row
        :gutter="24"
        v-if="formData.orderTypeName === '税务注销' && formData.taxClearanceList && formData.taxClearanceList.length"
      >
        <el-col :span="24">
          <el-form-item label="清税证明" prop="taxClearanceList">
            <FileList :list="formData.taxClearanceList" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 工单处理流程 -->

    <el-timeline v-if="isDisabled">
      <el-timeline-item
        v-for="(item, index) in formData.recordList"
        :class="[index === formData.recordList?.length - 1 ? 'highlight-circle' : '']"
        :key="index"
      >
        <div class="line-title">
          <span :class="[item.recordName === '超时完成工单' ? 'red-color' : '']">{{ item.recordName }}</span>
          <span>{{ item.createTime }}</span>
        </div>
        <div class="line-content">
          <p>操作人：{{ item.createBy }}</p>
          <p v-if="item.executorName">指派给：{{ item.executorName }}</p>
          <p v-if="item.remark">备注：{{ item.remark }}</p>
          <!-- 操作按钮是否显示 -->
          <template v-if="stepShow">
            <!-- 我的待办 -->
            <template v-if="index === formData.recordList.length - 1 && workType !== 'myCreate'">
              <!-- 待完成 -->
              <template v-if="formData.orderStatus === '0'">
                <div>
                  <el-button type="primary" @click="showCompleteForm">完成工单</el-button>
                  <el-button type="primary" v-hasPermi="['order:todo:assignOrback']" @click="showAssignForm">指派</el-button>
                  <el-button type="danger" v-hasPermi="['order:todo:assignOrback']" @click="showBackForm">回退</el-button>
                  <!-- 新增"标记异常"的功能按钮 -->
                  <el-button type="warning" @click="markException">标记异常</el-button>
                </div>
              </template>
              <!-- 回退 -->
              <template v-if="formData.orderStatus === '2'">
                <el-button type="primary" @click="handleReset">再次发起</el-button>
              </template>

              <!-- 标记异常 -->
              <template v-if="formData.orderStatus === '3'">
                <el-button type="primary" @click="handleProcess">继续办理</el-button>
              </template>
            </template></template
          >

          <!--  -->
          <template v-if="index === formData.recordList.length - 1 && workType === 'myCreate'">
            <!-- 如果是税务注销的工单类型 -->
            <template v-if="formData.orderTypeName === '税务注销' && formData.orderStatus === '0' && !formData.executor">
              <el-button type="primary" v-hasPermi="['order:todo:assignOrback']" @click="showAssignForm">指派</el-button>
            </template>
            <!-- 如果是税务变更的工单类型 -->
            <template v-if="formData.orderTypeName === '税务变更' && formData.orderStatus === '0' && !formData.executor">
              <el-button type="primary" v-hasPermi="['order:todo:assignOrback']" @click="showAssignForm">指派</el-button>
            </template>
            <!-- 回退 -->
            <template v-if="formData.orderStatus === '2'">
              <el-button type="primary" @click="handleReset">再次发起</el-button>
            </template>
          </template>
        </div>
      </el-timeline-item>
    </el-timeline>
    <template #footer>
      <el-button v-if="!isDisabled" type="primary" @click="handleSubmit(formRef)">保存信息</el-button>
      <el-button @click="handleClose">{{ type === 'detail' ? '关闭' : '取消' }}</el-button>
    </template>
  </el-dialog>
  <tableModal
    v-if="listSelectShow"
    title="关联客户"
    multiple
    rowKey="customerId"
    :init-param="{ discard: 0 }"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-multiple-select="handleSelect"
  />
</template>
<script setup lang="tsx">
import { computed, onMounted, ref } from 'vue'
import { getCustomers } from '@/api/customer/file'
import {
  orderSaveBatch,
  getWorkOrderDetail,
  getOrderDropdownList,
  orderAssign,
  orderBack,
  orderComplete,
  changeOrderStatus,
  exceptionOrder,
  getOrderList
} from '@/api/work/work'
import tableModal from '@/components/tableModal'
import { ColumnProps } from '@/components/ProTable/interface'
import { customerProperty, customerIndustry } from '@/utils/constants'
import SelectTree from '@/components/SelectTree'
import type { FormInstance } from 'element-plus'
import { useDic } from '@/hooks/useDic'
import { useDialog } from '@/hooks/useDialog'
import assignForm from './assign-form.vue'
import completeForm from './complete-form.vue'
import backForm from './back-form.vue'
import { changeFileList } from '@/utils/common'
import { getFileUrlByOss } from '@/api/file/file.js'
import FileList from '@/components/FileList'
import exceptionForm from './exception-form.vue'
import { getAreaTreeList } from '@/api/basicData/basicData'
const { getDic } = useDic()
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success', 'on-reset'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  type: {
    type: String,
    default: 'add' //默认为'add','detail'
  },
  id: Number,
  workType: {
    type: String,
    default: 'myCreate'
  },
  stepShow: {
    type: Boolean,
    default: true
  }
})

const isDisabled = computed(() => {
  return props.type === 'detail'
})

const formData = ref<any>({
  customerName: '',
  isUrgent: '0'
})
console.log('formData', formData.value)
// 日期选择器禁用
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用今天以前的时间
}
const rules = ref({
  customerName: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  isUrgent: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  orderTypeId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  orderTitle: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  content: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  executor: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
})
// 关联客户弹窗显示
const listSelectShow = ref(false)
const handleShow = () => {
  // 如果是详情状态 不需要显示弹窗
  if (isDisabled.value) return
  listSelectShow.value = true
}

// 选择单个客户后
const handleSelect = (selectedList: any) => {
  console.log('selectedList', selectedList)
  // const { customerId, customerName } = data
  // 填充记账合同表单

  const nameStr = `${selectedList[0].customerName}${selectedList.length === 1 ? '' : '等'}${selectedList.length}家企业`
  formData.value = Object.assign(formData.value, {
    // ciId: customerId,
    customerName: nameStr,
    // selectedList
    ciIdList: selectedList.map(item => item.customerId)
  })
}
const { proxy } = getCurrentInstance()
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_Property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]
const formRef = ref()
const handleSubmit = (formEl: FormInstance) => {
  if (!formEl) return
  formEl.validate(async (valid: any) => {
    if (valid) {
      // 校验成功
      const result: any = await orderSaveBatch({
        ...formData.value,
        address: formData.value.address?.join('/')
      })
      if (result.code === 200) {
        proxy.$modal.msgSuccess(`保存信息成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`保存信息失败!`)
      }
    }
  })
}
const getDetail = async (id: number) => {
  const { data } = await getWorkOrderDetail(id)
  formData.value = data || {}

  handleNodeClick({
    supplementExplain: data.supplementExplain
  })
  // 获取详情之后处理上传的fileList(图片集合)

  // 循环formData.fileList 获取oss url
  if (formData.value.fileList.length) {
    formData.value.files = []
    formData.value.fileList.forEach(async (file: any) => {
      const { data } = await getFileUrlByOss(file.urls)
      formData.value.files.push(data)
    })
  }
}
const orderList = ref([])
const getOrderTypeList = async () => {
  const { data } = await getOrderList({
    status: '1'
  })
  orderList.value = data || []
}
const handleChange = value => {
  if (!value) {
    formData.value.supplementExplain = ''
  }
}
const handleNodeClick = (node: any) => {
  if (!node.child) {
    formData.value.supplementExplain = node.supplementExplain
  }
}
// 展示完成工单弹窗
const { showDialog } = useDialog()
const handleSubmitCallback = () => {
  handleClose()
  emits('on-success')
}
const handleRevertParams = data => {
  console.log('data', data)
  if (data.fileList && data.fileList.length) {
    data.fileList = changeFileList(data.fileList)
  }
  if (data.taxClearanceList && data.taxClearanceList.length) {
    data.taxClearanceList = changeFileList(data.taxClearanceList)
  }
  data.orderId = formData.value.id // 工单id
}
const showAssignForm = () => {
  showDialog({
    title: '指派工单',
    customClass: 'assign-modal',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: assignForm, // 表单组件
    submitApi: orderAssign, // 提交api
    handleRevertParams: handleRevertParams, // 修改保存的参数
    submitCallback: handleSubmitCallback // 提交成功之后的回调函数
  })
}

const showCompleteForm = () => {
  showDialog({
    title: '完成工单',
    customClass: 'complete-modal',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: completeForm, // 表单组件
    submitApi: orderComplete, // 提交api
    handleConvertParams: data => {
      data.orderTypeName = formData.value.orderTypeName
    },
    handleRevertParams: handleRevertParams, // 修改保存的参数
    submitCallback: handleSubmitCallback // 提交成功之后的回调函数
  })
}

// 显示回退表单弹窗
const showBackForm = () => {
  showDialog({
    title: '回退工单',
    customClass: 'back-modal',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: backForm, // 表单组件
    submitApi: orderBack, // 提交api
    handleRevertParams: handleRevertParams, // 修改保存的参数
    submitCallback: handleSubmitCallback // 提交成功之后的回调函数
  })
}

// 再次发起
const handleReset = () => {
  handleClose()
  emits('on-reset', {
    ...formData.value,
    orderStatus: '0', // 默认待完成状态
    id: undefined, // id置空
    feedbackOrDescription: ''
  })
}

// 标记异常
const markException = () => {
  showDialog({
    title: '标记异常',
    component: exceptionForm, // 表单组件
    customClass: 'exception-modal',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    handleRevertParams: data => {
      data.orderId = formData.value.id // 工单id
    },
    submitApi: exceptionOrder, // 提交api
    submitCallback: handleSubmitCallback // 提交成功之后的回调函数
  })
}

// 标记异常状态的处理 将工单状态改为待完成状态
const handleProcess = async () => {
  const result: any = await changeOrderStatus(props.id)
  if (result.code === 200) {
    // 如果修改成功 则需要刷新记录列表
    getDetail(props.id!)
  }
}

onMounted(async () => {
  await getOrderTypeList()
  if (props.id && props.type === 'detail') {
    getDetail(props.id)
  }
})

const setFormData = data => {
  formData.value = data
}

const cascaderOptions = ref([])
const getAddressCascader = async () => {
  const { data } = await getAreaTreeList({
    enable: '1'
  })
  cascaderOptions.value = data || []
}
getAddressCascader()
defineExpose({
  setFormData
})
</script>
<style lang="scss">
.work-dialog {
  .el-date-editor--date {
    width: 100%;
  }
  .el-select.el-select--default {
    width: 100%;
  }
}
.order-dialog {
  .el-dialog__body {
    display: flex;
    .el-form {
      flex: 1.5;
    }
    .el-timeline-item__content {
      margin-top: 10px;
      .line-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        span:first-child {
          color: black;
          font-size: 18px;
          font-weight: 600;
          margin-right: 10px;
        }
      }
    }
  }
}

.red-color {
  color: #f56c6c;
}
</style>

<style lang="scss" scoped>
.el-image {
  margin-right: 20px;
}

:deep(.highlight-circle) {
  .el-timeline-item__node.el-timeline-item__node--normal {
    width: 16px;
    height: 16px;
    background: #ffffff;
    border: 4px solid #2383e7;
  }
}
</style>
