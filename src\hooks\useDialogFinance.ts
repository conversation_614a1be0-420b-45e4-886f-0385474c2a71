import { ElMessageBox, ElMessage } from 'element-plus'
// getCurrentInstance
import { h, nextTick, ref } from 'vue'
/**
 * @description dialog 弹窗  使用函数调用的方式显示和隐藏弹窗并且自定义内部组件
 * */
let instanceTemp = { title: '' }
let buttonLoading = false

export const useDialog = () => {
  // 显示弹窗
  const showDialog = (params: {
    title?: string
    cancelButtonText?: string
    confirmButtonText?: string
    component?: any
    componentSub?: any
    showConfirmButton?: string
    showClose?: boolean
    customClass?: string
    handelCallBack?: (params: any) => void
    submitApi?: (param: any) => Promise<any> // 保存接口,注意提供自定义修改接口参数的功能
    submitCallback?: (param: any, resultData?: any) => void
    cancelCallback?: () => void
    rowFormData?: any
    requestParams?: any // getApi查询参数
    handleRevertParams?: (param: any) => void
    getApi?: (param: any) => Promise<any>
  }) => {
    const {
      title,
      component,
      componentSub,
      customClass,
      submitApi,
      getApi,
      cancelButtonText,
      confirmButtonText,
      showConfirmButton,
      showClose,
      rowFormData,
      requestParams,
      handleRevertParams,
      submitCallback,
      cancelCallback
    } = params
    const compRef = ref()
    ElMessageBox({
      title,
      customClass,
      autofocus: true,
      // Should pass a function if VNode contains dynamic props
      message: () => {
        nextTick(() => {
          // collectionForm使用在五个场景：新增收款 关联收款 收款详情 收款审批 关联收款
          // 这一段判断不该写在这，应该在页面上配置对应值，透传进来，会稍好
          if (title === '收款详情') {
            compRef.value.disabledCode = 1 // 收款详情
          } else if (title === '收款审批') {
            compRef.value.disabledCode = 2 // 收款审批 已作废
          } else if (title?.includes('审批详情')) {
            compRef.value.disabledCode = 3 // 审批详情
          } else if (title === '关联收款') {
            compRef.value.disabledCode = 4 // 关联收款
          } else if (title === '通过收款') {
            compRef.value.disabledCode = 5 // 通过收款
          } else if (title === '驳回收款') {
            compRef.value.disabledCode = 6 // 驳回收款
          } else if (instanceTemp.title?.includes('编辑')) {
            // 此处用instanceTemp.title代替title
            compRef.value.disabled = false
          } else if (getApi && typeof title === 'string' && title.includes('详情')) {
            // 该逻辑涉及 财务管理-应收台账详情表单、基础数据配置-分公司详情表单
            compRef.value.disabled = true
          }
        })
        if (getApi) {
          // 注意：此处存在异步关系，dom没被渲染前，获取不到compRef.value
          getApi(requestParams).then(response => {
            // console.log('instanceTemp', instanceTemp)
            // console.log('compRef.value.formData', compRef.value.formData)
            // console.log('compRef.value.formData.value', compRef.value.formData.value)
            // 如果成功
            // 获取formData的key值
            const keys = Object.keys(response.data) // 全部按reactive来看待
            // 循环给formData赋值
            keys.forEach(key => {
              compRef.value.formData[key] = response.data[key]
            })
            // console.log('compRef.value.formData', compRef.value.formData)
            if (title === '收款审批') {
              compRef.value.formData['isChecked'] = 1
            }
          })
        } else if (rowFormData) {
          nextTick(() => {
            const keys = Object.keys(rowFormData)
            keys.forEach(key => {
              compRef.value.formData[key] = rowFormData[key]
            })
          })
        }
        return h(component || 'div', {
          ref: compRef
        })
      },
      showConfirmButton: showConfirmButton === 'hide' ? false : true,
      showClose: !(showClose + '' === 'false'),
      showCancelButton: true,
      closeOnClickModal: false,
      cancelButtonText: cancelButtonText || '取消',
      confirmButtonText: confirmButtonText || '保存',
      beforeClose: (action, instance, done) => {
        // console.log('beforeClose')
        if (action === 'confirm') {
          // 如果表单对象存在，则执行表单校验规则
          if (compRef.value.getFormRef && instance.confirmButtonText !== '编辑') {
            console.log('compRef.value.formData', compRef.value.formData)
            compRef.value
              .getFormRef()
              .validate(async (valid: any) => {
                if (valid) {
                  // 校验通过且如果存在api接口则调用接口
                  if (submitApi) {
                    handleRevertParams && handleRevertParams(compRef.value.formData)
                    if (buttonLoading) return
                    buttonLoading = true
                    // 临时用一下这个解除按钮上锁
                    setTimeout(() => {
                      buttonLoading = false
                    }, 3000)
                    const result = await submitApi(compRef.value.formData).finally(() => {
                      buttonLoading = false
                    })
                    // console.log('result', result)
                    // const { proxy } = getCurrentInstance() as any
                    if (result.data > 0 || result.code === 200) {
                      // 说明保存成功
                      buttonLoading = false
                      done()
                      submitCallback &&
                        submitCallback(
                          {
                            ...compRef.value.formData
                          },
                          result.data
                        )
                      nextTick(() => {
                        instanceTemp.title = ''
                        ElMessage({
                          message: '保存成功!',
                          type: 'success'
                        })
                      })
                    } else {
                      buttonLoading = false
                      ElMessage({
                        message: '保存失败!',
                        type: 'error'
                      })
                    }
                  } else {
                    done()
                    submitCallback &&
                      submitCallback({
                        ...compRef.value.formData
                      })
                  }
                } else {
                }
              })
              .catch((error: any) => {
                console.log('error', error)
              })
          } else {
            // 修改文案并刷新ElMessageBox
            if (confirmButtonText === '编辑') {
              compRef.value.disabled = false
              instance.title = instance.title.replace('详情', '编辑')
              instanceTemp.title = instance.title
              instance.cancelButtonText = '取消'
              instance.confirmButtonText = '保存'
            }
          }
        } else {
          // cancel情况下
          cancelCallback && cancelCallback()
          instanceTemp.title = ''
          done()
        }
      }
    }).then(() => {
      //
    })
  }

  return {
    showDialog
  }
}
