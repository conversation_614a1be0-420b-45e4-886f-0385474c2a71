<!--
 * @Description:  表单嵌套表格实现校验功能
 * @Author: thb
 * @Date: 2023-05-26 13:31:41
 * @LastEditTime: 2023-10-31 16:36:09
 * @LastEditors: thb
-->
<template>
  <div ref="formWrap" class="form-rap">
    <el-form ref="formRef" :model="formData">
      <el-table ref="formTable" :data="formData.tableData" v-bind="$attrs">
        <el-table-column v-bind="item" v-for="item in option" align="center" :prop="item.prop" :key="item" :type="item.type">
          <!-- <template #header>
            <span :class="formData.rules && formData.rules[item.prop] ? 'validate-icon' : ''">
              {{ item.label }}
            </span>
          </template> -->
          <template #default="{ row, column, $index }" v-if="slots[item.prop]">
            <el-form-item
              :prop="'tableData.' + $index + '.' + item.prop"
              :rules="formData.rules ? formData.rules[item.prop] : []"
            >
              <slot v-bind="{ row, column, $index }" :name="item.prop"></slot>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { ref, useSlots } from 'vue'
interface formTableType {
  tableData: any[]
  rules?: any
}
withDefaults(
  defineProps<{
    formData: formTableType
    option: any[]
  }>(),
  {
    formData: () => {
      return {
        tableData: [],
        rules: {}
      }
    }
  }
)
const slots = useSlots()
// const emits = defineEmits(['update:modelValue'])
// const form = computed({
//   get: () => {
//     console.log('modelValue111111', props.modelValue)
//     return props.modelValue
//   },
//   set: (val: any) => {
//     emits('update:modelValue', val)
//   }
// })

const formTable = ref()
const toggleRowSelection = (row: any, isSelect: boolean) => {
  formTable.value.toggleRowSelection(row, isSelect)
}

const formWrap = ref()
const getParentDom = () => {
  return formWrap.value
}
const formRef = ref()
const handleValidate = async () => {
  console.log('handleValidate', formRef.value)
  const result = await formRef.value.validate((valid: any) => {
    if (valid) {
      console.log('submit!')
    } else {
      console.log('error submit!')
      return false
    }
  })
  return result
}

// 表单清空校验
const handleResetValidate = () => {
  formRef.value.resetFields()
}

// const getFormRef = () => {
//   return formRef.value
// }
defineExpose({
  toggleRowSelection,
  getParentDom,
  // getFormRef,
  formRef,
  handleValidate,
  handleResetValidate
})
</script>
<style lang="scss" scoped>
.form-rap {
  background: white;
}

:deep(.el-form-item--default .el-form-item__content) {
  display: inline-block;
}
.validate-icon {
  &::before {
    content: '*';
    color: red;
  }
}
</style>
