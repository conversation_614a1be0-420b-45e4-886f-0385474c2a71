/*
 * @Description:
 * @Author: thb
 * @Date: 2023-07-20 10:33:12
 * @LastEditTime: 2023-08-08 17:49:28
 * @LastEditors: thb
 */
// 基础数据api
import request from '@/utils/request'

/* 行政区划 */
// 获取导入模板
export const postAreaImportTempUrl = '/basicAdministrative/importTemp'
export function postAreaImportTemp(data) {
  return request({
    url: '/basicAdministrative/importTemp',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 导入
export const postAreaImport = query => {
  return request({
    url: '/basicAdministrative/import',
    method: 'post',
    data: query
  })
}

// 列表查询
export function getAreaTreeList(params) {
  return request({
    url: '/basicAdministrative/tree',
    method: 'get',
    params
  })
}

// 提交
export function postAddArea(data) {
  return request({
    url: '/basicAdministrative/saveOrUpdate',
    method: 'post',
    data
  })
}

// 删除
export function deleteArea(params) {
  return request({
    url: '/basicAdministrative/delete?id=' + params,
    method: 'delete'
  })
}

// 获取详情
export function getAreaDetail(id) {
  return request({
    url: '/basicAdministrative/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 状态设置
export function postAreaEnable(data) {
  return request({
    url: '/basicAdministrative/enable?id=' + data.id,
    method: 'post'
  })
}

/* 税务局 */
// 获取导入模板
export const postTaxImportTempUrl = '/basicTaxBureau/importTemp'

// 导入
export const postTaxImport = query => {
  return request({
    url: '/basicTaxBureau/import',
    method: 'post',
    data: query
  })
}

// 列表查询
export function getTaxTreeList(params) {
  return request({
    url: '/basicTaxBureau/tree',
    method: 'get',
    params
  })
}

// 提交
export function postAddTax(data) {
  return request({
    url: '/basicTaxBureau/saveOrUpdate',
    method: 'post',
    data
  })
}

// 删除
export function deleteTax(params) {
  return request({
    url: '/basicTaxBureau/delete?id=' + params,
    method: 'delete'
  })
}

// 获取详情
export function getTaxDetail(id) {
  return request({
    url: '/basicTaxBureau/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 状态设置
export function postTaxEnable(data) {
  return request({
    url: '/basicTaxBureau/enable?id=' + data.id,
    method: 'post'
  })
}

/* 银行 */
// 获取导入模板
export const postBankImportTempUrl = '/basicBank/importTemp'

// 导入
export const postBankImport = query => {
  return request({
    url: '/basicBank/import',
    method: 'post',
    data: query
  })
}

// 列表查询
export function getBankTreeList(params) {
  return request({
    url: '/basicBank/tree',
    method: 'get',
    params
  })
}

// 提交
export function postAddBank(data) {
  return request({
    url: '/basicBank/saveOrUpdate',
    method: 'post',
    data
  })
}

// 删除
export function deleteBank(params) {
  return request({
    url: '/basicBank/delete?id=' + params,
    method: 'delete'
  })
}

// 获取详情
export function getBankDetail(id) {
  return request({
    url: '/basicBank/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 状态设置
export function postBankEnable(data) {
  return request({
    url: '/basicBank/enable?id=' + data.id,
    method: 'post'
  })
}

/* 停止记账原因 Basic Bookkeeping Stop Controller */
// 获取导入模板
export const postBookkeepingStopImportTempUrl = '/basicBookkeepingStop/importTemp'

// 导入
export const postBookkeepingStopImport = query => {
  return request({
    url: '/basicBookkeepingStop/import',
    method: 'post',
    data: query
  })
}

// 列表查询
export function getBookkeepingStopTreeList(params) {
  return request({
    url: '/basicBookkeepingStop/tree',
    method: 'get',
    params
  })
}

// 提交
export function postAddBookkeepingStop(data) {
  return request({
    url: '/basicBookkeepingStop/saveOrUpdate',
    method: 'post',
    data
  })
}

// 删除
export function deleteBookkeepingStop(params) {
  return request({
    url: '/basicBookkeepingStop/delete?id=' + params,
    method: 'delete'
  })
}

// 获取详情
export function getBookkeepingStopDetail(id) {
  return request({
    url: '/basicBookkeepingStop/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 状态设置
export function postBookkeepingStopEnable(data) {
  return request({
    url: '/basicBookkeepingStop/enable?id=' + data.id,
    method: 'post'
  })
}

/* 日记账科目 Basic Bookkeeping Subject Controller */
// 获取导入模板
export const postBookkeepingSubjectImportTempUrl = '/basicBookkeepingSubject/importTemp'

// 导入
export const postBookkeepingSubjectImport = query => {
  return request({
    url: '/basicBookkeepingSubject/import',
    method: 'post',
    data: query
  })
}

// 列表查询
export function getBookkeepingSubjectTreeList(params) {
  return request({
    url: '/basicBookkeepingSubject/tree',
    method: 'get',
    params
  })
}

// 提交
export function postAddBookkeepingSubject(data) {
  return request({
    url: '/basicBookkeepingSubject/saveOrUpdate',
    method: 'post',
    data
  })
}

// 删除
export function deleteBookkeepingSubject(params) {
  return request({
    url: '/basicBookkeepingSubject/delete?id=' + params,
    method: 'delete'
  })
}

// 获取详情
export function getBookkeepingSubjectDetail(id) {
  return request({
    url: '/basicBookkeepingSubject/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 状态设置
export function postBookkeepingSubjectEnable(data) {
  return request({
    url: '/basicBookkeepingSubject/enable?id=' + data.id,
    method: 'post'
  })
}

/* 分公司信息 Basic Company Controller */
// 获取导入模板
export const postCompanyImportTempUrl = '/basicCompany/importTemp'

// 导入
export const postCompanyImport = query => {
  return request({
    url: '/basicCompany/import',
    method: 'post',
    data: query
  })
}

// 列表查询
export function getCompanyTreeList(params) {
  return request({
    url: '/basicCompany/list',
    method: 'get',
    params
  })
}

// 提交
export function postAddCompany(data) {
  return request({
    url: '/basicCompany/saveOrUpdate',
    method: 'post',
    data
  })
}

// 删除
export function deleteCompany(params) {
  return request({
    url: '/basicCompany/delete?id=' + params,
    method: 'delete'
  })
}

// 获取详情
export function getCompanyDetail(id) {
  return request({
    url: '/basicCompany/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 状态设置
export function postCompanyEnable(data) {
  return request({
    url: '/basicCompany/enable?id=' + data.id,
    method: 'post'
  })
}

/* 合同预警 */
// 合同预警配置api
export function getContractWarningList(params) {
  return request({
    url: '/contractAlterJob/list',
    method: 'get',
    params
  })
}

// 合同预警配置提交
export function addContractWarning(data) {
  return request({
    url: '/contractAlterJob/save',
    method: 'post',
    data
  })
}

// 合同预警配置详情
export function getContractWarningDetail(id) {
  return request({
    url: 'contractAlterJob/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 合同预警配置删除
export function deleteContractWarning(id) {
  return request({
    url: '/contractAlterJob/delete',
    method: 'delete',
    params: {
      id
    }
  })
}

//到期预警查询列表
export function getExpirationtWarningList(params) {
  return request({
    url: '/contractAlert/list',
    method: 'get',
    params
  })
}
