<template>
  <div class="main-wrap">
    <ProTable
      ref="proTable"
      title="收款分析"
      :init-param="initParam"
      :columns="columns"
      :toolButton="false"
      rowKey="id"
      :request-api="financeReceiptAnalyseList"
    >
      <template #action="{ row }">
        <el-button v-if="row.analyseStatus === 0" type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="handleShowResultDetail(row)">分析结果</el-button>
        <el-button v-if="row.analyseStatus === 0" type="primary" link @click="handleClose(row)">关闭</el-button>
      </template>
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      </template>
    </ProTable>
  </div>
  <analysisForm ref="analysisFormRef" @ok="getList" />
  <resultDetail ref="resultDetailRef" />
</template>

<script setup lang="tsx">
import { financeReceiptAnalyseList, financeReceiptAnalyseUpdateStatus } from '@/api/finance/collection-analysis'
import { ref, reactive, nextTick } from 'vue'
import analysisForm from '@/views/finance/collection-analysis/components/form.vue'
import resultDetail from '@/views/finance/collection-analysis/components/result-detail.vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const analyseStatusArr = [
  { value: 0, label: '进行中' },
  { value: 1, label: '已关闭' }
]

const { proxy } = getCurrentInstance()

const proTable = ref()

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({})

const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'analyseName',
    label: '分析名称',
    minWidth: '200',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: '200',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.createTime ? `${scope.row.createTime.replace('T', ' ').replace('Z', '')}` : '--'}</span>
    }
  },
  {
    prop: 'analyseCustomerNumber',
    label: '分析企业数',
    width: '100',
    isColShow: false
  },
  {
    prop: 'fillNumber',
    label: '待填写数',
    width: '100',
    isColShow: false
  },
  {
    prop: 'receiveableAmount',
    label: '应收金额',
    width: '150',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.receiveableAmount >= 0 ? `${scope.row.receiveableAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'receiptAmount',
    label: '已收金额',
    width: '150',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.receiptAmount >= 0 ? `${scope.row.receiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'updateTime',
    label: '最后修改时间',
    width: '200',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.updateTime ? `${scope.row.updateTime.replace('T', ' ').replace('Z', '')}` : '--'}</span>
    }
  },
  {
    prop: 'analyseStatus',
    label: '进行状态',
    width: '150',
    isColShow: false,
    enum: analyseStatusArr,
    search: { el: 'select' }
  },
  {
    prop: 'action',
    label: '操作',
    width: 220,
    isColShow: false,
    fixed: 'right'
  }
]

/* 处理分析表单 --- start */
const handleAdd = () => {
  ElMessageBox.prompt('分析名称', '新建分析', {
    confirmButtonText: '选择客户',
    cancelButtonText: '取消',
    inputPattern: /[\S]/,
    inputErrorMessage: '请输入分析名称'
  })
    .then(({ value }) => {
      handleShowForm({ analyseName: value })
    })
    .catch(() => {})
}
const analysisFormRef = ref()
const handleShowForm = (data: any) => {
  analysisFormRef.value.onAdd(data)
}
const handleEdit = (data: any) => {
  analysisFormRef.value.onEdit(data)
}
/* 处理分析表单 --- end */

const handleClose = (row: any) => {
  proxy.$modal
    .confirm('是否确认关闭名称为"' + row.analyseName + '"的数据项？')
    .then(function () {
      return financeReceiptAnalyseUpdateStatus({ batchId: row.batchId })
    })
    .then(() => {
      proTable.value.search()
      proxy.$modal.msgSuccess('关闭成功')
    })
    .catch(() => {})
}

/* 处理分析结果 --- start */
const resultDetailRef = ref()
const handleShowResultDetail = (row: any) => {
  resultDetailRef.value.onShow(row)
}
/* 处理分析结果 --- end */

const getList = () => {
  proTable.value.getTableList()
}
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
