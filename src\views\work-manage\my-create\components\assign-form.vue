<!--
 * @Description: 完成工单表单
 * @Author: thb
 * @Date: 2023-07-19 08:41:17
 * @LastEditTime: 2023-07-19 08:57:27
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="指派给" prop="executor">
          <SelectTree v-model="formData.executor" clearable placeholder="请选择" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 6 }"
            maxlength="1000"
            placeholder=" 请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import SelectTree from '@/components/SelectTree'
const formData = ref({
  executor: '',
  remark: ''
})

const rules = {
  executor: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped>
.el-select.el-select--default {
  width: 100%;
}
</style>
