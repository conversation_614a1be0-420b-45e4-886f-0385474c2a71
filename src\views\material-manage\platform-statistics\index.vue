<template>
  <ProTable
    row-key="id"
    v-if="show"
    ref="proTable"
    :initParam="initParam"
    :columns="columns"
    :request-api="getTabList"
    :dataCallback="dataCallback"
    :pagination="false"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.tabType" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
  </ProTable>
</template>
<script setup lang="jsx">
import {
  clueAnalyseChannel,
  clueAnalyseCustomerIntroduction,
  clueAnalyseDirect,
  clueAnalysePlatform,
  clueAnalyseStaffOther
  // clueAnalyseStaffChannel,
  // clueAnalyseStaffCustomerIntroduction,
  // clueAnalyseStaffDirect,
  // clueAnalyseStaffPlatform
} from '@/api/material-manage/clue-analyse'
import dayjs from 'dayjs'
import { multiply, divide } from '@/utils/math'

const proTable = ref('')
const initParam = reactive({ tabType: '0' })

const tabs = [
  {
    dicValue: '0',
    dictLabel: '平台统计'
  },
  {
    dicValue: '1',
    dictLabel: '直投统计'
  },
  {
    dicValue: '2',
    dictLabel: '客户介绍统计'
  },
  {
    dicValue: '3',
    dictLabel: '渠道统计'
  },
  {
    dicValue: '11',
    dictLabel: '其他'
  }
]

const show = ref(true) // 重新渲染让ProTable能重新获取columns
const handleRadioChange = value => {
  proTable.value.pageable.pageNum = 1
  switch (value) {
    case '0':
      columns.value = columns_0
      break
    case '1':
      columns.value = columns_1
      break
    case '2':
      columns.value = columns_2
      break
    case '3':
      columns.value = columns_3
      break
    case '11':
      columns.value = columns_11
      break
  }
  show.value = false
  nextTick(() => {
    show.value = true
  })
  initParam.tabType = value
}

// getTabList 获取tab下的列表
const getTabList = async data => {
  data.pageSize = 999
  console.log('data', data)
  switch (data.tabType) {
    case '0':
      return clueAnalysePlatform(data)
    case '1':
      return clueAnalyseDirect(data)
    case '2':
      return clueAnalyseCustomerIntroduction(data)
    case '3':
      return clueAnalyseChannel(data)
    case '11':
      return clueAnalyseStaffOther(data)
  }
}

function renameChildToChildren(items) {
  return items.map(item => {
    if (item.userList) {
      return {
        ...item,
        children: renameChildToChildren(item.userList),
        userList: undefined
      }
    }
    return item
  })
}
const dataCallback = data => {
  let dataTemp = data.records
  if (dataTemp) {
    dataTemp = renameChildToChildren(dataTemp)
    console.log('dataTemp', dataTemp)
    return dataTemp
  }
}
// const dataCallback = data => {
//   if (data) {
//     const dataTemp = data.records
//     // for(const item of dataTemp){}
//     console.log('dataTemp-1', JSON.parse(JSON.stringify(dataTemp)))
//     // for(const item of dataTemp){
//     //   console.log('sourceId', item.id)
//     //   if (item.id === '28') {
//     //     const res = await clueAnalyseStaffChannel({
//     //       sourceId: item.id,
//     //       pageSize: 999,
//     //       yearMonth: dayjs().format('YYYY-MM')
//     //     })
//     //     console.log('records', res.data.records)
//     //     item.children = res.data.records
//     //   }
//     // }
//     // dataTemp.forEach(async item => {
//     //   console.log('sourceId', item.id)
//     //   if (item.id === '28') {
//     //     const res = await clueAnalyseStaffChannel({
//     //       sourceId: item.id,
//     //       pageSize: 999,
//     //       yearMonth: dayjs().format('YYYY-MM')
//     //     })
//     //     console.log('records', res.data.records)
//     //     item.children = res.data.records
//     //   }
//     // })
//     console.log('dataTemp-2', JSON.parse(JSON.stringify(dataTemp)))
//     return dataTemp
//   }
// }

// 表格配置项
const columns_0 = [
  {
    prop: 'index',
    label: '序号',
    width: 100,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '平台名称',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'appealCount',
    minWidth: 150,
    label: '申述量',
    render: scope => {
      return <span>{scope.row.appealCount || 0} 条</span>
    }
  },
  {
    prop: 'appealSuccessCount',
    minWidth: 150,
    label: '申述成功',
    render: scope => {
      return <span>{scope.row.appealSuccessCount || 0} 条</span>
    }
  },
  {
    prop: 'credibleClueCount',
    minWidth: 150,
    label: '有效线索',
    render: scope => {
      return <span>{scope.row.credibleClueCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索有效率',
    render: scope => {
      return (
        <span>
          {scope.row.credibleClueCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.credibleClueCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    render: scope => {
      // 线索转化率：成交量/线索量
      // return <span>{scope.row.clueCount > 0 ? scope.row.clueConversionCount / scope.row.clueCount : 0}</span>
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '有效线索转化率',
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.credibleClueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.credibleClueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'bookkeepingGmv',
    minWidth: 150,
    label: '记账收款额',
    render: scope => {
      return <span>{scope.row.bookkeepingGmv || 0} 元</span>
    }
  },
  {
    prop: 'unitPrice',
    minWidth: 150,
    label: '线索单价',
    render: scope => {
      return <span>{scope.row.unitPrice || 0} 元</span>
    }
  },
  // {
  //   prop: 'xxx',
  //   minWidth: 150,
  //   label: '线索成本',
  //   render: scope => {
  //     return (
  //       <span>
  //         {scope.row.unitPrice && scope.row.credibleClueCount
  //           ? multiply(scope.row.unitPrice, scope.row.credibleClueCount).toString()
  //           : 0}
  //         元
  //       </span>
  //     )
  //   }
  // },
  {
    prop: 'cost',
    minWidth: 150,
    label: '线索成本',
    render: scope => {
      return <span>{scope.row.cost || 0} 元</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '记账ROI',
    render: scope => {
      return (
        <span>
          {scope.row.gmv > 0 && scope.row.cost > 0
            ? `${multiply(divide(scope.row.bookkeepingGmv, scope.row.cost), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'addressGmv',
    minWidth: 150,
    label: '地址收款额',
    render: scope => {
      return <span>{scope.row.addressGmv || 0} 元</span>
    }
  },
  {
    prop: 'otherGmv',
    minWidth: 150,
    label: '其他收款额',
    render: scope => {
      return <span>{scope.row.otherGmv || 0} 元</span>
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '合计收款',
    render: scope => {
      // =记账收款+地址收款+其他收款
      return <span>{scope.row.gmv || 0} 元</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '总ROI',
    render: scope => {
      // ROI：收款额/线索成本
      // return <span>{scope.row.cost > 0 ? scope.row.gmv / scope.row.cost : 0}</span>
      return (
        <span>
          {scope.row.gmv > 0 && scope.row.cost > 0 ? `${multiply(divide(scope.row.gmv, scope.row.cost), 100).toFixed(2)}%` : '--'}
        </span>
      )
    }
  },
  {
    prop: 'userName',
    label: '人员姓名',
    isShow: false,
    search: {
      el: 'input'
    }
  }
]
const columns_1 = [
  {
    prop: 'index',
    label: '序号',
    width: 100,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '直投平台名称',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'bookkeepingGmv',
    minWidth: 150,
    label: '记账收款额',
    render: scope => {
      return <span>{scope.row.bookkeepingGmv || 0} 元</span>
    }
  },
  {
    prop: 'unitPrice',
    minWidth: 150,
    label: '线索单价',
    render: scope => {
      return (
        <span>{scope.row.unitPriceList?.length > 0 ? scope.row.unitPriceList.map(price => `${price}元`).join('，') : '--'} </span>
      )
    }
  },
  {
    prop: 'cost',
    minWidth: 150,
    label: '线索成本',
    render: scope => {
      return <span>{scope.row.cost || 0} 元</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '记账ROI',
    render: scope => {
      return (
        <span>
          {scope.row.gmv > 0 && scope.row.cost > 0
            ? `${multiply(divide(scope.row.bookkeepingGmv, scope.row.cost), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'addressGmv',
    minWidth: 150,
    label: '地址收款额',
    render: scope => {
      return <span>{scope.row.addressGmv || 0} 元</span>
    }
  },
  {
    prop: 'otherGmv',
    minWidth: 150,
    label: '其他收款额',
    render: scope => {
      return <span>{scope.row.otherGmv || 0} 元</span>
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '合计收款',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '总ROI',
    render: scope => {
      return (
        <span>
          {scope.row.gmv > 0 && scope.row.cost > 0 ? `${multiply(divide(scope.row.gmv, scope.row.cost), 100).toFixed(2)}%` : '--'}
        </span>
      )
    }
  },
  {
    prop: 'userName',
    label: '人员姓名',
    isShow: false,
    search: {
      el: 'input'
    }
  }
]
const columns_2 = [
  {
    prop: 'index',
    label: '序号',
    width: 100,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '客户名称',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'introductionCount',
    minWidth: 150,
    label: '介绍总数',
    render: scope => {
      return <span>{scope.row.introductionCount || 0} 家</span>
    }
  },
  {
    prop: 'bookkeepingGmv',
    minWidth: 150,
    label: '记账收款额',
    render: scope => {
      return <span>{scope.row.bookkeepingGmv || 0} 元</span>
    }
  },
  {
    prop: 'addressGmv',
    minWidth: 150,
    label: '地址收款额',
    render: scope => {
      return <span>{scope.row.addressGmv || 0} 元</span>
    }
  },
  {
    prop: 'otherGmv',
    minWidth: 150,
    label: '其他收款额',
    render: scope => {
      return <span>{scope.row.otherGmv || 0} 元</span>
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '合计收款',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  },
  {
    prop: 'userName',
    label: '人员姓名',
    isShow: false,
    search: {
      el: 'input'
    }
  }
]
const columns_3 = [
  {
    prop: 'index',
    label: '序号',
    width: 100,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '渠道名称',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'bookkeepingGmv',
    minWidth: 150,
    label: '记账收款额',
    render: scope => {
      return <span>{scope.row.bookkeepingGmv || 0} 元</span>
    }
  },
  {
    prop: 'addressGmv',
    minWidth: 150,
    label: '地址收款额',
    render: scope => {
      return <span>{scope.row.addressGmv || 0} 元</span>
    }
  },
  {
    prop: 'otherGmv',
    minWidth: 150,
    label: '其他收款额',
    render: scope => {
      return <span>{scope.row.otherGmv || 0} 元</span>
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '合计收款',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  },
  {
    prop: 'userName',
    label: '人员姓名',
    isShow: false,
    search: {
      el: 'input'
    }
  }
]
const columns_11 = [
  {
    prop: 'index',
    label: '序号',
    width: 100,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '人员姓名',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'deptName',
    minWidth: 150,
    label: '所属部门'
  },
  // {
  //   prop: 'deptId',
  //   minWidth: 150,
  //   label: '所属部门',
  //   isShow: false,
  //   enum: getDeptTree,
  //   search: {
  //     el: 'tree-select',
  //     props: { 'default-expand-all': true, props: { value: 'id', label: 'label', children: 'children' } }
  //   }
  // },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'bookkeepingGmv',
    minWidth: 150,
    label: '记账收款额',
    render: scope => {
      return <span>{scope.row.bookkeepingGmv || 0} 元</span>
    }
  },
  {
    prop: 'addressGmv',
    minWidth: 150,
    label: '地址收款额',
    render: scope => {
      return <span>{scope.row.addressGmv || 0} 元</span>
    }
  },
  {
    prop: 'otherGmv',
    minWidth: 150,
    label: '其他收款额',
    render: scope => {
      return <span>{scope.row.otherGmv || 0} 元</span>
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '合计收款',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  }
]

const columns = ref(columns_0)
</script>
<style lang="scss" scoped></style>
