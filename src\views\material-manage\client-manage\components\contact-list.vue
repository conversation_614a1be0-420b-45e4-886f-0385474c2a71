<!--
 * @Description: 联系人
 * @Author: thb
 * @Date: 2023-08-17 10:20:19
 * @LastEditTime: 2023-08-17 16:23:58
 * @LastEditors: thb
-->
<template>
  <el-table :data="tableData">
    <el-table-column type="expand">
      <template #default="{ row }">
        <el-row :gutter="20">
          <el-col :span="4">性别：{{ row.sex === '0' ? '未知' : row.sex === '1' ? '男' : '女' }} </el-col>
          <el-col :span="8">邮箱：{{ row.email || '--' }}</el-col>
          <el-col :span="6">QQ: {{ row.qq || '--' }} </el-col>
          <el-col :span="6">生日：{{ row.birthday || '--' }} </el-col>
        </el-row>
      </template>
    </el-table-column>
    <el-table-column label="姓名" prop="contactName" align="center" />
    <el-table-column label="手机号" prop="contactPhone" align="center" />
    <el-table-column label="常用联系人" prop="isOften" align="center">
      <template #default="{ row }">
        {{ row.isOften === 0 ? '否' : '是' }}
      </template>
    </el-table-column>
    <el-table-column label="职位" prop="post" align="center" />
    <el-table-column label="微信号" prop="wx" align="center" />
    <el-table-column label="关键决策人" prop="isLeader" align="center">
      <template #default="{ row }">
        {{ row.isLeader === 0 ? '否' : '是' }}
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup>
import { getClientContactList, saveBatchClientContactList } from '@/api/material-manage/client'

const props = defineProps({
  id: String,
  timestamp: String
})
const tableData = ref([])
watch(
  () => props.timestamp,
  () => {
    console.log('timeStamp', props.timestamp)
    getContactList()
  }
)
const getContactList = async () => {
  const { data } = await getClientContactList(props.id)
  tableData.value = data || []
}
onMounted(() => {
  getContactList()
})
</script>
<style lang="scss" scoped></style>
