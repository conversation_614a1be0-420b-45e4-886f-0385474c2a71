<!--
 * @Description: 股东信息表格组件
 * @Author: thb
 * @Date: 2023-11-08 10:08:51
 * @LastEditTime: 2023-11-08 13:53:54
 * @LastEditors: thb
-->
<template>
  <el-button type="primary" @click="handleAddRow">新增</el-button>
  <FormTable ref="formTableRef" style="width: 100%" :formData="formData" :option="option">
    <template #shareholderName="{ row }">
      <el-input v-model="row.shareholderName" length="20"></el-input>
    </template>
    <template #shareholderPhone="{ row }">
      <el-input v-model="row.shareholderPhone" length="20"></el-input>
    </template>
    <template #shareholderFileList="{ row, $index }">
      <FileUploadBiz
        v-model="row.shareholderFileList"
        :isShowTip="false"
        :limit="100"
        @on-load-success="() => validateFile($index)"
      />
    </template>
    <template #action="{ $index }">
      <el-button type="danger" @click="handleDeleteRow($index)">删除</el-button>
    </template>
  </FormTable>
</template>
<script setup>
import FormTable from '@/components/FormTable'
import FileUploadBiz from '@/components/FileUploadBiz'
const props = defineProps({
  formData: {
    type: Object,
    default: () => {
      return {
        tableData: [],
        rules: []
      }
    }
  }
})

const option = [
  {
    prop: 'shareholderName',
    label: '股东姓名'
  },
  {
    prop: 'shareholderPhone',
    label: '股东手机号'
  },
  {
    prop: 'shareholderFileList',
    label: '股东身份证'
  },
  {
    prop: 'action',
    label: '操作'
  }
]
const formTableRef = ref()
const validateFile = index => {
  formTableRef.value.formRef.validateField(`tableData.${index}.shareholderFileList`)
}
const handleAddRow = () => {
  props.formData?.tableData.push({
    shareholderName: '', // 股东姓名
    shareholderPhone: '', // 股东手机号
    shareholderFileList: [] // 股东附件
  })
  console.log('props.formData?.tableData', props.formData)
}

const handleDeleteRow = index => {
  props.formData?.tableData.splice(index, 1)
}

const getFormRef = () => {
  return formTableRef.value
}

defineExpose({
  getFormRef,
  formTableRef
})
</script>
<style lang="scss" scoped></style>
