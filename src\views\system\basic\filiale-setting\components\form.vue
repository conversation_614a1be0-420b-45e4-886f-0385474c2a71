<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-position="top"
    :disabled="disabled"
    :hide-required-asterisk="disabled"
  >
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="分公司名称" prop="name">
          <el-input v-model="formData.name" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="显示排序" prop="sort">
          <el-input-number style="width: 100%" v-model="formData.sort" class="mx-4" :min="1" controls-position="right" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="分公司地址" prop="address">
          <el-input v-model="formData.address" maxlength="100" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="分公司联系人" prop="contacts">
          <el-input v-model="formData.contacts" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="formData.phone" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="账号信息" prop="account">
          <el-input v-model="formData.account" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { getCompanyTreeList } from '@/api/basicData/basicData'
import { FormValidators } from '@/utils/validate'
import { reactive } from 'vue'
const formData = reactive({
  // parentId: undefined,
  name: '',
  sort: '',
  // enable: '1', // 默认为正常
  remark: '',
  address: '',
  phone: '',
  account: '', // 账号信息
  contacts: '',
  id: undefined
})

const disabled = ref(false)

const rules = {
  name: [{ required: true, trigger: 'blur', message: '请输入' }],
  sort: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入'
    }
  ],
  phone: [{ message: '请输入正确的联系电话', trigger: 'blur', validator: FormValidators.allPhone }]
}
const menuOptions = ref([])

/** 查询菜单下拉树结构 */
const getTreeselect = () => {
  getCompanyTreeList().then(res => {
    const temp = [
      {
        id: '0',
        name: '全部',
        child: res.data
      }
    ]
    menuOptions.value = temp
  })
}

getTreeselect()
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  disabled,
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
