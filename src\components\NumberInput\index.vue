<!--
 * @Description: 数字输入框
 * @Author: thb
 * @Date: 2023-05-29 15:25:28
 * @LastEditTime: 2023-08-10 15:36:27
 * @LastEditors: thb
-->
<template>
  <el-input v-model="inputValue" @input="ClearText" v-bind="$attr" placeholder="请输入">
    <template #suffix v-if="slots.suffix">
      <slot name="suffix" />
    </template>
  </el-input>
</template>
<script setup>
const slots = useSlots()
const props = defineProps({
  modelValue: {
    type: Number || String
  },
  regFormat: {
    type: RegExp
  }
})

const emits = defineEmits(['update:modelValue'])
const inputValue = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})
const ClearText = value => {
  let newVal = value.replace(props.regFormat || /[^\d]/g, '')

  // let newVal = value.replace(/^(0+)|[^\d]+/g, '')
  // 去除第一位第二位是零的情况
  if (Number(newVal) > 0) {
    if (newVal[0] == '0') {
      newVal = newVal.substr(1)
    }
  } else if (Number(newVal) == 0) {
    newVal = newVal.slice(0, 1)
  }
  inputValue.value = newVal
}
</script>
<style lang="scss" scoped></style>
