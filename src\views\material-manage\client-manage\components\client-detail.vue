<template>
  <el-dialog
    class="dialog-footer-1"
    align-center
    title="客户详情"
    width="1200"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <div class="container-t">
      <span class="avatar-icon"></span>
      <span class="name">{{ detail.companyName }}</span>
      <!-- 进度状态 -->
      <div class="flex-1">
        <el-tag>{{ detail.followStatus === '0' ? '未跟进' : detail.followStatus === '1' ? '跟进中' : '已转企业' }}</el-tag>
      </div>
      <!-- 私有客户的操作 -->
      <template v-if="tabType === '1'">
        <template v-if="!isSea">
          <el-button @click="handleEdit">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon
            ></template>
            编辑资料</el-button
          >
          <el-button @click="handleAddBusiness">
            <template #icon>
              <el-icon color="#3DB954"> <CirclePlus /> </el-icon
            ></template>

            新增商机</el-button
          >
          <el-button @click="handleEditContact">
            <template #icon>
              <el-icon color="#F5AC21"> <Switch /> </el-icon
            ></template>

            编辑联系人</el-button
          >
          <el-button @click="handleTransfer">
            <template #icon>
              <el-icon color="#F5AC21"> <Connection /> </el-icon
            ></template>
            转让客户</el-button
          >
          <el-button @click="handleRecycle">
            <template #icon>
              <el-icon color="#F5AC21"> <Refresh /> </el-icon
            ></template>

            回收公海</el-button
          >
          <!-- 只有当该客户有工商新注册的赢单才可以编辑附加信息 -->
          <!-- 编辑附加信息 v-if="detail.isExtra"-->
          <el-button @click="handleEditExtra" v-if="detail.isExtra">
            <template #icon>
              <el-icon color="#F5AC21"> <EditPen /> </el-icon
            ></template>

            编辑附加信息</el-button
          >
        </template>
        <template v-else>
          <el-button @click="handleEdit" v-if="userStore.user.userId === detail.manageId">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon
            ></template>
            编辑资料</el-button
          >

          <el-button @click="handleReceive" v-if="isCapableGet(isGet, detail.deptIds)">
            <template #icon>
              <el-icon color="#2383E7FF"> <Switch /> </el-icon
            ></template>
            领取客户</el-button
          >
          <el-button @click="handleDistribute" v-if="isCapableDispatch(isDivide)">
            <template #icon>
              <el-icon color="#3DB954"> <Connection /> </el-icon
            ></template>
            分配客户</el-button
          >

          <el-button @click="handleTransferToZone" v-if="userStore.user.userId === detail.manageId">
            <template #icon>
              <el-icon color="#F5AC21"> <Connection /> </el-icon
            ></template>
            转移公海</el-button
          >

          <el-button @click="handleDelete" v-if="userStore.user.userId === detail.manageId">
            <template #icon>
              <el-icon color="red"> <Delete /> </el-icon
            ></template>
            删除客户</el-button
          >
        </template>
      </template>
    </div>
    <div class="container-c">
      <span class="flex-1"
        >标签:
        <template v-if="!isSea">
          <span class="text-bold" v-if="detail.tagsName">{{ detail.tagsName || '--' }}</span>
          <span v-else class="blue-text" @click="showTag">
            <el-icon style="vertical-align: middle" color="#2383E7FF"> <EditPen /> </el-icon>打标签</span
          >
        </template>
        <template v-else>
          <span class="text-bold">{{ detail.tagsName || '--' }}</span>
        </template>
      </span>

      <span class="flex-1" v-if="detail.timeStr"
        >掉保时长:
        <span class="text-bold">{{ detail.timeStr }}</span>
      </span>
    </div>
    <div class="container-bottom">
      <div class="left">
        <el-tabs v-model="activeName">
          <el-tab-pane :label="tab.label" :name="tab.name" v-for="(tab, index) in tabs" :key="index">
            <component
              :is="tabMap[tab.label]"
              :detail="detail"
              :timestamp="timestamp"
              :id="detail.id"
              v-if="activeName === tab.label"
              @on-success="getDetail"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="right">
        <record :detail="detail" :tabType="tabType" @on-success="getDetail" :isSea="isSea" v-if="detail.id" />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
  <clientEdit v-if="contactShow" :id="detail.id" @on-close="contactShow = false" @on-success="handleSuccess" />
  <businessAdd v-if="businessShow" :detail="detail" @on-close="businessShow = false" @on-success="handleSuccess" />

  <contract v-if="contractShow" :normal="normal" @on-close="contractShow = false" @on-next="handelNext" />
  <normalCreate v-if="normalShow" :productIds="productIds" :associated="associated" @on-close="normalShow = false" />
  <templateCreate v-if="templateShow" :productIds="productIds" :associated="associated" @on-close="templateShow = false" />
</template>
<script setup>
import { getClientDetail, editCertificationFiles, getCertificationFiles } from '@/api/material-manage/client.js'
import record from '../../clue-manage/components/record.vue'
import actionRecord from '../../clue-manage/components/action-record.vue'
import businessInfo from '../components/business-info.vue'
import clientForm from '../components/client-form'
import contactList from '../components/contact-list'
import clientEdit from '../components/contact-edit'
import { useDialog } from '@/hooks/useDialog'
import clueTransfer from '../../clue-manage/components/clue-transfer.vue'
import { editTags, transferClue, recycleClue, transferClueToZone } from '@/api/material-manage/clue'
import tagForm from '../../clue-manage/components/tag-form'
import zonRecycle from '../../clue-manage/components/zone-recycle.vue'
import extraForm from './extra-form'
import businessAdd from './business-add.vue'
import contract from '@/views/customer/customer-file/components/contract.vue'
import normalCreate from '@/views/contract-manage/contract-list/components/normal-create.vue'
import templateCreate from '@/views/contract-manage/contract-list/components/template-create.vue'
import useUserStore from '@/store/modules/user'
import zoneTransfer from '../../clue-manage/components/zone-transfer'
import { getCustomerById } from '@/api/customer/file'
import { changeFileList } from '@/utils/common'
import { useDept } from '@/hooks/useDept'

const { isCapableGet, isCapableDispatch } = useDept()

const userStore = useUserStore()
// 跳转至创建新合同
// 手动新建还是模板新建的弹窗显示标志
const normalShow = ref(false)
const contractShow = ref(false)
const templateShow = ref(false)
// 传入的productIds(创建的为意向合同)
const productIds = ref([])
// 下一步
const handelNext = createType => {
  // '0'代表 普通新建
  // '1'代表 模板新建
  if (createType === '0') {
    normalShow.value = true
  }
  if (createType === '1') {
    templateShow.value = true
  }
}
// 编辑联系人触发
const timestamp = ref(new Date().getTime())
const normal = ref(false) // 是否能够选择模板创建合同
const associated = ref({})
const handleSuccess = async (ids, customerId) => {
  timestamp.value = new Date().getTime()
  // 跳出是否新建合同
  console.log('customerId', customerId)
  // 通过 customerId 获取档案详情 ，成功获取后 显示 弹窗
  if (customerId) {
    const { data } = await getCustomerById(customerId)
    if (data) {
      associated.value = data
    }
  }

  if (ids) {
    contractShow.value = true
    productIds.value = ids
  }
  // 如果存在多个业务类型 则需要手动录入信息
  if (ids && ids.length > 1) {
    normal.value = true
  } else {
    normal.value = false
  }

  // 获取详情接口更新数据
  getDetail()
}

const { showDialog } = useDialog()
const isEdit = ref(false)
const tabs = [
  {
    label: '客户资料',
    name: '客户资料'
  },
  {
    label: '操作记录',
    name: '操作记录'
  },
  {
    label: '商机详情',
    name: '商机详情'
  },
  {
    label: '联系人',
    name: '联系人'
  }
]
const tabMap = {
  客户资料: clientForm,
  操作记录: actionRecord,
  商机详情: businessInfo,
  联系人: contactList
}
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success', 'on-edit', 'on-delete', 'on-receive', 'on-distribute'])
const handleClose = () => {
  emits('on-close')
}
const showTag = () => {
  showDialog({
    title: '打标签',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: tagForm, // 表单组件
    submitApi: editTags,
    handleRevertParams: data => {
      if (data.tags.length) {
        data.tags = data.tags.map(item => {
          return {
            tagId: item
          }
        })
      }
      data.id = detail.value.id
    },
    submitCallback: () => {
      // 重新触发获取详情接口
      getDetail()
    } // 提交成功之后的回调函数
  })
}
// const { proxy } = getCurrentInstance()
// const { customer_level } = proxy.useDict('customer_level')
// const { customer_property } = proxy.useDict('customer_property')
const props = defineProps({
  id: Number,
  isSea: {
    type: Boolean,
    default: false
  }, // 是否是公海
  isDivide: {
    type: Boolean,
    default: false
  }, // 是否被分配
  isGet: {
    type: Boolean,
    default: false
  }, // 是否可领取
  tabType: {
    type: String,
    default: '1'
  }
})

// 获取客户详情
const detail = ref({})
const getDetail = async () => {
  const { data } = await getClientDetail(props.id)
  detail.value = data || {}
  detail.value.tagsName = data?.tags
    ?.map(item => {
      return item.name
    })
    .join(',')
}

const activeName = ref('客户资料')

// 编辑资料
const handleEdit = () => {
  handleClose()
  emits('on-edit', detail.value.id)
}

// 编辑联系人
const contactShow = ref(false)
const handleEditContact = () => {
  contactShow.value = true
}

// 转让客户回调函数
const submitCallbackTransfer = () => {
  handleClose()
  emits('on-success')
}
// 转让客户
const handleTransfer = () => {
  showDialog({
    title: '转让客户',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: clueTransfer, // 表单组件
    submitApi: transferClue, // 提交api
    handleConvertParams: data => {
      // 表示客户
      data.type = '1'
    },
    handleRevertParams: data => {
      data.id = detail.value.id
    },
    submitCallback: submitCallbackTransfer // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 回收公海
const handleRecycle = () => {
  showDialog({
    title: '回收公海',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: zonRecycle, // 表单组件
    handleConvertParams: data => {
      // 表示客户
      data.type = '1'
    },
    submitApi: recycleClue, // 提交api
    handleRevertParams: data => {
      data.id = detail.value.id
    },
    submitCallback: submitCallbackTransfer // 提交成功之后的回调函数
  })
}

// handleEditExtra 编辑附加资料(办证资料)

// 股东信息数据做处理，去除存在股东名称、手机号以及文件都不存在的行
const translateShareholderInfoList = list => {
  return list.filter(
    item => item.shareholderName || item.shareholderPhone || (item.shareholderFileList && item.shareholderFileList.length)
  )
}
const handleEditExtra = () => {
  showDialog({
    title: '编辑办证资料',
    customClass: 'certification-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: extraForm, // 表单组件
    getApi: getCertificationFiles,
    requestParams: detail.value.id,
    handleConvertParams: data => {
      data.shareholderInfoList = data.shareholderInfoList || []
    },
    handleRevertParams: data => {
      if (data.legalIdentityFileList && data.legalIdentityFileList.length) {
        data.legalIdentityFileList = changeFileList(data.legalIdentityFileList)
      }

      if (data.supervisorIdentityFileList && data.supervisorIdentityFileList.length) {
        data.supervisorIdentityFileList = changeFileList(data.supervisorIdentityFileList)
      }
      // 传列表
      if (Array.isArray(data.otherDocumentFileList) && data.otherDocumentFileList.length) {
        data.otherDocumentFileList = changeFileList(data.otherDocumentFileList)
      }

      // 处理 股东信息表格 去重空行数据
      data.shareholderInfoList = translateShareholderInfoList(data.shareholderInfoList)
      data.id = detail.value.id
    },
    submitApi: editCertificationFiles // 提交api editCertificationFiles , getCertificationFiles
  })
}
// const handleSuccessContactEdit = () => {
//   if (activeName === '联系人') {
//   }
// }

// 新增商机
const businessShow = ref(false)
const handleAddBusiness = () => {
  businessShow.value = true
}
// 领取线索
const handleReceive = () => {
  emits('on-receive', {
    id: detail.value.id
  })
}

// 分配客户
const handleDistribute = () => {
  emits('on-distribute', {
    id: detail.value.id
  })
}
// 删除客户
const handleDelete = () => {
  emits('on-delete', detail.value.id)
}
// 转移至公海
const handleTransferToZone = () => {
  showDialog({
    title: '转移公海',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: zoneTransfer, // 表单组件
    submitApi: transferClueToZone, // 提交api
    handleRevertParams: data => {
      data.id = detail.value.id
    },
    handleConvertParams: data => {
      data.currentSeaId = detail.value.seaId
      data.type = '1' // 改为客户
    },
    submitCallback: () => {
      handleClose()
      emits('on-success')
    } // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}
onMounted(() => {
  getDetail()
})
</script>
<style lang="scss" scoped>
.container-t {
  display: flex;
  align-items: center;
  .name {
    margin-right: 24px;
    margin-left: 12px;
    font-size: 18px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    color: #333333;
  }
  .el-button {
    color: #333333;
  }
  .el-tag {
    font-size: 15px;
    font-family: AlibabaPuHuiTi_2_65_Regular;
    color: #2383e7;
  }
}
.flex-1 {
  flex: 1;
}
.container-c {
  display: flex;
  align-items: center;
  margin-top: 25px;
  padding-bottom: 18px;
  border-bottom: 1px solid #e8e8e8;
}

.text-bold {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
}

.container-bottom {
  display: flex;
  min-height: 400px;
  .left {
    flex: 1;
    overflow: hidden;
    border-right: 1px solid #e8e8e8;
    padding-right: 16px;
  }
  .right {
    width: 310px;
    padding-left: 16px;
  }
}
.el-select {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}
</style>
