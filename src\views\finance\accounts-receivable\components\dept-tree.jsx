import { defineComponent, ref, watch } from 'vue'
import { ElTreeSelect } from 'element-plus'
import { deptTreeSelect } from '@/api/system/user'

export default defineComponent({
  props: {
    modelValue: {
      type: Number
    }
  },
  setup(props, { emit }) {
    const deptId = ref(undefined)
    watch(
      () => props.modelValue,
      val => {
        deptId.value = val
      },
      { deep: true, immediate: true }
    )

    const handleSelectChange = (node, row) => {
      emit('update:modelValue', node?.id || '')
    }

    const deptOptions = ref(undefined)

    function getDeptTree() {
      deptTreeSelect().then(response => {
        deptOptions.value = response.data
      })
    }
    getDeptTree()

    return {
      deptId,
      handleSelectChange,
      deptOptions
    }
  },
  render() {
    return (
      <ElTreeSelect
        vModel={this.deptId}
        clearable
        filterable
        data={this.deptOptions}
        onCurrentChange={this.handleSelectChange}
        onClear={this.handleSelectChange}
        renderAfterExpand={false}
        valueKey="id"
        defaultExpandAll
      />
    )
  }
})
