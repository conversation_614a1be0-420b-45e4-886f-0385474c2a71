<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-position="top"
    :hide-required-asterisk="isDisabled"
    :disabled="allDisabled"
  >
    <el-row :gutter="24">
      <el-col :span="12" v-if="isShow">
        <el-form-item label="合同附件" prop="file">
          <FileUpload class="file-width" :isShowTip="false" v-model="formData.file" @on-load-success="validateLoadSuccess" />
        </el-form-item>
      </el-col>
      <!-- 如果选择的产品名称中含有"变更"二字，需要显示 -->
      <el-col :span="isShow ? 12 : 24" v-if="formData.productName?.includes('变更')">
        <el-form-item label="变更科目" prop="changeSubjectList">
          <el-select
            v-model="formData.changeSubjectList"
            :disabled="isDisabled"
            multiple
            filterable
            allow-create
            default-first-option
            @change="handleChange"
            :placeholder="isDisabled ? ' ' : '请选择'"
            clearable
          >
            <el-option v-for="(option, index) in change_project" :key="index" :label="option.label" :value="option.value" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="关联企业" prop="customerName">
          <!-- 点击弹窗出现客户列表  -->
          <div @click="handleShow" style="width: 100%">
            <el-input
              v-model="formData.customerName"
              readonly
              maxlength="20"
              placeholder="请输入"
              :disabled="isDisabled || isAssociated || changeDisabled"
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户编码" prop="customerNo">
          <el-input v-model="formData.customerNo" maxlength="20" placeholder="请输入" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="地址" prop="address">
          <el-input v-model="formData.address" maxlength="1000" type="textarea" disabled placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="服务产品" prop="productName">
          <template v-if="changeDisabled">
            <el-tree-select
              v-model="formData.productId"
              ref="treeSelectRef"
              filterable
              :data="productTreeData"
              :props="defaultPopsFunction(row)"
              @current-change="treeSelectChange"
              node-key="id"
              :render-after-expand="false"
            />
          </template>

          <el-input v-else v-model="formData.productName" maxlength="20" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <!-- 服务费 -->
        <el-form-item label="服务费" prop="serviceCost">
          <!-- 新增 标准价 和 价格变动的选项 -->
          <el-radio-group v-model="formData.priceChangeFlag" @change="radioChange" :disabled="translateDisabled || isDisabled">
            <el-radio :label="false">标准价</el-radio>
            <el-radio :label="true" :disabled="formData.rowData?.isInContract === '1'">价格变动</el-radio>
          </el-radio-group>
          <!-- 是否在合同中定义 ，如果是在合同中定义则需要用户自己输入服务费 -->
          <NumberInput
            v-if="!formData.priceChangeFlag"
            v-model="formData.serviceCost"
            maxlength="20"
            :disabled="isDisabled || formData.rowData?.isInContract !== '1'"
            placeholder="请输入"
          >
            <!-- 如果是一次性收费 -->
            <template #suffix>
              <!-- 一次性收费 -->
              <div v-if="formData.rowData.feeType === '0'">元</div>
              <!-- 每年收费 -->
              <div v-if="formData.rowData.feeType === '1'">/年</div>
              <!-- 每月收费 -->
              <div v-if="formData.rowData.feeType === '2'">/月</div>
            </template>
          </NumberInput>
          <NumberInput v-else v-model="formData.serviceCost" maxlength="20" :disabled="isDisabled" placeholder="请输入">
            <!-- 如果是一次性收费 -->
            <template #suffix>
              <!-- 一次性收费 -->
              <div v-if="formData.rowData.feeType === '0'">元</div>
              <!-- 每年收费 -->
              <div v-if="formData.rowData.feeType === '1'">/年</div>
              <!-- 每月收费 -->
              <div v-if="formData.rowData.feeType === '2'">/月</div>
            </template>
          </NumberInput>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <!-- 备注 -->
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            maxlength="1000"
            :disabled="isDisabled"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="rowChange ? 24 : 12">
        <el-form-item label="所属公司" prop="branchOffice">
          <el-select
            style="width: 100%"
            v-model="formData.branchOffice"
            :disabled="isDisabled"
            :placeholder="isDisabled ? ' ' : '请选择'"
            clearable
            @change="handleSelectBranchOffice"
          >
            <el-option v-for="(option, index) in branch_office" :key="index" :label="option.name" :value="option.name" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="工本费" prop="productionCost">
          <el-select
            v-model="formData.productionCost"
            :disabled="isDisabled"
            :placeholder="isDisabled ? ' ' : '请选择'"
            clearable
          >
            <el-option v-for="(option, index) in productionCosts" :key="index" :label="option.label" :value="option.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <!-- totalCost-->
        <el-form-item label="合同总金额" prop="totalCostCn">
          <el-input v-model="formData.totalCost" disabled> </el-input>
          <div>大写：{{ formData.totalCostCn }}</div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="其他补充说明" prop="otherRemark">
          <el-input
            v-model="formData.otherRemark"
            :disabled="isDisabled"
            maxlength="1000"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24" v-if="isChange">
      <el-col :span="24">
        <el-form-item label="变更原因" prop="changeReason">
          <el-input v-model="formData.changeReason" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"> </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <tableModal
    v-if="listSelectShow"
    title="关联客户"
    :columns="columns"
    rowKey="customerId"
    :init-param="{ discard: 0 }"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
</template>
<script setup lang="tsx">
import FileUpload from '@/components/FileUpload'
import { changeNumMoneyToChinese } from '@/utils/index.js'
import tableModal from '@/components/tableModal'
import { ColumnProps } from '@/components/ProTable/interface'
import { getCustomers } from '@/api/customer/file'
import NumberInput from '@/components/NumberInput'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import { ref, watch, computed } from 'vue'
import { getCompanyTreeList } from '@/api/basicData/basicData'
import { useDic } from '@/hooks/useDic'
import { useSetDic } from '@/hooks/useSetDic'
const { setDic } = useSetDic()
const handleChange = value => {
  setDic('change_project', value[value.length - 1], value[value.length - 1], change_project)
}
const { getDic } = useDic()
const totalCost = computed(() => {
  console.log('computed tOTAL ', Number(formData.value?.serviceCost || 0), formData.value?.rowData.feeType)
  // 如果是在合同中定义或者是一次性收费，合同总金额的计算方式就是服务费 + 其他费用
  if (formData.value?.rowData.feeType === '0') {
    // feeType 为零 为一次性收费
    return formData.value?.serviceCost === '' ? '' : Number(formData.value?.serviceCost || 0)
  } else {
    return ''
  }
})

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  },
  isShow: {
    type: Boolean,
    default: true
  },
  isDisabled: {
    type: Boolean,
    default: false
  },
  isChange: {
    type: Boolean,
    default: false
  },
  isAssociated: {
    type: Boolean,
    default: false
  },
  changeDisabled: {
    type: Boolean,
    default: false
  },
  allDisabled: {
    type: Boolean,
    default: false
  },
  productTreeData: {
    type: Array,
    default: () => {
      return []
    }
  },
  rowChange: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:modelValue'])
const formData = computed({
  get: () => {
    // 如果changeSubjectList 为空 默认将changeSubjectList 置为空数组防止触发表单校验
    return Object.assign(props.modelValue, {
      changeSubjectList: props.modelValue.changeSubjectList || []
    })
  },
  set: (newVal: any) => {
    emits('update:modelValue', newVal)
  }
})

watch(
  totalCost,
  () => {
    console.log('totalCost', totalCost.value)
    // const num = formData.value.rowData?.isInContract === '1' ? totalCost.value || '' : totalCost.value
    const moneyCN = changeNumMoneyToChinese(totalCost.value)
    formData.value.totalCostCn = moneyCN
    // formData.value.totalCost = totalCost.value || ''
    formData.value.totalCost = totalCost.value
  },
  {
    immediate: true
  }
)
const nodeSearchById = (list, id) => {
  for (const node of list) {
    if (node.id === id) {
      return node
    }
    if (node.children && node.children?.length) {
      const nodeSearch = nodeSearchById(node.children, id)
      if (nodeSearch) {
        return nodeSearch
      }
    }
  }
}

// 监听 变更 存储服务产品下的服务费
watch(
  () => props.productTreeData,
  () => {
    // 查出 当前服务产品下的服务费 存储至serviceCostCopy
    const node = nodeSearchById(props.productTreeData, props.modelValue.productId)
    if (props.modelValue.priceChangeFlag) {
      formData.value.serviceCostCopy = node?.quotation
    }
  }
)
const radioChange = value => {
  if (value) {
    // serviceCost 缓存
    formData.value.serviceCostCopy = formData.value.serviceCost
    formData.value.serviceCost = ''
  } else {
    if (formData.value.serviceCostCopy === undefined) {
      formData.value.serviceCostCopy = ''
    }
    formData.value.serviceCost = formData.value.serviceCostCopy + '' || formData.value.serviceCost
    formData.value.serviceCostCopy = ''
  }
}
watch(
  formData,
  () => {
    if (formData.value.priceChangeFlag && formData.value.serviceCostCopy === Number(formData.value.serviceCost)) {
      formData.value.priceChangeFlag = false
      formData.value.serviceCostCopy = ''
    }
  },
  {
    deep: true
  }
)

const rules = {
  customerName: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  productionCost: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  changeReason: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  file: [
    {
      required: true,
      message: '请上传',
      trigger: ['change']
    }
  ],
  serviceCost: [
    {
      required: true,
      message: '请输入服务费',
      trigger: ['blur']
    }
  ],
  changeSubjectList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
const productionCosts = [
  {
    label: '根据单据由甲方支付',
    value: '根据单据由甲方支付'
  },
  {
    label: '由乙方全包费用',
    value: '由乙方全包费用'
  }
]
const { proxy } = getCurrentInstance()

const { change_project } = proxy.useDict('change_project')
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

// 客户列表弹窗显示
const listSelectShow = ref(false)
// 选择单个客户后
const handleSelect = (data: any) => {
  console.log('data', data)
  // 关联客户的数据有 客户名称  客户id(ciId/customerId) 客户编号 联系人 联系电话 所属公司
  const { customerName, customerId, customerNo, address, branchOffice } = data
  // 填充记账合同表单
  formData.value = Object.assign(formData.value, {
    customerName,
    customerId,
    address,
    customerNo,
    branchOffice
  })
}
// 关联客户弹窗显示
const handleShow = () => {
  // 如果是详情状态 不需要显示弹窗
  if (props.isDisabled) return
  if (props.isAssociated) return
  if (props.changeDisabled) return
  listSelectShow.value = true
}

// 处理所属公司下拉框
const branch_office = ref<any>([])
function onGetBasicData() {
  getCompanyTreeList({ enable: 1 }).then(res => {
    branch_office.value = res.data.records
  })
}
onGetBasicData()
const handleSelectBranchOffice = (value: string) => {
  const item = branch_office.value.find((item: any) => item.name === value)
  // console.log('handleSelectBranchOffice', item)
  // formData.value.companyPerson = item.contacts
  // formData.value.companyAddress = item.address
}

// 表单校验
const formRef = ref()
const validateForm = async () => {
  console.log('validateForm')
  console.log('formRef', formRef.value)
  return await formRef.value.validate((valid: any) => {
    if (valid) {
    } else {
    }
  })
}

// 校验上传文件
const validateLoadSuccess = () => {
  console.log('validateLoadSuccess')
  formRef.value.validateField('file')
}

const defaultPopsFunction = row => {
  return {
    value: 'id',
    label: 'name',
    disabled: data => {
      return (
        (data?.type === '业务类型' && !data?.children.length) ||
        (data.type === '产品类型' && (data.feeType === '1' || data.feeType === '2'))
      )
    }
  }
}
// 树节点选中事件触发
const treeSelectChange = node => {
  console.log('treeSelectChange', node)
  if (node.type === '产品类型') {
    formData.value.rowData.isInContract = node.isInContract
    formData.value.rowData.feeType = node.feeType
    formData.value.serviceCost = node.quotation
    // formData.value.serviceCostCopy = node.quotation
  }
}
defineExpose({
  validateForm
})
</script>
<style lang="scss" scoped>
.file-width {
  width: 100%;
}
</style>
