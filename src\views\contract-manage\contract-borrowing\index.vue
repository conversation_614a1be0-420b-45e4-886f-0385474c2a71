<!--
 * @Description: 借阅管理
 * @Author: thb
 * @Date: 2023-07-11 09:25:02
 * @LastEditTime: 2023-11-09 15:32:44
 * @LastEditors: thb
-->
<template>
  <ProTable ref="proTable" title="借阅管理" :init-param="initParam" :columns="columns" :request-api="getTabList">
    <!-- 表格tabs -->
    <template #tabs>
      <el-radio-group :model-value="initParam.tabType" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >

    <template #customerName="{ row }">
      <span class="blue-text" @click="handleShowCustomerDetail(row.ciId)">{{ row.customerName }}</span>
    </template>
    <!-- 操作 -->
    <template #operation="{ row }">
      <!-- 详情 -->
      <el-button link type="primary" @click="handleCheckDetail(row.id)">详情</el-button>
      <!-- 审批 -->
      <el-button link type="primary" v-if="initParam.tabType === '2' && row.status === '0'" f @click="handleReview(row.id)"
        >审批</el-button
      >
    </template>
  </ProTable>
  <!-- 详情 -->
  <CheckReport type="detail" v-if="detailShow" @on-close="detailShow = false" />
  <!-- 审批 -->
  <borrowReview :id="rowId" v-if="reviewShow" @on-close="reviewShow = false" @on-success="handleSuccess" />

  <customerDetail v-if="customerDetailShow" :id="customerId" :hideActionBtn="true" @on-close="customerDetailShow = false" />
</template>
<script setup lang="tsx">
import { ref, reactive, provide, onMounted } from 'vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { getReportContractBorrowList, getReportContractBorrowListByAudit } from '@/api/contract/contract'
import CheckReport from '@/views/contract-manage/contract-list/components/check-report.vue'
import { getBorrowContractDetail } from '@/api/contract/contract.js'
import borrowReview from './components/borrow-review.vue'
import { useRoute } from 'vue-router'
import useCommonStore from '@/store/modules/common'
const useCommon = useCommonStore()
const route = useRoute()
import { useCustomer } from '@/hooks/useCustomer'
const { customerDetailShow, rowId: customerId, handleShowCustomerDetail, customerDetail } = useCustomer()
const tabs = [
  {
    dictLabel: '我申请的',
    dicValue: '1'
  },
  {
    dictLabel: '由我审批的',
    dicValue: '2'
  }
]

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ tabType: '1' })
const columns: ColumnProps<any>[] = [
  {
    prop: 'contractNo',
    width: '150',
    label: '合同编号',
    search: { el: 'input' }
  },
  {
    prop: 'borrowReason',
    width: '200',
    label: '借阅事由'
  },
  {
    prop: 'customerName',
    label: '客户名称',
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    search: { el: 'input' }
  },
  {
    prop: 'nickName',
    width: '100',
    label: '借阅人'
  },
  {
    prop: 'expirationTime',
    width: '200',
    label: '借阅到期'
  },
  {
    prop: 'createTime',
    width: '200',
    label: '提交时间'
  },

  {
    prop: 'status',
    label: '审批状态',

    width: '100',
    enum: [
      {
        label: '待审批',
        value: '0'
      },
      {
        label: '通过',
        value: '1'
      },
      {
        label: '不通过',
        value: '2'
      }
    ],
    search: { el: 'select' }
  },
  {
    prop: 'operation',
    width: 150,
    fixed: 'right',
    label: '操作'
  }
]

// getTabList 获取tab下的列表
const getTabList = async (data: any) => {
  // 由我提交的
  if (data.tabType === '1') {
    const result1 = await getReportContractBorrowList(data)
    return result1
  } else {
    // 由我审批的
    const result2 = await getReportContractBorrowListByAudit(data)
    return result2
  }
}

// handleSuccess
const proTable = ref()
const handleSuccess = () => {
  proTable.value?.getTableList()
}
const detailShow = ref(false)
const rowData = ref({})
provide('customerData', rowData)
const handleCheckDetail = async (id: number | string) => {
  // 获取详情
  const { data } = await getBorrowContractDetail(id)
  rowData.value = data
  detailShow.value = true
}

// 借阅审批弹窗标志
const reviewShow = ref(false)
const rowId = ref()
const handleReview = (id: number | string) => {
  rowId.value = id
  reviewShow.value = true
}

const handleRadioChange = value => {
  proTable.value.pageable.pageNum = 1
  initParam.tabType = value
}

watch(
  () => useCommon.id,
  async () => {
    if (useCommon.id && useCommon.bizType === 'borrow') {
      handleCheckDetail(useCommon.id as string)
      useCommon.clearId()
      useCommon.clearBizType()
    }
  },
  {
    immediate: true
  }
)
</script>
<style lang="scss" scoped></style>
