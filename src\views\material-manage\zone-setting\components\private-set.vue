<!--
 * @Description: 保有量设置
 * @Author: thb
 * @Date: 2023-08-23 15:38:25
 * @LastEditTime: 2023-11-13 11:03:11
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" label-position="top">
    <Collapse title="线索管理">
      <div class="m-b">
        <span class="font-bold"> 回收规则 </span>
        <p>
          直接在私海中添加的线索,超过
          <NumberInput class="input-w m-r" v-model="formData.clueDuration" :regFormat="/^(0+)|[^\d]+/g">
            <template #suffix> 天 </template>
          </NumberInput>
          未产生跟进、转化，线索回收至
          <el-select placeholder="请选择公海" class="m-r" clearable v-model="formData.clueSeaId">
            <el-option v-for="item in seaOptions" :key="item.id" :label="item.name" :value="item.id" /> </el-select
          >中
        </p>
      </div>
      <div class="m-b">
        <span class="font-bold"> 回收提醒 </span>
        <p>
          提前
          <NumberInput class="input-w m-r" v-model="formData.clueRecovery" :regFormat="/^(0+)|[^\d]+/g">
            <template #suffix> 天 </template>
          </NumberInput>
          提醒线索跟进人员{{ type === '0' ? '线索' : '客户' }}即将被回收
        </p>
      </div>
      <el-table :data="formData.clueList">
        <el-table-column label="人员类型">
          <template #default="scope">{{ scope.row.name }} </template>
        </el-table-column>
        <el-table-column :label="`私海线索上限数`">
          <template #default="scope">
            <NumberInput v-model="scope.row.limit" class="m-r" :regFormat="/^(0+)|[^\d]+/g" />
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template #default="scope">
            <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" />
          </template>
        </el-table-column>
      </el-table>
    </Collapse>

    <Collapse title="客户管理">
      <div class="m-b">
        <span class="font-bold"> 回收规则 </span>
        <p>
          直接在私海中添加的客户,超过
          <NumberInput class="input-w m-r" v-model="formData.cusDuration" :regFormat="/^(0+)|[^\d]+/g">
            <template #suffix> 天 </template>
          </NumberInput>
          未产生跟进、转化，客户回收至
          <el-select placeholder="请选择公海" class="m-r" clearable v-model="formData.cusSeaId">
            <el-option v-for="item in seaOptions1" :key="item.id" :label="item.name" :value="item.id" /> </el-select
          >中
        </p>
      </div>
      <div class="m-b">
        <span class="font-bold"> 回收提醒 </span>
        <p>
          提前
          <NumberInput class="input-w m-r" v-model="formData.cusRecovery" :regFormat="/^(0+)|[^\d]+/g">
            <template #suffix> 天 </template>
          </NumberInput>
          提醒线索跟进人员客户即将被回收
        </p>
      </div>
      <el-table :data="formData.clientList">
        <el-table-column label="人员类型">
          <template #default="scope">{{ scope.row.name }} </template>
        </el-table-column>
        <el-table-column :label="`私海客户上限数`">
          <template #default="scope">
            <NumberInput v-model="scope.row.limit" class="m-r" :regFormat="/^(0+)|[^\d]+/g" />
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template #default="scope">
            <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" />
          </template>
        </el-table-column>
      </el-table>
    </Collapse>
  </el-form>
</template>
<script setup>
import ruleSet from './rule-set'
import Collapse from '@/components/Collapse'
import NumberInput from '@/components/NumberInput'
import { getZoneSettingList } from '@/api/material-manage/zone-setting'
const seaOptions = ref([])
const seaOptions1 = ref([])
const getSeaOptions = async () => {
  const { data } = await getZoneSettingList({
    pageSize: 1000,
    pageNum: 1,
    status: '1',
    type: '0'
  })
  seaOptions.value = data.records || []
  const result = await getZoneSettingList({
    pageSize: 1000,
    pageNum: 1,
    status: '1',
    type: '1'
  })
  seaOptions1.value = result.data.records || []
}
getSeaOptions()
const formData = ref({
  clueAdminNum: '',
  clueAdminStatus: '',
  clueDuration: '',
  clueRecovery: '',
  clueSeaId: '',
  clueStaffNum: '',
  clueStaffStatus: '',
  cusAdminNum: '',
  cusAdminStatus: '',
  cusDuration: '',
  cusRecovery: '',
  cusSeaId: '',
  cusStaffNum: '',
  cusStaffStatus: '',
  id: ''
})

const formRef = ref()
const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped>
.el-select.el-select--default {
  width: 168px;
}
.input-w {
  width: 150px;
}
.m-r {
  margin-left: 8px;
  margin-right: 8px;
}
.m-b {
  margin-bottom: 12px;
}
.font-bold {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
}
</style>
