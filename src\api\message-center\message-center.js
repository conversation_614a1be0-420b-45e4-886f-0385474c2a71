/*
 * @Description: 消息中心
 * @Author: thb
 * @Date: 2023-08-02 08:29:33
 * @LastEditTime: 2023-08-08 11:18:51
 * @LastEditors: thb
 */
// 基础数据api
import request from '@/utils/request'
// 获取通知公告列表
export const getMessageNoticeList = params => {
  return request({
    url: '/messageNotification/list',
    method: 'get',
    params
  })
}
// 新增或者编辑通知公告
export const saveMessageNotice = data => {
  return request({
    url: '/messageNotification/save',
    method: 'post',
    data
  })
}

// 获取通告详情
export const getMessageNoticeDetail = id => {
  return request({
    url: '/messageNotification/getById',
    method: 'get',
    params: {
      id
    }
  })
}
// 删除通知
export const deleteMessageNotice = id => {
  return request({
    url: '/messageNotification/delete',
    method: 'delete',
    params: {
      id
    }
  })
}
// 消息中心--我的消息列表
export const getMyMessageList = params => {
  return request({
    url: '/message/detail/list',
    method: 'get',
    params
  })
}
// // 获取消息详情
// export const getMessageDetail = params => {
//   return request({
//     url: '',
//     method: 'get',
//     params
//   })
// }
// 标记是否已读
export const setMessageIsRead = (data = []) => {
  return request({
    url: '/message/detail/read',
    method: 'post',
    data
  })
}
