<!--
 * @Description:  税务信息
 * @Author: thb
 * @Date: 2023-05-29 13:57:57
 * @LastEditTime: 2023-12-13 15:46:07
 * @LastEditors: thb
-->
<template>
  <el-form :model="formData" ref="formRef" :rules="rules" label-position="top" :hide-required-asterisk="disabled">
    <el-row :gutter="24">
      <!-- <el-col :span="8">
        <el-form-item label="法人身份证号" prop="identityNumber">
          <el-input
            v-model="formData.identityNumber"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col> -->
      <el-col :span="8">
        <el-form-item label="登记税务机关">
          <!-- <el-select
            v-model="formData.taxRegistrationOrgan"
            :disabled="disabled"
            :placeholder="disabled ? ' ' : '请选择'"
            clearable
          >
            <el-option v-for="(option, index) in org_list" :key="index" :label="option.name" :value="option.name" />
          </el-select> -->
          <el-tooltip
            :content="
              formData.taxRegistrationOrgan?.length ? formData.taxRegistrationOrgan[formData.taxRegistrationOrgan.length - 1] : ''
            "
            v-if="disabled && formData.taxRegistrationOrgan?.length"
            effect="light"
            placement="top-start"
          >
            <div style="width: 100%">
              <el-input v-model="formData.taxRegistrationOrgan" disabled />
            </div>
          </el-tooltip>
          <!-- 采用级联选择器 -->
          <el-cascader
            v-else
            v-model="formData.taxRegistrationOrgan"
            @change="handleChangeTaxRegistrationOrgan"
            filterable
            clearable
            :placeholder="disabled ? ' ' : '请选择'"
            :disabled="disabled"
            :props="{
              value: 'name',
              label: 'name',
              children: 'child'
            }"
            :options="org_list"
            :show-all-levels="false"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="税务机关地址">
          <el-tooltip
            :content="formData.taxOrganAddress"
            v-if="disabled && formData.taxOrganAddress"
            effect="light"
            placement="top-start"
          >
            <el-input
              v-model="formData.taxOrganAddress"
              maxlength="250"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            v-model="formData.taxOrganAddress"
            maxlength="250"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="税率登记">
          <el-select
            v-model="formData.rateRegistration"
            :disabled="disabled"
            multiple
            :placeholder="disabled ? ' ' : '请选择'"
            clearable
          >
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in rate_registration" :key="index" />
            <!-- <el-option label="1%" value="1%" />
            <el-option label="3%" value="3%" />
            <el-option label="6%" value="6%" />
            <el-option label="9%" value="9%" />
            <el-option label="13%" value="13%" />
            <el-option label="差额" value="差额" /> -->
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <!-- <el-col :span="8">
        <el-form-item label="法人实名登记">
          <el-radio-group v-model="formData.legalRealNameFlag" :disabled="disabled">
            <el-radio :label="false" size="large">否</el-radio>
            <el-radio :label="true" size="large">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col> -->
      <el-col :span="8">
        <el-form-item label="办税员实名认证">
          <el-radio-group v-model="formData.taxRealNameFlag" :disabled="disabled">
            <el-radio :label="false" size="large">否</el-radio>
            <el-radio :label="true" size="large">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="个体户核定">
          <el-radio-group v-model="formData.individualCheckFlag" :disabled="disabled">
            <el-radio :label="false" size="large">否</el-radio>
            <el-radio :label="true" size="large">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- <el-row :gutter="24"> </el-row> -->
    <Collapse title="税务首次报道">
      <el-row :gutter="24">
        <!-- <el-col :span="8">
          <el-form-item label="网上税务局注册">
            <el-radio-group v-model="formData.onlineRevenueRegistrationFlag" :disabled="disabled">
              <el-radio :label="false" size="large">否</el-radio>
              <el-radio :label="true" size="large">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="所得税认定方式">
            <el-radio-group v-model="formData.identificationMethodFlag" :disabled="disabled">
              <el-radio :label="false" size="large">查账</el-radio>
              <el-radio :label="true" size="large">核定</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="预留客户手机号" prop="reservedPhoneNumber">
            <el-input v-model="formData.reservedPhoneNumber" :placeholder="disabled ? '' : '请输入'" :disabled="disabled" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="自然人密码" prop="naturalPersonPassword">
            <el-input v-model="formData.naturalPersonPassword" :placeholder="disabled ? '' : '请输入'" :disabled="disabled" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="证书账号">
            <el-tooltip
              :content="formData.certificateAccount"
              v-if="disabled && formData.certificateAccount"
              effect="light"
              placement="top-start"
            >
              <el-input
                v-model="formData.certificateAccount"
                maxlength="100"
                :placeholder="disabled ? '' : '请输入'"
                :disabled="disabled"
              />
            </el-tooltip>
            <el-input
              v-else
              v-model="formData.certificateAccount"
              maxlength="100"
              :placeholder="disabled ? '' : '请输入'"
              :disabled="disabled"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="证书密码">
            <el-input
              v-model="formData.certificatePassword"
              maxlength="100"
              :placeholder="disabled ? '' : '请输入'"
              :disabled="disabled"
            />
          </el-form-item>
        </el-col>
        <!-- 新增客户首次确认 -->
        <el-col :span="8">
          <el-form-item label="客户首次确认">
            <FileUpload
              v-if="!disabled"
              v-model="formData.customerFirstConfirmList"
              :limit="100"
              :isShowTip="false"
              :fileSize="100"
              :fileType="['doc', 'xls', 'txt', 'pdf', 'docx', 'xlsx', 'jpg', 'png', 'jpeg', 'mp3', 'm4a', 'wav']"
            />
            <fileList v-else :list="formData.customerFirstConfirmList" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="客户开票资料">
            <FileUpload v-if="!disabled" v-model="formData.customerBillingInformationFileList" :limit="100" :isShowTip="false" />
            <!-- <span class="download-text" @click="downloadFile(formData.customerBillingInformationFile)" v-else>
              {{
                (formData.customerBillingInformationFile && formData.customerBillingInformationFile?.fileNames) || '暂无文件'
              }}</span
            > -->
            <fileList :list="formData.customerBillingInformationFileList" v-else />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="税务上传资料">
            <FileUpload v-if="!disabled" v-model="formData.taxInformationFileList" :limit="100" :isShowTip="false" />
            <!-- <span class="download-text" @click="downloadFile(formData.taxInformationFile)" v-else>
              {{ (formData.taxInformationFile && formData.taxInformationFile?.fileNames) || '暂无文件' }}</span
            > -->
            <fileList :list="formData.taxInformationFileList" v-else />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法人身份证号" prop="identityNumber">
            <el-input
              v-model="formData.identityNumber"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>

    <Collapse title="首次购买发票">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="开票盘">
            <el-radio-group v-model="formData.drawingSheetFlag" :disabled="disabled">
              <el-radio label="无" size="large">无</el-radio>
              <el-radio label="有,在我司" size="large">有,在我司</el-radio>
              <el-radio label="有,客户处" size="large">有,客户处</el-radio>
              <el-radio label="全电" size="large">全电</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票">
            <el-radio-group v-model="formData.invoiceFlag" :disabled="disabled">
              <el-radio label="无" size="large">无</el-radio>
              <el-radio label="有,在我司" size="large">有,在我司</el-radio>
              <el-radio label="有,客户处" size="large">有,客户处</el-radio>
              <el-radio label="全电" size="large">全电</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票章">
            <el-radio-group v-model="formData.invoiceSealFlag" :disabled="disabled">
              <el-radio label="无" size="large">无</el-radio>
              <el-radio label="有,在我司" size="large">有,在我司</el-radio>
              <el-radio label="有,客户处" size="large">有,客户处</el-radio>
              <!-- <el-radio label="全电" size="large">全电</el-radio> -->
            </el-radio-group>
          </el-form-item></el-col
        >
        <el-col :span="12">
          <el-form-item label="一般纳税人认定表">
            <FileUpload v-if="!disabled" v-model="formData.taxpayerIdentificationFormFileList" :limit="100" :isShowTip="false" />
            <!-- <span class="download-text" @click="downloadFile(formData.taxpayerIdentificationFormFile)" v-else>
              {{
                (formData.taxpayerIdentificationFormFile && formData.taxpayerIdentificationFormFile?.fileNames) || '暂无文件'
              }}</span
            > -->
            <fileList :list="formData.taxpayerIdentificationFormFileList" v-else />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="开票盘归属部门">
            <el-tree-select
              filterable
              v-model="formData.drawingSheetDept"
              :data="deptList"
              check-strictly
              :render-after-expand="false"
              :placeholder="disabled ? ' ' : '请选择'"
              :disabled="disabled"
              :props="defaultProps"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发票归属部门">
            <el-tree-select
              filterable
              v-model="formData.invoiceDept"
              :data="deptList"
              check-strictly
              :render-after-expand="false"
              :placeholder="disabled ? ' ' : '请选择'"
              :disabled="disabled"
              :props="defaultProps"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发票章归属部门">
            <el-tree-select
              filterable
              v-model="formData.invoiceSealDept"
              :data="deptList"
              check-strictly
              :render-after-expand="false"
              :placeholder="disabled ? ' ' : '请选择'"
              :disabled="disabled"
              :props="defaultProps"
              clearable
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item label="发票额度">
            <el-select v-model="formData.invoiceLimit" :placeholder="disabled ? ' ' : '请选择'" :disabled="disabled" clearable>

            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="发票类型">
            <el-select v-model="formData.invoiceType" :placeholder="disabled ? ' ' : '请选择'" :disabled="disabled" clearable>
              <el-option v-for="(option, index) in invoice_type_list" :key="index" :value="option.value" :label="option.label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开票盘类型">
            <el-select
              v-model="formData.drawingSheetType"
              :placeholder="disabled ? ' ' : '请选择'"
              :disabled="disabled"
              clearable
            >
              <el-option
                v-for="(option, index) in drawing_sheet_type_list"
                :key="index"
                :value="option.value"
                :label="option.label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter="24">
      </el-row> -->
    </Collapse>
    <!-- 客户性质为进出口的情况下展示-->
    <Collapse title="出口退税认定" v-if="formData.customerProperty === '进出口'">
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="看点时间">
            <el-date-picker
              :disabled="disabled"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              v-model="formData.watchTime"
              type="date"
              :placeholder="disabled ? ' ' : '请选择'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="进出口认定时间">
            <el-date-picker
              :disabled="disabled"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              v-model="formData.approvalTime"
              type="date"
              :placeholder="disabled ? ' ' : '请选择'"
            /> </el-form-item
        ></el-col>
        <el-col :span="8">
          <el-form-item label="所属部门">
            <el-tree-select
              filterable
              v-model="formData.dept"
              :data="deptList"
              check-strictly
              :render-after-expand="false"
              :placeholder="disabled ? ' ' : '请选择'"
              clearable
              :disabled="disabled"
              :props="defaultProps"
            /> </el-form-item
        ></el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="看点说明">
            <el-input
              v-model="formData.watchInstructions"
              :disabled="disabled"
              type="textarea"
              :placeholder="disabled ? '' : '请输入'"
          /></el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="出口明细表"> </el-form-item>

      <div class="action-btns">
        <el-button type="primary" v-if="!disabled" @click="handleAdd">新增</el-button>
        <el-button type="primary" @click="handleFullScreen">全屏</el-button>
        <el-button @click="handleExport" v-if="formData.taxId">导出</el-button>
      </div>

      <FormTable :formData="formData.taxTable" class="form-table" :option="option" ref="tableRef">
        <template #date="{ row }">
          <el-date-picker
            v-if="!disabled"
            format="YYYY/MM/DD"
            v-model="row.date"
            value-format="YYYY-MM-DD"
            type="date"
            style="width: 120px"
            :placeholder="disabled ? ' ' : '请选择'"
          />
          <span v-else>{{ row.date }}</span>
        </template>
        <template #number="{ row }">
          <NumberInput v-if="!disabled" v-model="row.number" :maxlength="5" />
          <span v-else>{{ row.number }}</span>
        </template>
        <template #customsDeclarationNumber="{ row }">
          <el-input v-if="!disabled" maxlength="100" v-model="row.customsDeclarationNumber" />
          <span v-else>{{ row.customsDeclarationNumber }}</span>
        </template>
        <template #tradeName="{ row }">
          <el-input v-if="!disabled" maxlength="100" v-model="row.tradeName" />
          <span v-else>{{ row.tradeName }}</span>
        </template>
        <template #unitFirst="{ row }">
          <el-input v-if="!disabled" maxlength="100" v-model="row.unitFirst" />
          <span v-else>{{ row.unitFirst }}</span>
        </template>
        <template #supplySource="{ row }">
          <el-input v-if="!disabled" maxlength="100" v-model="row.supplySource" />
          <span v-else>{{ row.supplySource }}</span>
        </template>
        <template #receiptInvoiceNumber="{ row }">
          <el-input v-if="!disabled" maxlength="100" v-model="row.receiptInvoiceNumber" />
          <span v-else>{{ row.receiptInvoiceNumber }}</span>
        </template>
        <template #receiptTradeName="{ row }">
          <el-input v-if="!disabled" maxlength="100" v-model="row.receiptTradeName" />
          <span v-else>{{ row.receiptTradeName }}</span>
        </template>
        <template #receiptNumber="{ row }">
          <NumberInput v-if="!disabled" v-model="row.receiptNumber" :maxlength="5" />
          <span v-else>{{ row.receiptNumber }}</span>
        </template>
        <template #unit="{ row }">
          <el-input v-if="!disabled" maxlength="20" v-model="row.unit" />
          <span v-else>{{ row.unit }}</span>
        </template>
        <template #remainder="{ row }">
          <NumberInput v-if="!disabled" v-model="row.remainder" :maxlength="5" />
          <span v-else>{{ row.remainder }}</span>
        </template>
        <template #declareFlag="{ row }">
          <el-select v-if="!disabled" v-model="row.declareFlag" :placeholder="disabled ? ' ' : '请选择'" clearable>
            <el-option label="否" value="否" />
            <el-option label="是" value="是" />
          </el-select>
          <span v-else>{{ row.declareFlag }}</span>
        </template>
        <template #taxRebateFlag="{ row }">
          <el-select v-if="!disabled" v-model="row.taxRebateFlag" :placeholder="disabled ? ' ' : '请选择'" clearable>
            <el-option label="否" value="否" />
            <el-option label="是" value="是" />
          </el-select>
          <span v-else>{{ row.taxRebateFlag }}</span>
        </template>
        <template v-for="(key, index) in list" :key="index" #[key]="{ row }">
          <el-checkbox :disabled="disabled" v-model="row[key]" />
        </template>
        <template #action="{ $index }" v-if="!disabled">
          <el-button type="danger" @click="handleDelete($index)">删除</el-button>
        </template>
      </FormTable>
    </Collapse>
  </el-form>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import Collapse from '@/components/Collapse'
import FormTable from '@/components/FormTable'
// import Screenfull from '@/components/Screenfull'
import iFrame from '@/components/iFrame'
import NumberInput from '@/components/NumberInput'
import { saveCustomerTax, getCustomerTaxByCiId } from '@/api/customer/file'
import { useRemote } from '@/hooks/useRemote'
import { FormValidators } from '@/utils/validate'
import { listDept } from '@/api/system/dept'
import { getTaxTreeList } from '@/api/basicData/basicData'
import fileList from '@/components/FileList'

const disabled = inject('disabled')
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const emits = defineEmits(['update:modelValue', 'on-edit'])
const formData = computed({
  get: () => {
    console.log('props.model', props.modelValue)
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})

const getDetail = async () => {
  const { data } = await getCustomerTaxByCiId(props.modelValue.ciId)
  const { watchTime, approvalTime, dept, watchInstructions } = data?.customerTaxRebateIdentified || {}

  formData.value = Object.assign(formData.value, {
    ...data,
    taxId: data?.id,
    taxTable: {
      tableData: data?.customerTaxExportDetail || []
    },
    watchTime,
    approvalTime,
    dept,
    watchInstructions,
    rateRegistration: data?.rateRegistration.split(',') || [],
    //taxRegistrationOrgan
    taxRegistrationOrgan: data.taxRegistrationOrgan?.split(',') || ''
  })
}
const defaultColumns = [
  {
    prop: 'date',
    width: '150',
    label: '出口日期'
  },
  {
    prop: 'customsDeclarationNumber',
    width: '150',
    label: '报关单号'
  },
  {
    prop: 'tradeName',
    width: '150',
    label: '品名'
  },
  {
    prop: 'number',
    width: '100',
    label: '数量'
  },
  {
    prop: 'unitFirst',
    width: '100',
    label: '单位'
  },
  {
    prop: 'supplySource',
    width: '100',
    label: '货源地'
  },
  {
    prop: 'receiptInvoiceNumber',
    width: '200',
    label: '进项发票号码'
  },
  {
    prop: 'receiptTradeName',
    width: '100',
    label: '进项品名'
  },
  {
    prop: 'receiptNumber',
    width: '100',
    label: '进项数量'
  },
  {
    prop: 'unit',
    width: '100',
    label: '计量单位'
  },
  {
    prop: 'remainder',
    width: '100',
    label: '余数'
  },
  {
    prop: 'declareFlag',
    width: '100',
    label: '是否申报'
  },
  {
    prop: 'taxRebateFlag',
    width: '100',
    label: '是否退税'
  },
  {
    prop: 'customsDeclarationFlag',
    label: '报关单'
  },
  {
    prop: 'billLadingFlag',
    label: '提单'
  },
  {
    prop: 'exportContractFlag',
    width: 200,
    label: '外销合同(形式合同)'
  },
  {
    prop: 'importContractFlag',
    label: '进货合同'
  },
  {
    prop: 'paperlessCertificateFlag',
    width: 200,
    label: '通关无纸化证书'
  },
  {
    prop: 'packingListFlag',
    label: '装箱单'
  },
  {
    prop: 'deliveryNoteFlag',
    label: '进仓单'
  },
  {
    prop: 'proformaInvoiceFlag',
    label: '形式发票'
  },
  {
    prop: 'forwarderInvoiceFlag',
    label: '货代发票'
  },
  {
    prop: 'collectionStatementFlag',
    label: '收汇流水'
  },
  {
    prop: 'paymentStatementFlag',
    label: '付款流水'
  },
  {
    prop: 'forwarderInvoiceStatementFlag',
    width: 200,
    label: '货代发票流水'
  },
  {
    prop: 'action',
    fixed: 'right',
    width: '100',
    label: '操作'
  }
]
const option = ref(defaultColumns)
watch(
  disabled,
  () => {
    if (disabled.value) {
      option.value.pop()
      getDetail()
    }
  },
  {
    immediate: true
  }
)

// 监听是否是编辑
const edit = inject('isEdit')
watch(
  edit,
  async () => {
    console.log('edit', edit.value)
    if (edit.value) {
      await getDetail()
      emits('on-edit')
    }
  },
  {
    immediate: true
  }
)
const rules = {
  identityNumber: [
    {
      message: '请输入正确的身份证号',
      trigger: 'blur',
      validator: FormValidators.idCardDif
    }
  ],
  reservedPhoneNumber: [
    {
      message: '请输入正确格式的手机号',
      trigger: 'blur',
      validator: FormValidators.mobilePhone
    }
  ]
}

const list = [
  'customsDeclarationFlag',
  'billLadingFlag',
  'exportContractFlag',
  'importContractFlag',
  'paperlessCertificateFlag',
  'packingListFlag',
  'deliveryNoteFlag',
  'proformaInvoiceFlag',
  'forwarderInvoiceFlag',
  'collectionStatementFlag',
  'paymentStatementFlag',
  'forwarderInvoiceStatementFlag'
]
const handleAdd = () => {
  formData.value.taxTable.tableData.push({})
}

const handleDelete = index => {
  formData.value.taxTable.tableData.splice(index, 1)
}

const formRef = ref()
const saveRemote = async () => {
  if (!formRef.value) return
  const result = await formRef.value.validate(valid => {
    if (valid) {
    } else {
    }
  })
  if (result) {
    const { watchTime, approvalTime, dept, watchInstructions } = formData.value
    const id = await useRemote(
      saveCustomerTax,
      {
        ...formData.value,
        rateRegistration: formData.value.rateRegistration.join(','),
        customerTaxExportDetail: formData.value.taxTable.tableData,
        customerTaxRebateIdentified: {
          watchTime,
          approvalTime,
          dept,
          watchInstructions
        },
        //taxRegistrationOrgan
        taxRegistrationOrgan:
          (Array.isArray(formData.value.taxRegistrationOrgan) && formData.value.taxRegistrationOrgan?.join(',')) || ''
      },
      ['taxpayerIdentificationFormFile', 'taxInformationFile', 'customerBillingInformationFile'],
      '税务信息',
      [
        'taxpayerIdentificationFormFileList',
        'taxInformationFileList',
        'customerBillingInformationFileList',
        'customerFirstConfirmList'
      ]
    )
    formData.value.taxId = id
    return id
  }
}

// exportTaxDetail
const handleExport = async () => {
  proxy.download(
    '/customerTaxExportDetail/export',
    {
      mainId: formData.value.taxId
    },
    'export.xlsx'
  )
}
// 全屏功能
const toggleFulScreen = dom => {
  if (
    !dom.fullscreenElement && // alternative standard method
    !dom.mozFullScreenElement &&
    !dom.webkitFullscreenElement &&
    !dom.msFullscreenElement
  ) {
    // current working methods
    if (dom.requestFullscreen) {
      dom.requestFullscreen()
    } else if (dom.msRequestFullscreen) {
      dom.msRequestFullscreen()
    } else if (dom.mozRequestFullScreen) {
      dom.mozRequestFullScreen()
    } else if (dom.webkitRequestFullscreen) {
      dom.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT)
    }
  } else {
    if (dom.exitFullscreen) {
      dom.exitFullscreen()
    } else if (dom.msExitFullscreen) {
      dom.msExitFullscreen()
    } else if (dom.mozCancelFullScreen) {
      dom.mozCancelFullScreen()
    } else if (dom.webkitExitFullscreen) {
      dom.webkitExitFullscreen()
    }
  }
}
const tableRef = ref()
const handleFullScreen = () => {
  const pDom = tableRef.value.getParentDom()
  console.log('pDom', pDom)
  toggleFulScreen(pDom)
}

/** 查询部门列表 */
const { proxy } = getCurrentInstance()
const deptList = ref([])
const defaultProps = {
  value: 'deptName',
  label: 'deptName',
  children: 'children'
}
function getList() {
  listDept().then(response => {
    deptList.value = proxy.handleTree(response.data, 'deptId')
  })
}

getList()
const { invoice_type_list } = proxy.useDict('invoice_type_list')
const { drawing_sheet_type_list } = proxy.useDict('drawing_sheet_type_list')
// 税率登记字典
const { rate_registration } = proxy.useDict('rate_registration')
const org_list = ref([])
function onGetBasicData() {
  getTaxTreeList({ enable: 1 }).then(res => {
    org_list.value = res.data // 已验证，新增下拉有对应数据
  })
}
onGetBasicData()

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

function handleChangeTaxRegistrationOrgan(value) {
  const address = org_list.value.find(item => item.name === value.at(-1)).address
  // console.log('handleChangeTaxRegistrationOrgan', value, value.at(-1), address)
  formData.value.taxOrganAddress = address
}
defineExpose({
  saveRemote
})
</script>
<style lang="scss" scoped>
.form-table {
  // width: 1000px;
}
.action-btns {
  display: flex;
  justify-content: flex-end;
}
:deep(.el-cascader.el-cascader--default) {
  width: 100%;
}
</style>
