<!--
 * @Description: 新增地址申请
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-12-04 08:56:27
 * @LastEditors: thb
-->
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-position="top"
    :hide-required-asterisk="isDisabled"
    :disabled="isDisabled"
  >
    <el-row :gutter="24">
      <el-col :span="6">
        <el-form-item label="企业名称" prop="customerName">
          <div style="width: 100%; cursor: pointer" @click="listShow = true">
            <el-input
              v-model="formData.customerName"
              maxlength="20"
              readonly
              :disabled="isDisabled"
              :placeholder="isDisabled ? '' : '请选择'"
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="申请区域" prop="areaId">
          <el-tree-select
            v-model="formData.areaId"
            :data="addressList"
            :props="{
              value: 'id',
              label: 'name',
              children: 'child'
            }"
            value-key="id"
            :disabled="isDisabled"
            :placeholder="isDisabled ? ' ' : '请选择'"
            clearable
            check-strictly
          />
          <!-- <el-cascader
            value-key="id"
            style="width: 100%"
            v-model="formData.areaId"
            filterable
            :props="{ label: 'name', value: 'id', children: 'child' }"
            :options="addressList"
          /> -->
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="关联账单" prop="paymentNo">
          <div style="width: 100%; cursor: pointer" @click="selectPayment">
            <el-input v-model="formData.paymentNo" :disabled="isDisabled" readonly :placeholder="isDisabled ? '' : '请选择'" />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="领用人" prop="createBy">
          <el-input disabled v-model="formData.createBy" placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="托管分类" prop="hostingType">
          <el-select allow-create filterable style="width: 100%" v-model="formData.hostingType" placeholder="请选择" clearable>
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in address_tuoguan_fenlei" :key="index" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="实际托管价格" prop="paymentAmount">
          <el-input disabled v-model="formData.paymentAmount" placeholder="请输入"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="托管期起" prop="paymentStartTime">
          <el-date-picker
            disabled
            style="width: 100%"
            v-model="formData.paymentStartTime"
            format="YYYY-MM"
            value-format="YYYY-MM"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="托管期止" prop="paymentEndTime">
          <el-date-picker
            disabled
            style="width: 100%"
            v-model="formData.paymentEndTime"
            format="YYYY-MM"
            value-format="YYYY-MM"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="formData.remark"
            :disabled="isDisabled"
            :placeholder="isDisabled ? '' : '请输入'"
            maxlength="1000"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="图片附件" prop="imageFileList">
          <ImageUpload
            v-if="!isDisabled || formData?.imageFileList?.length"
            :disabled="isDisabled"
            :limit="isDisabled ? formData?.imageFileList?.length || 1 : 100"
            v-model="formData.imageFileList"
            :isShowTip="false"
          />
          <span v-else class="download-text"> 暂无图片 </span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="文件附件" prop="fileList">
          <FileUpload v-if="!isDisabled" v-model="formData.fileList" :isShowTip="false" :limit="100" />
          <FileList v-else :list="formData.fileList" />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 待审批 提交时间 -->
    <el-row v-if="formData.status === '0' && isDisabled">
      <el-col :span="24">
        <el-form-item label="提交时间" prop="createTime">
          <el-input v-model="formData.createTime" :disabled="isDisabled" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <tableModal
    v-if="listShow"
    :init-param="{ discard: 0 }"
    rowKey="customerId"
    title="关联客户"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listShow = false"
    @on-select="handleSelect"
  />

  <tableModalBill
    v-if="listShowBill"
    :init-param="{ feeType: formData.feeTypeDefault, customerId: formData.customerId, receiveStatus: '已收款' }"
    rowKey="id"
    title="关联账单"
    :columns="columnsBillNext"
    :request-api="financePaymentList"
    @on-close="listShowBill = false"
    @on-select="handleSelectBill"
  >
    <template #feeType="{ row }">
      <span>{{ row.feeType || '--' }}</span>
    </template>
    <template #paymentAmount="{ row }">
      <span>{{ row.paymentAmount >= 0 ? `${row.paymentAmount}元` : '--' }}</span>
    </template>
    <template #allReceiptAmount="{ row }">
      <span>{{ row.allReceiptAmount >= 0 ? `${row.allReceiptAmount}元` : '--' }}</span>
    </template>
    <template #allReceivableAmount="{ row }">
      <span>{{ row.allReceivableAmount >= 0 ? `${row.allReceivableAmount}元` : '--' }}</span>
    </template>
  </tableModalBill>
</template>

<script setup lang="tsx">
import { reactive, ref } from 'vue'
import NumberInput from '@/components/NumberInput'
import { useTableModal } from '@/hooks/useTableModal'
import { useBillModal } from '@/hooks/useBillModal'
import { getAddressProviderList } from '@/api/address-provider'
import { getAreaTreeList } from '@/api/basicData/basicData'
import FileUpload from '@/components/FileUpload'
import FileList from '@/components/FileList'
import ImageUpload from '@/components/ImageUpload'
import useUserStore from '@/store/modules/user'
import { cloneDeep } from 'lodash'

const userStore = useUserStore()
const { listShow, tableModal, getCustomers, columns } = useTableModal()
const { listShowBill, tableModalBill, financePaymentList, columnsBill } = useBillModal()
const columnsBillNext = cloneDeep(columnsBill)
columnsBillNext[3].search = undefined
const { proxy } = getCurrentInstance()
const { address_tuoguan_fenlei } = proxy.useDict('address_tuoguan_fenlei')

const formData = reactive({
  customerName: '',
  customerId: '',
  address: '',
  areaId: '',
  status: '0',
  paymentNo: '',
  paymentId: '',
  id: undefined,
  createTime: '',
  remark: '',
  createBy: userStore.user.nickName,
  paymentStartTime: '',
  paymentEndTime: '',
  paymentAmount: '',
  hostingType: '',
  imageFileList: [],
  fileList: []
})
const isDisabled = ref(false)
watch(
  formData,
  () => {
    if (formData.isDisabled) {
      isDisabled.value = true
    }
  },
  {
    immediate: true
  }
)
// watch(
//   formData,
//   () => {
//     if (formData.id) {
//       formData.createBy = userStore.user.nickName
//     }
//   },
//   {
//     immediate: true
//   }
// )

const rules = {
  customerName: [{ required: true, message: '请选择', trigger: 'change' }],
  areaId: [{ required: true, message: '请选择', trigger: 'change' }],
  paymentNo: [{ required: true, message: '请选择', trigger: 'change' }]
}
// const addressList = ref([])
// const getAddressProviders = async () => {
//   const { data } = await getAddressProviderList({
//     page: 1,
//     pageSize: 1000,
//     enable: '1'
//   })
//   addressList.value = data.records || []
// }
// getAddressProviders()

const addressList = ref([])
/** 查询菜单下拉树结构 */
const getTreeselect = () => {
  getAreaTreeList({ enable: 1 }).then(res => {
    addressList.value = res.data
  })
}
getTreeselect()

const selectPayment = () => {
  if (!formData.customerId) {
    proxy.$modal.msgWarning(`请先选择企业`)
    return
  }
  listShowBill.value = true
}
const handleSelect = data => {
  formData.paymentNo = ''
  formData.paymentId = ''
  formData.customerName = data.customerName
  formData.customerId = data.customerId
}

const handleSelectBill = data => {
  const { paymentNo, id } = data
  formData.paymentNo = paymentNo
  formData.paymentId = id
  formData.paymentStartTime = data.paymentStartTime
  formData.paymentEndTime = data.paymentEndTime
  formData.paymentAmount = data.paymentAmount
  formData.remark = data.mark
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
