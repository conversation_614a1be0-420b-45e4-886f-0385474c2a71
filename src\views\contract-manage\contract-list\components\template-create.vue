<template>
  <el-dialog
    align-center
    class="normal-contract"
    :title="
      formTableData_0.tableData[0]?.isIntention === '1'
        ? '转为正式合同'
        : id
        ? !isRenewal
          ? '变更合同'
          : '续签合同'
        : '新增合同'
    "
    width="1400"
    :close-on-click-modal="false"
    v-model="visible"
    destroy-on-close
    @close="handleClose"
  >
    <template v-if="isPermission">
      <el-steps :active="active" align-center finish-status="success">
        <el-step v-for="(item, index) in stepArr" :key="index" :title="`步骤${index + 1}`" :description="item" />
      </el-steps>
      <div class="my-step" v-show="active === 0">
        <FormTable ref="formTableRef_0" :formData="formTableData_0" :option="option_0">
          <template #customerName="{ row }">
            <div @click="handleListSelectShow" style="width: 100%">
              <el-input
                :disabled="id || associated.customerId"
                v-model="row.customerName"
                readonly
                maxlength="20"
                style="width: 100%"
                :placeholder="disabled ? '' : '请输入'"
              />
            </div>
          </template>
          <template #customerNo="{ row }"><el-input disabled v-model="row.customerNo"></el-input></template>
          <template #companyPerson="{ row }"><el-input disabled v-model="row.companyPerson"></el-input></template>
          <template #companyPhone="{ row }"><el-input disabled v-model="row.companyPhone"></el-input></template>
        </FormTable>
        <FormTable ref="formTableRef" :formData="formTableData" :option="option">
          <template #productId="{ row }">
            <el-tree-select
              class="my-tree-select"
              style="width: 100%"
              v-model="row.productId"
              :ref="el => setTreeSelectRef(el, row.productId)"
              filterable
              :data="productTreeData"
              :props="defaultPopsFunction(row)"
              node-key="id"
              @current-change="node => handleChangeProd(node, row)"
              :render-after-expand="false"
              :show-checkbox="true"
              :check-strictly="true"
            />
            <!-- default-expand-all  -->
          </template>
          <template #contractType="{ row }">
            <el-select
              :disabled="id"
              style="width: 100%"
              v-model="row.contractType"
              placeholder="请选择"
              @change="value => handleChangeContractType(value, row)"
            >
              <el-option
                :disabled="
                  (item.label === '一次性合同' && (row.feeType === '1' || row.feeType === '2')) ||
                  (item.label !== '一次性合同' && row.feeType === '0')
                "
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in id ? contractTypeArr : row?.types"
                :key="index"
              />
            </el-select>
          </template>
          <template #tempId="{ row }">
            <el-select :disabled="id" style="width: 100%" v-model="row.tempId" placeholder="请选择">
              <!-- 一次性合同的模板只能是产品类型必须具备一次性属性 -->
              <el-option
                v-for="(item, index) in row.contractTempList"
                :key="index"
                :disabled="item.contractType === '1' && (row.feeType === '1' || row.feeType === '2')"
                :label="item.tempName"
                :value="item.id"
              />
            </el-select>
          </template>
          <template #action="{ row, $index }">
            <el-button type="primary" text @click="handleAdd" :disabled="id">新增</el-button>
            <!-- <el-button type="primary" text @click="handleAdd">编辑</el-button> -->
            <el-button type="danger" text @click="handleDelete(row, $index)" :disabled="formTableData.tableData.length === 1"
              >删除</el-button
            >
          </template>
        </FormTable>
        <el-form
          v-if="id && formTableData_0.tableData?.[0]?.isIntention !== '1'"
          ref="formTableRef_2"
          :model="formTableData_0.tableData[0]"
          :rules="formTableData_0.rules"
          label-position="top"
          style="margin: 15px 10px"
        >
          <el-form-item label="变更原因" prop="changeReason" v-if="!isRenewal">
            <el-input
              v-model="formTableData_0.tableData[0].changeReason"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
            /> </el-form-item
        ></el-form>
      </div>
      <div class="my-step" v-show="[1, 2].includes(active)">
        <el-tabs v-model="activeName">
          <el-tab-pane
            v-for="row in formTableData.tableData"
            :key="row.productId"
            :label="
              row.productName
                ? `${contractTypeArr.find(item => item.value === row.contractType)?.label}(${row.productName})`
                : `${contractTypeArr.find(item => item.value === row.contractType)?.label}`
            "
            :name="row.productId"
          >
            <templateInstance
              :ref="el => setCompRef(el, row.productId)"
              :isRenewal="isRenewal"
              :formDataPreStep="
                Object.assign({}, formTableData_0.tableData[0], row, {
                  changeReason: formTableData_0.tableData[0].changeReason
                })
              "
              @on-success="onSuccess(row)"
            ></templateInstance>
          </el-tab-pane>
        </el-tabs>
      </div>
    </template>
    <template v-else>
      <div class="wrap">
        <el-icon :size="40">
          <Lock />
        </el-icon>
        <p>您暂无权限对本合同进行变更</p>
      </div>
    </template>
    <template #footer>
      <template v-if="isPermission">
        <el-button
          plain
          type="primary"
          @click="handleStepPre"
          v-show="
            (formTableData_0.tableData[0].isIntention !== '1' && active > 0) ||
            (formTableData_0.tableData[0].isIntention === '1' && active > 1)
          "
          >上一步</el-button
        >
        <el-button type="primary" @click="handleStepAfter" v-show="active < 1">下一步</el-button>
        <el-button type="primary" @click="handleSubmit" v-show="active === 1" :loading="loading">保存当前合同</el-button>
        <el-button @click="handleClose">关闭</el-button>
        <!-- <el-button v-if="currentMode === 'development'" @click="saveDocx">saveDocx</el-button> -->
        <!-- <el-button v-if="currentMode === 'development'" @click="handlePrint">handlePrint【dev】</el-button> -->
        <!-- <el-button v-if="currentMode === 'development'" @click="handlePrint">handleInput【dev】</el-button> -->
        <!-- <template v-if="currentMode === 'development'">
          <span
            >【productId-{{ formTableData.tableData?.[0]?.productId }}】 【activeName-{{ activeName }}】【active-{{
              active
            }}】</span
          >
          <span>【productIds-{{ productIds }}】</span></template
        > -->
      </template>
      <template v-else>
        <el-button @click="handleClose">关闭</el-button>
      </template>
    </template>
  </el-dialog>
  <tableModal
    :init-param="{ discard: 0 }"
    v-if="listSelectShow"
    title="关联客户"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelectCustomer"
  />
</template>
<script setup lang="jsx">
import {
  getContractTempList,
  getContractTempGetFieldList,
  getContractTempGetById
} from '@/api/contract-template/contract-template.js'
import { checkContractChangeIsPermission, saveContract, customerContractChangeToFormal } from '@/api/contract/contract'
import { getCustomers } from '@/api/customer/file'
import { getBusinessList } from '@/api/business/business'
import { getCompanyTreeList } from '@/api/basicData/basicData'
import tableModal from '@/components/tableModal'
import { nextTick, reactive, watch } from 'vue'
import { customerProperty, customerIndustry } from '@/utils/constants'
import { contractTypeArr } from '@/utils/constants.js'
import { Lock } from '@element-plus/icons-vue'
import FormTable from '@/components/FormTable'
import templateInstance from './template-instance.vue'
import { useDic } from '@/hooks/useDic'
import dayjs from 'dayjs'
const { getDic } = useDic()

const activeName = ref('')
const currentMode = import.meta.env.MODE

const { proxy } = getCurrentInstance()
const branch_office = ref([])
function onGetBasicData() {
  getCompanyTreeList({ enable: 1 }).then(res => {
    branch_office.value = res.data.records // 已验证，新增下拉有对应数据
  })
}
onGetBasicData()

const emits = defineEmits(['on-close', 'on-success'])
const { customer_status } = proxy.useDict('customer_status')

const props = defineProps({
  id: Number,
  associated: {
    type: Object,
    default: () => {
      return {}
    }
  },
  productIds: {
    type: Array,
    default: () => []
  },
  isRenewal: {
    type: Boolean,
    default: false
  }
})

const option_0 = [
  {
    prop: 'customerName',
    label: '关联客户'
  },
  {
    prop: 'customerNo',
    label: '客户编码'
  },
  {
    prop: 'companyPerson',
    label: '联系人'
  },
  {
    prop: 'companyPhone',
    label: '手机号'
  }
]
const formTableData_0 = ref({
  tableData: [
    {
      type: 1, // type = 1 表示模板创建合同
      customerName: '',
      customerNo: '',
      companyPerson: '',
      companyPhone: ''
    }
  ],
  rules: {
    customerName: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
    changeReason: [{ required: true, message: '请输入', trigger: ['blur'] }]
  }
})
const option = [
  {
    prop: 'productId',
    label: '产品名称'
    // algin: 'left'
  },
  {
    prop: 'contractType',
    label: '合同类型'
  },
  {
    prop: 'tempId',
    label: '合同模板'
  },
  {
    prop: 'action',
    label: '操作'
  }
]
const formTableData = ref({
  tableData: [
    {
      productId: '',
      contractType: '',
      tempId: '',
      contractTempList: [] // 借用这个承载下拉数据
    }
  ],
  rules: {
    productId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    contractType: [{ required: true, message: '请选择', trigger: ['blur'] }], // 不能写change，额外手动触发校验
    tempId: [{ required: true, message: '请选择', trigger: ['blur'] }], // 不能写change，额外手动触发校验
    changeReason: [{ required: true, message: '请输入', trigger: ['blur'] }]
  }
})
// 新增行
const handleAdd = () => {
  formTableData.value.tableData.push({
    productId: '',
    contractType: '',
    tempId: '',
    contractTempList: [] // 借用这个承载下拉数据
  })
}

// 删除行
const handleDelete = (row, index) => {
  if (row.contractType) {
    // 如果该行的合同类型已经存在
    proxy.$modal
      .confirm('请注意,删除该行将不保存已填写的信息!')
      .then(() => {
        // 确认更改
        // 删除已经存在的tab
        formTableData.value.tableData.splice(index, 1)
        // activeName默认前一个tab
        activeName.value = formTableData.value.tableData[index === 0 ? 0 : index - 1].productId
        // 删除之后需要将相应的tab绑定的ref绑定
        delete compRefs[row.productId]
      })
      .catch(error => {
        console.log('error', error)
      })
  } else {
    formTableData.value.tableData.splice(index, 1)
  }
}

const visible = ref(true)
const stepArr = ref(['基础信息', '合同制作'])
const active = ref(0)
const disabled = ref(false)

const formTableRef_0 = ref(null)
const formTableRef = ref(null)
const formTableRef_2 = ref(null)
function handleStepPre() {
  active.value--
}
async function handleStepAfter() {
  if (active.value === 0) {
    if (await formTableRef_0.value.handleValidate()) {
      if (await formTableRef.value.handleValidate()) {
        if (!props.id || (props.id && (await formTableRef_2.value.validate()))) {
          formTableData.value.tableData.forEach((item, index) => {
            compRefs[item.productId].showThisStep()
          })
          active.value++
        }
      }
    }
  }
}

function handleSubmit() {
  compRefs[activeName.value].submit()
}

function onSuccess(row) {
  console.log('formTableData.value.tableData', JSON.parse(JSON.stringify(formTableData.value.tableData)))
  formTableData.value.tableData.splice(
    formTableData.value.tableData.findIndex(item => item.productId === row.productId),
    1
  )
  delete compRefs[row.productId]
  console.log('formTableData.value.tableData', JSON.parse(JSON.stringify(formTableData.value.tableData)))
  if (formTableData.value.tableData.length === 0) {
    emits('on-success')
    emits('on-close')
  } else {
    activeName.value = formTableData.value.tableData[0].productId
  }
}

const handleClose = () => {
  emits('on-close')
}

const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: customer_status.value.concat([
      {
        label: '废弃客户',
        value: '废弃客户'
      }
    ]),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    enum: getDic('customer_property', [
      {
        label: '废弃客户',
        value: '废弃客户'
      }
    ]),
    // enum: customerProperty.concat([
    //   {
    //     label: '废弃客户',
    //     value: '废弃客户'
    //   }
    // ]),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

// 客户列表弹窗显示
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  if (disabled.value || props.id || props.associated.customerId) return
  listSelectShow.value = true
}
// 客户表中，客户方甲方是contactXXX，伯沣方乙方是companyXXX
// 合同表中，地址类客户方甲方是companyXXX，伯沣方乙方是contactXXX；记账类客户方甲方是contactXXX，伯沣方乙方是companyXXX
// （合同表中，从模板引擎来看是这样，但是模板引擎拿到的后端XXX值是否对应呢？）
const handleSelectCustomer = data => {
  // console.log('data', data)
  formTableData_0.value.tableData[0].customerName = data.customerName
  formTableData_0.value.tableData[0].customerNo = data.customerNo
  formTableData_0.value.tableData[0].ciId = data.customerId
  formTableData_0.value.tableData[0].customerId = data.customerId
  formTableData_0.value.tableData[0].companyName = data.customerName
  formTableData_0.value.tableData[0].legalPerson = data.contactPerson
  formTableData_0.value.tableData[0].legalPhone = data.contactPhone
  formTableData_0.value.tableData[0].contactPhone = data.contactPhone
  formTableData_0.value.tableData[0].contactPhone_empty = data.contactPhone ? false : true // _empty作为判断是否要放开disabled以便能手动填入数据的标记
  formTableData_0.value.tableData[0].contactPerson = data.companyPerson
  formTableData_0.value.tableData[0].contactPerson_empty = data.companyPerson ? false : true // _empty作为判断是否要放开disabled以便能手动填入数据的标记
  // todo 地址取哪个
  formTableData_0.value.tableData[0].address = data.address
  formTableData_0.value.tableData[0].address_empty = data.address ? false : true // _empty作为判断是否要放开disabled以便能手动填入数据的标记
  formTableData_0.value.tableData[0].contactAddress = data.companyAddress
  formTableData_0.value.tableData[0].contactAddress_empty = data.companyAddress ? false : true // _empty作为判断是否要放开disabled以便能手动填入数据的标记
  /*会计公司方---start---*/
  formTableData_0.value.tableData[0].branchOffice = data.branchOffice
  formTableData_0.value.tableData[0].branchOffice_empty = data.branchOffice ? false : true // _empty作为判断是否要放开disabled以便能手动填入数据的标记
  formTableData_0.value.tableData[0].companyPerson = data.contactPerson
  formTableData_0.value.tableData[0].companyPerson_empty = data.contactPerson ? false : true // _empty作为判断是否要放开disabled以便能手动填入数据的标记
  formTableData_0.value.tableData[0].companyAddress = data.address
  formTableData_0.value.tableData[0].companyAddress_empty = data.address ? false : true // _empty作为判断是否要放开disabled以便能手动填入数据的标记
  formTableData_0.value.tableData[0].companyPhone = data.contactPhone
  formTableData_0.value.tableData[0].companyPhone_empty = data.contactPhone ? false : true // _empty作为判断是否要放开disabled以便能手动填入数据的标记
  /*会计公司方---end---*/
  console.log('formTableData_0.value.tableData[0]', formTableData_0.value.tableData[0])
}

// 获取所有的产品名称
const productTreeData = ref()
const defaultPopsFunction = row => {
  return {
    value: 'id',
    label: 'name',
    disabled: data => {
      // console.log('data', data)
      // 变更时候只能选择收费类型包含当前合同模板收费类型的产品
      // 不能再选第二个当前页已选过的产品
      return (
        (data?.type === '产品类型' &&
          (data.types.findIndex(item => item.value === formTableData_0.value.tableData[0].contractType) === -1 ||
            data.feeType !== formTableData_0.value.tableData[0].feeType) &&
          props.id) ||
        (data?.type === '产品类型' && formTableData.value.tableData.map(item => item.productId).includes(data.id))
      )
    }
  }
}
const getAllProducts = async () => {
  const { data } = await getBusinessList({
    pageNum: 1,
    pageSize: 10000
  })
  // 将后端传回的数据结构进行转换
  const revertData = []
  data.forEach(item => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      children: []
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach(child => {
        obj.children.push({
          productParent: {
            name: item.typeName,
            id: item.id
          },
          name: child.productName,
          types:
            item.contractType?.split(',')?.map(item1 => {
              // console.log('item1', item1)
              const itemValue = contractTypeArr.find(item0 => item0.label === item1).value
              // console.log('itemValue', itemValue)
              return {
                label: item1,
                value: itemValue
              }
            }) || [],
          type: '产品类型',
          quotation: child.quotation, // 合同报价
          isInContract: child.isInContract, // 是否在合同中定义
          feeType: child.feeType, //收费类型
          id: child.id // 产品类型id
        })
      })
    }
  })
  // console.log('revertData', revertData)
  productTreeData.value = revertData || []
}

// 产品下拉框选择事件
const treeSelectRefs = {}
const setTreeSelectRef = (el, id) => {
  // console.log('treeSelectRef-0', el, id)
  if (el && id) {
    // console.log('treeSelectRef-1', el, id)
    treeSelectRefs[id] = el
  }
}
let setCurrentKeyFlag = 0 // 上锁，避免setCurrentKey又触发一次handleChangeProd
// 产品选择
// why:为什么这里切换产品正好实现了清空step2中表单数据的逻辑，跟根据productId开辟的tab有关？
const handleChangeProd = (node, row) => {
  // console.log(
  //   'node',
  //   node,
  //   node.types.findIndex(item => item.value === formData.contractType)
  // )
  // console.log('formTableData', JSON.parse(JSON.stringify(formTableData.value)))
  // const if0 = row.productId
  // const if1 = formTableData.value.tableData.findIndex(item => item.productId === row.productId) !== -1
  // const if2 = node.id !== row.productId // @current-change时候绑定的row.productId还没跟着变化
  // const if3 = row.tempId
  // console.log('if0', if0)
  // console.log('if1', if1)
  // console.log('if2', if2)
  // console.log('if3', if3)
  // nextTick(() => {
  //   console.log('treeSelectRefs', treeSelectRefs, treeSelectRefs['1691728346516754434'].getCheckedNodes())
  // })
  if (setCurrentKeyFlag) return
  if (
    (node?.type === '产品类型' &&
      (node.types.findIndex(item => item.value === row.contractType) === -1 || node.feeType !== row.feeType) &&
      props.id) ||
    (node?.type === '产品类型' && formTableData.value.tableData.map(item => item.productId).includes(node.id))
  )
    return
  if (
    row.productId &&
    formTableData.value.tableData.findIndex(item => item.productId === row.productId) !== -1 &&
    node.type === '产品类型' &&
    node.id !== row.productId &&
    row.tempId
  ) {
    proxy.$modal
      .confirm('请注意,更改产品将不保存已填写的信息!') // 可能改为更改合同类型时触发更恰当
      .then(() => {
        changeProd(node, row)
        return
      })
      .catch(action => {
        if (action === 'cancel') {
          // console.log('row.productId', row.productId, treeSelectRefs[row.productId], treeSelectRefs['1710883684173295618'])
          // console.log('row.productId', row.productId)
          setCurrentKeyFlag = 1
          treeSelectRefs[row.productId].setCurrentKey(row.productId)
          nextTick(() => {
            setCurrentKeyFlag = 0
          })
        }
      })
    return
  } else {
    changeProd(node, row)
  }
}
const changeProd = (node, row) => {
  console.log('changeProd', node, row)
  // if (props.id || props.associated.customerId) return // 需要"|| props.associated.customerId"逻辑吗
  if (node.type === '产品类型') {
    row.productParent = node.productParent
    row.isInContract = node.isInContract // 是否在合同中定义
    row.types = node.types || []
    row.productId = node.id // 产品id
    row.productionId = node.id // 产品id XX
    row.name = node.name // 产品名称
    row.productName = node.name // 2023-12-20 产品名称(附加)
    row.quotation = node.quotation // 合同报价
    row.serviceCost = row.isInContract === '1' ? undefined : node.quotation // 合同报价
    // row.serviceCost = 0 // todo000
    row.serviceCost_empty = row.serviceCost ? false : true // _empty作为判断是否要放开disabled以便能手动填入数据的标记
    row.feeType = node.feeType // 收费类型
    nextTick(() => {
      // activeName.value = row.productId
      activeName.value = formTableData.value.tableData[0].productId
    })
    if (props.id) return
    // 清空当前的合同类型
    row.tempId = ''
    row.tempName = ''
    row.contractType = ''
    formTableRef.value.formRef.clearValidate() // 清空校验，但不精准，影响到了其他行（row）
    nextTick(() => {
      treeSelectRefs[row.productId]?.blur() // 无default-expand-all属性时可触发该方法
    })
  }
  // console.log('formTableData', JSON.parse(JSON.stringify(formTableData.value)))
}

// 类型选择
const handleChangeContractType = (value, row) => {
  // activeName.value = formTableData.value.tableData[0].productId
  row.tempId = undefined
  row.tempName = undefined
  getTemplateList(row)
  formTableRef.value.formRef.clearValidate() // 清空校验，但不精准，影响到了其他行（row）
}
function getTemplateList(row) {
  getContractTempList({ pageNum: 1, pageSize: 999, enable: 1, contractType: row.contractType }).then(res => {
    row.contractTempList = res.data.records
    console.log('row.contractTempList', row.contractTempList)
  })
}

// 动态组件的ref绑定
const compRefs = {}
const setCompRef = (el, id) => {
  if (el && id) {
    compRefs[id] = el
  }
  // console.log('setCompRef', compRefs)
}

const isPermission = ref(true) // 可能会有表单结构闪烁后再消失的现象?
// 【变更】【续签】获取原合同内容
const getContractDetail = async id => {
  const { data } = await checkContractChangeIsPermission(id)
  if (data) {
    console.log('data-1', data.contactPerson)
    isPermission.value = true
    const salesSplit = data?.salesRevenue?.split('/') || ''
    Object.assign(formTableData_0.value.tableData[0], data, {
      changeReason: ''
    })
    // 如果是记账合同，甲乙方对调【1/2】
    if (formTableData_0.value.tableData[0].contractType === '0') {
      const temp = Object.assign({}, formTableData_0.value.tableData[0])
      formTableData_0.value.tableData[0].contactPerson = temp.companyPerson
      formTableData_0.value.tableData[0].contactAddress = temp.companyAddress
      formTableData_0.value.tableData[0].contactPhone = temp.companyPhone
      // formTableData_0.value.tableData[0].address =
      formTableData_0.value.tableData[0].companyPerson = temp.contactPerson
      formTableData_0.value.tableData[0].companyAddress = temp.contactAddress
      formTableData_0.value.tableData[0].companyPhone = temp.contactPhone
    }
    // 【变更】【续签】时候只有一个合同，所以直接取数组0位
    Object.assign(formTableData.value.tableData[0], data, {
      salesRevenue: salesSplit.length === 2 && salesSplit[0] === '其他' ? '其他' : data.salesRevenue,
      otherSalesText: salesSplit.length === 2 && salesSplit[0] === '其他' ? salesSplit[1] : ''
    })
    // 【变更】【续签】时候要获取第二步中tab的name，formTableData.value.tableData[0].name 根据 formTableData.value.tableData[0].productId 去从productTreeData.value获取
    nextTick(() => {
      setTimeout(() => {
        // 此时成功绑定 treeSelectRefs
        activeName.value = formTableData.value.tableData[0].productId
        console.log('treeSelectRef', JSON.parse(JSON.stringify(treeSelectRefs)))
        const node = treeSelectRefs[formTableData.value.tableData[0].productId].getCheckedNodes()
        console.log('node', node)
        console.log('node', node[0].name)
        formTableData.value.tableData[0].name = node[0].name
      }, 1)
    })
    getTemplateList(formTableData.value.tableData[0])
    // await handleChangeContractTemplate(formTableData_0.value.tableData[0].tempId)
    // 【转正】合同
    // console.log('formTableData_0.value.tableData[0].isIntention', formTableData_0.value.tableData[0].isIntention)
    console.log('data-9', formTableData.value.tableData[0].contactPerson)
    nextTick(() => {
      setTimeout(() => {
        if (formTableData_0.value.tableData[0].isIntention === '1') {
          // handleStepAfter()
          compRefs[activeName.value].showThisStep()
          active.value = 1
          // console.log('data-99', formTableData.value.tableData[0].contactPerson)
        }
      }, 1)
    })
    // otherSalesTextFun() todo 应该写到哪里
  } else {
    isPermission.value = false
  }
}

// 根据节点id 查询对应id节点
const getTreeNodeById = (treeList, id) => {
  for (let i = 0; i < treeList.length; i++) {
    let node = treeList[i]
    if (node.id === id) {
      return node
    } else {
      if (node.children && node.children.length > 0) {
        let resNode = getTreeNodeById(node.children, id)
        if (resNode) {
          return resNode
        }
      }
    }
  }
}

onMounted(async () => {
  await getAllProducts()
  // 如果存在新建档案后的ids
  if (props.productIds && props.productIds.length > 0) {
    // console.log('productTreeData', productTreeData.value)
    // const nodeList = []
    props.productIds.forEach(async (id, index) => {
      const node = getTreeNodeById(productTreeData.value, id)
      console.log('node', node)
      // nodeList.push(node)
      if (index >= 1) {
        handleAdd()
      }
      await nextTick()
      handleChangeProd(node, formTableData.value.tableData[index])
    })
  }
  if (props.id) {
    // 一次性合同，获取产品父节点的信息
    // 需要等getDetail返回详情信息，所以写一个setInterval
    // 有时间可以把这些逻辑按顺序await处理
    const timer = setInterval(() => {
      if (formTableData_0.value.tableData[0].contractType) {
        clearInterval(timer)
        if (formTableData_0.value.tableData[0].contractType === '1') {
          // console.log('productTreeData.value', productTreeData.value)
          // console.log('formTableData_0.value.tableData[0].productId', formTableData_0.value.tableData[0].productId)
          const node = getTreeNodeById(productTreeData.value, formTableData_0.value.tableData[0].productId)
          formTableData_0.value.tableData[0].productParent = node.productParent
        }
      }
    }, 50)
  }
})

// 在template-create.vue获取还是在template-instance.vue获取
watch(
  () => [props.id, props.associated.customerId],
  async () => {
    // 变更合同 续签合同
    if (props.id) {
      getContractDetail(props.id)
      // row.name
    } else if (props.associated.customerId) {
      // 新建关联合同
      handleSelectCustomer(props.associated)
    }
  },
  { immediate: true }
)

// const tempValue = computed(() => {
//   return props.id ? formData.tempName : formData.tempId
// })

// const tempTypes = computed(() => {
//   return props.id ? contractTypeArr : formData.types
// })
</script>
<style lang="scss">
// 此页面需要回显产品名称，但后端不存，所以通过id从tree中获取，但是需要设置show-checkbox属性，该属性会展示checkbox，通过样式去隐藏
.my-tree-select {
  .el-tree-node__content > label.el-checkbox {
    display: none;
  }
}
</style>
<style lang="scss" scoped>
.my-step {
  .my-container {
    margin: 10px auto 0;
    display: flex;
    justify-content: center;
    .left {
      .my-office-docx {
        height: 580px;
        // padding: 10px;
        overflow-y: scroll;
        width: 860px;
        padding-bottom: 80px;
      }
    }

    .right {
      margin-left: 15px;
    }

    .my-office-form {
      height: 580px;
      padding: 10px;
      overflow-y: scroll;
    }
  }
}

.wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

p {
  font-size: 18px;
}

.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
</style>
