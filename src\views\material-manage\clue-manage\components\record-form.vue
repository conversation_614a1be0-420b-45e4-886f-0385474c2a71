<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-08-15 08:53:27
 * @LastEditTime: 2023-11-09 17:05:54
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :disabled="followStatus === '3'">
    <el-form-item label="跟进记录" prop="content">
      <el-input type="textarea" v-model="formData.content" maxlength="1000" :autosize="{ minRows: 2, maxRows: 4 }"> </el-input>
    </el-form-item>
    <el-form-item label="联系人" prop="contact">
      <el-input v-model="formData.contact" maxlength="20" clearable> </el-input>
    </el-form-item>
    <el-form-item label="跟进方式" prop="type">
      <el-select v-model="formData.type" placeholder="请选择" clearable>
        <el-option label="微信跟进" value="微信跟进" />
        <el-option label="电话跟进" value="电话跟进" />
        <el-option label="线下跟进" value="线下跟进" />
        <el-option label="其他" value="其他" />
      </el-select>
    </el-form-item>
    <div class="footer" v-if="followStatus !== '3'">
      <el-button type="primary" @click="handleSubmit(formRef)" pain>提交</el-button>
      <el-button @click="handleReset(formRef)">清空</el-button>
    </div>
  </el-form>
</template>
<script setup>
import { addClueRecord } from '@/api/material-manage/clue'
const followStatus = inject('followStatus')

const props = defineProps({
  detail: {
    type: Object,
    default: () => {
      return {}
    }
  },
  submitApi: Function
})
const formData = ref({
  contact: props.detail?.contactName
})
const rules = {
  content: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  contact: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  type: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

const emits = defineEmits(['on-success'])

const formRef = ref()
const { proxy } = getCurrentInstance()
const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      if (props.submitApi) {
        const result1 = await props.submitApi({
          ...formData.value,
          ccId: props.detail.id
        })
        if (result1.code === 200) {
          proxy.$modal.msgSuccess('提交成功!')
          emits('on-success')
          formEl.resetFields()
        } else {
          proxy.$modal.msgError('提交失败!')
        }
      } else {
        const result = await addClueRecord({
          ...formData.value,
          ccId: props.detail.id
        })
        if (result.code === 200) {
          proxy.$modal.msgSuccess('提交成功!')
          emits('on-success')
          formEl.resetFields()
        } else {
          proxy.$modal.msgError('提交失败!')
        }
      }
    } else {
    }
  })
}

const handleReset = formEl => {
  if (!formEl) return
  formEl.resetFields()
}
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
.footer {
  display: flex;
  // justify-content: center;
  align-item: center;
  .el-button {
    flex: 1;
  }
}
</style>
