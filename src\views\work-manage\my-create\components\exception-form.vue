<!--
 * @Description: 标记异常
 * @Author: thb
 * @Date: 2023-11-09 09:47:49
 * @LastEditTime: 2023-11-09 09:55:13
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 6 }"
            maxlength="1000"
            placeholder=" 请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
const formData = ref({
  remark: ''
})

const rules = {
  remark: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped></style>
