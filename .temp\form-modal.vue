<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    top="5vh"
    destroy-on-close
  >
  <el-button
      @click="handleEditSelf"
      >编辑提报信息</el-button
    >
  </el-dialog>
</template>

<script setup lang="jsx">
const emit = defineEmits()
const handleEditSelf = () => {
  console.log('handleEditSelf')
  emit('edit', { id: formData.id })
}
</script>
