<template>
  <div class="main-wrap">
    <ProTable
      ref="proTable"
      title="列表"
      :init-param="initParam"
      :columns="columns"
      :toolButton="false"
      rowKey="id"
      :request-api="customerUserFlowAnalyse"
    >
      <template #tableHeader>
        <el-button :icon="Download" @click="handleExport" v-hasPermi="['customer:import-process:export']">导出</el-button>
        <!--  -->
      </template>
      <template #tabs>
        <el-radio-group :model-value="initParam.role" @change="handleRadioChange">
          <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{
            item.dictLabel
          }}</el-radio-button>
        </el-radio-group></template
      >
    </ProTable>
  </div>
</template>

<script setup lang="tsx">
import { customerUserFlowAnalyse, customerUserFlowAnalyseExport } from '@/api/customer/flow-analyse'
import { ref, reactive, nextTick } from 'vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { Download } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { deptTreeSelect } from '@/api/system/user'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const tabs = [
  { dicValue: 'manager', dictLabel: '财税顾问' },
  { dicValue: 'customer_success', dictLabel: '客户成功' },
  { dicValue: 'sponsor_accounting', dictLabel: '主办会计' },
  { dicValue: 'counselor', dictLabel: '开票员' }
]

const proTable = ref()
const initParam = reactive({ role: 'manager' }) //

/** 查询部门下拉树结构 */
const getDeptTree = async () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await deptTreeSelect({})
    resolve({ data })
  })
}

const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'userName',
    label: '人员名称',
    minWidth: '120',
    search: { el: 'input' }
  },
  {
    prop: 'deptName',
    label: '所属部门',
    minWidth: '120'
  },
  {
    prop: 'deptId',
    label: '所属部门',
    minWidth: '120',
    isShow: false,
    enum: getDeptTree,
    search: {
      el: 'tree-select',
      props: {
        'check-strictly': true,
        'default-expand-all': true,
        filterable: true,
        props: { value: 'id', label: 'label', children: 'children' }
      }
    }
  },
  {
    prop: 'month',
    label: '分析月份',
    minWidth: '120',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    },
    render: scope => {
      return <span>{scope.row.month ? `${scope.row.month}月` : '--'}</span>
    }
  },
  {
    prop: 'monthStart',
    label: '期初',
    minWidth: '100',
    render: scope => {
      return <span>{scope.row.monthStart ? `${scope.row.monthStart}家` : '--'}</span>
    }
  },
  {
    prop: 'newRelate',
    label: '新接',
    minWidth: '100',
    render: scope => {
      return <span>{scope.row.newRelate ? `${scope.row.newRelate}家` : '--'}</span>
    }
  },
  {
    prop: 'normalLose',
    label: '正常流失',
    minWidth: '100',
    render: scope => {
      return <span>{scope.row.normalLose ? `${scope.row.normalLose}家` : '--'}</span>
    }
  },
  {
    prop: 'cancelLose',
    label: '注销',
    minWidth: '100',
    render: scope => {
      return <span>{scope.row.cancelLose ? `${scope.row.cancelLose}家` : '--'}</span>
    }
  },
  {
    prop: 'migrateIn',
    label: '迁入',
    minWidth: '100',
    render: scope => {
      return <span>{scope.row.migrateIn ? `${scope.row.migrateIn}家` : '--'}</span>
    }
  },
  {
    prop: 'migrateOut',
    label: '迁出',
    minWidth: '100',
    render: scope => {
      return <span>{scope.row.migrateOut ? `${scope.row.migrateOut}家` : '--'}</span>
    }
  },
  {
    prop: 'monthEnd',
    label: '期末',
    minWidth: '100',
    render: scope => {
      return <span>{scope.row.monthEnd ? `${scope.row.monthEnd}家` : '--'}</span>
    }
  },
  {
    prop: 'userStatus',
    label: '用户状态',
    isColShow: false,
    isShow: false,
    search: {
      el: 'select'
    },
    enum: [
      { label: '正常', value: 0 },
      { label: '停用', value: 1 }
    ]
  }
]

const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.role = e
}

// 导出列表功能
const handleExport = async () => {
  const result = await customerUserFlowAnalyseExport({
    ...initParam,
    ...proTable.value.searchParam
  })
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
