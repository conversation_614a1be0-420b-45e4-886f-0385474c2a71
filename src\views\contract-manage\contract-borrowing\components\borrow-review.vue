<!--
 * @Description: 借阅审批
 * @Author: thb
 * @Date: 2023-07-12 14:24:18
 * @LastEditTime: 2023-09-01 14:12:59
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center title="借阅审批" width="1200" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <div class="t-container">
      <div class="t-left">
        <div style="height: 20px; margin-bottom: 24px">
          <span class="name">{{ detail.contractNo }}</span>
          <!-- <span class="number"> {{ detail.contractNo }} </span> -->
          <el-tag>{{ detail.contractNo }} </el-tag>
        </div>
        <iframe :src="urlPreview" frameborder="no" style="width: 100%; height: calc(100% - 90px)" scrolling="auto" />

        <el-button :icon="ZoomIn" @click="previewShow = true">放大合同</el-button>
      </div>
      <div class="t-right">
        <el-form label-position="top" label-width="100px" ref="formRef" :model="detail" :rules="rules">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="关联客户">
                <el-input v-model="detail.customerName" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户编码">
                <el-input v-model="detail.customerNo" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="借阅人">
                <SelectTree v-model="detail.userId" placeholder="请选择" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="借阅到期">
                <el-date-picker
                  v-model="detail.expirationTime"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  type="date"
                  disabled
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="借阅事由">
            <el-input
              v-model="detail.borrowReason"
              maxlength="1000"
              disabled
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
          <el-form-item label="通过" prop="reviewStatus">
            <el-radio-group v-model="detail.reviewStatus">
              <el-radio label="1">通过</el-radio>
              <el-radio label="2">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="驳回原因" prop="reason" v-if="detail.reviewStatus === '2'">
            <el-input v-model="detail.reason" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
          </el-form-item>
        </el-form>
        <!-- 审批流程 -->
        <Collapse title="审批记录">
          <processSet
            v-if="detail.reviewList?.length"
            type="detail"
            :isReview="true"
            :isHorizontal="true"
            :createTime="detail.createTime"
            v-model="detail.reviewList"
        /></Collapse>
      </div>
    </div>

    <template #footer>
      <el-button type="primary" @click="handleSave(formRef)">保存</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
  <iFrame :src="detail?.file?.urls" v-model="previewShow" />
</template>
<script setup>
import { getBorrowContractDetail, handleBorrowReviewAudit } from '@/api/contract/contract.js'
import { usePreview } from '@/hooks/usePreview'
import processSet from '@/views/process-manage/contract-review/components/process-set.vue'
import Collapse from '@/components/Collapse'
import SelectTree from '@/components/SelectTree'
import iFrame from '@/components/iFrame'
import { ZoomIn } from '@element-plus/icons-vue'
const previewShow = ref(false)
const urlPreview = ref('')
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number
})

// 表单校验
const formRef = ref()
const { proxy } = getCurrentInstance()
const handleSave = formEl => {
  if (!formEl) return
  formEl.validate(async valid => {
    if (valid) {
      const result = await handleBorrowReviewAudit({
        mainId: detail.value.id, // 合同id
        reviewStatus: detail.value.reviewStatus, // '1'表示通过 '2'表示驳回
        reason: detail.value.reason,
        type: '1' // 合同借阅
      })
      if (result.code === 200) {
        proxy.$modal.msgSuccess(`保存成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`保存失败!`)
      }
    }
  })
}

// 获取借阅详情
const detail = ref({})
const rules = {
  reviewStatus: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  reason: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ]
}
const getDetail = async () => {
  const { data } = await getBorrowContractDetail(props.id)
  detail.value = data || {}
  detail.value.reviewStatus = '1'
  if (data?.file?.urls) {
    const previewUrl = await usePreview(data?.file?.urls)
    urlPreview.value = previewUrl
  }
}
getDetail()
</script>
<style lang="scss" scoped>
.t-container {
  display: flex;

  .t-left {
    // flex: 1.5;
    width: 500px;
    padding-right: 16px;
    border-right: 1px solid #e8e8e8ff;
    margin-right: 24px;
  }
  .t-right {
    flex: 1;
    // flex-shrink: 0;
  }
}
:deep(.process-wrap) {
  justify-content: left;
  margin-top: 40px;
}
.name {
  color: #333;
  font-size: 18px;
  margin-right: 14px;
}

.number {
  color: black;
  font-size: 14px;
}

.collapse {
  margin-bottom: 20px;
}

:deep(.el-collapse-item__wrap) {
  background: #f3f4f7;
  .el-collapse-item__content {
    height: 262px;
    display: flex;
    padding: 0 24px;
    align-items: center;
  }
}

.el-select {
  width: 100%;
}
:deep(.el-date-editor.el-date-editor--date) {
  width: 100%;
}
</style>
