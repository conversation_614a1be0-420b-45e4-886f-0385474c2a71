<template>
  <ProTable
    :init-param="initParam"
    ref="proTable"
    row-key="id"
    :columns="columns"
    :request-api="riskCustomerAuditList"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <!-- 表格 header 按钮 -->
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handleDeal(scope.row)">办理</el-button>
    </template>
  </ProTable>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    :hideActionBtnForRiskCustomer="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" @edit-self="editSelf" />
  <formModal
    v-if="formModalShowSelf"
    ref="formModalRefSelf"
    @close="formModalCloseSelf"
    @ok="getList"
    @update-self="updateSelf"
  />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { riskCustomerAuditList } from '@/api/customer/risk.js'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from '@/views/customer/risk-audit/components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
import { useDic } from '@/hooks/useDic'
import useCommonStore from '@/store/modules/common'
import { CirclePlus } from '@element-plus/icons-vue'
import { reasonDict, stageDict } from '@/utils/constants.js'
import { log } from 'mathjs'

const userStore = useUserStore()
const useCommon = useCommonStore()
const { getDic } = useDic()
const { proxy } = getCurrentInstance()

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}
const initParam = reactive({})
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'customerName',
    label: '客户名称',
    search: { el: 'input' },
    minWidth: 300,
    sortable: 'custom',
    sortName: 'risk.customer_id'
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'recentContractExpirationDate',
    label: '最近合同到期日期',
    width: 150
  },
  {
    prop: 'reason',
    label: '风险原因',
    width: 180,
    enum: reasonDict,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true }
    },
    render: scope => {
      return <span>{scope.row.reason || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'risk.reason'
  },
  {
    prop: 'remark',
    label: '说明',
    minWidth: 200
  },
  {
    prop: 'createBy',
    label: '创建人',
    search: { el: 'input' },
    width: 150,
    sortable: 'custom',
    sortName: 'risk.create_by'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180,
    search: {
      el: 'date-picker',
      props: { type: 'daterange', valueFormat: 'YYYY-MM-DD' }
    },
    sortable: 'custom',
    sortName: 'risk.create_time'
  },
  {
    prop: 'stage',
    label: '当前阶段',
    width: 200,
    enum: stageDict[0].children.slice(0, -1),
    search: {
      el: 'select'
    },
    render: scope => {
      return <span>{scope.row.stage || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'risk.stage'
  },
  {
    prop: 'handleUserName',
    label: '办理人',
    width: 150
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 100
  }
]

// 自定义
const transformRequestParams = (data: any) => {
  // if (data.createTime) {
  //   data.createTimeStart = data.createTime[0]
  //   data.createTimeEnd = data.createTime[1]
  // }
}

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)
const formModalClose = () => {
  formModalShow.value = false
}
const handleDeal = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDeal(row)
  })
}

// ------------main弹窗Self---------------
const formModalRefSelf = ref()
const formModalShowSelf = ref(false)
const formModalCloseSelf = () => {
  formModalShowSelf.value = false
}
const editSelf = (data: any) => {
  formModalShowSelf.value = true
  nextTick(() => {
    formModalRefSelf.value.onEdit(data)
  })
}
const updateSelf = (data: any) => {
  // console.log('updateSelf')
  formModalRef.value.onRefresh(data)
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

// onMounted(() => {
// })
</script>
<style lang="scss" scoped></style>
