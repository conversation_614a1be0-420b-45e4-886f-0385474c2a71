<!--
 * @Description: 合同变更
 * @Author: thb
 * @Date: 2023-07-10 13:27:29
 * @LastEditTime: 2024-03-11 10:58:36
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    :title="isRenewal ? '续签合同' : '变更合同'"
    :width="isPermission ? 1200 : 800"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <!-- 头部信息 -->
    <template v-if="isPermission">
      <div class="container">
        <div class="left">
          <p class="p-t">
            原合同文件: <span class="p-t-small">{{ detail.contractNo }}</span>
          </p>
          <!-- <p class="p-t p-t-small">{{ detail.contractNo }}</p> -->
          <iframe :src="urlPreview" frameborder="no" style="width: 100%; height: 100%" scrolling="auto" />
        </div>
        <div class="right">
          <p class="p-t" v-if="!isRenewal">变更合同信息</p>
          <p class="p-t" v-else>续签合同信息</p>
          <!-- 根据合同类型去动态渲染对应的合同表单组件 -->
          <component
            :isChange="true"
            :changeDisabled="true"
            :rowChange="true"
            :isRenewal="isRenewal"
            :productTreeData="productTreeData"
            :ref="el => setCompRef(el, detail.contractId)"
            :is="formMap[detail.contractTypeName]"
            v-model="detail"
          />
        </div>
      </div>
    </template>
    <template v-else>
      <div class="wrap">
        <el-icon :size="40"><Lock /></el-icon>
        <p>您暂无权限对本合同进行变更</p>
      </div>
      <!-- <checkPermission @on-success="handleClose" />  -->
    </template>
    <template #footer>
      <template v-if="isPermission">
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </template>

      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { Lock } from '@element-plus/icons-vue'
import { getBusinessList } from '@/api/business/business'
import { usePreview } from '@/hooks/usePreview'
import accountingForm from './accounting-form.vue'
import oneOff from './one-off.vue'
import addressService from './address-service.vue'
import checkPermission from './check-permission.vue'
import { getContractDetailById, checkContractDetailIsPermission, checkContractChangeIsPermission } from '@/api/contract/contract'
import { cloneDeep } from 'lodash'
import { saveBatchContract } from '@/api/contract/contract'
import { changeFileData } from '@/utils/common'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}
const props = defineProps({
  id: Number,
  isRenewal: {
    type: Boolean,
    default: false
  }
})
const formMap = {
  记账合同: accountingForm,
  一次性合同: oneOff,
  地址服务协议合同: addressService
}
const detail = ref({})
const urlPreview = ref('')
const isPermission = ref(false)
const getContractDetail = async id => {
  const { data } = await checkContractChangeIsPermission(id)
  if (data) {
    isPermission.value = true
    const { isInContract, feeType, salesRevenue } = data
    const salesSplit = salesRevenue?.split('/') || ''
    console.log('salesSplit', salesRevenue, salesSplit)
    if (data.fileList && data.fileList.length > 0) {
      data.file = data.fileList.at(-1)
    }
    const file = data.file
    if (file) {
      delete file.id
    }

    detail.value =
      Object.assign(data, {
        file: file, // 对上传的文件进行处理file
        salesRevenue: salesSplit.length === 2 && salesSplit[0] === '其他' ? '其他' : salesRevenue,
        otherSalesText: salesSplit.length === 2 && salesSplit[0] === '其他' ? salesSplit[1] : '',
        rowData: {
          isInContract,
          feeType
        },
        changeReason: '' // 变更的时候默认为空
      }) || {}
    // 后续恢复以下注释
    if (data?.file?.urls) {
      const previewUrl = await usePreview(data?.file?.urls)
      urlPreview.value = previewUrl
    }

    // 如果是续签,只需要修改detail中的信息即可，修改合同起始时间、合同结束时间以及 服务月份,同时将活动优惠置空
    console.log('props.isRenewal', props.isRenewal)
    if (props.isRenewal) {
      // detail.value.startTime = detail.value.endTime
      //地址
      if (detail.value.contractType === '2') {
        // 合同的起始时间修改逻辑：合同里的结束之间是到最后一天的，所以如果没有优惠的话续签要从结束时间的后一天开始，
        detail.value.startTime = dayjs(detail.value.endTime).add(1, 'day').format('YYYY-MM-DD')
        // 单位是年
        detail.value.monthNum = 1
        // 默认原来结束日期前一天
        detail.value.endTime = dayjs(detail.value.endTime).add(1, 'year').add(1, 'day').add(-1, 'day').format('YYYY-MM-DD')
      }
      // 记账
      if (detail.value.contractType === '0') {
        // 合同的起始时间修改逻辑：合同里的结束之间是到最后一天的，所以如果没有优惠的话续签要从结束时间的下个月开始，有优惠的话加上优惠时间（或参加活动的结束时间）后一个月开始
        const { discount } = detail.value
        if (discount) {
          if (discount === '时长优惠') {
            // endTime加上时长优惠
            detail.value.startTime = dayjs(detail.value.endTime)
              .add(detail.value.discountTime, 'month')
              .add(1, 'month')
              .format('YYYY-MM')
          }

          if (discount === '活动优惠') {
            if (detail.value.activityDiscountTime) {
              detail.value.startTime = dayjs(detail.value.startTime)
                .add(detail.value.activityDiscountTime, 'month')
                .add(-1, 'day')
                .add(1, 'month')
                .format('YYYY-MM')
            } else {
              detail.value.startTime = dayjs(detail.value.endTime).add(1, 'month').format('YYYY-MM')
            }
          }
        } else {
          // 若没有
          detail.value.startTime = dayjs(detail.value.endTime).add(1, 'month').format('YYYY-MM')
        }

        detail.value.monthNum = 12
        detail.value.endTime = dayjs(detail.value.startTime).add(12, 'month').add(-1, 'month').format('YYYY-MM')
      }
      // 活动优惠相关数据置空
      Object.assign(detail.value, {
        activityId: '',
        activityTxt: '',
        activityQuotation: '',
        activityDiscountTime: '',
        remark: '',
        // 优惠选择项
        discount: '',
        discountTime: ''
      })
    } else {
      // 如果获取详情中 detail中如果discount ==='时长优惠' 且 discountTime有值
      if (detail.value.discount === '时长优惠' && detail.value.discountTime) {
        detail.value.discountDisabled = true
      } else {
        detail.value.discountDisabled = false
      }
    }
  } else {
    isPermission.value = false
  }
}
getContractDetail(props.id)

const compRefs = {}
const setCompRef = (el, id) => {
  if (el && id) {
    compRefs[id] = el
  }
}
const { proxy } = getCurrentInstance()
const handleSubmit = async () => {
  // 注意提示
  // 变更合同的逻辑
  if (!props.isRenewal) {
    ElMessageBox.confirm(
      `<p>
      请注意!
    </p>
    <p>
      提交变更合同后，原合同将在变更合同审批通过后自动终止，与原合同关联的账单将被关闭并生成新的账单
    </p>
    `,
      '提示',
      {
        confirmButtonText: '继续提交',
        cancelButtonText: '返回修改',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }
    )
      .then(async () => {
        // 继续提交
        console.log('compRef', compRefs)
        // 遍历循环compRefs 进行校验
        for (let [contractId, comRef] of Object.entries(compRefs)) {
          console.log('key', contractId)
          console.log('value', comRef)
          const result = await comRef.validateForm()
          console.log('result', result)
          if (!result) {
            // 如果检验没成功，需要提示
            return
          }
        }

        const queryData = cloneDeep(detail.value)
        if (queryData.contractTypeName === '记账合同') {
          queryData.startTime = queryData.startTime + '-01'
          queryData.endTime = queryData.endTime + '-01'
        }
        const contractId = queryData.contractId
        // 删除原有contractId
        delete queryData.contractId

        // changeStartTime 分yyyy-mm 或者yyyy-mm-dd
        let length = 0

        if (queryData.changeStartTime) {
          length = queryData.changeStartTime.split('-').length
        }
        // const formDataTemp = Object.assign({}, formData, {
        //   changeStartTime: formData.changeStartTime
        //     ? length === 2
        //       ? formData.changeStartTime + '-01'
        //       : formData.changeStartTime
        //     : ''
        // })
        const data = await saveBatchContract([
          {
            ...queryData,
            originId: contractId,
            file: Array.isArray(queryData.file) && queryData.file[0] ? changeFileData(queryData.file) : queryData.file,
            bizType: '1', // '1'为变更合同
            salesRevenue: queryData.salesRevenue === '其他' ? '其他/' + queryData.otherSalesText : queryData.salesRevenue,
            changeStartTime: queryData.changeStartTime
              ? length === 2
                ? queryData.changeStartTime + '-01'
                : queryData.changeStartTime
              : ''
          }
        ])
        if (data.code === 200) {
          // 说明变更成功
          proxy.$modal.msgSuccess(`提交成功!`)
          handleClose()
          emits('on-success')
        } else {
          proxy.$modal.msgError(`提交失败!`)
        }
      })
      .catch(() => {
        // 返回修改
      })
  } else {
    // 续签合同的逻辑 相当于新增

    // 遍历循环compRefs 进行校验
    for (let [contractId, comRef] of Object.entries(compRefs)) {
      console.log('key', contractId)
      console.log('value', comRef)
      const result = await comRef.validateForm()
      console.log('result', result)
      if (!result) {
        // 如果检验没成功，需要提示
        return
      }
    }

    const queryData = cloneDeep(detail.value)
    if (queryData.contractTypeName === '记账合同') {
      queryData.startTime = queryData.startTime + '-01'
      queryData.endTime = queryData.endTime + '-01'
    }
    const contractId = queryData.contractId
    // 删除原有contractId
    delete queryData.contractId

    const data = await saveBatchContract([
      {
        ...queryData,
        // originId: contractId,
        bizType: undefined,
        originId: undefined,
        ciId: queryData.ciId,
        file: Array.isArray(queryData.file) && queryData.file[0] ? changeFileData(queryData.file) : queryData.file,
        type: '0', // 录入合同,
        salesRevenue: queryData.salesRevenue === '其他' ? '其他/' + (queryData.otherSalesText || '') : queryData.salesRevenue
      }
    ])
    if (data.code === 200) {
      // 说明续签成功
      proxy.$modal.msgSuccess(`续签成功!`)
      handleClose()
      emits('on-success')
    } else {
      proxy.$modal.msgError(`续签失败!`)
    }
  }
}

// 获取所有的产品名称
const productTreeData = ref()
const getAllProducts = async () => {
  const { data } = await getBusinessList({
    pageNum: 1,
    pageSize: 10000
  })
  // 将后端传回的数据结构进行转换
  const revertData = []
  data.forEach(item => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      children: []
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach(child => {
        obj.children.push({
          name: child.productName,
          types:
            item.contractType?.split(',')?.map(item => {
              return {
                label: item,
                value: item
              }
            }) || [],
          type: '产品类型',
          quotation: child.quotation, // 合同报价
          isInContract: child.isInContract, // 是否在合同中定义
          feeType: child.feeType, //收费类型
          id: child.id // 产品类型id
        })
      })
    }
  })

  console.log('revertData', revertData)
  productTreeData.value = revertData || []
}

getAllProducts()
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  .left {
    flex: 1;
    margin-right: 24px;
  }
  .right {
    flex: 1;
  }
}
.p-t {
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
}
.p-t-small {
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #2383e7;
}

.wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
p {
  font-size: 18px;
}
</style>
