// 业务管理api
import request from '@/utils/request'

// 获取业务列表
export function getBusinessList(params) {
  return request({
    url: '/businessType/tree',
    method: 'get',
    params
  }).then(response => {
    // console.log('params', params)
    if (params.noFilterFlag) {
      return response
    } else {
      const originalData = response.data // 原始数据
      const filteredData = originalData.filter(item => item.enable) // 过滤数据
      filteredData.forEach(item => {
        item.child = item.child.filter(item => item.enable)
      })
      // console.log('response', response, originalData, filteredData)
      return Object.assign(response, { data: filteredData }) // 返回过滤后的数据
    }
  })
}

// 新增或者编辑业务
export function addBusiness(query) {
  return request({
    url: '/businessType/saveOrUpdate',
    method: 'post',
    data: query
  })
}

// 通过业务id获取业务详情
export function getBusinessDetailById(id) {
  return request({
    url: '/businessType/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 通过业务id获取业务详情（实际）
export function getBusinessProductGetById(id) {
  return request({
    url: '/businessProduct/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 删除业务
export function removeBusiness(id) {
  return request({
    url: '/businessType/delete',
    method: 'delete',
    params: {
      id
    }
  })
}

// 新增或者 编辑 产品
export function addProduct(query) {
  return request({
    url: '/businessProduct/saveOrUpdate',
    method: 'post',
    data: query
  })
}

// 根据产品id查询启用的活动列表
export function getActivityListByProductId(params) {
  return request({
    url: '/businessProduct/getActivityListByProductId',
    method: 'get',
    params
  })
}

// 通过产品的id 获取产品详情
export function getProductDetailById(id) {
  return request({
    url: '/businessProduct/getById',
    method: 'get',
    params: {
      id
    }
  })
}
// 根据产品id 删除产品
export function deleteProduct(id) {
  return request({
    url: '/businessProduct/delete',
    method: 'delete',
    params: {
      id
    }
  })
}
