<template>
  <div class="main-wrap">
    <ProTable
      ref="proTable"
      title="列表"
      :init-param="initParam"
      :columns="columns"
      :toolButton="false"
      rowKey="id"
      :request-api="getContractTempList"
    >
      <template #tabs>
        <el-radio-group :model-value="initParam.expireType" @change="handleRadioChange">
          <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{
            item.dictLabel
          }}</el-radio-button>
        </el-radio-group></template
      >
      <template #tempName="{ row }">
        <span class="blue-text" @click="handleDetail(row)">{{ row.tempName }}</span>
      </template>
      <template #tableHeader="scope">
        <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
        <el-button type="danger" :icon="Delete" @click="handleDelete(scope.selectedListIds)">删除</el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx">
import {
  getContractTempList,
  postContractTempEnable,
  deleteContractTempDeleteByIds
} from '@/api/contract-template/contract-template'
import { ref, reactive, nextTick } from 'vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { contractTypeArr } from '@/utils/constants.js'
import { useHandleData } from '@/hooks/useHandleData'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const proTable = ref()

const initParam = reactive({
  expireType: 0
})
const tabs = [
  {
    dictLabel: '全部',
    dicValue: 0
  },
  {
    dictLabel: '办理中',
    dicValue: 1
  },
  {
    dictLabel: '已完成',
    dicValue: 2
  },
  {
    dictLabel: '已作废',
    dicValue: 3
  }
]

const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'createTime',
    label: '企业名称',
    minWidth: '200',
    search: { el: 'input' }
  },
  {
    prop: 'createTime',
    label: '企业编号',
    minWidth: '200',
    search: { el: 'input' }
  },
  {
    prop: 'createTime',
    label: '业务名称',
    minWidth: '200',
    enum: [
      {
        label: '000',
        value: '0'
      },
      {
        label: '111',
        value: '1'
      },
      {
        label: '222',
        value: '2'
      }
    ],
    search: { el: 'select' }
  },
  {
    prop: 'createTime',
    label: '流程编号',
    minWidth: '200',
    search: { el: 'input' }
  },
  {
    prop: 'createTime',
    label: '办理阶段',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '当前办理人',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '财税顾问',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '创建人',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '最近更新时间',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '关联合同',
    minWidth: '200',
    fixed: 'right'
  }
]

const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.expireType = e
}

function handleAdd() {
  router.push({ path: `/contract-template/form`, query: { redirect: route.fullPath } })
}

const handleDelete = async (ids: string[]) => {
  if (!ids.length) return
  await useHandleData(deleteContractTempDeleteByIds, ids, '删除所选合同模板信息')
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
}
function handleDetail(row: any) {
  console.log('handleDetail', row)
  router.push({ path: `/contract-template/detail`, query: { id: row.id } })
}
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
