<!--
 * @Description: row-check
 * @Author: thb
 * @Date: 2023-09-28 09:23:40
 * @LastEditTime: 2023-12-21 13:23:51
 * @LastEditors: thb
-->
<template>
  <el-col :span="12">
    <el-checkbox :disabled="disabled" v-if="!flagKey" v-model="data[labelKey + 'Flag']" />
    <el-checkbox :disabled="disabled" v-else v-model="data[flagKey]" />
    <el-form-item :label="label" :prop="labelKey">
      <FileUploadBiz
        v-model="data[labelKey]"
        :disabled="flagKey ? !data[flagKey] : !data[labelKey + 'Flag']"
        :limit="10"
        :isShowTip="false"
        v-if="!disabled"
        @on-load-success="validateFormField(labelKey)"
      />
      <template v-else>
        <!-- <template v-for="(file, index) in data[labelKey]" :key="index">
          <div class="download-text" @click="previewFile(file)">{{ file?.fileNames || '暂无文件' }}</div>
        </template> -->
        <fileList :list="data[labelKey]" />
      </template> </el-form-item
  ></el-col>
</template>
<script setup>
import FileUploadBiz from '@/components/FileUploadBiz'
import fileList from './file-list'
defineProps({
  label: {
    type: String,
    default: ''
  },
  labelKey: {
    type: String,
    default: ''
  },
  flagKey: {
    type: String,
    default: ''
  },
  data: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const disabled = inject('disabled')
const emits = defineEmits('on-preview', 'on-load-success')
const previewFile = file => {
  emits('on-preview', file)
}

// 文件上传成功后检验
const validateFormField = field => {
  emits('on-load-success', field)
}
</script>
<style lang="scss" scoped></style>
