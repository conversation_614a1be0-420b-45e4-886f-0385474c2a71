<!--
 * @Description: 日记账
 * @Author: thb
 * @Date: 2023-09-06 15:11:27
 * @LastEditTime: 2023-10-30 08:35:30
 * @LastEditors: thb
-->
<template>
  <div class="main-wrap">
    <el-row :gutter="20" style="height: 100%">
      <el-col :span="5" :xs="24">
        <div class="left-con">
          <el-input
            v-model="receiptMethodFilter"
            placeholder="请输入账户名称"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
          <el-tree
            style="height: 100%"
            ref="receiptMethodRef"
            :data="receipt_method"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            node-key="value"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <el-col :span="19" :xs="24">
        <div class="right-con">
          <ProTable
            ref="proTable"
            title="日记账"
            :init-param="initParam"
            :columns="columns"
            :toolButton="false"
            :request-api="getDayBooKListAnalysis"
            :data-callback="handleSearch"
            :requestAuto="false"
            :transformRequestParams="transformRequestParams"
          >
            <template #index="{ $index }">
              <span>
                {{ $index !== 0 ? $index : '' }}
              </span>
            </template>
            <template #yearMonth="{ row }">
              <span>{{ row.receiptDate ? row.receiptDate.slice(0, 7) : '--' }}</span>
            </template>
            <template #day="{ row }">
              <span>{{ row.receiptDate ? row.receiptDate.slice(8) : '--' }}</span>
            </template>
            <template #projectName="{ row }">
              <span>{{ row.projectName || '--' }}</span>
            </template>
            <template #digest="{ row }">
              <span v-if="row.digest === '期初金额'" class="blue-text" @click="handleEditInitial()">{{
                row.digest || '--'
              }}</span>
              <span v-else>{{ row.digest || '--' }}</span>
            </template>
            <!-- analysisBalance -->
            <template #analysisBalance="{ row }">
              <span>{{ row.analysisBalance }}</span>
            </template>
            <!-- 收款单 -->
            <template #receiptNo="{ row }">
              <span v-if="row.receiptId" class="blue-text" @click="handleShowCollectionDetail(row.receiptId)">{{
                row.receiptNo
              }}</span>
              <span v-else>--</span>
            </template>
          </ProTable>
          <el-row class="footer">
            <el-col :span="20">
              <el-row :gutter="20">
                <el-col :span="6">合计：</el-col>
                <el-col :span="6">借方：{{ footerData.incomeAmount === 0 ? 0 : footerData.incomeAmount || '--' }} 元</el-col>
                <el-col :span="6"> 贷方：{{ footerData.paymentAmount === 0 ? 0 : footerData.paymentAmount || '--' }} 元</el-col>
                <el-col :span="6"> 余额：{{ footerData.balance === 0 ? 0 : footerData.balance || '--' }} 元</el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
  </div>
  <div v-show="false">
    <!-- <InputRange></InputRange> -->
  </div>
  <!-- <ImportExcel ref="dialogRef" :yearShow="true" /> -->
</template>
<script setup lang="tsx">
import { nextTick, reactive, ref, watch } from 'vue'
import { ColumnProps } from '@/components/ProTable/interface'
import {
  getDayBooKListAnalysis,
  // deleteDayBook,
  // addOrUpdateDayBook,
  getDayBookById,
  journalImport,
  saveOrUpdateInitialAmountAnalysis
} from '@/api/finance/day-book'
import { CirclePlus, Upload } from '@element-plus/icons-vue'
// import ImportExcel from '@/components/ImportExcel/index.vue'
import { getBusinessList } from '@/api/business/business'
import { getBookkeepingSubjectTreeList } from '@/api/basicData/basicData'
import { useHandleData } from '@/hooks/useHandleData'
import { useDialog } from '@/hooks/useDialog'
import { useDialog as useDialogFinance } from '@/hooks/useDialogFinance'
import dayjs from 'dayjs'
// import dayForm from './components/day-form'
import dayFormInitial from './components/day-form-initial'
import { getDayBookStatisticDataByMonth } from '@/api/finance/day-book'
import { getReviewerTreeData } from '@/api/process/process'
import { useDic } from '@/hooks/useDic'
import collectionForm from '@/views/finance/collection-ledger/components/form.vue'
import { getFinanceReceiptGetById } from '@/api/finance/collection-ledger'
import InputRange from '@/views/finance/day-book-statement/components/input-range'

const { getDic } = useDic()
const { proxy } = getCurrentInstance()
const { receipt_method } = proxy.useDict('receipt_method')

const { showDialog } = useDialog()
const { showDialog: showDialogFinance } = useDialogFinance()
const initParam = reactive({ initialAmountFlag: true, receiptMethod: undefined })

const convertData = (data: any, revertData: any) => {
  data.forEach((item: any) => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      child: [] as any
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach((child: any) => {
        obj.child.push({
          name: child.productName,
          type: '产品类型',
          id: child.id // 产品类型id
        })
      })
    }
  })
}
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '',
    width: 60
  },
  {
    prop: 'yearMonth',
    label: '月',
    width: 150,
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'day',
    label: '日',
    width: 100,
    search: {
      el: 'date-picker',
      props: {
        type: 'date',
        valueFormat: 'DD',
        format: 'DD'
      }
    }
  },
  {
    prop: 'projectName',
    label: '对方科目',
    width: 200,
    enum: () => {
      return new Promise(async (resolve, reject) => {
        const { data } = await getBusinessList({
          pageSize: 1000,
          pageNum: 1
        })
        // 将后端传回的数据结构进行转换
        const revertData: any = []
        convertData(data, revertData)
        const result = await getBookkeepingSubjectTreeList({
          enable: 1,
          pageSize: 1000,
          pageNum: 1
        })
        console.log('result', result.data)
        resolve({
          data: revertData.concat(result.data)
        })
      })
    },
    fieldNames: {
      label: 'name',
      // value: 'id',
      value: 'name',
      children: 'child'
    },
    search: {
      el: 'tree-select',
      props: {
        'check-strictly': true
      }
    }
  },
  {
    prop: 'digest',
    minWidth: 200,
    label: '摘要',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'incomeAmount',
    width: 150,
    label: '收入(借方)金额(元)',
    search: {
      render: ({ searchParam }) => {
        console.log(
          '===incomeAmount-index===',
          searchParam.incomeAmount,
          searchParam.incomeAmount && JSON.parse(JSON.stringify(searchParam.incomeAmount))
        )
        return <InputRange vModel={searchParam.incomeAmount} />
      }
    }
  },
  {
    prop: 'paymentAmount',
    width: 150,
    label: '付出(贷方)金额(元)',
    search: {
      render: ({ searchParam }) => {
        return <InputRange vModel={searchParam.paymentAmount} />
      }
    }
  },
  {
    prop: 'analysisBalance',
    width: 150,
    label: '余额(元)'
  }
]

const proTable = ref()
// 自定义
const transformRequestParams = (data: any) => {
  if (data.incomeAmount) {
    data.incomeAmountMin = data.incomeAmount[0]
    data.incomeAmountMax = data.incomeAmount[1]
  }

  if (data.paymentAmount) {
    data.paymentAmountMin = data.paymentAmount[0]
    data.paymentAmountMax = data.paymentAmount[1]
  }
}

// const dialogRef = ref<InstanceType<typeof ImportExcel> | null>(null)
// const handleImport = () => {
//   const params = {
//     title: '日记账',
//     tempApi: '/financeJournal/importTemp', // 下载模板接口
//     importApi: journalImport, // 导入家口
//     getTableList: proTable.value?.getTableList
//   }
//   dialogRef.value?.acceptParams(params)
// }

// // 删除日记账
// const handleDelete = async (row: any) => {
//   await useHandleData(deleteDayBook, row.id, `删除所选${row.receiptDate}  日记账信息`)
//   proTable.value?.getTableList()
//   // getFooterData()
//   // timeStamp.value = new Date().getTime() + ''
// }
const timeStamp = ref('')
// 新增日记账
// const handleAdd = () => {
//   showDialog({
//     title: '新增',
//     customClass: 'day-dialog',
//     cancelButtonText: '取消',
//     confirmButtonText: '保存',
//     component: dayForm, // 表单组件
//     // handleConvertParams: data => {
//     //   // 处理业务归属
//     //   data.deptSource = data.deptId + '-' + data.deptType
//     // }, // 详情接口数据自定义
//     handleRevertParams: data => {
//       // 处理业务归属
//       if (data.vestSource) {
//         data.vestType = data.vestSource.split('-')[0]
//         data.vestId = data.vestSource.split('-')[1]
//       }
//     }, //提交参数自定义
//     submitApi: addOrUpdateDayBook, // 提交api
//     submitCallback: (data: any) => {
//       proTable.value.searchParam['yearMonth'] = data['receiptDate'].slice(0, 7)
//       proTable.value.searchParam['day'] = data['receiptDate'].slice(8)
//       proTable.value?.search()
//       // getFooterData()
//       // timeStamp.value = new Date().getTime() + ''
//     } // 提交成功之后的回调函数
//     // handleRevertParams: handleRevertBusinessParams // 处理提交参数
//   })
// }

// // 编辑日记账
// const handleEdit = (row: any) => {
//   showDialog({
//     title: '编辑',
//     customClass: 'day-dialog',
//     cancelButtonText: '取消',
//     confirmButtonText: '保存',
//     component: dayForm, // 表单组件
//     handleConvertParams: data => {
//       // 处理业务归属
//       data.formType = 'edit'
//       if (data.vestId && data.vestName) {
//         // data.vestSource = data.vestId + '-' + data.vestName
//         data.vestSource = data.vestType + '-' + data.vestId
//       }
//       if (data.incomeAmount === 0) {
//         data.incomeAmount = ''
//       }
//       if (data.paymentAmount === 0) {
//         data.paymentAmount = ''
//       }
//     }, // 详情接口数据自定义
//     handleRevertParams: data => {
//       // 处理业务归属
//       console.log('handleRevertParams', data)
//       if (data.vestSource) {
//         data.vestType = data.vestSource.split('-')[0]
//         data.vestId = data.vestSource.split('-')[1]
//       }
//     }, //提交参数自定义
//     submitApi: addOrUpdateDayBook, // 提交api
//     requestParams: row.id,
//     getApi: getDayBookById,
//     submitCallback: (data: any) => {
//       proTable.value.searchParam['yearMonth'] = data['receiptDate'].slice(0, 7)
//       proTable.value.searchParam['day'] = data['receiptDate'].slice(8)
//       proTable.value?.search()
//     } // 提交成功之后的回调函数
//     // handleRevertParams: handleRevertBusinessParams // 处理提交参数
//   })
// }

const handleEditInitial = () => {
  // console.log('handleEditInitial', proTable.value.searchParam['yearMonth'])
  showDialog({
    title: '编辑期初金额',
    customClass: 'day-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: dayFormInitial, // 表单组件
    handleConvertParams: data => {
      data.receiptMethod = initParam.receiptMethod
      data.yearMonth = proTable.value.searchParam['yearMonth']
      if (data.incomeAmount === 0) {
        data.incomeAmount = ''
      }
      if (data.paymentAmount === 0) {
        data.paymentAmount = ''
      }
    },
    submitApi: saveOrUpdateInitialAmountAnalysis, // 提交api
    submitCallback: (data: any) => {
      proTable.value.searchParam['yearMonth'] = data['yearMonth']
      proTable.value?.search()
    }
  })
}

const footerData = ref<any>({})
const getFooterData = async () => {
  const { data } = await getDayBookStatisticDataByMonth({ ...proTable.value.searchParam, ...initParam })
  footerData.value = data || {}
}
// 自定义扩展搜索查询列表功能
const handleSearch = data => {
  getFooterData()
  timeStamp.value = new Date().getTime() + ''
  return data
}
function handleReset(e) {
  console.log('handleReset', e)
}

/** 展示收款单 */
const handleShowCollectionDetail = (id: any) => {
  console.log('id', id)
  showDialogFinance({
    title: '收款详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: collectionForm,
    getApi: getFinanceReceiptGetById,
    requestParams: { id }
  })
}

/** 左侧筛选 */
const receiptMethodFilter = ref('')
const receiptMethodRef = ref('')
const filterNode = (value, data) => {
  // console.log('receiptMethodFilter', receiptMethodFilter.value)
  if (!value) return true
  return data.label.indexOf(receiptMethodFilter.value) !== -1
}
function handleNodeClick(data: any) {
  console.log('handleNodeClick-data', data)
  initParam.receiptMethod = data.value
}
watch(receiptMethodFilter, val => {
  receiptMethodRef.value.filter(val)
})
if (receipt_method.value && receipt_method.value[0]) {
  console.log('receipt_method-a')
  nextTick(() => {
    initParam.receiptMethod = receipt_method.value?.[0].value
    receiptMethodRef.value.setCurrentKey(receipt_method.value?.[0].value)
  })
}
watch(receipt_method, val => {
  console.log('receipt_method-b')
  // console.log('receipt_method.value', receipt_method.value)
  nextTick(() => {
    initParam.receiptMethod = receipt_method.value?.[0].value
    receiptMethodRef.value.setCurrentKey(receipt_method.value?.[0].value)
  })
})

watch(
  () => proTable.value?.searchParam['yearMonth'],
  val => {
    // console.log('val', val)
    proTable.value.searchParam['day'] = undefined
  },
  { deep: true }
)
</script>
<style lang="scss" scoped>
.main-wrap {
  height: 100%;
  display: flex;
  width: 100%;
  flex-direction: column;

  // padding: 20px;
  .left-con {
    height: 100%;
    display: flex;
    width: 100%;
    flex-direction: column;
  }

  .right-con {
    height: 100%;
    display: flex;
    width: 100%;
    flex-direction: column;
  }
}

.footer {
  background: #fff;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding: 20px;
}

:deep(.el-tree-node__label) {
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
</style>
