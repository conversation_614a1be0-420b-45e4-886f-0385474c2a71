<!-- 业务管理 -->
<template>
  <ProTable
    ref="proTable"
    row-key="id"
    title="业务管理"
    class="pro-table"
    :pagination="false"
    :columns="columns"
    :init-param="initParam"
    :request-api="getBusinessList"
  >
    <template #enable="{ row }">
      <el-switch
        :disabled="!checkPermiBool"
        :active-value="true"
        :inactive-value="false"
        :model-value="row.enable"
        @change="handleChangeEnable(row, addBusiness, 'enable')"
      />
    </template>
    <template #enterpriseShowFlag="{ row }">
      <el-switch
        :disabled="!checkPermiBool"
        :active-value="true"
        :inactive-value="false"
        :model-value="row.enterpriseShowFlag"
        @change="handleChangeEnable(row, addBusiness, 'enterpriseShowFlag')"
      />
    </template>
    <template #expand="{ row }">
      <el-table class="row-table" :data="row.child">
        <el-table-column type="index" label="序号" align="center" width="100" />
        <el-table-column label="操作" align="center" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleCheckProduct(row)" v-hasPermi="['product:check']">详情</el-button>
            <el-button link type="primary" @click="handleUpdateProduct(row)" v-hasPermi="['product:update']">编辑</el-button>
            <el-button link type="primary" @click="handleRemoveProduct(row)" v-hasPermi="['product:delete']">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column label="产品名称" align="center" prop="productName">
          <template #default="{ row }">
            <span>{{ row.productName || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="产品代码" align="center" prop="code">
          <template #default="{ row }">
            <span>{{ row.code || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="产品报价" align="center" prop="quotation">
          <template #default="{ row }">
            <!-- <span>{{ row.quotation || '--' }}</span> -->
            <span>{{ getQuotation(row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="createBy">
          <template #default="{ row }">
            <span>{{ row.createBy || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用状态" align="center" prop="enable">
          <template #default="{ row }">
            <el-switch
              :disabled="!checkPermiBool"
              :active-value="true"
              :inactive-value="false"
              :model-value="row.enable"
              @change="handleChangeEnable(row, addProduct, 'enable')"
            />
          </template>
        </el-table-column>
        <el-table-column label="展示状态" align="center" prop="enterpriseShowFlag">
          <template #default="{ row }">
            <el-switch
              :disabled="!checkPermiBool"
              :active-value="true"
              :inactive-value="false"
              :model-value="row.enterpriseShowFlag"
              @change="handleChangeEnable(row, addProduct, 'enterpriseShowFlag')"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="{ row }">
            <span>{{ row.createTime || '--' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAddBusiness" v-hasPermi="['business:add']">新增</el-button>
    </template>

    <!-- 表格操作 -->
    <template #operation="{ row }">
      <el-button type="primary" link @click="handleAddProduct(row)" v-hasPermi="['product:add']">创建产品</el-button>
      <!-- 编辑业务 -->
      <el-button type="primary" link @click="handleUpdateBusiness(row)" v-hasPermi="['business:update']">编辑</el-button>
      <!-- 删除业务 -->
      <el-button type="primary" link @click="handleRemoveBusiness(row)" v-hasPermi="['business:delete']">删除</el-button>
    </template>
  </ProTable>
</template>
<script setup lang="tsx">
import { reactive, ref } from 'vue'
import { getBusinessList } from '@/api/business/business'
import { ColumnProps } from '@/components/ProTable/interface'
import { useDialog } from '@/hooks/useDialog'
import businessForm from './components/business-form.vue'
import productForm from './components/product-form.vue'
import {
  addBusiness,
  addProduct,
  removeBusiness,
  getBusinessDetailById,
  getProductDetailById,
  deleteProduct
} from '@/api/business/business'
import { useHandleData } from '@/hooks/useHandleData'
import { CirclePlus } from '@element-plus/icons-vue'
import { checkPermi } from '@/utils/permission'

const checkPermiBool = checkPermi(['product:update'])
// console.log("checkPermi(['product:update'])", checkPermi(['product:update']), checkPermiBool)

const { proxy } = getCurrentInstance()

const initParam = reactive({ noFilterFlag: true })
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    width: 100,
    label: '序号',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'typeName',
    label: '业务类型',
    search: { el: 'input' }
  },
  {
    prop: 'productName',
    isShow: false,
    isColShow: false,
    label: '产品名称',

    search: { el: 'input' }
  },
  {
    prop: 'code',
    label: '业务代码'
  },
  {
    prop: 'code',
    label: '代码',
    isShow: false,
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'contractType',
    label: '合同类型',
    enum: [
      {
        label: '记账合同',
        value: '记账合同'
      },
      {
        label: '一次性合同',
        value: '一次性合同'
      },
      {
        label: '地址服务协议合同',
        value: '地址服务协议合同'
      }
    ],
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.contractType || '--'}</span>
    }
  },
  {
    prop: 'createBy',
    label: '创建人'
  },
  {
    prop: 'enable',
    label: '使用状态'
  },
  {
    prop: 'enterpriseShowFlag',
    label: '展示状态'
  },
  {
    prop: 'createTime',
    label: '创建时间'
  },
  {
    prop: 'operation',
    fixed: 'right',
    label: '操作'
  }
]
// 创建业务
const { showDialog } = useDialog()
// 处理创建业务的传递参数
const handleRevertBusinessParams = (data: any) => {
  console.log('data', data)
  if (data.feeType === '0' || data.activityStatus !== '1') {
    data.activityQuotation = undefined
    data.discountTime = undefined
  }
  data.contractType = data.contractType.join(',')
}

const businessSubmitCallback = () => {
  // 刷新列表
  proTable.value?.getTableList()
}
const handleAddBusiness = () => {
  showDialog({
    title: '新增',
    customClass: 'business-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: businessForm, // 表单组件
    submitApi: addBusiness, // 提交api
    submitCallback: businessSubmitCallback, // 提交成功之后的回调函数
    handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}
// 编辑业务
// 自定义获取的数据
const handleConvertParamsBusiness = (data: any) => {
  data.contractType = data.contractType.split(',')
}

const handleUpdateBusiness = (row: any) => {
  console.log('row', row)
  showDialog({
    title: '编辑',
    customClass: 'product-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: businessForm,
    submitApi: addBusiness,
    getApi: getBusinessDetailById,
    requestParams: row.id,
    handleConvertParams: handleConvertParamsBusiness,
    submitCallback: businessSubmitCallback, // 提交成功之后的回调函数
    handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

const handleChangeEnable = (row: any, func: any, key: string) => {
  const formData = {}
  Object.assign(formData, row, { [key]: !row[key] })
  // console.log('key', key, !row[key], { [key]: !row[key] })
  console.log('formData', formData)
  func(formData).then(res => {
    if (res.code === 200) {
      proTable.value?.getTableList()
    }
  })
}

// 查看产品
const handleCheckProduct = (row: any) => {
  showDialog({
    title: '详情',
    customClass: 'product-dialog-next',
    cancelButtonText: '关闭',
    showConfirmButton: false,
    component: productForm,
    getApi: getProductDetailById,
    requestParams: row.id,
    handleConvertParams: data => {
      data.isDetail = true
    }
  })
}

// 删除业务
const proTable = ref()
const handleRemoveBusiness = async (row: any) => {
  await useHandleData(removeBusiness, row.id, `删除所选业务 ${row.typeName} 信息`)
  proTable.value?.getTableList()
}

// 创建产品
const handleRevertProductParams = (data: any, requestParams: any) => {
  console.log('data', data)
  if (data.tableData) {
    for (const item of data.tableData) {
      if (!item.activityQuotation) {
        proxy.$message.warning('请输入活动报价')
        return true
      }
    }
    if (data.tableData?.[0]?.activityQuotation) {
      data.activityList = data.tableData
    }
  }
  if (data.quotation) {
    data.quotation = Number(data.quotation)
  }
  if (!data.typeId) {
    data.typeId = requestParams
  }
}
const handleAddProduct = (row: any) => {
  showDialog({
    title: '新增',
    customClass: 'product-dialog-next',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: productForm,
    submitApi: addProduct,
    requestParams: row.id,
    submitCallback: businessSubmitCallback,
    handleRevertParams: handleRevertProductParams
  })
}
// 编辑产品
const handleConvertParamsProduct = () => {}
const handleUpdateProduct = (row: any) => {
  console.log('row', row)
  showDialog({
    title: '编辑',
    customClass: 'product-dialog-next',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: productForm,
    submitApi: addProduct,
    getApi: getProductDetailById,
    requestParams: row.id,
    handleConvertParams: handleConvertParamsProduct,
    submitCallback: businessSubmitCallback, // 提交成功之后的回调函数
    handleRevertParams: handleRevertProductParams // 处理提交参数
  })
}

// 删除产品
const handleRemoveProduct = async (row: any) => {
  await useHandleData(deleteProduct, row.id, `删除所选产品 ${row.productName} 信息`)
  proTable.value?.getTableList()
}
const typeMap = {
  '0': '元',
  '1': '元/年',
  '2': '元/月'
}
// getQuotation
const getQuotation = (row: any) => {
  const { isInContract, quotation, feeType } = row
  if (isInContract === '0') {
    return quotation + ' ' + typeMap[feeType as keyof typeof typeMap]
    // 代表不是在合同在定义
  } else if (isInContract === '1') {
    return '在合同中定义'
  } else {
    return ''
  }
}
</script>
<style lang="scss" scoped>
.row-table {
  margin-left: 45px;
  width: auto;
  border-left: 1px solid #ebeef5;
}

:deep(.pro-table) {
  .el-table__expanded-cell {
    padding: 0;
  }
}
</style>
