<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    top="5vh"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-position="top"
      :rules="rules"
      :disabled="['详情'].includes(mode)"
      :hide-required-asterisk="['详情'].includes(mode)"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-tag v-if="formData.status" style="margin-bottom: 15px" :type="tagType[formData.status]">{{
            formData.status
          }}</el-tag>
          <span class="tips" v-if="!['详情'].includes(mode)"
            >线索提交申述后直到标记申述结果，过程中不能进行其他操作，请再次确认后进行操作！</span
          >
        </el-col>
        <el-col :span="12">
          <el-form-item label="线索名称" prop="clueName">
            <el-input v-model="formData.clueName" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="线索来源" prop="sourceName">
            <el-input v-model="formData.sourceName" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="申述原因" prop="remark">
            <el-input type="textarea" v-model="formData.remark"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
      <el-button v-if="!['详情'].includes(mode)" type="primary" @click="handleSubmit" :loading="loading"> 保存 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { getClueAppealRecordGetById, postClueAppealRecordSave } from '@/api/material-manage/clue-appeal-record.js'

const { proxy } = getCurrentInstance()
const emit = defineEmits()

const mode = ref('')
const formRef = ref()
const formData = reactive({})
const visible = ref(true)

const rules = {
  remark: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
}

function getDetail(row) {
  getClueAppealRecordGetById({ id: row.id }).then(res => {
    Object.assign(formData, res.data)
  })
}

const onAdd = row => {
  formData.clueId = row.id
  formData.clueName = row.contactName
  formData.sourceName = row.sourceName
  mode.value = '线索申述'
}

const onDetail = row => {
  getDetail(row)
  mode.value = '详情'
}
const handleClose = () => {
  emit('close')
}

const loading = ref(false)
const handleSubmit = async () => {
  console.log('formData', formData)
  if (loading.value) return
  await formRef.value.validate()
  loading.value = true
  postClueAppealRecordSave(formData)
    .then(res => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess('保存成功')
        emit('ok')
        handleClose()
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const tagType = {
  申述中: '',
  申述失败: 'danger',
  申述成功: 'success'
}

defineExpose({
  onAdd,
  onDetail
})
</script>

<style lang="scss" scoped>
.tips {
  color: #aaa;
}
</style>
