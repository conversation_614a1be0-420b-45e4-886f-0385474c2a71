<!--
 * @Description: 商机漏斗
 * @Author: thb
 * @Date: 2023-09-05 09:35:24
 * @LastEditTime: 2023-09-15 15:20:22
 * @LastEditors: thb
-->
<template>
  <dataWrap class="top-mid" ref="wrapRef" title="商机漏斗" :request-api="getBusinessData" :charts="charts">
    <template #default>
      <div ref="funnelRef" class="funnel-chart"></div>
    </template>
  </dataWrap>
</template>
<script setup>
import { onMounted } from 'vue'
import dataWrap from '../../collecting-panel/components/data-wrap'
import { getBusinessData } from '@/api/panel-data/sale'
import * as echarts from 'echarts'
const wrapRef = ref()
// 绘制漏斗
const funnelRef = ref()
const charts = ref([])
const drawFunnel = () => {
  console.log('wrapRef', wrapRef.value.panelData)
  let myChart = echarts.init(funnelRef.value)
  charts.value[0] = myChart
  const panelData = wrapRef.value.panelData
  const valueMap = [70, 60, 50, 40, 30, 20, 10]
  const dataChanged = panelData.map((item, index) => {
    return {
      ...item,
      realValue: item.value,
      value: valueMap[index]
    }
  })
  const option = {
    backgroundColor: '#ffffff',
    color: ['#DAEAFCFF', '#DAEAFCFF', '#D3E6FAFF', '#9DC8F5FF', '#74B1F1FF', '#2383E7FF', '#2383E7FF'],
    series: [
      {
        top: '10%',
        bottom: '5%',
        type: 'funnel',
        left: '0%',
        width: '70%',
        gap: 8,
        minSize: '20%',
        maxSize: '70%',
        label: {
          show: true,
          position: 'inside',
          formatter: dec => {
            console.log('dec', dec)
            return dec.data.realValue
          }
        },

        data: dataChanged
      },

      {
        top: '30%',
        type: 'funnel',
        left: '5%',
        width: '50%',
        gap: 100,
        z: -1,
        minSize: '10%',
        maxSize: '65%',
        label: {
          normal: {
            color: '#2383E7FF',
            position: 'right'
          }
        },
        //右侧的百分比显示的地方
        labelLine: {
          show: true,
          normal: {
            length: 100,
            position: 'right',
            lineStyle: {
              width: 1,
              color: '#2383E7FF',
              type: 'solid'
            }
          }
        },
        //主体是透明的
        itemStyle: {
          normal: {
            color: 'transparent',
            borderWidth: 0,
            opacity: 1
          }
        },
        data: dataChanged
      }
    ]
  }
  myChart.setOption(option)
}
onMounted(async () => {
  await wrapRef.value.requestResult
  drawFunnel()
})
</script>
<style lang="scss" scoped>
.top-mid {
  flex: 1;
}
.funnel-chart {
  flex: 1;
}
</style>
