<!--
 * @Description: 新增流程
 * @Author: thb
 * @Date: 2023-06-16 16:30:49
 * @LastEditTime: 2023-08-30 15:31:48
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    class="step-dialog"
    :title="type === 'detail' ? '流程详情' : type === 'add' ? '新增流程' : '编辑流程'"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <!-- <processView /> -->
    <el-steps :active="active" finish-status="success" simple>
      <el-step title="步骤1/基础信息">
        <template #icon> <span class="icon circle-icon circle-icon-default">1</span> </template>
      </el-step>
      <el-step title="步骤2/流程设置">
        <template #icon><span class="icon circle-icon circle-icon-default">2</span> </template>
      </el-step>
    </el-steps>
    <basicInfo v-model="paramsData" ref="basicRef" v-if="active === 0" :disabled="disabled" />
    <processSet ref="processSetRef" v-model="paramsData.nodeList" :type="type" v-if="active === 1" />
    <template #footer>
      <template v-if="active === 0">
        <template v-if="type === 'detail'">
          <el-button @click="handleUpdate">编辑</el-button>
          <!-- <el-button type="danger" @click="handleDelete" :disabled="paramsData.enable && paramsData.enable === '1'">删除</el-button> -->
        </template>
        <el-button type="primary" @click="handleStepAfter">下一步</el-button>
        <el-button @click="handleReset" v-if="type !== 'detail'">清空</el-button>
      </template>
      <template v-if="active === 1">
        <template v-if="type === 'detail'">
          <el-button @click="handleUpdate">编辑</el-button>
          <!-- <el-button type="danger" @click="handleDelete" :disabled="paramsData.enable && paramsData.enable === '1'">删除</el-button> -->
        </template>
        <el-button type="primary" @click="handleStepBefore">上一步</el-button>
        <template v-if="type !== 'detail'">
          <el-button @click="handleSubmit">保存</el-button>
        </template>
        <el-button @click="handleClose">关闭</el-button>
      </template>
    </template>
  </el-dialog>
</template>
<script setup>
// import processView from '@/components/ProcessView'
import { addStep, getStepDetail, deleteProcess } from '@/api/process/process'
import basicInfo from './basic-info.vue'
import processSet from './process-set.vue'
import { getCurrentInstance } from 'vue'
import { useHandleData } from '@/hooks/useHandleData'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-list', 'on-edit'])
const handleClose = () => {
  emits('on-close')
}
const active = ref(0)
const props = defineProps({
  id: Number,
  type: {
    type: String,
    default: 'add' // 'add'表示新增 'detail'表示详情 'update'表示编辑
  },
  stepType: {
    type: String,
    default: '2' // '2'合同审批 '1'合同借阅 '0'模板审批
  }
})

// 监听id, 获取详情
const disabled = ref(false)
watch(
  () => props.type,
  async () => {
    if (props.type === 'detail') {
      disabled.value = true
    }

    if (props.type === 'detail' || props.type === 'update') {
      // 如果id存在
      const { data } = await getStepDetail(props.id)
      paramsData.value = data
      paramsData.value.deptIds = paramsData.value.deptIds.split(',')
    }
  },
  {
    immediate: true
  }
)
const basicRef = ref()
// 清空表单
const handleReset = () => {
  console.log('basicRef', basicRef.value)
  basicRef.value.resetForm()
}

// 执行下一步
const handleStepAfter = async () => {
  // 执行下一步之前先校验
  const result = await basicRef.value.handleFormValidate()
  if (result) {
    active.value = 1
  }
}

const handleStepBefore = () => {
  active.value = 0
}

// 新增流程

// data
const paramsData = ref({
  name: '',
  contractType: '',
  remark: '',
  type: props.stepType, // '0'代表模板审批流程 ,'1'代表合同借阅, '2'代表合同审批
  nodeList: [],
  deptIds: ''
})
console.log('paramsData', paramsData.value)
const processSetRef = ref()
const { proxy } = getCurrentInstance()
const handleSubmit = async () => {
  console.log('paramsData', processSetRef.value.viewProcess)
  const reviewerNode = processSetRef.value.viewProcess.filter(item => item.type === 'reviewNode')
  paramsData.value.nodeList = reviewerNode.map(item => {
    return {
      nodeKey: item.nodeKey,
      parentKey: item.parentKey,
      userId: item.reviewerId
    }
  })
  paramsData.value.deptIds = paramsData.value.deptIds.join(',')
  const result = await addStep(paramsData.value)
  if (result.code === 200) {
    // 保存成功
    proxy.$modal.msgSuccess(`保存成功!`)
    handleClose()
    emits('on-list')
  } else {
    // 保存失败
    proxy.$modal.msgError(`保存失败!`)
  }
}
// 删除该流程
const handleDelete = async () => {
  await useHandleData(deleteProcess, paramsData.value?.id, '删除所选流程信息')
}

// 编辑
const handleUpdate = () => {
  disabled.value = false
  emits('on-edit')
}
</script>
<style lang="scss" scoped></style>
