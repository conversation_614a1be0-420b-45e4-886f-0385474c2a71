<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    top="5vh"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-position="top"
      :rules="rules"
      :disabled="['详情'].includes(mode)"
      :hide-required-asterisk="['详情'].includes(mode)"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="用户姓名" prop="userName">
            <el-input v-model="formData.userName" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业名称" prop="customerName">
            <el-input v-model="formData.customerName" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布时间" prop="publishTime">
            <el-input v-model="formData.publishTime" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="建议内容" prop="suggestionData">
            <el-input type="textarea" v-model="formData.suggestionData" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="my-filelist">
            <fileList :list="formData.fileList" />
          </div>
        </el-col>
        <template v-if="['详情'].includes(mode) && formData.responseData">
          <el-col :span="12">
            <el-form-item label="回复人" prop="responseUserName">
              <el-input v-model="formData.responseUserName" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回复时间" prop="responseTime">
              <el-input v-model="formData.responseTime" disabled></el-input>
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="24" v-if="!['详情'].includes(mode) || formData.responseData">
          <el-form-item label="回复" prop="responseData">
            <el-input type="textarea" v-model="formData.responseData"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="['详情'].includes(mode) && formData.responseData">
          <el-form-item label="用户评价" prop="star">
            <el-rate v-if="formData.star" v-model="formData.star" disabled size="40" />
            <span v-else>暂未评价</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 取消 </el-button>
      <el-button v-if="!['详情'].includes(mode)" type="primary" @click="handleSubmit" :loading="loading"> 保存 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import fileList from '@/components/FileList'
import { getUserSuggestionGetById, postUserSuggestionResponse } from '@/api/user-suggestion/user-suggestion.js'

const { proxy } = getCurrentInstance()
const emit = defineEmits()

const mode = ref('')
const formRef = ref()
const formData = reactive({})
const visible = ref(true)

const rules = {
  responseData: [{ required: true, message: '请输入', trigger: ['blur'] }]
}

function getDetail(row) {
  getUserSuggestionGetById({ id: row.id }).then(res => {
    Object.assign(formData, res.data)
  })
}

const onDeal = row => {
  getDetail(row)
  mode.value = '回复建议'
}

const onDetail = row => {
  getDetail(row)
  mode.value = '详情'
}
const handleClose = () => {
  emit('close')
}

const loading = ref(false)
const handleSubmit = async () => {
  console.log('formData', formData)
  if (loading.value) return
  await formRef.value.validate()
  loading.value = true
  postUserSuggestionResponse(formData)
    .then(res => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess('保存成功')
        emit('ok')
        handleClose()
      }
    })
    .finally(() => {
      loading.value = false
    })
}

defineExpose({
  onDeal,
  onDetail
})
</script>

<style lang="scss" scoped>
.my-filelist {
  margin: 10px 0 20px;
}
</style>
