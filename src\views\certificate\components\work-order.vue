<!--
 * @Description: 派工
 * @Author: thb
 * @Date: 2023-09-27 13:37:03
 * @LastEditTime: 2023-12-20 08:48:22
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :hide-required-asterisk="disabled" :model="data" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item
          :label="data.bizType === 'business_cancellation' || data.bizType === 'business_change' ? '办理人员' : '办证人员'"
          prop="userId"
        >
          <SelectTree
            style="width: 100%"
            v-model="data.userId"
            :disabled="disabled"
            placeholder="请选择"
            clearable
          /> </el-form-item
      ></el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import SelectTree from '@/components/SelectTree'

defineProps({
  data: Object
})
const disabled = inject('disabled')
const rules = {
  userId: [{ required: true, message: '请输入', trigger: ['change'] }]
}

// 校验表单的方法
const formRef = ref()
const validateForm = async () => {
  const result = await formRef.value.validate(valid => {
    if (valid) {
      console.log('检验成功')
    } else {
      console.log('校验失败')
      return false
    }
  })
  return result
}
defineExpose({
  validateForm
})
</script>
<style lang="scss" scoped></style>
