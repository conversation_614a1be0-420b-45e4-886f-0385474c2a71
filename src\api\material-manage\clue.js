import request from '@/utils/request'

// 线索管理页面头部统计
export const getClueHeaderStatistic = params => {
  return request({
    url: '/cusCustomerOrClue/clueHeaderStatistic',
    method: 'get',
    params
  })
}

// 获取跟进线索
export const getFollowedClueList = params => {
  // console.log('params', params)
  params.createTime = undefined
  params.lastFollowTime = undefined
  return request({
    url: '/cusCustomerOrClue/allClueList',
    method: 'get',
    params
  })
}
// 获取我的线索
export const getMyClueList = params => {
  params.createTime = undefined
  params.lastFollowTime = undefined
  return request({
    url: '/cusCustomerOrClue/myClueList',
    method: 'get',
    params
  })
}

// 获取共享线索
export const getSharedClueList = params => {
  params.createTime = undefined
  params.lastFollowTime = undefined
  return request({
    url: '/cusCustomerOrClue/shareClueList',
    method: 'get',
    params
  })
}

// 新增或者编辑线索
export const saveClue = data => {
  return request({
    url: '/cusCustomerOrClue/saveOrUpdate',
    method: 'post',
    data
  })
}

// 获取线索详情
export const getClueDetail = id => {
  return request({
    url: '/cusCustomerOrClue/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 编辑标签
export const editTags = data => {
  return request({
    url: '/cusCustomerOrClue/updateTagsForm',
    method: 'post',
    data
  })
}

// 删除线索
export const deleteClue = id => {
  return request({
    url: '/cusCustomerOrClue/delete',
    method: 'delete',
    params: {
      id
    }
  })
}
// 线索快速跟进
export const addClueRecord = data => {
  return request({
    url: '/cusCcFollow/save',
    method: 'post',
    data
  })
}

// 线索跟进记录
export const getClueRecordList = params => {
  return request({
    url: '/cusCcFollow/list',
    method: 'get',
    params
  })
}
// 查询线索共享人id列表
export const getClueShareIds = ccId => {
  return request({
    url: '/cusCustomerOrClue/getUserIds',
    method: 'get',
    params: {
      ccId
    }
  })
}

// 线索共享
export const saveClueShare = data => {
  return request({
    url: '/cusCustomerOrClue/ccShare',
    method: 'post',
    data
  })
}

// 转让线索
export const transferClue = data => {
  return request({
    url: '/cusCustomerOrClue/ccChange',
    method: 'post',
    data
  })
}
// 线索回收公海
export const recycleClue = data => {
  return request({
    url: '/cusCustomerOrClue/ccRecovery',
    method: 'post',
    data
  })
}
// 转为客户(新建客户)
export const transformCustomerFromClue = data => {
  return request({
    url: '/cusCustomerOrClue/changeToCustomer',
    method: 'post',
    data
  })
}

// 线索公海列表
export const getClueZoneList = params => {
  params.createTime = undefined
  params.lastFollowTime = undefined
  params.lastModifiedTime = undefined
  return request({
    url: '/cusCustomerOrClue/clueInSeaList',
    method: 'get',
    params
  })
}
// 公海线索或者客户的领取
export const receiveZoneClue = data => {
  return request({
    url: '/cusCustomerOrClue/ccGet',
    method: 'post',
    data
  })
}
// 分配线索或者客户
export const divideClue = data => {
  return request({
    url: '/cusCustomerOrClue/ccDivide',
    method: 'post',
    data
  })
}
// 线索转移公海
export const transferClueToZone = data => {
  return request({
    url: '/cusCustomerOrClue/ccTransfer',
    method: 'post',
    data
  })
}
// 操作记录列表查询
export const getActionRecordList = ccId => {
  return request({
    url: '/cusCcRecord/listByCcId',
    method: 'get',
    params: {
      ccId
    }
  })
}

// 跟进线索导出
export const cusCustomerOrClueAllClueListExport = query => {
  return request({
    url: '/cusCustomerOrClue/allClueListExport',
    method: 'post',
    data: query
  })
}

// 我的线索导出
export const cusCustomerOrClueMyClueListExport = query => {
  return request({
    url: '/cusCustomerOrClue/myClueListExport',
    method: 'post',
    data: query
  })
}

// 共享线索导出
export const cusCustomerOrClueShareClueListExport = query => {
  return request({
    url: '/cusCustomerOrClue/shareClueListExport',
    method: 'post',
    data: query
  })
}

// 线索公海列表导出
export const cusCustomerOrClueClueInSeaListExport = query => {
  return request({
    url: '/cusCustomerOrClue/clueInSeaListExport',
    method: 'post',
    data: query
  })
}
