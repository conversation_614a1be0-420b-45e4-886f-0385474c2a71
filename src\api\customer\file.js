/*
 * @Description: 客户管理（客户基本信息、银行信息、社保信息等接口）
 * @Author: thb
 * @Date: 2023-05-25 09:52:51
 * @LastEditTime: 2023-10-27 12:31:35
 * @LastEditors: thb
 */
import request from '@/utils/request'

// 客户基础信息
// 查询客户列表
export const getCustomers = params => {
  // 此处修改params是可以影响到页面组件里的proTable.value.searchParam，那为什么回显没被赋值undefined呢
  params.sponsorAccountingAllocationDate = undefined
  params.customerSuccessAllocationDate = undefined
  params.establishDate = undefined
  if (params.queryColumAndValue) {
    params.queryColumAndValue.sponsorAccountingAllocationDate = undefined
    params.queryColumAndValue.customerSuccessAllocationDate = undefined
    params.queryColumAndValue.establishDate = undefined
  }
  return request({
    url: '/customerInformation/list',
    method: 'get',
    params
  })
}

// 批量删除客户
export const deleteCustomers = ids => {
  return request({
    url: '/customerInformation/deleteByIds?ids=' + ids.join(','),
    method: 'delete'
    // params: {
    //   ids: JSON.stringify(ids)
    // }
  })
}

// 新增单个客户信息
export const saveCustomer = query => {
  return request({
    url: '/customerInformation/save',
    method: 'post',
    data: query
  })
}

// 变更经理接口
export function changeCustomerManager(query) {
  return request({
    url: '/customerInformation/changeManger',
    method: 'post',
    data: query
  })
}
// 更新客户信息
export const updateCustomer = query => {
  return request({
    url: '/customerInformation/update',
    method: 'post',
    data: query
  })
}

// 根据id获取客户基础信息
export const getCustomerById = id => {
  return request({
    url: '/customerInformation/getById/',
    method: 'get',
    params: {
      id
    }
  })
}

// 导入客户信息
export const uploadCustomer = query => {
  return request({
    url: '/customerInformation/upload',
    method: 'post',
    data: query
  })
}
// 导入税务信息
export const uploadTax = query => {
  return request({
    url: '/customerTaxInformation/importTaxInfo',
    method: 'post',
    data: query
  })
}

// 获取联系人导入模板
export const getContactImportTemplate = '/customerInformation/getContactImportTemplate'

// 联系人导入
export const uploadContact = query => {
  return request({
    url: '/customerInformation/importContact',
    method: 'post',
    data: query
  })
}

// 工商信息

// 删除工商信息
export const deleteCustomerBusiness = id => {
  return request({
    url: '/customerBusinessInformation/delete' + id,
    method: 'delete'
  })
}
export const getCommercialDetail = query => {
  return request({
    url: '/tianyancha/getInfo',
    method: 'get',
    params: query
  })
}
export const getCustomerBusinessByCiId = id => {
  return request({
    url: '/customerBusinessInformation/getByCiId',
    method: 'get',
    params: {
      id
    }
  })
}
// 通过id获取工商信息
export const getCustomerBusinessById = id => {
  return request({
    url: '/customerBusinessInformation/getById',
    method: 'get',
    params: {
      id
    }
  })
}

//保存工商信息
export const saveCustomerBusiness = query => {
  return request({
    url: '/customerBusinessInformation/saveOrUpdate',
    method: 'post',
    data: query
  })
}

// 更新工商信息
export const updateCustomerBusiness = query => {
  return request({
    url: '/customerBusinessInformation/udpate',
    method: 'post',
    data: query
  })
}

// 客户社保信息

// 删除客户社保信息
export const deleteCustomerSocialFund = id => {
  return request({
    url: '/dev-api/customerSocialFund/delete/' + id,
    method: 'delete'
  })
}

// 通过id获取客户社保信息
export const getCustomerSocialFundById = id => {
  return request({
    url: '/customerSocialFund/getById/',
    method: 'get',
    params: {
      id
    }
  })
}
export const getCustomerSocialFundByCiId = ciId => {
  return request({
    url: '/customerSocialFund/getByCiId/',
    method: 'get',
    params: {
      ciId
    }
  })
}

// 保存客户社保信息
export const saveCustomerSocialFund = query => {
  return request({
    url: '/customerSocialFund/saveOrUpdate',
    method: 'post',
    data: query
  })
}

// 更新客户社保信息
export const updateCustomerSocialFund = query => {
  return request({
    url: '/customerInformation/update',
    method: 'post',
    data: query
  })
}
// 客户税收信息

// 根据id获取客户税收信息
export const getCustomerTaxById = id => {
  return request({
    url: '/customerTaxInformation/getById',
    method: 'get',
    params: {
      id
    }
  })
}

export const getCustomerTaxByCiId = ciId => {
  return request({
    url: '/customerTaxInformation/getDetailByCiId',
    method: 'get',
    params: {
      ciId
    }
  })
}

// 保存客户税收信息
export const saveCustomerTax = query => {
  return request({
    url: '/customerTaxInformation/addOrUpdate',
    method: 'post',
    data: query
  })
}

// 更新客户税收信息
export const updateCustomerTax = query => {
  return request({
    url: '/customerTaxInformation/update',
    method: 'post',
    data: query
  })
}
// 客户联系人信息

// 根据id获取客户联系人信息
export const getCustomerContactById = id => {
  return request({
    url: '/customerContact/getById',
    method: 'get',
    params: {
      id
    }
  })
}

export const getCustomerContactByCiId = ciId => {
  return request({
    url: '/customerContact/getByCiId',
    method: 'get',
    params: {
      ciId
    }
  })
}

// 保存客户联系人信息
export const saveCustomerContact = query => {
  return request({
    url: '/customerContact/saveBatch',
    method: 'post',
    data: query
  })
}

// 更新客户联系人信息
export const updateCustomerContact = query => {
  return request({
    url: '/customerContact/update',
    method: 'post',
    data: query
  })
}

// 客户许可信息

// 根据id获取客户许可信息
export const getCustomerLicenseById = id => {
  return request({
    url: '/customerLicense/getById/',
    method: 'get',
    params: {
      id
    }
  })
}

export const getCustomerLicenseByCiId = ciId => {
  return request({
    url: '/customerLicense/getByCiId/',
    method: 'get',
    params: {
      ciId
    }
  })
}

// 保存客户许可信息
export const saveCustomerLicense = query => {
  return request({
    url: '/customerLicense/saveBatch',
    method: 'post',
    data: query
  })
}

// 更新客户许可信息
export const updateCustomerLicense = query => {
  return request({
    url: '/customerLicense/update',
    method: 'post',
    data: query
  })
}

// 客户银行信息
// 通过id获取客户银行信息
export const getCustomerBankById = id => {
  return request({
    url: '/customerBank/getById/',
    method: 'get',
    params: {
      id
    }
  })
}

export const getCustomerBankByCiId = id => {
  return request({
    url: '/customerBank/getByCiId/',
    method: 'get',
    params: {
      id
    }
  })
}

// 保存客户银行信息
export const saveCustomerBank = query => {
  return request({
    url: '/customerBank/save',
    method: 'post',
    data: query
  })
}
// 更新客户银行信息
export const updateCustomerBank = query => {
  return request({
    url: '/customerBank/update',
    method: 'get',
    data: query
  })
}
// 客户合同信息

// 保存客户合同
export const saveCustomerContract = query => {
  return request({
    url: '/customerContract/save',
    method: 'post',
    data: query
  })
}

// 编辑客户合同
export const updateCustomerContract = query => {
  return request({
    url: '/customerContract/update',
    method: 'post',
    data: query
  })
}

// 根据id获取客户合同详情
export const getCustomerContractById = id => {
  return request({
    url: '/customerContract/getById/',
    method: 'get',
    params: {
      id
    }
  })
}
// 客户个性化信息

// 保存个性化
export const saveCustomerPersonal = query => {
  return request({
    url: '/customerPersonalizedInformation/save',
    method: 'post',
    data: query
  })
}

// 根据id获取个性化
export const getCustomerPersonalById = id => {
  return request({
    url: '/customerPersonalizedInformation/getById/',
    method: 'get',
    params: {
      id
    }
  })
}
export const getCustomerPersonalByCiId = ciId => {
  return request({
    url: '/customerPersonalizedInformation/getByCiId/',
    method: 'get',
    params: {
      ciId
    }
  })
}
// 跟进记录保存
export const saveRecord = query => {
  return request({
    url: '/follow/save',
    method: 'post',
    data: query
  })
}

// 跟进记录查询
export const getRecordList = query => {
  return request({
    url: '/follow/list',
    method: 'get',
    params: query
  })
}

// 文件管理
export function getCustomerAllFiles(bizId) {
  return request({
    url: '/common-biz-file/selectByMainIdAndBizType',
    method: 'get',
    params: {
      bizId
    }
  })
}

// 查询相关附件信息
export function getRelateFiles(customerId) {
  return request({
    url: '/customerInformation/getRelateFiles',
    method: 'get',
    params: {
      customerId
    }
  })
}

// 文件删除
export function deleteCustomerFile(id) {
  return request({
    url: '/common-biz-file/delete',
    method: 'delete',
    params: {
      id
    }
  })
}
// 客户废弃接口
export function abandonCustomer(query) {
  return request({
    url: '/customerDiscardRecord/save',
    method: 'post',
    data: query
  })
}
// 客户信息统计接口
export function getCustomerStatics() {
  return request({
    url: '/customerInformation/statistics',
    method: 'get'
  })
}
// 人员变更
export function changePerson(query) {
  return request({
    url: '/customerInformation/changePerson',
    method: 'post',
    data: query
  })
}

// 获取人员分配记录
export function getUserRelateRecord(params) {
  return request({
    url: '/customerInformation/getUserRelateRecord',
    method: 'get',
    params
  })
}

// 文件导入状态列表

export function fileImportList(params) {
  return request({
    url: '/progress/list',
    method: 'get',
    params
  })
}
// 客户信息修改记录
export function getCustomerChangeRecord(params) {
  return request({
    url: '/customerChangeRecord/list',
    method: 'get',
    params
  })
}
// 导出企业档案列表
export const downloadFile = query => {
  return request({
    url: '/customerInformation/export',
    method: 'post',
    data: query
  })
}

// 获取注销办理的流程
export const getCancelProgressById = id => {
  return request({
    url: '/customerInformation/getCancellationRecord',
    method: 'post',
    params: {
      customerId: id
    }
  })
}
