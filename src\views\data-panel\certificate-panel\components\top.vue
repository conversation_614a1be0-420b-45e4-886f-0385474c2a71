<!--
 * @Description: 办证数量
 * @Author: thb
 * @Date: 2023-09-06 08:44:16
 * @LastEditTime: 2023-09-19 16:33:21
 * @LastEditors: thb
-->
<template>
  <dataWrap title="办证数量" class="top" :request-api="getCertificateNums">
    <template #default="{ data }">
      <div class="top-row">
        <div class="row-item item-1">
          <div class="item-top">
            <span class="icon num-icon"></span>
            办证数量
          </div>
          <div class="item-bottom">
            <span class="number text-blue">{{ data.license || '--' }}</span>
            <span class="unit">件</span>
          </div>
        </div>
        <div class="row-item item-2" style="padding: 4px 12px">
          <div class="item-left">
            <div class="item-top">
              <span class="icon sign-icon-1"></span>
              新注册
            </div>
            <div class="item-bottom">
              <span class="number text-green">
                {{
                  Number(data.domesticBusinessRegistration) + Number(data.foreignBusinessRegistration) === 0
                    ? 0
                    : Number(data.domesticBusinessRegistration) + Number(data.foreignBusinessRegistration) || '--'
                }}
              </span>
              <span class="unit">件</span>
            </div>
          </div>
          <div class="item-right">
            <div class="right-left">
              内资注册: <span class="number text-black">{{ data.domesticBusinessRegistration || '--' }}</span
              >件
            </div>
            <div class="right-right">
              外资注册: <span class="number text-black">{{ data.foreignBusinessRegistration || '--' }}</span
              >件
            </div>
          </div>
        </div>
        <div class="row-item item-1">
          <div class="item-top">
            <span class="icon sign-icon"></span>
            单注册
          </div>
          <div class="item-bottom">
            <span class="number text-green"> {{ data.singleRegistration || '--' }}</span>
            <span class="unit">件</span>
          </div>
        </div>
      </div>
      <div class="top-row row-bg">
        <div class="row-1">
          <div class="item-top">
            <span class="icon sign-icon"></span>
            变更
          </div>
          <div class="item-bottom">
            <span class="number text-orange">{{
              Number(data.regionModification) + Number(data.businessModification) + Number(data.cancellation) === 0
                ? 0
                : Number(data.regionModification) + Number(data.businessModification) + Number(data.cancellation) || '--'
            }}</span>
            <span class="unit">件</span>
          </div>
        </div>
        <div class="row-3">
          跨区变更: <span class="number text-black">{{ data.regionModification || '--' }}</span
          >件
        </div>
        <div class="row-3">
          工商变更: <span class="number text-black">{{ data.businessModification || '--' }}</span
          >件
        </div>

        <div class="row-3">
          注销: <span class="number text-black">{{ data.cancellation || '--' }}</span
          >件
        </div>
      </div>
    </template>
  </dataWrap>
</template>
<script setup>
import dataWrap from '../../collecting-panel/components/data-wrap'
import { getCertificateNums } from '@/api/panel-data/certificate'
</script>
<style lang="scss" scoped>
.top {
  flex: 1;

  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #7d8592;
  .top-row {
    flex: 1;
    display: flex;
    gap: 16px;
    .row-item {
      border-radius: 4px;
      border: 1px solid #e8e8e8;
      padding: 16px 12px;
      // padding-bottom: 0;
      display: flex;
      flex-direction: column;
    }
    .item-1 {
      flex: 1;
    }
    .item-2 {
      flex: 2;
      display: flex;
      flex-direction: row;
    }
    .item-top {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #333333;
      // margin-bottom: 12px;
    }
    .item-bottom {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
    }
  }
}
:deep(.content) {
  flex-direction: column;
  gap: 16px;
}

.icon {
  margin-right: 8px;
}
.number {
  font-size: 24px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  margin-right: 4px;
}
.unit {
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #7d8592;
}
.text-blue {
  color: #45a0ffff;
}
.text-green {
  color: #26b52b;
}
.text-black {
  color: #333;
  margin-left: 5px;
}
.text-orange {
  color: #f2831dff;
}
.item-left {
  flex: 1;
  padding-top: 12px;
}
.item-right {
  flex: 4;
  background: #fafafa;
  border-radius: 4px;
  display: flex;
  padding-left: 26px;
  padding-right: 26px;
  .right-left {
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      width: 1px;
      height: 33px;
      background: #d8d8d8;
      right: 12px;
    }
  }

  .right-right {
    flex: 1;
    display: flex;
    align-items: center;
  }
}

.row-bg {
  background: #fff8ef;
  border-radius: 4px;
  padding: 16px 12px;
}

.row-3 {
  flex: 4;
  display: flex;
  align-items: center;
  position: relative;
  &:last-child::after {
    display: none;
  }
  &::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 33px;
    background: #d8d8d8;
    right: 12px;
  }
}

.row-1 {
  flex: 2;
}
</style>
