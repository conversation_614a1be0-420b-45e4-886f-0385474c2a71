import useUserStore from '@/store/modules/user'
import { getZoneSettingDetail } from '@/api/material-manage/zone-setting'
export const useDept = () => {
  // 判断当前用户的所属部门是否是公海下的部门
  const includeDept = (list: string[] = []) => {
    // 如果用户是admin 则不需要都禁用
    if (useUserStore().user.admin) {
      return false
    }
    return !(list || []).includes(useUserStore().user.deptId)
  }
  // 判断当前用户是否可以领取线索或者客户 分配线索或者客户
  const isCapableGet = (isCapable: Boolean, deptIds: string[]) => {
    // 如果是admin 则权限最大 可以领取或者
    if (useUserStore().user.admin && isCapable) {
      return true
    }
    // 不是admin的前提下
    if (deptIds && isCapable) {
      return !includeDept(deptIds)
    } else {
      return isCapable
    }
  }

  // 判断是否可以分配线索或者客户
  const isCapableDispatch = (isDispatch: Boolean) => {
    return isDispatch
  }

  return {
    includeDept,
    isCapableGet,
    isCapableDispatch
  }
}
