<template>
  <el-dialog
    v-model="visible"
    title="客户账单详情"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1350px"
    top="5vh"
    append-to-body
  >
    <div class="top">
      <div class="title">
        {{ detail?.customerName }}
        <el-tag v-if="detail?.customerNo">{{ detail?.customerNo }}</el-tag>
      </div>
      <div class="tag-name">
        <span>财税顾问： {{ detail?.manger }}</span>
      </div>

      <div class="tag-name">
        <span>主办会计： {{ detail?.sponsorAccounting || '暂无' }}</span>
      </div>
      <div class="tag-name">
        <span>开票员： {{ detail?.counselor || '暂无' }}</span>
      </div>
      <div class="tag-name">
        <span>客户成功： {{ detail?.customerSuccess || '暂无' }}</span>
      </div>
    </div>
    <el-card>
      <div class="wrap">
        <div class="left">
          <div class="list-item">
            <span class="color-blue">当月营业额：</span>
            <span>{{ monthlyTurnover || 0 }}</span
            >元
          </div>
          <div class="list-item">
            <span class="color-blue">当月记账营业额：</span>
            <span>{{ bookkeepingMonthlyTurnover || 0 }}</span
            >元
          </div>
          <div class="list-item">
            <span class="color-blue">当月地址营业额：</span>
            <span>{{ addressMonthlyTurnover || 0 }}</span
            >元
          </div>
          <div class="list-item">
            <span class="color-blue">账款总额：</span>
            <span>{{ detail.totalPayment || 0 }}</span
            >元
          </div>
          <div class="list-item">
            <span class="color-blue">已收款：</span>
            <span>{{ detail.payment || 0 }}</span
            >元
          </div>
          <div class="list-item-b">
            <el-progress :percentage="detail.totalReturnRate && multiply(detail.totalReturnRate, 100)" :format="format" />
          </div>
          <div class="list-item" v-if="detail.totalArrears">
            <span class="color-red">总欠费合计：</span>
            <span>{{ detail.totalArrears || 0 }}</span
            >元
          </div>
          <template v-for="(item, index) in detail.arrearageAmonut" :key="index">
            <div class="list-item" v-if="item.arrearageAmount">
              <span class="color-gray">{{ item.feeTypeName }}：</span>
              <span>{{ item.arrearageAmount || 0 }}</span
              >元
            </div>
          </template>
          <div class="list-item">
            <span class="color-green">记账费余额：</span>
            <span>{{ detail.walletAmount || 0 }}</span
            >元
          </div>
          <!-- <div class="list-item" v-if="detail.commercialBusinessArrears">
            <span class="color-gray">工商业务欠费：</span>
            <span>{{ detail.commercialBusinessArrears || 0 }}</span
            >元
          </div>
          <div class="list-item" v-if="detail.addressFeeArrears">
            <span class="color-gray">地址费欠费：</span>
            <span>{{ detail.addressFeeArrears || 0 }}</span
            >元
          </div>
          <div class="list-item" v-if="detail.agencyBookkeepingFeeArrears">
            <span class="color-gray">代理记账费欠费：</span>
            <span>{{ detail.agencyBookkeepingFeeArrears || 0 }}</span
            >元
          </div> -->
        </div>
      </div>
    </el-card>
    <div ref="monthlyTurnoverRef" style="height: 250px" />
    <el-radio-group v-model="initParam.type">
      <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
    </el-radio-group>
    <!-- 此处列表不变动，使用v-show切换 -->
    <div class="my-table" v-show="initParam.type === 0">
      <ProTable
        ref="proTable1"
        title="应收台账"
        :isShowSearch="false"
        :init-param="initParam"
        :columns="columnsAccount"
        :toolButton="false"
        rowKey="id"
        :request-api="financePaymentList"
      >
        <template #paymentNo="{ row }">
          <span class="blue-text" @click="handleShowAccountsDetail(row, row.id)">{{ row.paymentNo }}</span>
        </template>
        <template #contractNo="{ row }">
          <span class="blue-text" @click="handlShowContractDetail(row.contractId)">{{ row.contractNo }}</span>
        </template>
        <template #action="{ row }">
          <el-button v-if="row.paymentStatus !== 'close'" type="primary" text @click="handleRelateCollection(row)"
            >关联收款</el-button
          >
          <span v-else>--</span>
        </template>
      </ProTable>
    </div>
    <div class="my-table" v-show="initParam.type === 1">
      <ProTable
        ref="proTable2"
        title="收款台账"
        :isShowSearch="false"
        :init-param="initParam"
        :columns="columnsCollection"
        :toolButton="false"
        rowKey="id"
        :request-api="getFinanceReceiptList"
      >
        <!-- 收款单 -->
        <template #receiptNo="{ row }">
          <span class="blue-text" @click="handleShowCollectionDetail(row.id)">{{ row.receiptNo }}</span>
        </template>
        <!-- 账单 -->
        <template #paymentNo="{ row }">
          <span class="blue-text" @click="handleShowAccountsDetail(row, row.paymentId)">{{ row.paymentNo }}</span>
        </template>
        <!-- 合同 -->
        <template #contractNo="{ row }">
          <span class="blue-text" @click="handlShowContractDetail(row.contractId)">{{ row.contractNo }}</span>
        </template>
      </ProTable>
    </div>
    <div class="my-table" v-show="initParam.type === 2">
      <ProTable
        ref="proTable3"
        title="月营业额明细"
        :isShowSearch="false"
        :init-param="initParamStatement"
        :columns="columnsStatement"
        :requestAuto="false"
        :toolButton="false"
        rowKey="id"
        :request-api="turnoverStatementGetMonthlyTurnoverDetailTab"
        :hideBtn="true"
        :row-class-name="tableRowClassName"
      >
        <!-- 账单 -->
        <template #paymentNo="{ row }">
          <span class="blue-text" @click="handleShowAccountsDetail(row, row.paymentId)">{{ row.paymentNo }}</span>
        </template>
      </ProTable>
    </div>
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
    </template>
    <contractDetail v-if="contractDetailShow" :id="contractId" :hideActionBtn="true" @on-close="contractDetailShow = false" />
  </el-dialog>
</template>

<script setup lang="jsx">
import { turnoverStatementDetail, turnoverStatementGetMonthlyTurnoverDetailTab } from '@/api/finance/account-statement'
import { financePaymentList, financePaymentGetById } from '@/api/finance/accounts-receivable'
import { getFinanceReceiptList, getFinanceReceiptGetById } from '@/api/finance/collection-ledger'
import { postFinanceReceiptSaveOrUpdate } from '@/api/finance/collection-ledger'
import accountsForm from '@/views/finance/accounts-receivable/components/form.vue'
import collectionForm from '@/views/finance/collection-ledger/components/form.vue'
import * as echarts from 'echarts'
import { useDialog } from '@/hooks/useDialogFinance'
import { multiply } from '@/utils/math'
import bus from 'vue3-eventbus'
import contractDetail from '@/views/contract-manage/contract-list/components/contract-detail.vue'
import dayjs from 'dayjs'

const { proxy } = getCurrentInstance()

const tabs = [
  { dicValue: 0, dictLabel: '应收账款' },
  { dicValue: 1, dictLabel: '收款单' },
  { dicValue: 2, dictLabel: '月营业额明细' }
]

const emit = defineEmits()
const handleClose = () => {
  emit('on-close')
}

const props = defineProps({
  customerNo: String,
  monthlyTurnoverList: Array,
  monthlyTurnover: String,
  bookkeepingMonthlyTurnover: Number,
  addressMonthlyTurnover: Number
})

const format = percentage => `总回款率 ${percentage}%`

const visible = ref(true)
const detail = ref({})
const monthlyTurnoverRef = ref(null)
const getDetail = () => {
  turnoverStatementDetail({ customerNo: props.customerNo }).then(res => {
    detail.value = res.data
    drawChart()
    handleMonthChange(dayjs().format('YYYY-MM'))
  })
}

const drawChart = () => {
  const monthlyTurnoverIntance = echarts.init(monthlyTurnoverRef.value)
  monthlyTurnoverIntance.setOption({
    dataZoom: [
      {
        type: 'slider',
        show: props.monthlyTurnoverList.length / 12 > 1,
        start: 0,
        end: props.monthlyTurnoverList.length / 12 > 1 ? 100 / (props.monthlyTurnoverList.length / 12) : 100 // 控制滚动条的位置，这个例子中，默认展示前50%的数据
      }
    ],
    xAxis: {
      type: 'category',
      data: props.monthlyTurnoverList.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    legend: {
      show: true
    },
    series: [
      {
        name: '月营业额',
        data: props.monthlyTurnoverList.map(item => item.amount),
        type: 'bar',
        showBackground: true,
        label: {
          show: true,
          position: 'top'
        },
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  })
}

watch(
  () => props.customerNo,
  async () => {
    getDetail()
  },
  {
    immediate: true
  }
)

const proTable1 = ref(null)
const proTable2 = ref(null)
const proTable3 = ref(null)
const initParam = reactive({ customerNo: props.customerNo, type: 0 })
const initParamStatement = reactive({ customerNo: props.customerNo })
const columnsAccount = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'paymentNo',
    label: '账单编号',
    width: '200'
  },
  {
    prop: 'feeType',
    label: '费用类别',
    width: '150',
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    }
  },
  {
    prop: 'paymentAmount',
    label: '账款金额', // 账款金额->账款金额总计
    width: '120',
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'discount',
    label: '优惠',
    width: '200',
    isColShow: false,
    render: scope => {
      return (
        <span>
          {scope.row.discount === '金额优惠'
            ? `金额优惠：${scope.row.discountAmount}元`
            : scope.row.discount === '时长优惠'
            ? `时长优惠：${scope.row.discountTime}月`
            : scope.row.discount === '无优惠'
            ? '无优惠'
            : scope.row.discount === '活动优惠'
            ? `活动优惠：${scope.row.activityTxt}`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'allReceiptAmount',
    label: '已收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.allReceiptAmount >= 0 ? `${scope.row.allReceiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'allReceivableAmount',
    label: '应待收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.allReceivableAmount >= 0 ? `${scope.row.allReceivableAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'paymentDate',
    label: '账期',
    width: '100',
    render: scope => {
      return <span>{scope.row.paymentDate ? `${scope.row.paymentDate}月` : '--'}</span>
    }
  },
  {
    prop: 'paymentStartTime',
    label: '账期开始时间',
    width: '120'
  },
  {
    prop: 'paymentEndTime',
    width: '120',
    label: '账期结束时间'
  },
  {
    prop: 'contractNo',
    label: '关联合同',
    width: '150'
  },
  {
    prop: 'action',
    label: '操作',
    width: 120,
    isColShow: false,
    fixed: 'right'
  }
]
const columnsCollection = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'receiptNo',
    label: '收款单编号',
    width: '200',
    isColShow: false
  },
  {
    prop: 'feeType',
    label: '费用类别',
    width: '150',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    }
  },
  {
    prop: 'payee',
    width: '100',
    label: '收款人'
  },
  {
    prop: 'receiptAmount',
    width: '100',
    label: '收款金额',
    render: scope => {
      return <span>{scope.row.receiptAmount || 0}元</span>
    }
  },
  {
    prop: 'receiptDate',
    width: '180',
    label: '收款时间'
  },
  {
    prop: 'receiptMethod',
    width: '100',
    label: '收款渠道'
  },
  {
    prop: 'paymentNo',
    width: '150',
    label: '关联账单'
  },
  {
    prop: 'mark',
    width: '100',
    label: '收款备注'
  },
  {
    prop: 'contractNo',
    label: '关联合同',
    width: '150'
  }
]
const columnsStatement = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'analysisMonth',
    label: '所属月份',
    width: '120',
    render: scope => {
      return <span>{proTable3.value?.searchParam.analysisMonth}</span>
    },
    search: {
      render: ({ searchParam }) => {
        return (
          <elDatePicker
            modelValue={searchParam.analysisMonth}
            onChange={value => {
              handleMonthChange(value)
            }}
            type="month"
            valueFormat="YYYY-MM"
            clearable={false}
            editable={false}
          />
        )
      }
    }
  },
  {
    prop: 'monthAmount',
    label: '月营业额',
    width: '120',
    render: scope => {
      return <span>{scope.row.monthAmount >= 0 ? `${scope.row.monthAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'paymentNo',
    label: '来源账单',
    width: '200'
  },
  {
    prop: 'typeName',
    label: '费用类别',
    width: '150',
    render: scope => {
      return <span>{scope.row.typeName || '--'}</span>
    }
  },
  {
    prop: 'paymentAmount',
    label: '账款金额', // 账款金额->账款金额总计
    width: '120',
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'discount',
    label: '优惠',
    width: '200',
    isColShow: false,
    render: scope => {
      return (
        <span>
          {scope.row.discount === '金额优惠'
            ? `金额优惠：${scope.row.discountAmount}元`
            : scope.row.discount === '时长优惠'
            ? `时长优惠：${scope.row.discountTime}月`
            : scope.row.discount === '无优惠'
            ? '无优惠'
            : scope.row.discount === '活动优惠'
            ? `活动优惠：${scope.row.activityTxt}`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'sumReceiptAmount',
    label: '已收金额',
    width: '100',
    render: scope => {
      return <span>{scope.row.sumReceiptAmount >= 0 ? `${scope.row.sumReceiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'paymentDate',
    label: '账期',
    width: '100',
    render: scope => {
      return <span>{scope.row.paymentDate ? `${scope.row.paymentDate}月` : '--'}</span>
    }
  },
  {
    prop: 'paymentStartTime',
    label: '账期开始时间',
    width: '120'
  },
  {
    prop: 'paymentEndTime',
    width: '120',
    label: '账期结束时间'
  },
  {
    prop: 'lastReceiptDate',
    label: '更新时间',
    width: '120'
  }
]

const tableRowClassName = ({ row, rowIndex }) => {
  if (
    dayjs(dayjs(row.lastReceiptDate).format('YYYY-MM')).valueOf() > dayjs(proTable3.value?.searchParam.analysisMonth).valueOf()
  ) {
    return 'warning-row'
  }
  return ''
}

const handleMonthChange = value => {
  proTable3.value.searchParam.analysisMonth = value
  proTable3.value?.search()
  console.log('handleMonthChange', value, proTable3.value.searchParam)
}

const { showDialog } = useDialog()
/* 账单详情弹窗 ---start--- */
const handleShowAccountsDetail = (row, id) => {
  console.log('id', id)
  showDialog({
    title: '账单详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: accountsForm,
    getApi: financePaymentGetById,
    requestParams: { id }
  })
  nextTick(() => {
    bus.emit('paymentNo', row.paymentNo)
    bus.emit('paymentId', row.paymentId || row.id)
    bus.emit('feeType', row.feeType || row.typeName)
  })
}
/* 账单弹窗 ---end--- */
/* 合同详情弹窗 ---start--- */
const contractDetailShow = ref(false)
const contractId = ref()
const handlShowContractDetail = id => {
  // proxy.$modal.msgWarning(`合同详情模块建设中!`)
  contractDetailShow.value = true
  contractId.value = id
}
/* 合同弹窗 ---end--- */
/* 收款详情弹窗 ---start--- */
const handleShowCollectionDetail = id => {
  console.log('id', id)
  showDialog({
    title: '收款详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: collectionForm,
    getApi: getFinanceReceiptGetById,
    requestParams: { id }
  })
}
/* 收款弹窗 ---end--- */
/* 操作列 ---start--- */
const handleRelateCollection = row => {
  console.log(row)
  showDialog({
    title: '关联收款',
    component: collectionForm,
    customClass: 'customer-dialog',
    rowFormData: {
      customerName: row.customerName,
      customerId: row.customerId,
      customerNo: row.customerNo,
      paymentId: row.id,
      paymentNo: row.paymentNo
    },
    submitApi: postFinanceReceiptSaveOrUpdate,
    submitCallback,
    handleRevertParams: handleRevertParamsCollection
  })
}
// 处理表单提交参数
const handleRevertParamsCollection = data => {
  if (Array.isArray(data.receiptVoucherFile) && data.receiptVoucherFile[0]) {
    const file = data.receiptVoucherFile[0]
    data.receiptVoucherFile = {
      fileSize: file.uploadSize,
      fileNames: file.newFileName,
      // bizType: 'dddd', // 假数据
      uploadBy: file.uploadBy,
      uploadTime: file.uploadTime,
      urls: file.url
    }
  } else {
    delete data.receiptVoucherFile
  }
}
const submitCallback = () => {
  setTimeout(() => {
    proxy.$modal.msgSuccess(`新增收款审核通过后将完成关联，待审核收款可在收款审批中查看`)
  }, 1000)
}
/* 操作列 ---end--- */
</script>

<style lang="scss">
.my-table {
  min-height: 300px;
  display: flex;
  .el-table__append-wrapper {
  }
  .card {
    box-sizing: border-box;
    padding: 0;
    overflow-x: hidden;
    background-color: none;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }
}
</style>
<style lang="scss" scoped>
:deep(.el-table) {
  .warning-row {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
  }
  .success-row {
    --el-table-tr-bg-color: var(--el-color-success-light-9);
  }
}
:deep(.table-search) {
  margin-top: 15px;
  margin-bottom: 0px;
}
.top {
  display: flex;
  justify-content: space-around;
  align-items: center;
  .title {
    flex: 1;
    font-weight: 700;
    font-size: 18px;
  }
  .tag-name {
    margin-right: 20px;
  }
}
.el-card {
  margin: 15px 0;
  .wrap {
    display: flex;
    width: 100%;
    padding: 25px 25px 10px;
    .left,
    .right {
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      .list-item-b {
        width: 20%;
        margin-bottom: 15px;
      }
      .list-item {
        width: 16%;
        margin-bottom: 15px;
      }
    }
    .color-blue {
      color: #409eff;
    }
    .color-red {
      color: #f56c6c;
    }
    .color-green {
      color: #67c23a;
    }
    .color-gray {
      // color: #999;
    }
  }
}

.el-progress {
  margin-left: 30px;
  min-width: 100%;
}
</style>
