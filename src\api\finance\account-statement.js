import request from '@/utils/request'

// 客户账单详情
export const turnoverStatementDetail = params => {
  return request({
    url: '/turnoverStatement/detail',
    method: 'get',
    params
  })
}

// 回款分析列表查询
export const turnoverStatementGetCollectionAnalysis = params => {
  return request({
    url: '/turnoverStatement/getCollectionAnalysis',
    method: 'get',
    params
  })
}

// 月营业额明细列表查询
export const turnoverStatementGetMonthlyTurnoverDetail = params => {
  return request({
    url: '/turnoverStatement/getMonthlyTurnoverDetail',
    method: 'get',
    params
  })
}

// 月营业额明细列表导出
export const turnoverStatementGetMonthlyTurnoverDetailExport = params => {
  return request({
    url: '/turnoverStatement/getMonthlyTurnoverDetailExport',
    method: 'post',
    data: params
  })
}

// 月营业额明细列表查询Tab
export const turnoverStatementGetMonthlyTurnoverDetailTab = params => {
  return request({
    url: '/turnoverStatement/getMonthlyTurnoverDetailTab',
    method: 'get',
    params
  })
}

// 客户账单通过customerId去查询
export const getCustomerBillDataById = params => {
  return request({
    url: '/turnoverStatement/detailByCustomerId',
    method: 'get',
    params
  })
}

// 收款面板/月营业额明细
export const getMonthlySalesBreakdown = params => {
  return request({
    url: '/kanban/payment/monthlySalesBreakdown2',
    method: 'get',
    params
  })
}

// 回款分析统计
export const getTurnoverStatementGetCollectionAnalysisStatistic = params => {
  return request({
    url: '/turnoverStatement/getCollectionAnalysisStatistic',
    method: 'get',
    params
  })
}

// 客户成功详情
export const getTurnoverStatementCustomerSuccessDetail = params => {
  return request({
    url: '/turnoverStatement/customerSuccessDetail',
    method: 'get',
    params
  })
}

// 客户成功详情月营业额实收欠费折线图
export const getTurnoverStatementMonthlyCustomerSuccessTurnover = params => {
  return request({
    url: '/turnoverStatement/monthlyCustomerSuccessTurnover',
    method: 'get',
    params
  })
}
