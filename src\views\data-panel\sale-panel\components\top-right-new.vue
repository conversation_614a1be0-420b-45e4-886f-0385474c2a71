<template>
  <dataWrap class="top-right" title="销售漏斗" ref="wrapRef" :request-api="getSaleRateData">
    <template #default>
      <div class="funnel-chart">
        <div class="chart">
          <div v-for="(item, index) in list" :key="index" class="funnel-item" :class="[classMap[index]]">
            {{ item.label }}( {{ item.num }})
            <!-- 定位 图标转化率 -->
            <div class="transfer" v-if="index !== 3">
              <span class="icon transfer-icon"></span>
              {{ item.percentage }}
            </div>
          </div>
        </div>
        <div class="right-label">
          <template v-for="(item, index) in list" :key="index">
            <div class="label-item" :class="[bgMap[index]]" v-if="index !== 3">
              <span class="disper">{{ item.disPer }}</span>
              <span class="disper-num">流失线索{{ item.disNum }}</span>
            </div>
          </template>
        </div>
      </div>
    </template>
  </dataWrap>
</template>
<script setup>
import { onMounted } from 'vue'
import dataWrap from '../../collecting-panel/components/data-wrap'
import { getSaleRateData } from '@/api/panel-data/sale'
import lineBg1 from '@/assets/images/line-bg1.png'
import lineBg0 from '@/assets/images/line-bg0.png'
import lineBg2 from '@/assets/images/line-bg2.png'
const classMap = {
  0: 'funnel-0',
  1: 'funnel-1',
  2: 'funnel-2',
  3: 'funnel-3'
}
const bgMap = {
  0: 'bg-0',
  1: 'bg-1',
  2: 'bg-2'
}

const list = ref([
  {
    label: '线索总数',
    key: 'clueNum',
    num: '',
    disPer: '',
    disNum: '',
    percentage: ''
  },
  {
    label: '转客户线索数',
    num: '1',
    key: 'changeToCusNum',
    disNum: '',
    disPer: '',
    percentage: ''
  },
  {
    label: '添加商机客户数',
    num: '',
    disNum: '',
    key: 'addBusinessNum',
    disPer: '',
    percentage: ''
  },
  {
    label: '转企业客户数',
    key: 'changeToCompanyNum',
    num: '',
    percentage: ''
  }
])
const wrapRef = ref()
const setData = () => {
  const panelData = wrapRef.value.panelData

  list.value.forEach(item => {
    item.num = Number(panelData[item['key']])
  })
  list.value.forEach((item, index) => {
    if (list.value[index + 1]) {
      item.disNum = Number(panelData.clueNum) - list.value[index + 1].num
      item.disPer = calculatePercentage(Number(panelData.clueNum) - list.value[index + 1].num, Number(panelData.clueNum))
      item.percentage = calculatePercentage(list.value[index + 1].num, list.value[index].num)
    }
  })
}

const calculatePercentage = (value, total) => {
  if (!total) {
    return ''
  } else {
    return ((Number(value) / Number(total)) * 100).toFixed(1) + '%'
  }
}
onMounted(async () => {
  await wrapRef.value.requestResult
  setData()
})
</script>
<style lang="scss" scoped>
.top-right {
  flex: 1;
  overflow: hidden;
}
:deep(.content) {
  display: flex;
}
.funnel-chart {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon {
  vertical-align: text-top;
}
.chart {
  width: 60%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 27px;
  clip-path: polygon(100% 0, 75% 100%, 25% 100%, 0 0);
  margin-left: 20px;
}
.right-label {
  height: 151px;
  width: 160px;
  position: relative;
  .label-item {
    position: absolute;
    .disper {
      position: absolute;
      bottom: 5px;
      font-size: 14px;
      left: calc(20%);
    }
    .disper-num {
      position: absolute;
      top: calc(50%);
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #2383e7;
      font-size: 14px;
      font-family: SourceHanSansCN-Medium, SourceHanSansCN;
      font-weight: 500;
      color: #2383e7;
      padding-left: 6px;
      padding-right: 6px;
    }
  }
}
.funnel-item {
  height: 21px;
  width: 100%;
  text-align: center;
  line-height: 21px;
  font-size: 12px;
  line-height: 21px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  color: #0f4da6;
  position: relative;

  .transfer {
    height: 27px;
    position: absolute;
    right: 0;
    left: 0;
    top: calc(120%);
    text-align: center;
    bottom: 0;
    font-size: 12px;
    // line-height: 27px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #2383e7;
  }
}

.funnel-0 {
  background: #daeafc;
}

.funnel-1 {
  background: #9dc8f5;
}

.funnel-2 {
  background: rgba(35, 131, 231, 0.8);
}

.funnel-3 {
  background: #2383e7;
}

.bg-0 {
  width: 83px;
  height: 55px;
  background: url('@/assets/images/line-bg0.png') no-repeat;
  .disper-num {
    z-index: 1000;
    right: -40px;
    top: 16% !important;
  }
}

.bg-1 {
  width: 130px;
  height: 103px;
  left: -20px;
  background: url('@/assets/images/line-bg1.png') no-repeat;
  .disper {
    left: calc(15%) !important;
  }
  .disper-num {
    z-index: 1000;
    right: -10px;
    top: 55% !important;
  }
}

.bg-2 {
  width: 160px;
  height: 151px;
  left: -30px;
  background: url('@/assets/images/line-bg2.png') no-repeat;
  .disper {
    left: calc(10%) !important;
  }
  .disper-num {
    z-index: 1000;
    right: 10px;
    top: 75% !important;
  }
}
</style>
