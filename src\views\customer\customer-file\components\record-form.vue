<!--
 * @Description: 跟进表单提交
 * @Author: thb
 * @Date: 2023-05-30 10:44:15
 * @LastEditTime: 2023-11-07 10:13:41
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" class="record-form" :model="formData" :rules="rules" label-position="top">
    <el-form-item label="跟进记录" prop="followUpRecord">
      <el-input type="textarea" v-model="formData.followUpRecord" maxlength="1000"> </el-input>
    </el-form-item>
    <el-form-item label="联系人" prop="contactName">
      <el-input v-model="formData.contactName" maxlength="20"> </el-input>
    </el-form-item>
    <el-form-item label="跟进方式" prop="mode">
      <el-select v-model="formData.mode" placeholder="请选择" clearable>
        <el-option label="微信沟通" value="微信沟通" />
        <el-option label="电话沟通" value="电话沟通" />
        <el-option label="线下拜访" value="线下拜访" />
        <el-option label="其他" value="其他" />
      </el-select>
    </el-form-item>
    <el-form-item label="跟进时间" prop="date">
      <el-date-picker v-model="formData.date" format="YYYY/MM/DD" value-format="YYYY-MM-DD" type="date" placeholder="请选择" />
    </el-form-item>
    <!-- 当前用户的姓名 -->
    <el-form-item label="跟进人" prop="followUpName">
      <!-- <el-select placeholder="请选择" clearable>
        <el-option :label="item.nickName" :value="item.nickName" v-for="(item, index) in userList" :key="index" />
      </el-select> -->
      <SelectTree v-model="formData.followUpId" @on-node-click="handleNodeClick" />
    </el-form-item>
    <!-- 当前用户所属部门 -->
    <el-form-item label="归属部门" prop="dept">
      <!-- <el-tree-select
        filterable
        v-model="formData.dept"
        :data="deptList"
        check-strictly
        :render-after-expand="false"
        :placeholder="disabled ? ' ' : '请选择'"
        :disabled="disabled"
        :props="defaultProps"
        clearable
      /> -->
      <el-input v-model="formData.dept" disabled=""> </el-input>
    </el-form-item>
    <div class="footer">
      <el-button class="pain-btn" pain @click="handleSubmit(formRef)">提交</el-button>
      <el-button @click="handleReset(formRef)">清空</el-button>
    </div>
  </el-form>
  <revivification :detail="detail" v-if="revShow" @on-close="revShow = false" @on-success="handleSuccess" />
</template>
<script setup>
import { saveRecord } from '@/api/customer/file'
import revivification from './revivification.vue'
// import { listUser } from '@/api/system/user'
// import { listDept } from '@/api/system/dept'
import useUserStore from '@/store/modules/user'
import { ElMessageBox } from 'element-plus'
import SelectTree from '@/components/SelectTree'
const userStore = useUserStore()
const formData = ref({
  followUpRecord: '',
  contactName: '',
  followUpName: '',
  date: '',
  mode: '',
  dept: ''
})
const emits = defineEmits('on-success')

const handleSuccess = () => {
  revShow.value = false
  emits('on-success')
}
const props = defineProps({
  ciId: Number,
  discard: Number,
  detail: Object
})
const rules = {
  followUpRecord: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  contactName: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  mode: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  date: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ]
}

const formRef = ref()
const { proxy } = getCurrentInstance()
const revShow = ref(false)
const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      console.log('discard', props.discard)
      if (props.discard === 1) {
        // 表示废弃
        // 废弃的情况下
        ElMessageBox.confirm('请注意该客户已被废弃,是否继续提交跟进记录', '注意', {
          confirmButtonText: '提交记录',
          cancelButtonText: '还原客户',
          distinguishCancelAndClose: true,
          type: 'warning'
        })
          .then(async () => {
            // 提交记录
            // contractShow.value = true
            const result = await saveRecord({
              ...formData.value,
              ciId: props.ciId
            })
            if (result.code === 200) {
              // 提交成功
              proxy.$modal.msgSuccess(`提交成功!`)
              // 同时清空
              handleReset(formEl)
            } else {
              //提交失败
              proxy.$modal.msgError(`提交失败!`)
            }
          })
          .catch(action => {
            if (action === 'cancel') {
              // 还原客户
              revShow.value = true
            }
          })
        return
      }
      const result = await saveRecord({
        ...formData.value,
        ciId: props.ciId
      })
      if (result.code === 200) {
        // 提交成功
        proxy.$modal.msgSuccess(`提交成功!`)
        handleReset(formEl)
      } else {
        //提交失败
        proxy.$modal.msgError(`提交失败!`)
      }
    } else {
    }
  })
}

const handleReset = formEl => {
  if (!formEl) return
  formEl.resetFields()
  // 默认展示跟进人 和 归属部门
  // 跟进人默认当前用户
  formData.value.followUpName = userStore.user.nickName
  formData.value.followUpId = userStore.user.userId
  // 归属部门默认当前用户的部门
  formData.value.dept = userStore.user.dept.deptName
}
/** 查询用户列表 */
// const userList = ref([])
// const getUserList = async () => {
//   const result = await listUser()
//   userList.value = result.rows || []
// }
/** 查询部门列表 */
// const deptList = ref([])
// const defaultProps = {
//   value: 'deptId',
//   label: 'deptName',
//   children: 'children'
// }
// function getList() {
//   listDept().then(response => {
//     deptList.value = proxy.handleTree(response.data, 'deptId')
//   })
// }

// 节点点击
const handleNodeClick = (node, node1) => {
  formData.value.followUpName = node.label
  formData.value.dept = node1.parent.data.label
}
onMounted(async () => {
  // await getUserList()
  // await getList()
  // 跟进人默认当前用户
  console.log('userStore.user', userStore.user)
  nextTick(() => {
    formData.value.followUpId = userStore.user.userId
    formData.value.followUpName = userStore.user.nickName
    // 归属部门默认当前用户的部门
    formData.value.dept = userStore.user.dept.deptName
  })
})
</script>
<style lang="scss" scoped>
.record-form {
  :deep(.el-input.el-input--default) {
    width: 100%;
  }
}
.footer {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-button {
    flex: 1;
  }
}

.pain-btn {
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #2383e7;
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #2383e7;
  &:hover {
    color: #fff;
    background: #2383e7;
  }
}
</style>
