<!--
 * @Description: 新增合同
 * @Author: thb
 * @Date: 2023-06-20 09:17:29
 * @LastEditTime: 2023-07-26 17:04:54
 * @LastEditors: thb
-->
<template>
  <div class="container">
    <el-steps :active="active" align-center finish-status="success">
      <el-step title="步骤1" description="新增合同"> </el-step>
      <el-step title="步骤2" description="创建合同" />
    </el-steps>
    <el-form ref="formRef" :model="formData" label-position="top">
      <el-radio-group v-model="formData.radio" class="radio-group">
        <el-radio label="0" size="large">已有纸质合同,录入信息并上传附件</el-radio>
        <el-radio label="1" size="large">使用模板创建合同</el-radio>
      </el-radio-group>
      <el-button type="primary" style="margin-top: 24px" @click="handelNext">下一步</el-button>
    </el-form>
  </div>

  <!-- <contract v-if="contractShow" @on-close="contractShow = false" @on-next="handelNext" /> -->
  <normalCreate v-if="normalShow" @on-close="normalShow = false" />
  <templateCreate v-if="templateShow" @on-close="templateShow = false" />
</template>
<script setup lang="ts">
import { ref } from 'vue'
// import contract from '@/views/customer/customer-file/components/contract.vue'
import templateCreate from '@/views/contract-manage/contract-list/components/template-create.vue'
import normalCreate from '@/views/contract-manage/contract-list/components/normal-create.vue'
import { useRouter } from 'vue-router'

const active = ref(0)

const formData = ref({
  radio: '0'
})
// 手动新建还是模板新建的弹窗显示标志
const contractShow = ref(false)

// 手动新建的弹窗显示标志
const normalShow = ref(false)

const templateShow = ref(false)
// 下一步
const handelNext = () => {
  // '0'代表 普通新建
  // '1'代表 模板新建
  if (formData.value.radio === '0') {
    normalShow.value = true
  }
  if (formData.value.radio === '1') {
    templateShow.value = true
  }
}

// 关闭窗口 默认跳转到 合同台账
// const handleClose = () => {}

// 添加合同成功后需要跳转到合同台账页面
// const router = useRouter()
// const handleSuccess = () => {
//   router.push('/contract-manage/contract-list')
// }
</script>
<style lang="scss" scoped>
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.el-form {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.el-radio-group.radio-group {
  flex-direction: column;
  align-items: start;
}
</style>
