<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-09-11 14:35:14
 * @LastEditTime: 2023-09-15 16:46:35
 * @LastEditors: thb
-->
<template>
  <div class="data-wrap">
    <div class="wrap-top">
      <topLeft />
      <topRight />
    </div>
    <div class="wrap-mid">
      <middleLeft />
      <middleRight />
    </div>
    <div class="wrap-bottom"><bottom /></div>
  </div>
</template>
<script setup>
import topLeft from './components/top-left'
import topRight from './components/top-right'
import middleLeft from './components/middle-left'
import middleRight from './components/middle-right'
import bottom from './components/bottom'
</script>
<style lang="scss" scoped>
.data-wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 16px;
  width: 100%;
  .wrap-top {
    flex: 1;
    display: flex;
    gap: 16px;
  }
  .wrap-mid {
    flex: 1;
    display: flex;
  }
  .wrap-bottom {
    flex: 1;
    display: flex;
  }
}
</style>
