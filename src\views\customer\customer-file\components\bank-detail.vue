<!--
 * @Description: 银行信息
 * @Author: thb
 * @Date: 2023-05-26 16:10:23
 * @LastEditTime: 2023-11-28 10:26:07
 * @LastEditors: thb
-->
<template>
  <el-form :model="formData" label-position="top" :hide-required-asterisk="disabled">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="基本户开户银行">
          <!-- <el-select
            class="select-class"
            :disabled="disabled"
            v-model="formData.bankBaseName"
            :placeholder="disabled ? ' ' : '请选择'"
            clearable
          >
            <el-option v-for="(option, index) in bank_list" :key="index" :label="option.label" :value="option.value" />
          </el-select> -->
          <!-- 采用级联选择器 -->
          <!-- 导入企业信息，存在bank_list之外的数据，cascader不能直接展示 -->
          <el-input v-if="disabled" v-model="formData.bankBaseName" disabled />
          <el-cascader
            v-else
            class="select-class"
            v-model="formData.bankBaseName"
            filterable
            clearable
            :placeholder="disabled ? ' ' : '请选择'"
            :disabled="disabled"
            :props="{
              value: 'name',
              label: 'name',
              children: 'child'
            }"
            :options="bank_list"
            :show-all-levels="false"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="基本户账号">
          <el-input
            v-model="formData.bankBaseAccount"
            maxlength="100"
            autocomplete="off"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>

      <!-- <el-col :span="8">
        <el-form-item label="网银">
          <el-radio-group v-model="formData.internetbankFlag" :disabled="disabled">
            <el-radio label="0" size="large">无</el-radio>
            <el-radio label="1" size="large">有</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col> -->
    </el-row>

    <el-row :gutter="24">
      <!-- <el-col :span="8">
        <el-form-item label="结算卡">
          <el-radio-group v-model="formData.debitCardFlag" :disabled="disabled">
            <el-radio label="0" size="large">无</el-radio>
            <el-radio label="1" size="large">有</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col> -->
      <el-col :span="12">
        <el-form-item label="回单卡">
          <el-radio-group v-model="formData.receiptCardFlag" :disabled="disabled" @change="handleChange">
            <el-radio label="0" size="large">无</el-radio>
            <el-radio label="1" size="large">有</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-show="formData.receiptCardFlag === '1'">
        <el-form-item label="回单卡类型">
          <el-radio-group v-model="formData.receiptCardType" :disabled="disabled" @change="changeClear">
            <el-radio label="卡" size="large">卡</el-radio>
            <el-radio label="账号密码" size="large">账号密码</el-radio>
            <el-radio label="自动获取" size="large">自动获取</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- <el-row :gutter="24"> </el-row> -->
    <!-- 回单卡类型为 卡-->
    <el-row :gutter="24" v-show="formData.receiptCardType === '卡'">
      <el-col :span="12">
        <el-form-item label="回单卡密码">
          <el-input
            v-model="formData.receiptCardPassword"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24" v-show="formData.receiptCardType === '账号密码'">
      <el-col :span="12">
        <el-form-item label="回单卡账号">
          <el-input
            v-model="formData.receiptCardAccount"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="回单卡密码">
          <el-input
            v-model="formData.receiptCardPassword"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <Collapse title="一般户信息">
      <template #button v-if="!disabled">
        <el-button type="primary" @click.stop="handleAddRow"> 新增 </el-button>
      </template>
      <el-table :data="formData.commonList" style="width: 100%">
        <el-table-column prop="commonBankName" width="200" label="一般户开户银行">
          <template #default="{ row }">
            <el-tooltip
              :content="row.commonBankName?.length ? row.commonBankName[row.commonBankName.length - 1] : ''"
              v-if="disabled && row.commonBankName?.length"
              effect="light"
              placement="top-start"
            >
              <!-- <el-select :disabled="disabled" v-model="row.commonBankName" :placeholder="disabled ? ' ' : '请选择'" clearable>
                <el-option v-for="(option, index) in common_bank_list" :key="index" :label="option.name" :value="option.name" />
              </el-select> -->
              <div style="width: 100%">
                <el-input v-model="row.commonBankName" disabled />
              </div>
            </el-tooltip>
            <!-- <el-select
              v-else
              :disabled="disabled"
              v-model="row.commonBankName"
              :placeholder="disabled ? ' ' : '请选择'"
              clearable
            >
              <el-option v-for="(option, index) in common_bank_list" :key="index" :label="option.name" :value="option.name" />
            </el-select> -->

            <!-- 采用级联选择器 -->
            <el-cascader
              v-else
              v-model="row.commonBankName"
              filterable
              clearable
              :placeholder="disabled ? ' ' : '请选择'"
              :disabled="disabled"
              :props="{
                value: 'name',
                label: 'name',
                children: 'child'
              }"
              :options="bank_list"
              :show-all-levels="false"
            />
          </template>
        </el-table-column>
        <el-table-column prop="commonBankAccount" width="200" label="一般户账号">
          <template #default="{ row }">
            <el-tooltip
              :content="row.commonBankAccount"
              v-if="disabled && row.commonBankAccount"
              effect="light"
              placement="top-start"
            >
              <el-input
                v-model="row.commonBankAccount"
                maxlength="100"
                :disabled="disabled"
                :placeholder="disabled ? '' : '请输入'"
              />
            </el-tooltip>
            <el-input
              v-else
              v-model="row.commonBankAccount"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </template>
        </el-table-column>
        <el-table-column prop="commonInternetbankFlag" width="200" label="一般户网银">
          <template #default="{ row }">
            <customSelect :disabled="disabled" v-model="row.commonInternetbankFlag" />
          </template>
        </el-table-column>
        <el-table-column prop="commonReceiptCardFlag" width="150" label="一般户回单卡">
          <template #default="{ row }">
            <el-select
              :disabled="disabled"
              v-model="row.commonReceiptCardFlag"
              :placeholder="disabled ? ' ' : '请选择'"
              @change="value => handleChange2(value, row)"
              clearable
            >
              <el-option label="无" value="0"></el-option>
              <el-option label="有" value="1"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <!-- 回单卡为有才显示 -->
        <el-table-column prop="commonInternetbankType" width="150" label="一般户回单卡类型">
          <template #default="{ row }">
            <el-select
              v-if="row.commonReceiptCardFlag === '1'"
              :disabled="disabled"
              v-model="row.commonInternetbankType"
              :placeholder="disabled ? ' ' : '请选择'"
              @change="value => handleChange3(value, row)"
              clearable
            >
              <el-option label="卡" value="卡"></el-option>
              <el-option label="账号密码" value="账号密码"></el-option>
              <el-option label="自动获取" value="自动获取"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <!-- 回单卡类型为账号密码才显示-->
        <el-table-column prop="commonInternetbankAccount" width="150" label="一般户回单卡账号">
          <template #default="{ row }">
            <template v-if="row.commonInternetbankType === '账号密码'">
              <el-tooltip
                :content="row.commonInternetbankAccount"
                v-if="disabled && row.commonInternetbankAccount"
                effect="light"
                placement="top-start"
              >
                <el-input
                  v-model="row.commonInternetbankAccount"
                  maxlength="100"
                  :disabled="disabled"
                  :placeholder="disabled ? '' : '请输入'"
                />
              </el-tooltip>
              <el-input
                v-else
                v-model="row.commonInternetbankAccount"
                maxlength="100"
                :disabled="disabled"
                :placeholder="disabled ? '' : '请输入'"
              />
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="commonReceiptCardPassword" width="150" label="一般户回单卡密码">
          <template #default="{ row }">
            <el-input
              v-if="row.commonInternetbankType === '账号密码' || row.commonInternetbankType === '卡'"
              v-model="row.commonReceiptCardPassword"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作" width="100" fixed="right" v-if="!disabled">
          <template #default="{ $index }">
            <el-button text type="danger" @click="handleDeleteRow($index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </Collapse>
    <!-- <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="对账周期">
          <el-select
            class="select-class"
            v-model="formData.cycle"
            :disabled="disabled"
            :placeholder="disabled ? ' ' : '请选择'"
            clearable
          >
            <el-option label="月度对账" value="月度对账" />
            <el-option label="季度对账" value="季度对账" />
            <el-option label="半年度对账" value="半年度对账" />
            <el-option label="年度对账" value="年度对账" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row> -->
    <el-row>
      <el-col :span="24">
        <el-form-item label="基本存款账户信息表(开户许可证)">
          <FileUpload v-if="!disabled" v-model="formData.accountOpenFileList" :limit="100" :isShowTip="false" />
          <!-- <span class="download-text" @click="downloadFile(formData.accountOpenFile)" v-else>
            {{ (formData.accountOpenFile && formData.accountOpenFile?.fileNames) || '暂无文件' }}</span
          > -->
          <fileList v-else :list="formData.accountOpenFileList" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="其他附件">
          <FileUpload v-if="!disabled" v-model="formData.changeInfoFileList" :limit="100" :isShowTip="false" />
          <!-- <span class="download-text" @click="downloadFile(formData.changeInfoFile)" v-else>
            {{ (formData.changeInfoFile && formData.changeInfoFile?.fileNames) || '暂无文件' }}</span
          > -->
          <fileList :list="formData.changeInfoFileList" v-else />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import { saveCustomerBank, getCustomerBankByCiId } from '@/api/customer/file'
import { getBankTreeList } from '@/api/basicData/basicData'
// import { changeFileData } from '@/utils/common'
// import { downloadFile } from '@/utils/common'
import customSelect from './custom-select.vue'
import Collapse from '@/components/Collapse'
import iFrame from '@/components/iFrame'
import fileList from '@/components/FileList'
const disabled = inject('disabled')

import { useRemote } from '@/hooks/useRemote'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const emits = defineEmits(['update:modelValue', 'on-change', 'on-edit'])
const formData = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})

const getDetail = async () => {
  const { data } = await getCustomerBankByCiId(props.modelValue.ciId)
  if (!data) {
    data = {}
  }
  if (Number(data?.commonInternetbankFlag) === 0 || Number(data?.commonInternetbankFlag) === 1) {
    // 选择了无或者有
    formData.value.commonInternetbankText = ''
  } else {
    formData.value.commonInternetbankText = data?.commonInternetbankFlag || ''
  }
  formData.value = Object.assign(formData.value, {
    ...data,
    bankBaseName: data.bankBaseName?.split(',') || [],
    commonList:
      Array.isArray(data.commonList) &&
      data.commonList.map(item => {
        return {
          ...item,
          commonBankName: item.commonBankName?.split(',') || []
        }
      })
    // commonBankName: data.commonBankName?.split(',') || []
  })
}
// 监听
watch(
  disabled,
  () => {
    if (disabled.value) {
      getDetail()
    }
  },
  {
    immediate: true
  }
)

// 监听是否是编辑
const edit = inject('isEdit')
watch(
  edit,
  async () => {
    if (edit.value) {
      await getDetail()
      emits('on-edit')
    }
  },
  {
    immediate: true
  }
)

// 单选框选择
const handleChange = value => {
  if (value === '0') {
    formData.value.receiptCardType = ''
    formData.value.receiptCardAccount = ''
    formData.value.receiptCardPassword = ''
  }
}

const handleChange2 = (value, row) => {
  if (value === '0') {
    row.commonInternetbankType = ''
    row.commonInternetbankAccount = ''
    row.commonReceiptCardPassword = ''
  }
}

const handleChange3 = (value, row) => {
  if (value !== '账户密码') {
    row.commonInternetbankAccount = ''
    row.commonReceiptCardPassword = ''
  }
}
// 一般户网银单选框切换
const handleRadioChange = value => {
  if (value === '0' || value === '1') {
    formData.value.commonInternetbankText = ''
  }
}

const { proxy } = getCurrentInstance()
// const { bank_list } = proxy.useBasicDict('bank_list')
const bank_list = ref([])
function onGetBasicData() {
  getBankTreeList({ enable: 1 }).then(res => {
    bank_list.value = res.data // 已验证，新增下拉有对应数据
    // bank_list.value.push({ name: '哈哈哈银行', value: '哈哈哈银行' })
  })
}
onGetBasicData()
const saveRemote = async () => {
  const id = await useRemote(
    saveCustomerBank,
    {
      ...formData.value,
      // bankBaseName
      bankBaseName: (Array.isArray(formData.value.bankBaseName) && formData.value.bankBaseName.join(',')) || '',
      commonList: formData.value.commonList.map(item => {
        return {
          ...item,
          commonBankName: (Array.isArray(item.commonBankName) && item.commonBankName.join(',')) || ''
        }
      })
    },
    ['accountOpenFile', 'changeInfoFile'],
    '银行信息',
    ['accountOpenFileList', 'changeInfoFileList']
  )
  formData.value.bankId = id
  return id
}
// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

const handleAddRow = () => {
  formData.value.commonList.push({
    commonBankName: [],
    commonBankAccount: '',
    commonInternetbankFlag: '无',
    commonReceiptCardFlag: '无',
    commonInternetbankType: '',
    commonInternetbankAccount: '',
    commonReceiptCardPassword: ''
  })
}

const handleDeleteRow = index => {
  // formData.value.commonList.splice(index)
  formData.value.commonList.splice(index, 1)
}

// changeClear
const changeClear = value => {
  if (value !== '卡') {
    formData.value.receiptCardPassword = ''
  }
  if (value !== '账号密码') {
    formData.value.receiptCardAccount = ''
    formData.value.receiptCardPassword = ''
  }

  if (value !== '自动获取') {
    formData.value.receiptCardAccount = ''
    formData.value.receiptCardPassword = ''
  }
}
defineExpose({
  saveRemote
})
</script>
<style lang="scss" scoped>
:deep(.select-class) {
  width: 100%;
}
:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
.collapse {
  margin-bottom: 20px;
}
</style>
