<template>
  <el-dialog
    v-model="visible"
    title="通知会计"
    :close-on-click-modal="false"
    :before-close="onHandleClose"
    width="500px"
    destroy-on-close
    append-to-body
  >
    <el-alert
      style="margin-bottom: 10px"
      title="该企业需要办理代理记账业务，请选择需要通知的会计人员："
      type="warning"
      show-icon
      :closable="false"
    />
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-form-item label="会计" prop="userId">
        <SelectTree style="width: 100%" v-model="formData.userId" placeholder="请选择" clearable @on-node-click="handleChange" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onHandleClose"> 取消 </el-button>
      <el-button type="primary" @click="handleSubmit"> 确认 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { bizAccountantNotify } from '@/api/certificate/certificate'
import SelectTree from '@/components/SelectTree'

const { proxy } = getCurrentInstance()

const formRef = ref(null)
const formData = reactive({
  taskId: undefined,
  userId: undefined
})

const rules = {
  userId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
}

const visible = ref(false)
const onShow = data => {
  visible.value = true
  formData.taskId = data.taskId
}
const onHandleClose = () => {
  visible.value = false
}
const handleSubmit = () => {
  formRef.value.validate(valid => {
    if (valid) {
      bizAccountantNotify(formData).then(res => {
        if (res.code === 200) {
          proxy.$message.success('操作成功')
          onHandleClose()
        }
      })
    } else {
    }
  })
}

const handleChange = node => {
  formData.userId = node.label
}

defineExpose({
  onShow
})
</script>

<style lang="scss" scoped>
:deep(.el-select) {
  width: 100%;
}
</style>
