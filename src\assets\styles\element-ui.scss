// cover some element-ui styles
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}
.el-upload {
  input[type="file"] {
    display: none !important;
  }
}
.el-upload__input {
  display: none;
}
.cell {
  .el-tag {
    margin-right: 0;
  }
}
.small-padding {
  .cell {
    padding-right: 5px;
    padding-left: 5px;
  }
}
.fixed-width {
  .el-button--mini {
    width: 60px;
    padding: 7px 10px;
  }
}
.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;
    .el-tag {
      margin-right: 0;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  position: relative;
  left: 0;
  margin: 0 auto;
  transform: none;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}
.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}
.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

/* custom card */
.card {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}

/* ProTable 不需要 card 样式（在组件内使用 ProTable 会使用到） */
.no-card {
  .card {
    padding: 0;
    background-color: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }
.table-search {
    padding: 18px 0 0 !important;
    margin-bottom: 0 !important;
  }
}

/* content-box (常用内容盒子) */
.content-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  .text {
    margin: 20px 0 30px;
    font-size: 23px;
    font-weight: bold;
    color: var(--el-text-color-regular);
  }
  .el-descriptions {
    width: 100%;
    padding: 40px 0 0;
    .el-descriptions__title {
      font-size: 18px;
    }
    .el-descriptions__label {
      width: 200px;
    }
  }
}

/* main-box (树形表格 treeFilter 页面会使用，左右布局 flex) */
.main-box {
  display: flex;
  width: 100%;
  height: 100%;
  .table-box {
    // 这里减去的是 treeFilter 组件宽度
    width: calc(100% - 230px);
  }
}

/* proTable */
.table-box,
.table-main {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  border-radius: 4px;
  // table-search 表格搜索样式
  .table-search {
    padding: 18px 18px 0;
    margin-bottom: 16px;
    border-radius: 4px;
    .el-form {
      .el-form-item__content > * {
        width: 100%;
      }

      // 去除时间选择器上下 padding
      .el-range-editor.el-input__wrapper {
        padding: 0 10px;
      }
    }
    .operation {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 18px;
    }
  }

  // 表格 header 样式
  .table-header {
    margin-bottom:12px;
    .header-button-lf {
      float: left;
    }
    .header-button-ri {
      float: right;
    }
    .el-button {
      // margin-bottom: 15px;
    }
  }

  // el-table 表格样式
  .el-table {
    flex: 1;

    // 修复 safari 浏览器表格错位 https://github.com/HalseySpicy/Geeker-Admin/issues/83
    table {
      width: 100%;
    }
    .el-table__header th {
      height: 48px;
      font-size: 15px;
      font-weight: bold;
      color: var(--el-text-color-primary);
      background: var(--el-fill-color-light);
    }
    .el-table__row {
      height: 48px;
      font-size: 14px;
      .el-table__placeholder {
        display: inline;
      }
    }

    // 设置 el-table 中 header 文字不换行，并省略
    .el-table__header .el-table__cell > .cell {
      white-space: nowrap;
      color:#333;
    }

    // 解决表格数据为空时样式不居中问题(仅在element-plus中)
    .el-table__empty-block {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      .table-empty {
        line-height: 30px;
      }
    }

    // table 中 image 图片样式
    .table-image {
      width: 50px;
      height: 50px;
      border-radius: 50%;
    }
  }

  // 表格 pagination 样式
  .el-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

.table-main{
  padding: 12px;
}
/* el-table 组件大小 */
.el-table--small {
  .el-table__header th {
    height: 40px !important;
    font-size: 14px !important;
  }
  .el-table__row {
    height: 40px !important;
    font-size: 13px !important;
  }
}
.el-table--large {
  .el-table__header th {
    height: 50px !important;
    font-size: 16px !important;
  }
  .el-table__row {
    height: 50px !important;
    font-size: 15px !important;
  }
}

/* el-step 上一步骤展示完成并展示为蓝色 */
// .el-step__head.is-success{
//   color:#409EFF!important;
//   border-color:#409EFF!important;
// }
// .el-step__title.is-success{
//   color:#409EFF!important;
// }
// .el-step__description.is-success{
//   color:#409EFF!important;
// }

    // 设置 el-table 中 header 文字不换行，并省略
    .el-table__header .el-table__cell > .cell {
      white-space: nowrap;
      color:#333;
    }


// input输入框自动填充账号密码出现背景色
input:-webkit-autofill { box-shadow: 0 0 0px 1000px white inset !important;}


// pain-primary按钮样式设置
.el-button.el-button--default.el-button--primary[pain] {
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #2383e7;
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #2383e7;
  width: 64px;
  &:hover {
    color: #fff;
    background: #2383e7;
  }
}

.el-button.el-button--default.el-button--danger[pain] {
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #F56C6C;
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #F56C6C;
  width: 64px;
  &:hover {
    color: #fff;
    background: #F56C6C;
  }
}

.el-message-box{
  .el-message-box__header{
    border-bottom: 1px solid #E8E8E8FF;
  }
  .el-message-box__btns{
    border-top: 1px solid #E8E8E8FF;
    padding-top: 10px;
  }
}