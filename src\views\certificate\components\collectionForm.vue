<template>
  <el-form :model="formData" ref="formRef" :disabled="disabledSteps.includes(2)" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-checkbox
          :disabled="formData.legalIdentityFileList.length"
          :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
          v-model="formData.legalIdentityFileListFlag"
          label="法人身份证"
        />
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item prop="legalIdentityFileList" :class="[disabledSteps.includes(2) ? 'flex-column' : '']">
              <FileUploadBiz
                :disabled="!formData.legalIdentityFileListFlag"
                v-model="formData.legalIdentityFileList"
                :limit="10"
                :isShowTip="false"
                @on-load-success="validateFormField('legalIdentityFileList')"
                v-if="!disabledSteps.includes(2)"
              />

              <template v-else>
                <!-- <template v-if="formData.legalIdentityFileList?.length">
                  <span
                    class="download-text"
                    v-for="(file, index) in formData.legalIdentityFileList"
                    :key="index"
                    @click="downloadFile(file)"
                  >
                    {{ file?.fileNames || '暂无文件' }}</span
                  >
                </template>
                <template v-else>
                  <span class="download-text"> 暂无文件 </span>
                </template> -->
                <fileList :list="formData.legalIdentityFileList" />
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="12">
        <el-checkbox
          :disabled="formData.supervisorIdentityFileList.length"
          :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
          v-model="formData.supervisorIdentityFileListFlag"
          label="监事身份证"
        />
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item prop="supervisorIdentityFileList" :class="[disabledSteps.includes(2) ? 'flex-column' : '']">
              <FileUploadBiz
                :disabled="!formData.supervisorIdentityFileListFlag"
                v-model="formData.supervisorIdentityFileList"
                :limit="10"
                :isShowTip="false"
                @on-load-success="validateFormField('supervisorIdentityFileList')"
                v-if="!disabledSteps.includes(2)"
              />
              <template v-else>
                <!-- <template v-if="formData.supervisorIdentityFileList?.length">
                  <span
                    class="download-text"
                    v-for="(file, index) in formData.supervisorIdentityFileList"
                    :key="index"
                    @click="downloadFile(file)"
                  >
                    {{ file?.fileNames || '暂无文件' }}</span
                  >
                </template>
                <template v-else>
                  <span class="download-text"> 暂无文件 </span>
                </template> -->
                <fileList :list="formData.supervisorIdentityFileList" />
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="12">
        <el-checkbox
          :disabled="formData.legalPhone.length"
          :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
          v-model="formData.legalPhoneFlag"
          label="法人手机号"
        />
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item prop="legalPhone">
              <el-input :disabled="!formData.legalPhoneFlag" v-model="formData.legalPhone" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="12">
        <el-checkbox
          :disabled="formData.supervisorPhone.length"
          :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
          v-model="formData.supervisorPhoneFlag"
          label="监事手机号"
        />
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item prop="supervisorPhone">
              <el-input :disabled="!formData.supervisorPhoneFlag" v-model="formData.supervisorPhone" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="12">
        <el-checkbox
          :disabled="formData.ownershipCertificateFileList.length"
          :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
          v-model="formData.ownershipCertificateFileListFlag"
          label="产权证明"
        />
        <el-form-item prop="ownershipCertificateFileList">
          <FileUploadBiz
            v-if="!disabledSteps.includes(2)"
            :disabled="!formData.ownershipCertificateFileListFlag"
            v-model="formData.ownershipCertificateFileList"
            :isShowTip="false"
            @on-load-success="validateFormField('ownershipCertificateFileList')"
          />
          <!-- <span class="download-text" @click="downloadFile(formData.ownershipCertificateFileList[0])" v-else>
            {{ formData.ownershipCertificateFileList[0]?.fileNames || '暂无文件' }}</span
          > -->
          <fileList :list="formData.ownershipCertificateFileList" v-else />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-checkbox
          :disabled="formData.consultationMaterialFileList.length"
          v-model="formData.consultationMaterialFileListFlag"
          label="会商资料"
        />
        <el-form-item prop="consultationMaterialFileList">
          <FileUploadBiz
            v-if="!disabledSteps.includes(2)"
            :disabled="!formData.consultationMaterialFileListFlag"
            v-model="formData.consultationMaterialFileList"
            :isShowTip="false"
          />

          <!-- <span class="download-text" @click="downloadFile(formData.consultationMaterialFileList[0])" v-else>
            {{ formData.consultationMaterialFileList[0]?.fileNames || '暂无文件' }}</span
          > -->
          <fileList :list="formData.consultationMaterialFileList" v-else />
        </el-form-item>
      </el-col>
      <template v-if="formData.bizType === 'foreign_business_registration'">
        <el-col :span="12">
          <el-checkbox
            :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
            :disabled="formData.foreignEnterpriseCertificateFileList.length"
            v-model="formData.foreignEnterpriseCertificateFileListFlag"
            label="外资企业证明"
          />
          <el-form-item prop="foreignEnterpriseCertificateFileList">
            <FileUploadBiz
              v-if="!disabledSteps.includes(2)"
              :disabled="!formData.foreignEnterpriseCertificateFileListFlag"
              v-model="formData.foreignEnterpriseCertificateFileList"
              :isShowTip="false"
              @on-load-success="validateFormField('foreignEnterpriseCertificateFileList')"
            />

            <!-- <span class="download-text" @click="downloadFile(formData.foreignEnterpriseCertificateFileList[0])" v-else>
              {{ formData.foreignEnterpriseCertificateFileList[0]?.fileNames || '暂无文件' }}</span
            > -->
            <fileList :list="formData.foreignEnterpriseCertificateFileList" v-else />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-checkbox
            :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
            :disabled="formData.translatedDocumentFileList.length"
            v-model="formData.translatedDocumentFileListFlag"
            label="翻译件"
          />
          <el-form-item prop="translatedDocumentFileList">
            <FileUploadBiz
              v-if="!disabledSteps.includes(2)"
              :disabled="!formData.translatedDocumentFileListFlag"
              v-model="formData.translatedDocumentFileList"
              :isShowTip="false"
              @on-load-success="validateFormField('translatedDocumentFileList')"
            />

            <!-- <span class="download-text" @click="downloadFile(formData.translatedDocumentFileList[0])" v-else>
              {{ formData.translatedDocumentFileList[0]?.fileNames || '暂无文件' }}</span
            > -->
            <fileList :list="formData.translatedDocumentFileList" v-else />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-checkbox
            :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
            :disabled="formData.foreignPassportFileList.length"
            v-model="formData.foreignPassportFileListFlag"
            label="外籍护照"
          />
          <el-form-item prop="foreignPassportFileList">
            <FileUploadBiz
              v-if="!disabledSteps.includes(2)"
              :disabled="!formData.foreignPassportFileListFlag"
              v-model="formData.foreignPassportFileList"
              :isShowTip="false"
              @on-load-success="validateFormField('foreignPassportFileList')"
            />

            <!-- <span class="download-text" @click="downloadFile(formData.foreignPassportFileList[0])" v-else>
              {{ formData.foreignPassportFileList[0]?.fileNames || '暂无文件' }}</span
            > -->
            <fileList :list="formData.foreignPassportFileList" v-else />
          </el-form-item>
        </el-col>
      </template>
      <el-col :span="12">
        <el-checkbox
          :disabled="formData.newRegistrationInformationConfirmationDocumentFileList.length"
          :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
          v-model="formData.newRegistrationFileListFlag"
          label="新注册信息确认单"
        />
        <el-form-item prop="newRegistrationInformationConfirmationDocumentFileList">
          <FileUploadBiz
            v-if="!disabledSteps.includes(2)"
            :disabled="!formData.newRegistrationFileListFlag"
            v-model="formData.newRegistrationInformationConfirmationDocumentFileList"
            :isShowTip="false"
            @on-load-success="validateFormField('newRegistrationInformationConfirmationDocumentFileList')"
          />

          <!-- <span
            class="download-text"
            @click="downloadFile(formData.newRegistrationInformationConfirmationDocumentFileList[0])"
            v-else
          >
            {{ formData.newRegistrationInformationConfirmationDocumentFileList[0]?.fileNames || '暂无文件' }}</span
          > -->

          <fileList :list="formData.newRegistrationInformationConfirmationDocumentFileList" v-else />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 新增股东信息表格 -->
    <el-row>
      <el-col :span="24">
        <el-checkbox
          :disabled="formData.shareholderInfoList.length"
          v-model="formData.shareholderInfoListFlag"
          label="股东信息"
        />
        <el-button
          type="primary"
          v-if="!disabledSteps.includes(2)"
          :disabled="!formData.shareholderInfoListFlag"
          style="margin-left: 24px"
          @click="handleAddRow"
          >新增</el-button
        >
        <el-form-item prop="shareholderInfoList">
          <FormTable
            ref="formTableRef"
            style="width: 100%"
            :formData="{
              tableData: formData.shareholderInfoList,
              rules: formData.rules
            }"
            :class="[disabledSteps.includes(2) ? 'invisible-star' : '']"
            :option="option"
          >
            <template #shareholderName="{ row }">
              <el-input v-model="row.shareholderName" length="20" :disabled="disabledSteps.includes(2)"></el-input>
            </template>
            <template #shareholderPhone="{ row }">
              <el-input v-model="row.shareholderPhone" length="20" :disabled="disabledSteps.includes(2)"></el-input>
            </template>
            <template #shareholderFileList="{ row, $index }">
              <FileUploadBiz
                v-model="row.shareholderFileList"
                :isShowTip="false"
                :limit="10"
                @on-load-success="() => validateFile($index)"
                v-if="!disabledSteps.includes(2)"
              />
              <template v-else>
                <!-- <div
                  class="download-text"
                  v-for="(file, index) in row.shareholderFileList"
                  :key="index"
                  @click="downloadFile(file)"
                >
                  {{ file?.fileNames || '暂无文件' }}
                </div> -->
                <fileList :list="row.shareholderFileList" />
              </template>
            </template>
            <template #action="{ $index }">
              <el-button type="danger" @click="handleDeleteRow($index)" :disabled="disabledSteps.includes(2)">删除</el-button>
            </template>
          </FormTable>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import FileUploadBiz from '@/components/FileUploadBiz'
import { FormValidators } from '@/utils/validate'
import iFrame from '@/components/iFrame'
import FormTable from '@/components/FormTable'
import fileList from './file-list'
const props = defineProps({
  formData: Object,
  disabledSteps: Array,
  collectionOrHandleStepValidateSubmit: Function
})
// 文件上传后检验
const validateFormField = field => {
  formRef.value.validateField(field)
}
const rules = {
  legalIdentityFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],

  supervisorIdentityFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],

  legalPhone: [
    { required: false, message: '请输入', trigger: ['blur', 'change'] },
    {
      trigger: 'blur',
      validator: FormValidators.mobilePhone
    }
  ],
  supervisorPhone: [
    { required: false, message: '请输入', trigger: ['blur', 'change'] },
    {
      trigger: 'blur',
      validator: FormValidators.mobilePhone
    }
  ],
  ownershipCertificateFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  // consultationMaterialFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  foreignEnterpriseCertificateFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  translatedDocumentFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  foreignPassportFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  newRegistrationInformationConfirmationDocumentFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
}
const option = ref([
  {
    prop: 'shareholderName',
    label: '股东姓名'
  },
  {
    prop: 'shareholderPhone',
    label: '股东手机号'
  },
  {
    prop: 'shareholderFileList',
    label: '股东身份证'
  },
  {
    prop: 'action',
    label: '操作'
  }
])

//删除详情中的股东信息表格操作列
if (props.disabledSteps?.includes(2)) {
  option.value.pop()
}

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

const handleAddRow = () => {
  props.formData.shareholderInfoList.push({
    taskId: props.formData.taskId_temp, // 任务id
    shareholderName: '', // 股东姓名
    shareholderPhone: '', // 股东手机号
    shareholderFileList: [] // 股东附件
  })
}

const handleDeleteRow = index => {
  props.formData.shareholderInfoList.splice(index, 1)
}

const formTableRef = ref()
const getFormTableRef = () => {
  return formTableRef.value
}

const validateShareholderInfoList = async () => {
  // 框选的情况下检验股东信息
  if (props.formData.shareholderInfoListFlag) {
    const result = await formTableRef.value.handleValidate()
    if (!result) {
      // proxy.$message.warning('股东信息需要上传完整!')
      return false
    } else {
      return true
    }
  } else {
    return true
  }
}

const formData_step_1 = reactive({
  /* 步骤2 */
  legalIdentityFileList: [],
  supervisorIdentityFileList: [],
  legalPhone: '',
  supervisorPhone: '',
  ownershipCertificateFileList: [],
  consultationMaterialFileList: [],
  foreignEnterpriseCertificateFileList: [],
  translatedDocumentFileList: [],
  foreignPassportFileList: [],
  newRegistrationInformationConfirmationDocumentFileList: []
})
// 检验除了股东表单的检验方法
const { proxy } = getCurrentInstance()
const collectionFormValidate = async () => {
  // 检验股东信息表格
  const valResult = await validateShareholderInfoList()
  const strs = ['legalPhone', 'supervisorPhone']
  // 校验 法人手机号 操作手机号 邮箱格式是否正确
  let valResult1 = true,
    valResult2 = true
  if (props.formData.legalPhone) {
    valResult1 = await formRef.value.validateField('legalPhone')
  }
  if (props.formData.supervisorPhone) {
    valResult2 = await formRef.value.validateField('supervisorPhone')
  }
  if (!valResult1 || !valResult2) {
    return false
  }

  if (!valResult) {
    return false
  } else {
    return true
    // 如果检验成功
    // formDataTemp.shareholderInfoList = formData.shareholderInfoList
  }
  return true
}
const validateFile = index => {
  formTableRef.value.formRef.validateField(`tableData.${index}.shareholderFileList`)
}
const formRef = ref()
const validateForm = async () => {
  const valid = props.collectionOrHandleStepValidateSubmit(props.formData)
  const valResult = await collectionFormValidate()
  return valid && valResult
}
defineExpose({
  getFormTableRef,
  validateForm, // submit时的校验
  collectionFormValidate //save时的检验
})
</script>
<style lang="scss" scoped>
.my-required-label {
  :deep(.el-checkbox__label) {
    &:before {
      content: '*';
      color: var(--el-color-danger);
      margin-right: 4px;
    }
  }
}
.my-required-label-extra-hide {
  :deep(.el-checkbox__label) {
    &:before {
      content: '';
    }
  }
}
.my-required-div {
  margin-bottom: 8px;
  line-height: 22px;
  font-weight: 700;
  &:before {
    content: '*';
    color: var(--el-color-danger);
    margin-right: 4px;
  }
}
.my-required-div-extra-hide {
  &:before {
    content: '';
  }
}

.invisible-star {
  :deep(.validate-icon::before) {
    content: '';
    display: none;
  }
}

.flex-column {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: start;
  }
}
</style>
