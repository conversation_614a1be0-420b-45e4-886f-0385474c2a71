<!--
 * @Description: 我发起的
 * @Author: thb
 * @Date: 2023-07-17 13:45:05
 * @LastEditTime: 2024-03-26 09:24:38
 * @LastEditors: thb
-->
<!-- 业务管理 -->

<template>
  <ProTable
    ref="proTable"
    rowKey="id"
    title="我发起的"
    :init-param="initParam"
    :columns="columns"
    :transformRequestParams="transformRequestParams"
    :request-api="getTabList"
    @on-change="selectChange"
    @sort-change="sortChange"
  >
    <!-- tabs -->
    <template #tabs>
      <el-radio-group :model-value="initParam.tabType" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >

    <template #orderNo="{ row }">
      <span
        :class="[workType === 'todo' ? (initParam.tabType === 'assignToMe' ? '' : 'blue-text') : 'blue-text']"
        @click="showDetail(row)"
        >{{ row.orderNo }}</span
      >
    </template>
    <template #orderStatus="{ row }">
      <span :class="[orderStatus[row.orderStatus]['class']]">{{ orderStatus[row.orderStatus]['label'] }}</span>
    </template>
    <template #isUrgent="{ row }">
      <el-tag :type="row.isUrgent === '0' ? '' : 'danger'">{{ row.isUrgent === '0' ? '一般' : '紧急' }}</el-tag>
    </template>

    <template #completeTime="{ row }">
      <span :class="[getStatusColor(row) ? 'danger-status' : '']">{{ transformCompleteTime(row) }}</span>
    </template>
    <template #customerName="{ row }">
      <span class="blue-text" @click="handleShowCustomerDetail(row.ciId)">{{ row.customerName }}</span>
    </template>
    <!-- 新增办理功能 -->
    <template #action="{ row }">
      <span class="blue-text" @click="handelDetail(row)" v-if="workType !== 'todo' || initParam.tabType !== 'assignToMe'">
        详情
      </span>
      <span class="blue-text" @click="handelDetail(row)" v-if="initParam.tabType === 'assignToMe'"> 办理 </span>
      <span class="blue-text" @click="deleteOrder(row.id)" v-if="workType === 'myCreate' && !row.executorName"> 删除 </span>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader="{ row }">
      <el-button
        type="primary"
        :icon="CirclePlus"
        @click="handleAddWork(row)"
        v-if="workType === 'myCreate'"
        v-hasPermi="['order:todo:assignOrback']"
        >新增</el-button
      >

      <!-- 增加列表导出功能 -->
      <el-button :icon="Download" v-if="workType === 'myCreate'" @click="handleExport">导出</el-button>
    </template>
  </ProTable>

  <workForm
    v-if="workShow"
    ref="workRef"
    :id="orderId"
    :type="type"
    :workType="workType"
    :stepShow="stepShow"
    @on-close="workClose"
    @on-success="getList"
    @on-reset="handleReset"
  />

  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="customerDetailShow = false"
    @on-list="getList"
  />
</template>
<script setup lang="tsx">
import { onMounted, ref, watch } from 'vue'
import {
  getWorkListByMyCreate,
  getListMyAssign,
  getListAll,
  getListException,
  getOrderList,
  orderExport,
  deleteOrderById
} from '@/api/work/work'
import { ColumnProps } from '@/components/ProTable/interface'
import workForm from './components/work-form.vue'
import { CirclePlus, Download } from '@element-plus/icons-vue'
import { useCustomer } from '@/hooks/useCustomer'
import useCommonStore from '@/store/modules/common'
import { progressProps } from 'element-plus'
import { getReviewerTreeData } from '@/api/process/process'
import { useHandleData } from '@/hooks/useHandleData'

const workClose = () => {
  workShow.value = false
  getList()
}
const orderStatus: any = {
  '0': {
    label: '待完成',
    class: 'default-status'
  },
  '1': {
    label: '已完成',
    class: 'success-status'
  },
  '2': {
    label: '回退',
    class: 'danger-status'
  },
  '3': {
    label: '异常',
    class: 'exception-status'
  }
}
const useCommon = useCommonStore()
const { customerDetailShow, rowId, handleShowCustomerDetail, customerDetail } = useCustomer()
// 获取当前路由
const route = useRoute()
const proTable = ref()
const transformRequestParams = data => {
  console.log('transformRequestParams', route.params, route.params.orderNo)
  if (data.createTime) {
    data.createTimeStart = data.createTime[0]
    data.createTimeEnd = data.createTime[1]
  }
  if (data.completeTime) {
    data.completeTimeStart = data.completeTime[0]
    data.completeTimeEnd = data.completeTime[1]
  }
  // if (useCommon.orderNo) {
  //   data.orderNo = useCommon.orderNo
  //   proTable.value.searchParam.orderNo = useCommon.orderNo
  //   useCommon.clearOrderNo()
  // }
}
interface WorkProps {
  workType?: string
  module?: string
  requestApi?: (params: any) => Promise<any> // 请求表格数据的 api ==> 非必传
}
// requestApi?: (params: any) => Promise<any> // 请求表格数据的 api ==> 非必传
const props = withDefaults(defineProps<WorkProps>(), {
  workType: 'myCreate',
  requestApi: getWorkListByMyCreate
})

// tabs
const initParam = reactive({ tabType: '' })
const tabs = ref<any>([])
const changeTabs = () => {
  if (props.workType === 'todo') {
    initParam.tabType = 'assignToMe'
    tabs.value = [
      {
        dictLabel: '指派给我',
        dicValue: 'assignToMe'
      },
      {
        dictLabel: '由我指派',
        dicValue: 'assignFromMe'
      }
    ]
  } else {
    initParam.tabType = 'all'
    tabs.value = [
      {
        dictLabel: '全部',
        dicValue: 'all'
      },
      {
        dictLabel: '待完成',
        dicValue: '0'
      },
      {
        dictLabel: '已完成',
        dicValue: '1'
      },
      {
        dictLabel: '回退',
        dicValue: '2'
      },

      // {
      //   dictLabel: '由我发起',
      //   dicValue: 'launch'
      // },
      {
        dictLabel: '异常工单',
        dicValue: 'exception'
      },
      {
        dictLabel: '超时工单',
        dicValue: 'overTime'
      }
    ]
  }
}
// 根据不同的页面切换不同的tab
changeTabs()

const orderTypeIdDefault = ref('')
const isRemote = ref(false)
const getTree = async () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await getOrderList({
      pageNum: 1,
      pageSize: 10000
    })

    // 将后端传回的数据结构进行转换
    const revertTreeData = (data = []) => {
      data.forEach(item => {
        item.label = item.typeName
        item.value = item.id
        item.children = item.child
        if (Array.isArray(item.children) && item.children.length) {
          revertTreeData(item.children)
        }
      })
    }
    let filterData = []
    if (props.module === 'commercial') {
      // 将data 中的工商工单 部分筛选出来
      filterData = data.filter(item => item.typeName === '工商工单')
      orderTypeIdDefault.value = filterData[0]?.id
    }

    revertTreeData(props.module === 'commercial' ? filterData : data)

    if (data) {
      resolve({
        data: props.module === 'commercial' ? filterData : data
      })
      isRemote.value = true
    } else {
      reject({
        data: []
      })
    }
  })
}

const getTreeData = () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await getReviewerTreeData()
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}

const columns = ref<any>([
  {
    prop: 'index',
    label: '序号',
    fixed: 'left',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  // {
  //   prop: 'orderNo',
  //   label: '工单编号',
  //   width: 200,
  //   fixed: 'left',
  //   search: {
  //     el: 'input'
  //     // defaultValue: route.query.orderNo
  //   }
  // },

  {
    prop: 'orderTypeId',
    width: 150,
    label: '工单类型',
    enum: getTree,
    search: {
      el: 'tree-select',
      props: {
        'check-strictly': true
      }
    },
    sortable: 'custom',
    sortName: 'order_type_id'
  },
  {
    prop: 'orderStatus',
    width: 150,
    enum: [
      {
        value: '0',
        label: '待完成'
      },
      {
        value: '1',
        label: '已完成'
      },
      {
        value: '2',
        label: '回退'
      },
      {
        value: '3',
        label: '异常'
      }
    ],
    search: {
      el: 'select'
    },
    label: '工单状态',
    sortable: 'custom',
    sortName: 'order_status'
  },
  {
    prop: 'customerName',
    label: '关联客户',
    search: { el: 'input', order: 1 },
    width: 300,
    sortable: 'custom',
    sortName: 'ci_id',
    fixed: 'left'
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: 200,
    search: { el: 'input' }
  },

  {
    prop: 'isUrgent',
    width: 150,
    enum: [
      {
        value: '0',
        label: '一般'
      },
      {
        value: '1',
        label: '紧急'
      }
    ],
    search: { el: 'select' },
    label: '紧急状态',
    sortable: 'custom',
    sortName: 'is_urgent'
  },
  {
    prop: 'noExecutorFlag',
    width: 150,
    label: '当前指派给',
    isShow: false,
    enum: [
      {
        value: true,
        label: '待派工'
      },
      {
        value: false,
        label: '在人员中选择'
      }
    ],
    search: {
      el: 'select'
    }
  },
  {
    prop: 'executor',
    width: 150,
    label: '人员选择',
    isShow: false,
    enum: getTreeData,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          },
          onChange: value => {
            console.log('人员选择change事件', value)
          }
        }
      }
    }
  },
  {
    prop: 'executorName',
    width: 150,
    label: '当前指派给',
    render: scope => {
      return <span>{scope.row.executorName || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'executor'
  },
  {
    prop: 'createTime',
    width: 200,
    label: '创建时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'create_time'
  },
  {
    prop: 'createBy',
    width: 200,
    label: '创建人',
    enum: getTreeData,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'label',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'label',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          },
          onChange: value => {
            console.log('人员选择change事件', value)
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.createBy || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'create_by'
  },
  {
    prop: 'completeTime',
    width: 200,
    // fixed: 'right',
    label: '完成时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'complete_time'
  },
  {
    prop: 'action',
    width: 200,
    fixed: 'right',
    label: '操作'
  }
])
// 监听workType,如果是我的待办todo,则需要修改列名的顺序
watch(
  () => props.workType,
  () => {
    if (props.workType === 'todo') {
      columns.value = [
        {
          prop: 'index',
          label: '序号',
          fixed: 'left',
          render: scope => {
            return <span>{scope.$index + 1}</span>
          }
        },
        {
          prop: 'orderTypeId',
          width: 150,
          label: '工单类型',
          enum: getTree,
          search: {
            el: 'tree-select',
            props: {
              'check-strictly': true
            }
          },
          sortable: 'custom',
          sortName: 'order_type_id'
        },
        // {
        //   prop: 'orderNo',
        //   label: '工单编号',
        //   width: 200,
        //   search: {
        //     el: 'input'
        //     // defaultValue: route.query.orderNo
        //   }
        // },

        {
          prop: 'orderStatus',
          width: 150,
          enum: [
            {
              value: '0',
              label: '待完成'
            },
            {
              value: '1',
              label: '已完成'
            },
            {
              value: '2',
              label: '回退'
            },
            {
              value: '3',
              label: '异常'
            }
          ],
          search: { el: 'select' },
          label: '工单状态',
          sortable: 'custom',
          sortName: 'order_status'
        },
        {
          prop: 'customerName',
          label: '关联客户',
          search: { el: 'input', order: 1 },
          width: 300,
          sortable: 'custom',
          sortName: 'ci_id',
          fixed: 'left'
        },
        {
          prop: 'customerNo',
          label: '客户编号',
          width: 200,
          search: { el: 'input' }
        },

        {
          prop: 'isUrgent',
          width: 150,
          enum: [
            {
              value: '0',
              label: '一般'
            },
            {
              value: '1',
              label: '紧急'
            }
          ],
          search: { el: 'select' },
          label: '紧急状态',
          sortable: 'custom',
          sortName: 'is_urgent'
        },
        {
          prop: 'executorName',
          width: 150,
          label: '当前指派给',
          sortable: 'custom',
          sortName: 'executor'
        },
        {
          prop: 'createTime',
          width: 200,
          label: '创建时间',
          search: {
            el: 'date-picker',
            props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
          },
          sortable: 'custom',
          sortName: 'create_time'
        },
        {
          prop: 'createBy',
          width: 200,
          label: '创建人',
          enum: getTreeData,
          search: {
            el: 'tree-select',
            props: {
              'default-expand-all': true,
              filterable: true,
              props: {
                value: 'label',
                label: 'label',
                children: 'children',
                defaultProps: {
                  value: 'label',
                  label: 'label',
                  children: 'children',
                  disabled: (data, node) => {
                    return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
                  }
                },
                onChange: value => {
                  console.log('人员选择change事件', value)
                }
              }
            }
          },
          render: scope => {
            return <span>{scope.row.createBy || '--'}</span>
          },
          sortable: 'custom',
          sortName: 'create_by'
        },
        {
          prop: 'completeTime',
          width: 200,
          // fixed: 'right',
          label: '完成时间',
          search: {
            el: 'date-picker',
            props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
          },
          sortable: 'custom',
          sortName: 'complete_time'
        },
        {
          prop: 'action',
          width: 200,
          fixed: 'right',
          label: '操作'
        }
      ]
    }
  },
  {
    immediate: true
  }
)

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// 判断完成时间
const transformCompleteTime = (row: any) => {
  // 如果存在期望时间才做判断
  if (row.expectTime) {
    // 首先判断当前状态
    if (row.orderStatus === '0') {
      // 如果是待完成状态下完成时间肯定没有
      const currentTime = new Date()
      if (currentTime <= new Date(row.expectTime + ' 23:59:59')) {
        // 如果当前时间小于期望时间
        return '--'
      } else {
        return '已超时'
      }
    } else {
      return row.completeTime || '--'
    }
  } else {
    return row.completeTime || '--'
  }
}

const getStatusColor = (row: any) => {
  if (row.expectTime && row.orderStatus === '0' && new Date() > new Date(row.expectTime + ' 23:59:59')) {
    return true
  }
  if (
    row.expectTime &&
    (row.orderStatus === '1' || row.orderStatus === '2' || row.orderStatus === '3') &&
    new Date(row.completeTime) > new Date(row.expectTime + ' 23:59:59')
  ) {
    return true
  }
}

const workShow = ref(false)

const handleAddWork = (row: any) => {
  type.value = 'add'
  workShow.value = true
}
// 查看工单详情
const type = ref('add')
const orderId = ref()

// 是否展示工单详情右侧功能区域
const stepShow = ref(true)
const handelDetail = (row: any) => {
  if (initParam.tabType === 'assignFromMe') {
    stepShow.value = false
  } else {
    stepShow.value = true
  }
  type.value = 'detail'
  orderId.value = row.id
  workShow.value = true
}

const showDetail = row => {
  if (props.workType === 'todo' && initParam.tabType === 'assignToMe') return
  handelDetail(row)
}
const getList = () => {
  proTable.value?.getTableList()
}

// handleReset 再次发起
const workRef = ref()
const handleReset = data => {
  workShow.value = false
  nextTick(() => {
    handleAddWork()
    // 重新赋值
    nextTick(() => {
      workRef.value.setFormData(data)
    })
  })
}
watch(
  () => useCommon.id,
  () => {
    if (useCommon.id && useCommon.bizType === 'order') {
      handelDetail({
        id: useCommon.id
      })
      useCommon.clearId()
      useCommon.clearBizType()
    }
    if (useCommon.id && useCommon.bizType === 'order_my_create') {
      initParam.tabType = 'launch'
      // http://10.100.1.235:8082/task-view-3627.html?tid=4vl41aze中“我的发起：显示创建人我 的工单，点击后到 由我发起 页面显示工单详情”所需，增加一个前端自定义的bizType'order_my_create'，不经过消息中心
      handelDetail({
        id: useCommon.id
      })
      useCommon.clearId()
      useCommon.clearBizType()
    }
  },
  {
    immediate: true
  }
)

const deleteColumn = (prop: string) => {
  const index = columns.value.findIndex((column: any) => column.prop === prop)
  if (index > 0) {
    columns.value.splice(index, 1)
  }
}

const addColumn = (option: any) => {
  if (option?.prop) {
    const index = columns.value.findIndex((column: any) => column.prop === option.prop)
    if (index < 0) {
      columns.value.push(option)
    }
  }
}
const getTodoTabList = async data => {
  console.log('getTodoTabList')
  if (props.module === 'commercial' && !data.orderTypeId) {
    await getTree()
    data.orderTypeId = orderTypeIdDefault.value
    proTable.value.searchParam.orderTypeId = orderTypeIdDefault.value
  }

  // await getTree()
  // 指派给我的
  if (data.tabType === 'assignToMe') {
    // 切换到指派给我的列表时 需要增加操作列
    // addColumn({
    //   prop: 'action',
    //   width: 200,
    //   fixed: 'right',
    //   isColShow: true,
    //   isShow: true,
    //   label: '操作'
    // })
    const result1 = await props.requestApi(data)
    return result1
  } else {
    // 由我指派的
    // 切换到由我指派的列表时 需要删除操作列
    // deleteColumn('action')
    const result2 = await getListMyAssign(data)
    return result2
  }
}

const editColumn = option => {
  if (option?.props) {
    const searchColumn = columns.value.find((column: any) => column.prop === option.prop)
    if (searchColumn) {
      searchColumn.search.props = option?.props
    }
  }
}
const deleteOrderStatus = () => {
  // 删除工单状态查询
  editColumn({
    prop: 'orderStatus',
    props: {
      disabled: true
    }
  })
}
const getMyCreateTabList = async data => {
  const { tabType } = data
  if (tabType === 'all') {
    editColumn({
      prop: 'orderStatus',
      props: {
        disabled: false
      }
    })
    const result1 = await getListAll({
      ...data,
      orderStatus: proTable.value.searchParam.orderStatus === '' ? '' : data.orderStatus
    })
    return result1
  } else if (tabType === 'exception') {
    proTable.value.searchParam.orderStatus = ''
    editColumn({
      prop: 'orderStatus',

      props: {
        disabled: true
      }
    })
    const result3 = await getListException({
      ...data,
      orderStatus: proTable.value.searchParam.orderStatus || '3'
    })
    return result3
  } else if (tabType === 'overTime') {
    // 超时工单 等后端接口
    // proTable.value.searchParam.orderStatus = ''
    // deleteOrderStatus()
    editColumn({
      prop: 'orderStatus',
      props: {
        disabled: false
      }
    })
    const result2 = await props.requestApi({
      ...data,
      timeoutFlag: true
    })
    return result2
  } else {
    proTable.value.searchParam.orderStatus = ''
    deleteOrderStatus()
    const result2 = await props.requestApi({
      ...data,
      orderStatus: tabType
    })
    return result2
  }
}
const tabHandlerMap = {
  todo: getTodoTabList,
  myCreate: getMyCreateTabList
}

// getTabList 获取tab下的列表
const getTabList = async (data: any) => {
  return tabHandlerMap[props.workType as keyof typeof tabHandlerMap](data)
}

const handleRadioChange = (value: string) => {
  proTable.value.pageable.pageNum = 1
  initParam.tabType = value
}

// 搜索区域条件改变触发该事件
const selectChange = (value, key) => {
  if (key === 'noExecutorFlag' && value) {
    proTable.value.searchParam.executor = ''
  }
  if (key === 'executor' && value) {
    proTable.value.searchParam.noExecutorFlag = false
  }
}

// 删除工单
const deleteOrder = async id => {
  if (!id) return
  await useHandleData(deleteOrderById, id, '删除所选工单')
  proTable.value?.getTableList()
}

// 文件导出
const { proxy } = getCurrentInstance()
const handleExport = async () => {
  const result = await orderExport({
    ...proTable.value.searchParam,
    queryType: initParam.tabType === 'all' ? 'all' : initParam.tabType === 'launch' ? 'myCreate' : 'abnormal'
  })
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}
</script>
<style lang="scss" scoped>
.default-status {
  color: #409eff;
}

.danger-status {
  color: #f56c6c;
}

.success-status {
  color: #0ec27f;
}
.exception-status {
  color: #f6a01dff;
}
</style>
