<!--
 * @Description:  人员变更
 * @Author: thb
 * @Date: 2023-06-01 10:14:48
 * @LastEditTime: 2024-01-30 14:51:01
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center width="400" :close-on-click-modal="false" :title="getTitle()" v-model="visible" @close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-form-item :label="titleMap[type]" prop="nameStr">
        <!-- <el-select v-model="formData.nameStr" placeholder="请选择" clearable>
          <el-option :label="item.nickName" :value="item.nickName" v-for="(item, index) in userList" :key="index" />
        </el-select> -->
        <SelectTree filterable v-model="formData.nameStr" placeholder="请选择" clearable @on-node-click="handleChange" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit(formRef)">确认变更</el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { changePerson } from '@/api/customer/file'
// import { listUser } from '@/api/system/user'
import SelectTree from '@/components/SelectTree'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number,
  type: Number
})

const formData = ref({})

const rules = {
  nameStr: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ]
}
const { proxy } = getCurrentInstance()
const formRef = ref()
const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      console.log('customerId', props.id)
      const data = await changePerson({
        ...formData.value,
        type: props.type,
        customerId: props.id
      })
      if (data.code === 200) {
        proxy.$modal.msgSuccess(`变更成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`变更失败!`)
      }
    } else {
    }
  })
}

// const userList = ref([])
// const getUserList = async () => {
//   const result = await listUser()
//   userList.value = result.rows || []
// }
// getUserList()
const parentNameSearch = () => {
  let nameStr = []
  const searchNameByNode = node => {
    // nameStr = nameStr + '/' + node.data.label
    if (node.level === 1) {
      return nameStr
    } else {
      nameStr.push(node.data.label)
    }
    return searchNameByNode(node.parent)
  }
  return searchNameByNode
}
const handleChange = (node, node1) => {
  const names = parentNameSearch()(node1)
  nextTick(() => {
    formData.value.userId = node.id
    formData.value.nameStr = names.reverse().join('/')
  })
}

const titleMap = {
  0: '主办会计',
  1: '开票员',
  2: '客户成功'
}
// 变更人员
const getTitle = () => {
  return '变更' + titleMap[props.type]
}
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
