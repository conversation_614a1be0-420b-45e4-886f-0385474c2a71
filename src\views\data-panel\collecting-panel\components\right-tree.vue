<!--
 * @Description: 数据面板right 选择框
 * @Author: thb
 * @Date: 2023-09-04 10:55:56
 * @LastEditTime: 2023-09-06 14:24:10
 * @LastEditors: thb
-->
<template>
  <el-tree-select
    v-model="selectValue"
    :data="options"
    check-strictly
    :props="defaultProps"
    :render-after-expand="false"
    v-bind="$attr"
  />
</template>
<script setup>
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: () => {
      return []
    }
  }
})

const defaultProps = {
  value: 'id',
  label: 'name'
}
const emits = defineEmits('update:modelValue')
const selectValue = computed({
  get: () => {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  }
})
</script>
<style lang="scss" scoped></style>
