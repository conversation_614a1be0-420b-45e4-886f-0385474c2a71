<template>
  <el-form
    ref="formRef"
    :model="formData"
    :disabled="allDisabled"
    :rules="rules"
    label-position="top"
    :hide-required-asterisk="isDisabled"
  >
    <el-row :gutter="24">
      <template v-if="isShow">
        <el-col :span="12">
          <el-form-item label="合同附件" prop="file">
            <FileUpload class="file-width" :isShowTip="false" v-model="formData.file" @on-load-success="validateLoadSuccess" />
          </el-form-item>
        </el-col>
      </template>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="服务产品" prop="productName">
          <template v-if="changeDisabled">
            <el-tree-select
              v-model="formData.productId"
              ref="treeSelectRef"
              filterable
              :data="productTreeData"
              :props="defaultPopsFunction(row)"
              @current-change="treeSelectChange"
              node-key="id"
              :render-after-expand="false"
            />
          </template>
          <el-input v-else v-model="formData.productName" maxlength="20" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="关联企业" prop="customerName">
          <!-- 点击弹窗出现客户列表  -->
          <div @click="handleShow" style="width: 100%">
            <el-input
              v-model="formData.customerName"
              readonly
              maxlength="20"
              :disabled="isDisabled || isAssociated || changeDisabled"
              placeholder="请输入"
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="客户编码" prop="customerNo">
          <el-input v-model="formData.customerNo" maxlength="20" placeholder="请输入" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="联系人(具体办理人)" prop="contactPerson">
          <el-input v-model="formData.contactPerson" maxlength="20" disabled placeholder="请输入" />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="办理人联系地址" prop="contactAddress">
          <el-input
            v-model="formData.contactAddress"
            maxlength="20"
            :disabled="isDisabled || changeDisabled"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col> -->

      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="甲方地址" prop="companyAddress">
          <el-input v-model="formData.companyAddress" maxlength="255" :disabled="isDisabled || changeDisabled" />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="甲方联系人" prop="companyPerson">
          <el-input v-model="formData.companyPerson" maxlength="20" :disabled="isDisabled || changeDisabled" />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="甲方联系方式" prop="companyPhone">
          <el-input v-model="formData.companyPhone" maxlength="20" :disabled="isDisabled || changeDisabled" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <!-- <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="身份证号码" prop="identityNumber">
          <el-input
            v-model="formData.identityNumber"
            maxlength="18"
            :disabled="isDisabled || changeDisabled"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col> -->
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="拟注册成立企业名称" prop="companyName">
          <el-input v-model="formData.companyName" maxlength="20" :disabled="isDisabled || changeDisabled" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="拟注册成立企业法定代表人姓名" prop="legalPerson">
          <el-input v-model="formData.legalPerson" maxlength="20" :disabled="isDisabled || changeDisabled" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="拟注册成立企业法定代表人联系电话" prop="legalPhone">
          <el-input v-model="formData.legalPhone" maxlength="20" :disabled="isDisabled || changeDisabled" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="托管住所地址" prop="custodyAddress">
          <el-input
            v-model="formData.custodyAddress"
            type="textarea"
            maxlength="100"
            :disabled="isDisabled"
            placeholder="请输入"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="服务费" prop="serviceCost">
          <!-- 新增 标准价 和 价格变动的选项 -->
          <el-radio-group v-model="formData.priceChangeFlag" @change="radioChange" :disabled="translateDisabled || isDisabled">
            <el-radio :label="false">标准价</el-radio>
            <el-radio :label="true" :disabled="formData.rowData?.isInContract === '1'">价格变动</el-radio>
          </el-radio-group>
          <!-- 是否在合同中定义 ，如果是在合同中定义则需要用户自己输入服务费 -->
          <!-- formData.rowData?.isInContract === '1' 为在合同中定义 需要自己输入 -->
          <NumberInput
            v-if="!formData.priceChangeFlag"
            v-model="formData.serviceCost"
            maxlength="20"
            :disabled="isDisabled || formData.rowData?.isInContract !== '1'"
            placeholder="请输入"
          >
            <!-- 如果是一次性收费 -->
            <template #suffix>
              <!-- 一次性收费 -->
              <div v-if="formData.rowData.feeType === '0'">元</div>
              <!-- 每年收费 -->
              <div v-if="formData.rowData.feeType === '1'">/年</div>
              <!-- 每月收费 -->
              <div v-if="formData.rowData.feeType === '2'">/月</div>
            </template>
          </NumberInput>
          <NumberInput v-else v-model="formData.serviceCost" maxlength="20" :disabled="isDisabled" placeholder="请输入">
            <!-- 如果是一次性收费 -->
            <template #suffix>
              <!-- 一次性收费 -->
              <div v-if="formData.rowData.feeType === '0'">元</div>
              <!-- 每年收费 -->
              <div v-if="formData.rowData.feeType === '1'">/年</div>
              <!-- 每月收费 -->
              <div v-if="formData.rowData.feeType === '2'">/月</div>
            </template>
          </NumberInput>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="以后每年" prop="everyYear">
          <NumberInput
            v-model="formData.everyYear"
            maxlength="20"
            :regFormat="/^(0+)|[^\d]+/g"
            :disabled="isDisabled"
            placeholder="请输入"
          >
            <template #suffix>
              <div>元</div>
            </template>
          </NumberInput>
        </el-form-item>
      </el-col>

      <!-- 变更合同时 新增一个变更合同开始时间-->
      <el-col
        :span="rowChange ? 12 : 6"
        v-if="(allDisabled && formData.changeStartTime) || (isDisabled && formData.changeStartTime) || (isChange && !isRenewal)"
      >
        <el-form-item label="变更开始时间" prop="changeStartTime">
          <el-date-picker
            v-model="formData.changeStartTime"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="date"
            :disabled="isDisabled"
            :disabledDate="disabledDate"
            placeholder="请选择"
          />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <template v-if="isChange && isRenewal">
          <el-form-item label="协议开始时间" prop="startTime">
            <el-date-picker
              v-model="formData.startTime"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              type="date"
              placeholder="请选择"
              @change="handleDateChange"
            />
          </el-form-item>
        </template>
        <el-form-item label="协议开始时间" prop="startTime" v-else>
          <el-date-picker
            v-model="formData.startTime"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled="isDisabled || (isChange && !isRenewal)"
            type="date"
            placeholder="请选择"
            @change="handleDateChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="服务期限" prop="monthNum">
          <NumberInput
            v-model="formData.monthNum"
            :disabled="isDisabled || (isChange && !isRenewal)"
            @change="handleInputChange"
            maxlength="20"
            placeholder="请输入"
          >
            <template #suffix>
              <div>年</div>
            </template></NumberInput
          >
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="协议结束时间" prop="endTime">
          <el-date-picker
            v-model="formData.endTime"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder=""
            disabled
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="所属公司" prop="branchOffice">
          <el-select
            style="width: 100%"
            v-model="formData.branchOffice"
            :disabled="isDisabled"
            :placeholder="isDisabled ? ' ' : '请选择'"
            clearable
            @change="handleSelectBranchOffice"
          >
            <el-option v-for="(option, index) in branch_office" :key="index" :label="option.name" :value="option.name" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="乙方联系电话" prop="contactPhone">
          <el-input
            v-model="formData.contactPhone"
            maxlength="20"
            :disabled="isDisabled || changeDisabled"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="甲方地址" prop="companyAddress">
          <el-input v-model="formData.companyAddress" maxlength="255" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="甲方联系人" prop="companyPerson">
          <el-input v-model="formData.companyPerson" maxlength="20" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="rowChange ? 12 : 6">
        <el-form-item label="甲方联系方式" prop="companyPhone">
          <el-input v-model="formData.companyPhone" maxlength="20" disabled />
        </el-form-item>
      </el-col> -->
      <el-col :span="rowChange ? 12 : 6">
        <!-- totalCost-->
        <el-form-item label="合同总金额" prop="totalCostCn">
          <el-input v-model="formData.totalCost" disabled> </el-input>
          <div>大写：{{ formData.totalCostCn }}</div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24" v-if="isChange && !isRenewal">
      <el-col :span="24">
        <el-form-item label="变更原因" prop="changeReason">
          <el-input v-model="formData.changeReason" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"> </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>

  <tableModal
    v-if="listSelectShow"
    title="关联客户"
    rowKey="customerId"
    :init-param="{ discard: 0 }"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
</template>
<script setup lang="tsx">
import { FormValidators } from '@/utils/validate'
import NumberInput from '@/components/NumberInput'
import { getCustomers } from '@/api/customer/file'
import tableModal from '@/components/tableModal'
import { ColumnProps } from '@/components/ProTable/interface'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import { changeNumMoneyToChinese } from '@/utils/index.js'
import { computed, watch, ref } from 'vue'
import { useDic } from '@/hooks/useDic'
import { getCompanyTreeList } from '@/api/basicData/basicData'
import useUserStore from '@/store/modules/user'

const disabledDate = time => {
  return !(
    time.getTime() > new Date(formData.value.startTime).getTime() - 8.64e7 &&
    time.getTime() < new Date(formData.value.endTime).getTime() + 8.64e7
  )
}

const userStore = useUserStore()
const { getDic } = useDic()
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  },
  isShow: {
    type: Boolean,
    default: true
  },
  isDisabled: {
    type: Boolean,
    default: false
  },
  isChange: {
    type: Boolean,
    default: false
  },
  isAssociated: {
    type: Boolean,
    default: false
  },
  changeDisabled: {
    type: Boolean,
    default: false
  },
  allDisabled: {
    type: Boolean,
    default: false
  },
  productTreeData: {
    type: Array,
    default: () => {
      return []
    }
  },
  rowChange: {
    type: Boolean,
    default: false
  },
  isRenewal: {
    type: Boolean,
    default: false
  }
})

const rules = {
  customerName: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  contactPerson: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  contactPhone: [
    {
      required: false,
      validator: FormValidators.allPhone,
      trigger: ['blur']
    }
  ],
  identityNumber: [
    {
      required: true,
      validator: (rules: any, value: any, callback: any) => {
        if (value === '' || value === undefined) {
          callback(new Error('请输入'))
        } else {
          FormValidators.idCardDif(rules, value, callback)
        }
      },
      trigger: ['blur']
    }
  ],
  companyName: [
    {
      required: true,
      message: '请输入拟注册成立企业名称',
      trigger: ['blur']
    }
  ],
  legalPerson: [
    {
      required: true,
      message: '请输入拟注册成立企业法定代表人姓名',
      trigger: ['blur']
    }
  ],
  legalPhone: [
    {
      required: true,
      message: '请输入拟注册成立企业法定代表人联系电话',
      validator: (rules: any, value: any, callback: any) => {
        if (value === '' || value === undefined) {
          callback('请输入')
        } else {
          FormValidators.allPhone(rules, value, callback)
        }
      },
      trigger: ['blur']
    }
  ],
  custodyAddress: [
    {
      required: true,
      message: '请输入托管住所地址',
      trigger: ['blur']
    }
  ],
  startTime: [
    {
      required: true,
      message: '请选择协议开始时间',
      trigger: ['change']
    }
  ],
  monthNum: [
    {
      required: true,
      message: '请输入服务期限',
      trigger: ['blur']
    }
  ],
  file: [
    {
      required: true,
      message: '请上传',
      trigger: ['change']
    }
  ],
  changeReason: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  serviceCost: [
    {
      required: true,
      message: '请输入服务费',
      trigger: ['blur']
    }
  ],
  contactAddress: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  companyPerson: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  branchOffice: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  companyAddress: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  companyPhone: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  changeStartTime: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
const totalCost = computed(() => {
  // 如果是在合同中定义或者是一次性收费，合同总金额的计算方式就是服务费 + 其他费用
  console.log('feeType', formData.value?.rowData.feeType)
  if (formData.value?.rowData.feeType === '0') {
    console.log('serviceCost', Number(formData.value?.serviceCost || 0))
    // feeType 为零 为一次性收费
    return formData.value?.serviceCost === '' ? '' : Number(formData.value?.serviceCost || 0)
  } else {
    // 每年收费
    if (formData.value?.rowData.feeType === '1') {
      return formData.value?.serviceCost === '' || formData.value?.monthNum === ''
        ? ''
        : Number(formData.value?.serviceCost || 0) * Number(formData.value?.monthNum || 0)
    } else {
      // 每月收费
      return formData.value?.serviceCost === '' || formData.value?.monthNum === ''
        ? ''
        : Number(formData.value?.serviceCost || 0) * (Number(formData.value?.monthNum || 0) * 12 || 0)
    }
  }
})

const emits = defineEmits(['update:modelValue'])
const formData = computed({
  get: () => {
    console.log('modelValue', props.modelValue)
    // 只有在新增合同的时候
    if (!props.isDisabled && !props.isChange && !props.allDisabled) {
      Object.assign(props.modelValue, {
        contactPerson: userStore.user.nickName
      })
    }

    return props.modelValue
  },
  set: newVal => {
    emits('update:modelValue', newVal)
  }
})
watch(
  totalCost,
  () => {
    console.log('totalCost', totalCost.value)
    const moneyCN = changeNumMoneyToChinese(totalCost.value)
    formData.value.totalCostCn = moneyCN
    formData.value.totalCost = totalCost.value
    console.log('totalCostCn', formData.value.totalCostCn)
  },
  {
    immediate: true
  }
)
const nodeSearchById = (list, id) => {
  for (const node of list) {
    if (node.id === id) {
      return node
    }
    if (node.children && node.children?.length) {
      const nodeSearch = nodeSearchById(node.children, id)
      if (nodeSearch) {
        return nodeSearch
      }
    }
  }
}

// 监听 变更 存储服务产品下的服务费
watch(
  () => props.productTreeData,
  () => {
    // 查出 当前服务产品下的服务费 存储至serviceCostCopy
    console.log('props.productTreeData', props.productTreeData)
    const node = nodeSearchById(props.productTreeData, props.modelValue.productId)
    if (props.modelValue.priceChangeFlag) {
      formData.value.serviceCostCopy = node?.quotation
    }
  }
)
const radioChange = value => {
  if (value) {
    // serviceCost 缓存
    formData.value.serviceCostCopy = formData.value.serviceCost
    formData.value.serviceCost = ''
  } else {
    if (formData.value.serviceCostCopy === undefined) {
      formData.value.serviceCostCopy = ''
    }
    formData.value.serviceCost = formData.value.serviceCostCopy + '' || formData.value.serviceCost
    formData.value.serviceCostCopy = ''
  }
}
watch(
  formData,
  () => {
    if (formData.value.priceChangeFlag && formData.value.serviceCostCopy === Number(formData.value.serviceCost)) {
      formData.value.priceChangeFlag = false
      formData.value.serviceCostCopy = ''
    }
  },
  {
    deep: true
  }
)
// 客户列表弹窗显示
const listSelectShow = ref(false)

const { proxy } = getCurrentInstance()
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]
// 选择单个客户后
const handleSelect = (data: any) => {
  console.log('data', data)
  // 关联客户的数据有 客户名称  客户id(ciId/customerId) 客户编号 联系人 联系电话 所属公司
  const {
    customerName,
    customerId,
    customerNo,
    contactPerson,
    contactPhone,
    branchOffice,
    address,
    companyAddress,
    companyPerson,
    companyPhone
  } = data
  // 填充记账合同表单
  formData.value = Object.assign(formData.value, {
    customerName,
    customerId,
    customerNo,
    companyName: customerName, // 拟注册成立企业名称
    legalPerson: contactPerson, // 拟注册成立企业法定代表人姓名
    legalPhone: contactPhone, // 拟注册成立企业法定代表人联系电话
    // contactPerson,
    // contactPhone,
    branchOffice,
    contactAddress: address,
    // companyAddress,
    companyPerson: contactPerson,
    companyPhone: contactPhone
  })

  // 自动校验联系人和联系电话
  formRef.value.validateField('contactPerson')
  // formRef.value.validateField('contactPhone')
  formRef.value.validateField('companyName')
  formRef.value.validateField('legalPerson')
  formRef.value.validateField('legalPhone')
  formRef.value.validateField('branchOffice')
  formRef.value.validateField('contactAddress')
  // formRef.value.validateField('companyAddress')
  formRef.value.validateField('companyPerson')
  formRef.value.validateField('companyPhone')
}

// 服务月份 改变触发事件
const handleInputChange = (value: string) => {
  console.log('handleInputChange', value)
  const yearGap = Number(value)
  const startTime = formData.value.startTime
  if (startTime) {
    calculateEndTime(startTime, Number(yearGap))
  }
}

// 合同起始时间改变触发事件
const handleDateChange = (value: string) => {
  const serviceYear = formData.value.monthNum
  if (serviceYear) {
    calculateEndTime(value, Number(serviceYear))
  }
}

// 计算合同结束时间
const calculateEndTime = (startTime: string, yearGap: number) => {
  const arr = startTime.split('-')

  let endTime = new Date(startTime)
  endTime.setFullYear(endTime.getFullYear() + yearGap)

  // 默认前一天
  endTime.setDate(endTime.getDate() - 1)
  let endDate = endTime.getDate() < 10 ? '0' + endTime.getDate() : endTime.getDate()
  const year = endTime.getFullYear()
  const month = endTime.getMonth() + 1
  const date = endTime.getDate()

  formData.value.endTime = year + '-' + (month < 10 ? '0' + month : month) + '-' + (date < 10 ? '0' + date : date)
}

// 处理所属公司下拉框
const branch_office = ref<any>([])
function onGetBasicData() {
  getCompanyTreeList({ enable: 1 }).then(res => {
    branch_office.value = res.data.records
  })
}
onGetBasicData()
const handleSelectBranchOffice = (value: string) => {
  // const item = branch_office.value.find((item: any) => item.name === value)
  // // console.log('handleSelectBranchOffice', item)
  // formData.value.companyPerson = item.contacts
  // formData.value.companyAddress = item.address
  // formData.value.companyPhone = item.phone
  // formRef.value.validateField('companyAddress')
  // formRef.value.validateField('companyPerson')
  // formRef.value.validateField('companyPhone')
}

// 表单校验
const formRef = ref()
const validateForm = async () => {
  console.log('validateForm')
  console.log('formRef', formRef.value)
  return await formRef.value.validate((valid: any) => {
    if (valid) {
    } else {
    }
  })
}

// 关联客户弹窗显示
const handleShow = () => {
  // 如果是详情状态 不需要显示弹窗
  if (props.isDisabled) return
  if (props.isAssociated) return
  if (props.changeDisabled) return
  listSelectShow.value = true
}
// 校验上传文件
const validateLoadSuccess = () => {
  formRef.value.validateField('file')
}

const defaultPopsFunction = row => {
  return {
    value: 'id',
    label: 'name',
    disabled: data => {
      return data?.type === '业务类型' && !data?.children.length
    }
  }
}

// 树节点选中事件触发
const treeSelectChange = node => {
  console.log('treeSelectChange', node)
  if (node.type === '产品类型') {
    formData.value.rowData.isInContract = node.isInContract
    formData.value.rowData.feeType = node.feeType
    formData.value.serviceCost = node.quotation
  }
}
defineExpose({
  validateForm
})
</script>
<style lang="scss" scoped>
:deep(.el-date-editor) {
  width: 100%;
}
</style>
