/*
 * @Description: 获取字典
 * @Author: thb
 * @Date: 2023-07-18 10:33:18
 * @LastEditTime: 2023-07-18 11:00:51
 * @LastEditors: thb
 */

import { getDicts } from '@/api/system/dict/data'
import useDictStore from '@/store/modules/dict'
export const useDic = () => {
  const getDic = (key: string, extraData?: any[]) => {
    return () => {
      return new Promise(async (resolve, reject) => {
        const dicts = useDictStore().getDict(key)
        if (dicts) {
          if (extraData) {
            resolve({
              data: dicts.concat(extraData)
            })
          } else {
            resolve({
              data: dicts
            })
          }
        } else {
          const resp: any = await getDicts(key)
          if (resp.code === 200) {
            const result = resp.data.map((p: any) => ({
              label: p.dictLabel,
              value: p.dictValue,
              elTagType: p.listClass,
              elTagClass: p.cssClass
            }))
            if (extraData) {
              // 需要添加自定义参数
              resolve({
                data: result.concat(extraData)
              })
              useDictStore().setDict(key, result)
            } else {
              resolve({
                data: result
              })
              useDictStore().setDict(key, result)
            }
          } else {
            reject({
              data: []
            })
          }
        }
      })
    }
  }
  return {
    getDic
  }
}
