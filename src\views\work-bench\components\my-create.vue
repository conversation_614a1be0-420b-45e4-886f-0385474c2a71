<!--
 * @Description: 我发起的
 * @Author: thb
 * @Date: 2023-07-17 13:45:05
 * @LastEditTime: 2024-01-30 09:37:54
 * @LastEditors: thb
-->
<!-- 业务管理 -->

<template>
  <div class="table-wrap">
    <ProTable
      ref="proTable"
      title="我发起的"
      :init-param="initParam"
      :columns="columns"
      :request-api="requestApi"
      :dataCallback="dataCallback"
      :pagination="false"
      :border="false"
      @row-click="handleRowClick"
      :row-style="{ cursor: 'pointer' }"
    >
      <!-- <template #orderNo="{ row }">
        <span class="blue-text" @click="handelDetail(row)">{{ row.orderNo }}</span>
      </template> -->
      <template #orderStatus="{ row }">
        <span
          :class="[row.orderStatus === '0' ? 'default-status' : row.orderStatus === '1' ? 'success-status' : 'danger-status']"
          >{{ row.orderStatus === '0' ? '待完成' : row.orderStatus === '1' ? '已完成' : '回退' }}</span
        >
      </template>
      <template #isUrgent="{ row }">
        <el-tag :type="row.isUrgent === '0' ? '' : 'danger'">{{ row.isUrgent === '0' ? '一般' : '紧急' }}</el-tag>
      </template>

      <template #completeTime="{ row }">
        <span :class="[getStatusColor(row) ? 'danger-status' : '']">{{ transformCompleteTime(row) }}</span>
      </template>
      <template #customerName="{ row }">
        <span class="blue-text" @click="handleShowCustomerDetail(row.ciId)">{{ row.customerName }}</span>
      </template>
    </ProTable>
  </div>

  <workForm
    v-if="workShow"
    ref="workRef"
    :id="orderId"
    :type="type"
    :workType="workType"
    @on-close="workShow = false"
    @on-success="getList"
    @on-reset="handleReset"
  />

  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="customerDetailShow = false"
    @on-list="getList"
  />
</template>
<script setup lang="tsx">
import { onMounted, ref, watch } from 'vue'
import { getWorkListByMyCreate } from '@/api/work/work'
import { ColumnProps } from '@/components/ProTable/interface'
import workForm from '@/views/work-manage/my-create/components/work-form.vue'
import { CirclePlus } from '@element-plus/icons-vue'
import { useCustomer } from '@/hooks/useCustomer'
import useCommonStore from '@/store/modules/common'
import { useRouter } from 'vue-router'

const router = useRouter()
const useCommon = useCommonStore()
const { customerDetailShow, rowId, handleShowCustomerDetail, customerDetail } = useCustomer()
const { proxy } = getCurrentInstance()

interface WorkProps {
  go?: boolean
  orderStatus?: string
  workType?: string
  requestApi?: (params: any) => Promise<any> // 请求表格数据的 api ==> 非必传
  columns?: ColumnProps[] // 列配置项  ==> 非必传
}
// requestApi?: (params: any) => Promise<any> // 请求表格数据的 api ==> 非必传
const props = withDefaults(defineProps<WorkProps>(), {
  go: false,
  orderStatus: '',
  workType: 'myCreate',
  requestApi: getWorkListByMyCreate,
  columns: () => [
    {
      prop: 'orderTypeName',
      label: '工单类型',
      minWidth: 300,
      align: 'left'
    },
    {
      prop: 'orderStatus',
      minWidth: 100,
      enum: [
        {
          value: '0',
          label: '待完成'
        },
        {
          value: '1',
          label: '已完成'
        },
        {
          value: '2',
          label: '回退'
        }
      ],
      label: '当前状态',
      align: 'left'
    },
    {
      prop: 'createTime',
      minWidth: 200,
      label: '发起时间',
      align: 'left'
    },
    {
      prop: 'isUrgent',
      minWidth: 100,
      enum: [
        {
          value: '0',
          label: '一般'
        },
        {
          value: '1',
          label: '紧急'
        }
      ],
      label: '紧急状态',
      align: 'left'
    },
    {
      prop: 'executorName',
      minWidth: 150,
      label: '处理人',
      align: 'left'
    }
  ]
})

const initParam = reactive({ pageSize: 4, orderStatus: props.orderStatus })

const handleRowClick = (row: any) => {
  if (props.go) {
    router.push({
      path: props.workType === 'myCreate' ? '/work-manage/my-create' : '/work-manage/my-deal'
    })
    useCommon.setId(row.id)
    useCommon.setBizType(props.workType === 'myCreate' ? 'order_my_create' : 'order')
  } else {
    proxy.$modal.msgWarning('当前暂无访问权限')
  }
}

// 判断完成时间
const transformCompleteTime = (row: any) => {
  // 如果存在期望时间才做判断
  if (row.expectTime) {
    // 首先判断当前状态
    if (row.orderStatus === '0') {
      // 如果是待完成状态下完成时间肯定没有
      const currentTime = new Date()
      if (currentTime <= new Date(row.expectTime + ' 23:59:59')) {
        // 如果当前时间小于期望时间
        return '--'
      } else {
        return '已超时'
      }
    } else {
      return row.completeTime || '--'
    }
  } else {
    return row.completeTime || '--'
  }
}

const getStatusColor = (row: any) => {
  if (row.expectTime && row.orderStatus === '0' && new Date() > new Date(row.expectTime + ' 23:59:59')) {
    return true
  }
  if (
    row.expectTime &&
    (row.orderStatus === '1' || row.orderStatus === '2') &&
    new Date(row.completeTime) > new Date(row.expectTime + ' 23:59:59')
  ) {
    return true
  }
}

const workShow = ref(false)

const handleAddWork = (row: any) => {
  type.value = 'add'
  workShow.value = true
}
// 查看工单详情
const type = ref('add')
const orderId = ref()
const handelDetail = (row: any) => {
  type.value = 'detail'
  orderId.value = row.id
  workShow.value = true
}

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}

// handleReset 再次发起
const workRef = ref()
const handleReset = data => {
  workShow.value = false
  nextTick(() => {
    handleAddWork()
    // 重新赋值
    nextTick(() => {
      workRef.value.setFormData(data)
    })
  })
}
// 工作台 页面 查看工单详情不需要watch
// watch(
//   () => useCommon.id,
//   () => {
//     console.log('work-bentch', useCommon.id)
//     if (useCommon.id && useCommon.bizType === 'order') {
//       handelDetail({
//         id: useCommon.id
//       })
//       useCommon.clearId()
//       useCommon.clearBizType()
//     }
//   },
//   {
//     immediate: true
//   }
// )

const dataCallback = (data: any) => {
  if (data) {
    return data.records
  }
}
</script>
<style lang="scss" scoped>
.table-wrap {
  min-height: 150px;
  display: flex;
}
.default-status {
  color: #409eff;
}

.danger-status {
  color: #f56c6c;
}

.success-status {
  color: #0ec27f;
}
</style>
