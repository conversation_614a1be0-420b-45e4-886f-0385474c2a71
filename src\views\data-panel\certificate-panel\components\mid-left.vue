<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-09-06 08:48:59
 * @LastEditTime: 2023-09-14 16:27:51
 * @LastEditors: thb
-->
<template>
  <dataWrap title="许可证" class="mid-left" :request-api="getCertificateNums">
    <template #default="{ data }">
      <div class="row-item">
        <div class="item-top">
          <span class="icon export-icon"></span>
          进出口经营权
        </div>
        <div class="item-bottom">
          <span class="number text-blue">{{ data.importAndExportLicense || '--' }}</span>
          <span class="unit">件</span>
        </div>
      </div>
      <div class="row-item">
        <div class="item-top">
          <span class="icon labor-icon"></span>
          劳务派遣许可证
        </div>
        <div class="item-bottom">
          <span class="number text-blue">{{ data.laborDispatchLicense || '--' }}</span>
          <span class="unit">件</span>
        </div>
      </div>
      <div class="row-item">
        <div class="item-top">
          <span class="icon verification-icon"></span>
          验资
        </div>
        <div class="item-bottom">
          <span class="number text-blue">{{ data.capitalVerificationLicense || '--' }}</span>
          <span class="unit">件</span>
        </div>
      </div>
      <div class="row-item">
        <div class="item-top">
          <span class="icon food-icon"></span>
          食品经营许可证
        </div>
        <div class="item-bottom">
          <span class="number text-blue">{{ data.foodBusinessLicense || '--' }}</span>
          <span class="unit">件</span>
        </div>
      </div>
      <div class="row-item">
        <div class="item-top">
          <span class="icon load-icon"></span>
          道路运输许可证
        </div>
        <div class="item-bottom">
          <span class="number text-blue">{{ data.transportationLicense || '--' }}</span>
          <span class="unit">件</span>
        </div>
      </div>
    </template>
  </dataWrap>
</template>
<script setup>
import dataWrap from '../../collecting-panel/components/data-wrap'
import { getCertificateNums } from '@/api/panel-data/certificate'
</script>
<style lang="scss" scoped>
.mid-left {
  flex: 3;
}
:deep(.content) {
  gap: 16px;
}
.row-item {
  flex: 1;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  padding: 10px 12px;
  padding-bottom: 0;
}
.icon {
  margin-right: 8px;
}

.item-top {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #333333;
  // margin-bottom: 12px;
}

.item-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.number {
  font-size: 24px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  margin-right: 4px;
  color: #45a0ff;
}
.unit {
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #7d8592;
}
</style>
