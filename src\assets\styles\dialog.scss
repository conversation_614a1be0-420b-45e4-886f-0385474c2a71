// 客户档案弹窗样式
.customer-dialog.el-message-box {
  --el-messagebox-width: 1000px;
  .el-message-box__content{
    max-height: 700px;
    overflow-y: auto;
  }
}

.dialog-1200.el-message-box {
  --el-messagebox-width: 1200px;
  .el-message-box__content{
    max-height: 700px;
    overflow-y: auto;
  }
}

// 高级新建弹窗
.complex-dialog{
  --el-messagebox-width: 1000px;
  .el-dialog__body{
    max-height: 700px;
    overflow-y: auto;
  }
}

// 客户档案详细
.cus-detail-dialog{
  width: 1200px!important;
}
.cus-detail-dialog>.el-dialog__body{
  display: flex;
  flex-direction: column;
  height: 700px;
  padding:  0 20px 20px 20px;
  overflow-y: auto;
}


// 工商信息弹窗
.commercial-dialog{
  --el-messagebox-width: 600px;
}

// 废弃弹窗
.abandon-dialog{
  width: 400px!important;
}

// .business-dialog 业务类型弹窗
.business-dialog.el-message-box{
  --el-messagebox-width: 600px;
}

//product-dialog 产品弹窗
.product-dialog.el-message-box{
  --el-messagebox-width: 600px;
}

//product-dialog 产品弹窗-加长（新增活动价表格）
.product-dialog-next.el-message-box{
  --el-messagebox-width: 1000px;
}

// 流程设置弹窗
.step-dialog{
  width: 800px!important;
}
.step-dialog>.el-dialog__body,.business-dialog>.el-dialog__body{
  max-height: 700px;
  padding:  0 20px 20px 20px;
  overflow-y: auto;
  .el-step.is-simple{
    flex-basis: 36% !important;
  }
  .el-step__head.is-wait{
    .circle-icon-default{
      // background: #2383E7;
      // border: 1px solid #fff;
      // color: #FFFFFF;
      background: #fff;
      border: 1px solid #B2B5B9FF;
      color: #B2B5B9;
    }
  }
  .el-step__head.is-success{
    .circle-icon-default{
      // background: #2383E7;
      // border: 1px solid #fff;
      // color: #FFFFFF;
      background: #fff;
      border: 1px solid #B2B5B9FF;
      color: #B2B5B9;
    }
  }
  .el-step__title.is-success{
    color: #B2B5B9!important;
  }
  .el-step.is-simple .el-step__icon {
    width: auto;
    height: auto;
  }
  .el-steps.el-steps--simple{
    padding: 13px 1%;
    background: none;
  }

  .process-wrap {
    min-height: 630px;
    padding-top: 13px;
    padding-bottom: 13px;
  }
}

// 所有弹窗 body设置
.el-dialog.is-align-center {
  .el-dialog__header{
    height: 56px;
    background: #FFFFFF;
    border-radius: 4px 4px 0px 0px;
    border-bottom: 1px solid #E8E8E8FF;
    margin-right: 0;
  }
  .el-dialog__body{
    padding: 12px 16px 0 16px;
    border-bottom: 1px solid #E8E8E8FF;
    max-height: 707px;
    // min-height: 707px;
    overflow-y: auto;
  }
  .el-dialog__header{
    .el-dialog__title{
      font-size: 18px;
      font-family: AlibabaPuHuiTi_2_65_Medium;
      color: #333333;
      // color: black;
    }
  }
  .el-dialog__footer{
    // padding-top: 20px;
    height: 56px;
    padding: 12px 16px;
  }
}

// mini弹窗
.mini-dialog.el-message-box {
  --el-messagebox-width: 400px;
  .el-message-box__content{
    max-height: 500px;
    overflow-y: auto;
  }
}
// medium弹窗
.medium-dialog.el-message-box {
  --el-messagebox-width: 600px;
  .el-message-box__content{
    max-height: 500px;
    overflow-y: auto;
  }
}

// 工单弹窗
.order-modal.el-message-box{
  --el-messagebox-width: 600px;
}

.warning-dialog.el-message-box{
  --el-messagebox-width: 800px;
}



.complete-dialog.el-message-box{
  --el-messagebox-width: 400px;
}

.day-dialog.el-message-box{
  --el-messagebox-width: 600px;
}


// 弹窗下的form style 样式设置 
.el-dialog.is-align-center {
  .el-form-item__label{
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: #000000;
  }
  .el-input.el-input--default.is-disabled{
    .el-input__inner{
      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #7D8592;
    }
  }
} 


.el-message-box{
  .el-message-box__header{
    .el-message-box__title{
      font-size: 18px;
      font-family: AlibabaPuHuiTi_2_65_Medium;
      color: #333333;
    }
  }
  .el-message-box__content{
    .el-form-item__label{
      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #000000;
    }
    .el-input.el-input--default.is-disabled{
      .el-input__inner{
        font-size: 14px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        color: #7D8592;
      }
    }
  }
}

// notice-dialog
.notice-dialog.el-message-box{
  --el-messagebox-width: 800px;
}

//zone-dialog
.zone-dialog.el-message-box{
  --el-messagebox-width: 850px;
}
.clue-dialog.el-message-box{
  --el-messagebox-width: 1200px;
}

.dialog-footer-1.is-align-center{
   .el-dialog__body{
    padding-bottom: 0;
  }

  .el-dialog__footer{
    border-top: 1px solid #E8E8E8;
  }
}

.certification-dialog.el-message-box{
  --el-messagebox-width: 800px;
  .el-message-box__content{
    max-height: 700px;
    overflow-y: auto;
  }
}

.license-dialog{
  .el-dialog__body{
    min-height: 700px;
  }
}

.document-dialog.el-message-box{
  --el-messagebox-width: 800px;
}

.exception-modal.el-message-box{
  --el-messagebox-width: 600px;
}

.address-provider-modal.el-message-box{
  --el-messagebox-width: 800px;
}

.address-review-modal.el-message-box{
  --el-messagebox-width: 800px;
}
