import request from '@/utils/request'
// "name": "审核",
// "method": "post",
// "path": "/financePaymentReviewRecord/audit",
export const financeReviewAudit = params => {
  return request({
    url: '/financePaymentReviewRecord/audit',
    method: 'post',
    data: params
  })
}

// "name": "审批列表查询",
// "method": "get",
// "path": "/financePaymentReviewRecord/auditList",
export const financeReviewAuditList = params => {
  return request({
    url: '/financePaymentReviewRecord/auditList',
    method: 'get',
    params
  })
}

// "name": "根据id删除",
// "method": "delete",
// "path": "/financePaymentReviewRecord/delete",
export const financeReviewDelete = id => {
  return request({
    url: '/financePaymentReviewRecord/delete',
    method: 'delete',
    params: {
      id
    }
  })
}

// "name": "列表查询",
// "method": "get",
// "path": "/financePaymentReviewRecord/list",
// export const financeReviewList = params => {
//   return request({
//     url: '/financePaymentReviewRecord/list',
//     method: 'get',
//     params
//   })
// }

// "name": "导出",
// "method": "post",
// "path": "/financePaymentReviewRecord/export",
export const financeReviewExport = params => {
  return request({
    url: '/financePaymentReviewRecord/export',
    method: 'post',
    data: params
  })
}

// "name": "详情",
// "method": "get",
// "path": "/financePaymentReviewRecord/getById",
export const financeReviewGetById = params => {
  return request({
    url: '/financePaymentReviewRecord/getById',
    method: 'get',
    params
  })
}

// "name": "办理",
// "method": "post",
// "path": "/financePaymentReviewRecord/rectify",
export const financeReviewRectify = params => {
  return request({
    url: '/financePaymentReviewRecord/rectify',
    method: 'post',
    data: params
  })
}

// "name": "办理列表查询",
// "method": "get",
// "path": "/financePaymentReviewRecord/rectifyList",
export const financeReviewRectifyList = params => {
  return request({
    url: '/financePaymentReviewRecord/rectifyList',
    method: 'get',
    params
  })
}

// "name": "保存数据",
// "method": "post",
// "path": "/financePaymentReviewRecord/saveOrUpdate",
export const financeReviewSaveOrUpdate = params => {
  return request({
    url: '/financePaymentReviewRecord/saveOrUpdate',
    method: 'post',
    data: params
  })
}
