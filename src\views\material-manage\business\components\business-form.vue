<!--
 * @Description: 商机资料表单
 * @Author: thb
 * @Date: 2023-08-23 10:26:24
 * @LastEditTime: 2023-08-30 17:42:39
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="detail" label-position="top">
    <Collapse title="基础信息">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="商机名称" prop="name">
            <el-input v-model="detail.name" maxlength="20" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户名称" prop="companyName">
            <el-input v-model="detail.companyName" maxlength="20" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预计成交金额(元)" prop="expectAmount">
            <el-input v-model="detail.expectAmount" maxlength="20" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="预计成交时间" prop="expectTime">
            <el-input v-model="detail.expectTime" maxlength="20" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售阶段" prop="stage">
            <el-input v-model="detail.stage" maxlength="20" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="阶段百分比" prop="stagePercentage">
            <el-input v-model="detail.stagePercentage" maxlength="20" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="阶段进度" prop="status">
            <el-input v-model="detail.status" disabled />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" v-if="detail.stage === '赢单' || detail.stage === '已收定金,待打尾款'">
          <el-form-item label="实际成交金额" prop="actualAmount">
            <el-input v-model="detail.actualAmount" disabled />
          </el-form-item>
        </el-col> -->
      </el-row>
      <!-- <el-row :gutter="20" v-if="detail.stage === '赢单' || detail.stage === '已收定金,待打尾款'">
        <el-col :span="24">
          <el-form-item label="签约业务">
            <el-input v-model="detail.businessStr" disabled />
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-table :data="detail.list" v-if="detail.stage === '赢单' || detail.stage === '已收定金,待打尾款'">
        <el-table-column prop="productName" label="签约业务" />
        <el-table-column prop="actualAmount" label="实际成交金额">
          <template #default="{ row }">
            <span>{{ row.actualAmount || '--' }}元</span>
          </template>
        </el-table-column>
      </el-table>
      <el-row :gutter="20" v-if="detail.stage === '输单'">
        <el-col :span="8">
          <el-form-item label="输单原因">
            <el-input v-model="detail.reason" disabled />
          </el-form-item>
        </el-col>

        <el-col :span="16">
          <el-form-item label="输单描述">
            <el-input
              v-model="detail.remark"
              disabled
              maxlength="1000"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
    <Collapse title="其他信息">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="创建时间" prop="createTime">
            <el-input v-model="detail.createTime" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建人" prop="createBy">
            <el-input v-model="detail.createBy" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最近修改时间" prop="updateTime">
            <el-input v-model="detail.updateTime" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" v-if="detail.stage === '赢单'">
          <el-form-item label="赢单时间" prop="winTime">
            <el-input v-model="detail.winTime" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="detail.stage === '输单'">
          <el-form-item label="输单时间">
            <el-input v-model="detail.loseTime" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
  </el-form>
</template>
<script setup>
import Collapse from '@/components/Collapse'
defineProps({
  detail: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
</script>
<style lang="scss" scoped></style>
