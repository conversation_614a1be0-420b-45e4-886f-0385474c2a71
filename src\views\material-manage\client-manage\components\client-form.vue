<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-08-17 09:50:46
 * @LastEditTime: 2023-11-24 09:46:46
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="detail" label-position="top">
    <Collapse title="基础信息">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客户名称" prop="companyName">
            <el-input v-model="detail.companyName" maxlength="20" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="contactPhone">
            <el-input v-model="detail.contactPhone" maxlength="20" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客户来源" prop="sourceName">
            <el-input v-model="detail.sourceName" maxlength="20" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="detail.sourceId === '3'">
          <el-form-item label="介绍来源" prop="introductionCustomerName">
            <el-input v-model="detail.introductionCustomerName" maxlength="20" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户等级" prop="level">
            <el-select v-model="detail.level" clearable placeholder="" disabled>
              <el-option :label="item.label" :value="item.value" v-for="(item, index) in customer_level" :key="index" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="业务类型" prop="productName">
            <el-input v-model="detail.productName" maxlength="20" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="detail.remark" disabled maxlength="1000" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="税务性质" prop="taxNature">
            <el-select v-model="detail.taxNature" clearable placeholder=" " disabled>
              <el-option v-for="(item, index) in customer_property" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>

    <Collapse title="更多信息">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="行业" prop="industry">
            <el-select
              v-model="detail.industry"
              clearable
              placeholder=" "
              disabled
              filterable
              allow-create
              default-first-option
              @change="handleChange"
            >
              <el-option v-for="(item, index) in industry" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="地区" prop="area">
            <el-input v-model="detail.area" maxlength="20" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="detail.address" maxlength="100" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="detail.phone" maxlength="100" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
    <Collapse title="其他信息">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="创建时间" prop="createTime">
            <el-input v-model="detail.createTime" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建人" prop="createBy">
            <el-input v-model="detail.createBy" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="成为客户时间" prop="becomeTime">
            <el-input v-model="detail.becomeTime" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="领取/分配时间" prop="getTime">
            <el-input v-model="detail.getTime" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最近跟进时间" prop="lastFollowTime">
            <el-input v-model="detail.lastFollowTime" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="跟进人" prop="currentUserName">
            <el-input v-model="detail.currentUserName" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="录入方式" prop="entryType">
            <el-input :value="detail.entryType === '0' ? '手动录入' : '公海录入'" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属公海" prop="seaName">
            <el-input :value="detail.seaName || '无'" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
  </el-form>
</template>
<script setup>
import { useSetDic } from '@/hooks/useSetDic'
const { setDic } = useSetDic()
const handleChange = value => {
  setDic('industry', value, value, industry)
}

const { proxy } = getCurrentInstance()
const { industry } = proxy.useDict('industry')
const { customer_level } = proxy.useDict('customer_level')
const { customer_property } = proxy.useDict('customer_property')

defineProps({
  detail: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
</script>
<style lang="scss" scoped></style>
