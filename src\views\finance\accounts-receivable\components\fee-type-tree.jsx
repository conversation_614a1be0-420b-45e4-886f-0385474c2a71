import { defineComponent, ref, watch } from 'vue'
import { ElTreeSelect } from 'element-plus'
import { getBusinessList } from '@/api/business/business'

export default defineComponent({
  props: {
    modelValue: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }) {
    const feeType = ref('')
    watch(
      () => props.modelValue,
      val => {
        console.log('watch-feeType')
        feeType.value = val
      },
      { deep: true, immediate: true }
    )

    const handleSelectChange = (node, row) => {
      // if (node?.type === '业务类型') return
      // console.log('node', node, row)
      emit('update:modelValue', node?.id || '')
      emit('onSelectChange', node, row)
    }

    // 获取所有的产品名称
    const productTreeData = ref()
    const defaultPopsFunction = row => {
      console.log('row', row)
      return {
        value: 'id',
        label: 'name'
        // disabled: data => {
        //   return data?.type === '业务类型' && !data?.children.length
        // }
      }
    }

    const getAllProducts = async () => {
      const { data } = await getBusinessList({
        pageNum: 1,
        pageSize: 10000
      })
      // 将后端传回的数据结构进行转换
      const revertData = []
      data.forEach(item => {
        const obj = {
          contractType: item.contractType,
          name: item.typeName,
          id: item.id,
          type: '业务类型',
          children: []
        }
        revertData.push(obj)
        // if (Array.isArray(item.child) && item.child.length) {
        //   item.child.forEach(child => {
        //     obj.children.push({
        //       name: child.productName,
        //       types:
        //         item.contractType?.split(',')?.map(item => {
        //           return {
        //             label: item,
        //             value: item
        //           }
        //         }) || [],
        //       type: '产品类型',
        //       feeType: child.feeType,
        //       quotation: child.quotation,
        //       id: child.id // 产品类型id
        //     })
        //   })
        // }
      })
      productTreeData.value = revertData || []
    }
    getAllProducts()

    return {
      feeType,
      handleSelectChange,
      productTreeData,
      defaultPopsFunction
    }
  },
  render() {
    return (
      <ElTreeSelect
        vModel={this.feeType}
        clearable
        filterable
        data={this.productTreeData}
        props={this.defaultPopsFunction({})}
        onCurrentChange={this.handleSelectChange}
        onClear={this.handleSelectChange}
        renderAfterExpand={false}
        defaultExpandAll
      />
    )
  }
})
