<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    top="5vh"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      :hide-required-asterisk="['详情'].includes(mode)"
    >
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="关联企业" prop="customerName">
            <!-- 点击弹窗出现客户列表  -->
            <div @click="handleListSelectShow" style="width: 100%">
              <el-input
                :disabled="['详情', '整改办理', '审批'].includes(mode)"
                v-model="formData.customerName"
                readonly
                maxlength="20"
                :placeholder="['详情', '整改办理', '审批'].includes(mode) ? '' : '请输入'"
                style="width: 100%"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="企业类型" prop="industry">
            <el-input disabled v-model="formData.industry" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="期望完成时间" prop="expectCompleteDate">
            <el-date-picker
              :disabled-date="disabledDate"
              :editable="false"
              style="width: 100%"
              v-model="formData.expectCompleteDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="['详情', '整改办理', '审批'].includes(mode)"
              type="date"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分类" prop="category">
            <el-select
              :disabled="['详情', '整改办理', '审批'].includes(mode)"
              v-model="formData.category"
              style="width: 100%"
              allow-create
              multiple
              filterable
              default-first-option
            >
              <el-option v-for="item in finance_review_category" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="凭证所属期" prop="voucherPeriod">
            <el-date-picker
              :editable="false"
              style="width: 100%"
              v-model="formData.voucherPeriod"
              format="YYYY-MM"
              value-format="YYYY-MM"
              :disabled="['详情', '整改办理', '审批'].includes(mode)"
              type="month"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="查账日期" prop="reviewDate">
            <el-date-picker
              :editable="false"
              style="width: 100%"
              v-model="formData.reviewDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="['详情', '整改办理', '审批'].includes(mode)"
              type="date"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="问题" prop="issue">
            <el-input
              :disabled="['详情', '整改办理', '审批'].includes(mode)"
              type="textarea"
              v-model="formData.issue"
              placeholder=""
            />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="原因" prop="reason">
            <el-input
              maxlength="1000"
              :disabled="['详情', '整改办理', '审批'].includes(mode)"
              v-model="formData.reason"
              placeholder=""
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="凭证号" prop="voucherCode">
            <el-input
              maxlength="255"
              :disabled="['详情', '整改办理', '审批'].includes(mode)"
              v-model="formData.voucherCode"
              placeholder=""
            />
          </el-form-item>
        </el-col>
        <!-- 新增审账人 -->
        <el-col :span="8" v-if="['详情', '编辑', '审批'].includes(mode)">
          <el-form-item label="审账人" prop="createBy">
            <el-input maxlength="255" disabled v-model="formData.createBy" placeholder="" />
          </el-form-item>
        </el-col>
        <template v-if="['新增记录', '编辑', '详情', '审批'].includes(mode)">
          <el-col :span="8">
            <el-form-item label="做账会计" prop="sponsorAccountingUserName">
              <el-input disabled v-model="formData.sponsorAccountingUserName" placeholder="" />
            </el-form-item> </el-col
        ></template>
        <template
          v-if="['整改办理', '审批'].includes(mode) || (['详情'].includes(mode) && formData.rectificationStatus !== 'pending')"
        >
          <el-col :span="8">
            <el-form-item label="整改结果" prop="rectificationStatus">
              <el-radio-group :disabled="['详情', '审批'].includes(mode)" v-model="formData.rectificationStatus">
                <el-radio label="rectified">已整改</el-radio>
                <el-radio label="not_rectified">不予整改</el-radio>
              </el-radio-group>
            </el-form-item> </el-col
          ><el-col :span="8" v-if="formData.rectificationStatus !== 'not_rectified'">
            <el-form-item label="改账日期" prop="rectificationTime">
              <el-date-picker
                :editable="false"
                style="width: 100%"
                v-model="formData.rectificationTime"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                disabled
                type="date"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16" v-if="formData.rectificationStatus === 'not_rectified'">
            <el-form-item label="备注" prop="rectificationRemark">
              <el-input
                maxlength="1000"
                :disabled="['详情', '审批'].includes(mode)"
                v-model="formData.rectificationRemark"
                placeholder=""
              />
            </el-form-item> </el-col></template
      ></el-row>
      <el-row :gutter="24">
        <template
          v-if="['审批'].includes(mode) || (['详情'].includes(mode) && !['pending', '', null].includes(formData.auditStatus))"
        >
          <el-col :span="8">
            <el-form-item label="审批结果" prop="auditStatus">
              <el-radio-group :disabled="['详情'].includes(mode)" v-model="formData.auditStatus">
                <el-radio label="pass">通过</el-radio>
                <el-radio label="not_pass">不通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="['详情'].includes(mode)">
            <el-col :span="8">
              <el-form-item label="审批人" prop="auditUserName">
                <el-input disabled v-model="formData.auditUserName" placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审批时间" prop="auditTime">
                <el-input disabled v-model="formData.auditTime" placeholder="" />
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="16" v-if="formData.auditStatus === 'not_pass'">
            <el-form-item label="不通过原因" prop="auditRemark">
              <el-input maxlength="1000" :disabled="['详情'].includes(mode)" v-model="formData.auditRemark" placeholder="" />
            </el-form-item> </el-col
        ></template>
      </el-row>
    </el-form>
    <!-- finance_review_category-{{ finance_review_category }} -->
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
      <el-button v-if="['新增记录', '编辑', '整改办理', '审批'].includes(mode)" type="primary" @click="handleSubmit">
        保存
      </el-button>
    </template>
  </el-dialog>
  <tableModal
    :init-param="{ discard: 0 }"
    v-if="listSelectShow"
    rowKey="customerId"
    title="关联企业"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
</template>

<script setup lang="jsx">
import useDictStore from '@/store/modules/dict'
import { getCustomers } from '@/api/customer/file'
import tableModal from '@/components/tableModal'
import { addData } from '@/api/system/dict/data'
import dayjs from 'dayjs'
import {
  financeReviewSaveOrUpdate,
  financeReviewGetById,
  financeReviewRectify,
  financeReviewAudit
} from '@/api/finance-review/finance-review.js'
import { useDic } from '@/hooks/useDic'
import { useSetDic } from '@/hooks/useSetDic'
const { getDic } = useDic()
const { setDic } = useSetDic()

const { proxy } = getCurrentInstance()
const { finance_review_category } = proxy.useDict('finance_review_category')
const emit = defineEmits()

const visible = ref(true)
const disabled = ref(false)
const mode = ref('')

const formRef = ref(null)
const formData = reactive({
  id: undefined,
  reviewDate: dayjs().format('YYYY-MM-DD')
})
const rules = {
  customerName: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  voucherPeriod: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  reviewDate: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  issue: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  sponsorAccountingUserName: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  rectificationTime: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  rectificationRemark: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  auditRemark: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
}
const getDetail = async row => {
  // console.log('getDetail', row)
  await financeReviewGetById({ id: row.id }).then(res => {
    Object.assign(formData, res.data, {
      category: res.data.category?.split(',')
    })
  })
}
const onAdd = row => {
  mode.value = '新增记录'
}
const onEdit = async row => {
  await getDetail(row)
  mode.value = '编辑'
}
const onDetail = row => {
  getDetail(row)
  mode.value = '详情'
}
const onDeal = async row => {
  await getDetail(row)
  mode.value = '整改办理'
  formData.rectificationTime = dayjs().format('YYYY-MM-DD')
  formData.rectificationStatus = 'rectified'
}
const onReview = async row => {
  await getDetail(row)
  mode.value = '审批'
  formData.auditStatus = 'pass'
}
const handleClose = () => {
  emit('close')
}
const handleSubmit = () => {
  // console.log('formData', formData)
  formRef.value.validate(async valid => {
    if (!valid) return
    if (['新增记录', '编辑'].includes(mode.value)) {
      // 新增字典再这条记录入库
      if (finance_review_category.value.findIndex(item => item.value === formData.category) === -1) {
        // setDic会影响finance_review_category当前值
        formData.category.forEach(async category => {
          await setDic('finance_review_category', category, category, finance_review_category)
        })

        // console.log('ufinance_review_category', finance_review_category)
        // console.log('useDictStore().dict', JSON.stringify(useDictStore().getDict('finance_review_category')))
        financeReviewSaveOrUpdate({
          ...formData,
          category: formData.category?.join(',')
        }).then(res => {
          if (res.code === 200) {
            emit('ok', finance_review_category)
            proxy.$modal.msgSuccess('保存成功')
            handleClose()
          }
        })
      } else {
        financeReviewSaveOrUpdate(formData).then(res => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess('保存成功')
            emit('ok')
            handleClose()
          }
        })
      }
    }
    if (mode.value === '整改办理') {
      financeReviewRectify(formData).then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
    }
    if (mode.value === '审批') {
      financeReviewAudit(formData).then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
    }
  })
}

// 客户列表弹窗显示
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  if (disabled.value) return
  listSelectShow.value = true
}
const handleSelect = data => {
  formData.customerName = data.customerName
  formData.customerNo = data.customerNo
  formData.customerId = data.customerId
  formData.industry = data.industry
  formData.sponsorAccountingUserName = data.sponsorAccountingUserName
}
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'sponsorAccountingUserName',
    width: '150',
    label: '做账会计'
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

// 日期选择器禁用
const disabledDate = time => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用今天以前的时间
}

defineExpose({
  onAdd,
  onEdit,
  onDetail,
  onDeal,
  onReview
})
</script>

<style lang="scss" scoped></style>
