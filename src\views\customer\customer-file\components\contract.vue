<!--
 * @Description: 新建合同弹窗
 * @Author: thb
 * @Date: 2023-05-30 09:56:01
 * @LastEditTime: 2023-08-30 16:48:02
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    width="600"
    class="contract-dialog"
    title="新建合同"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" label-position="top">
      <el-radio-group v-model="formData.radio" class="radio-group">
        <el-radio label="0" size="large">已有纸质合同,录入信息并上传附件</el-radio>
        <el-radio label="1" size="large">使用模板创建合同</el-radio>
      </el-radio-group>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit(formRef)">下一步</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-next'])
const handleClose = () => {
  emits('on-close')
}
const formData = ref({
  radio: '0'
})

const props = defineProps({
  normal: {
    type: Boolean,
    default: false
  }
})
console.log('normal', props.normal)

const formRef = ref()
const handleSubmit = async formRef => {
  if (!formRef) return
  formRef.validate(valid => {
    if (valid) {
      handleClose()
      // '0'代表 普通新建
      // '1'代表 模板新建
      emits('on-next', formData.value.radio)
    } else {
    }
  })
}
</script>
<style lang="scss">
.contract-dialog {
  .el-dialog__body {
    height: auto;
    .radio-group {
      flex-direction: column;
      align-items: start;
    }
  }
}
</style>
