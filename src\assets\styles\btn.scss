@import "./variables.module.scss";

@mixin colorBtn($color) {
  background: $color;
  &:hover {
    color: $color;
    &::before,
    &::after {
      background: $color;
    }
  }
}
.blue-btn {
  @include colorBtn($blue);
}
.light-blue-btn {
  @include colorBtn($light-blue);
}
.red-btn {
  @include colorBtn($red);
}
.pink-btn {
  @include colorBtn($pink);
}
.green-btn {
  @include colorBtn($green);
}
.tiffany-btn {
  @include colorBtn($tiffany);
}
.yellow-btn {
  @include colorBtn($yellow);
}
.pan-btn {
  position: relative;
  display: inline-block;
  padding: 14px 36px;
  font-size: 14px;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  outline: none;
  transition: 600ms ease all;
  &:hover {
    background: #ffffff;
    &::before,
    &::after {
      width: 100%;
      transition: 600ms ease all;
    }
  }
  &::before,
  &::after {
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 2px;
    content: "";
    transition: 400ms ease all;
  }
  &::after {
    top: inherit;
    right: inherit;
    bottom: 0;
    left: 0;
  }
}
.custom-button {
  box-sizing: border-box;
  display: inline-block;
  padding: 10px 15px;
  margin: 0;
  font-size: 14px;
  line-height: 1;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  cursor: pointer;
  background: #ffffff;
  border-radius: 4px;
  outline: 0;
  appearance: none;
}

//  按钮失焦问题css解决
.el-button.el-button--primary:focus{
  background-color: rgba(49, 131, 226, 1);
}

.el-button.el-button--danger:focus{
  background-color: rgba(245, 108, 108, 1);
}

// button 文字字体样式设置
.el-button{
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
} 

.el-button.el-button--primary{
  background-color: rgba(49, 131, 226, 1);
}
.el-button.el-button--danger{
  background-color: rgba(245, 108, 108, 1);
}
