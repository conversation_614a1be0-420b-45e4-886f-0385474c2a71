<template>
  <el-dialog align-center title="其他附件" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <el-table :data="list" style="width: 100%; margin-bottom: 24px">
      <el-table-column type="index" width="50" />
      <el-table-column label="附件名称">
        <template #default="{ row }">
          <span>
            {{ row.fileNames }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button type="primary" @click="handlePreview(row)">预览</el-button>
          <el-button type="primary" @click="handleUpload(row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import { getFileUrlByOss } from '@/api/file/file.js'
import iFrame from '@/components/iFrame'
const visible = ref(true)
const emits = defineEmits(['on-close'])
const handleClose = () => {
  emits('on-close')
}

defineProps({
  list: Array
})

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const handlePreview = row => {
  previewShow.value = true
  previewUrl.value = row.urls
}

const handleUpload = async row => {
  const { data } = await getFileUrlByOss(row.urls)
  window.open(data)
}
</script>
<style lang="scss" scoped></style>
