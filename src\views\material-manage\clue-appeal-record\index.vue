<template>
  <ProTable
    :init-param="initParam"
    ref="proTable"
    row-key="id"
    :columns="columns"
    :request-api="getClueAppealRecordList"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <!-- 表格操作 -->
    <template #clueName="{ row }">
      <span class="blue-text" @click="handleShowDetail(row)">{{ row.clueName }}</span>
    </template>
    <template #operation="scope">
      <el-button type="primary" link @click="handlDetail(scope.row)">详情</el-button>
    </template>
  </ProTable>
  <ClueDetail v-if="detailShow" :id="clueId" @on-close="handleClose" @on-success="getList" />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { getClueAppealRecordList } from '@/api/material-manage/clue-appeal-record.js'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from '@/views/material-manage/clue-appeal-record/components/form-modal.vue'
import ClueDetail from '@/views/material-manage/clue-manage/components/clue-detail.vue'
import { cusSourceTree } from '@/api/material-manage/source'

const getTree = async () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await cusSourceTree({ enable: 1 })
    // 将后端传回的数据结构进行转换
    const revertTreeData = (data = []) => {
      data.forEach(item => {
        item.label = item.name
        item.value = item.id
        item.children = item.child
        if (Array.isArray(item.children) && item.children.length) {
          revertTreeData(item.children)
        }
      })
    }
    revertTreeData(data)
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}
const initParam = reactive({})
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'clueName',
    label: '线索名称',
    search: { el: 'input' },
    minWidth: 300,
    sortable: 'custom',
    sortName: 'contact.contact_name'
  },
  {
    prop: 'sourceId',
    label: '线索来源',
    width: 150,
    enum: getTree,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'name',
          children: 'child',
          disabled: data => {
            return data.child
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.sourceName}</span>
    },
    sortable: 'custom',
    sortName: 'source.name'
  },
  {
    prop: 'remark',
    label: '申述原因',
    minWidth: 300,
    search: { el: 'input' },
    sortable: 'custom',
    sortName: 'record.remark'
  },
  {
    prop: 'status',
    label: '状态',
    width: 150,
    search: { el: 'select' },
    enum: [
      {
        label: '申述中',
        value: '申述中'
      },
      {
        label: '申述失败',
        value: '申述失败'
      },
      {
        label: '申述成功',
        value: '申述成功'
      }
    ],
    sortable: 'custom',
    sortName: 'record.status'
  },
  {
    prop: 'createUserName',
    label: '创建人',
    search: { el: 'input' },
    width: 150,
    sortable: 'custom',
    sortName: 'record.create_by'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180,
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'record.create_time'
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 100
  }
]

// 自定义
const transformRequestParams = (data: any) => {
  if (data.createTime) {
    data.createTimeStart = data.createTime[0]
    data.createTimeEnd = data.createTime[1]
  }
}

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handlDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}

/** 线索详情弹窗 */
const detailShow = ref(false)
const clueId = ref()
// const tabType = ref('1') // 代表是我的线索 // '2'代表是共享线索
const handleShowDetail = (row: any) => {
  clueId.value = row.clueId
  // tabType.value = initParam.tabType
  detailShow.value = true
}
const handleClose = () => {
  detailShow.value = false
  getList()
}

// onMounted(() => {
// })
</script>
<style lang="scss" scoped></style>
