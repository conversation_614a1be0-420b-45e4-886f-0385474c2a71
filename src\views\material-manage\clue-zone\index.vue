<!--
 * @Description: 线索公海
 * @Author: thb
 * @Date: 2023-07-25 11:16:37
 * @LastEditTime: 2023-12-20 10:32:00
 * @LastEditors: thb
-->
<template>
  <ProTable
    ref="proTable"
    title="线索公海"
    :columns="columns"
    :request-api="getClueZoneList"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button :icon="Download" @click="handleExport" v-hasPermi="['material-manage:clue-zone:export']">导出</el-button>
    </template>

    <template #contactName="{ row }">
      <span class="blue-text" @click="handleShowDetail(row)">{{ row.contactName }}</span>
    </template>
    <template #operation="{ row }">
      <el-button type="primary" link v-if="isCapableGet(row.isGet, row.deptIds)" @click="handleReceive(row)">领取</el-button>

      <el-button type="primary" link @click="handleDivide(row)" v-if="isCapableDispatch(row.isDivide)">分配</el-button>
    </template>
  </ProTable>
  <clueForm v-if="clueShow" :type="type" @on-close="clueShow = false" @on-success="getList" />

  <ClueDetail
    v-if="detailShow"
    :isSea="true"
    :isDivide="isDivide"
    :isGet="isGet"
    :id="clueId"
    @on-close="detailShow = false"
    @on-edit="handleEditClue"
    @on-receive="handleReceive"
    @on-success="getList"
    @on-delete="handleDelete"
  />
</template>
<script setup lang="tsx">
import { getZoneSettingList } from '@/api/material-manage/zone-setting'
import { ColumnProps } from '@/components/ProTable/interface'
import { ref } from 'vue'
import {
  getClueZoneList,
  getClueDetail,
  saveClue,
  receiveZoneClue,
  deleteClue,
  divideClue,
  cusCustomerOrClueClueInSeaListExport
} from '@/api/material-manage/clue'
import { useDic } from '@/hooks/useDic'
import { CirclePlus, Download } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialog'
import { useHandleData } from '@/hooks/useHandleData'
import clueForm from './clue-form'
import ClueForm2 from '../clue-manage/components/clue-form.vue'
import ClueDetail from '../clue-manage/components/clue-detail'
import clueDistribute from '../clue-manage/components/clue-distribute.vue'
import { useDept } from '@/hooks/useDept'
import { cusSourceTree } from '@/api/material-manage/source'

const { proxy } = getCurrentInstance()
const { isCapableGet, isCapableDispatch } = useDept()

const { getDic } = useDic()
const { showDialog } = useDialog()

const getTree = async () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await cusSourceTree({ enable: 1 })
    // 将后端传回的数据结构进行转换
    const revertTreeData = (data = []) => {
      data.forEach(item => {
        item.label = item.name
        item.value = item.id
        item.children = item.child
        if (Array.isArray(item.children) && item.children.length) {
          revertTreeData(item.children)
        }
      })
    }
    revertTreeData(data)
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '序号',
    fixed: 'left',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'contactName',
    width: 150,
    fixed: 'left',
    label: '姓名',
    search: {
      el: 'input'
    },
    sortable: 'custom',
    sortName: 'ccc.contact_name'
  },
  {
    prop: 'seaId',
    enum: () => {
      return new Promise(async (resolve, reject) => {
        const { data } = await getZoneSettingList({
          pageSize: 1000,
          pageNum: 1,
          status: '1',
          type: '0'
        })
        if (data) {
          resolve({
            data: data.records.map(item => {
              return {
                label: item.name,
                value: item.id
              }
            })
          })
        }
      })
    },

    label: '所属公海',
    width: 150,
    search: {
      el: 'select'
    },
    sortable: 'custom',
    sortName: 'ccoc.sea_id'
  },
  {
    prop: 'contactPhone',
    width: 200,
    label: '手机号',
    search: {
      el: 'input'
    },
    sortable: 'custom',
    sortName: 'ccc.contact_phone'
  },
  {
    prop: 'sourceId',
    label: '线索来源',
    width: 300,
    enum: getTree,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'name',
          children: 'child',
          disabled: data => {
            return data.child
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.sourceName}</span>
    },
    sortable: 'custom',
    sortName: 'source.name'
  },

  {
    prop: 'followStatus',
    width: 150,
    enum: [
      {
        label: '未跟进',
        value: '0'
      },
      {
        label: '跟进中',
        value: '1'
      },
      {
        label: '已转企业',
        value: '2'
      }
    ],
    label: '跟进状态',
    search: {
      el: 'select'
    },
    sortable: 'custom',
    sortName: 'ccoc.follow_status'
  },
  {
    prop: 'lastModifiedTime',
    width: 200,
    label: '最近修改时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'ccoc.last_modified_time'
  },
  {
    prop: 'lastFollowTime',
    width: 200,
    label: '最近跟进时间',
    search: {
      el: 'date-picker',
      // span: 1,
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'ccoc.last_follow_time'
  },
  {
    prop: 'tagsName',
    width: 200,
    label: '标签'
  },
  {
    prop: 'remark',
    width: 300,
    label: '备注',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'currentUserName',
    width: 150,
    label: '前跟进人',
    search: {
      el: 'input'
    },
    sortable: 'custom',
    sortName: 'su.nick_name '
  },
  {
    prop: 'createBy',
    width: 150,
    search: {
      el: 'input'
    },
    label: '创建人',
    sortable: 'custom',
    sortName: 'ccoc.create_by'
  },
  {
    prop: 'createTime',
    width: 200,
    // fixed: 'right',
    search: {
      el: 'date-picker',
      // span: 1,
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    label: '创建时间',
    sortable: 'custom',
    sortName: 'ccoc.create_time'
  },
  {
    prop: 'operation',
    width: 150,
    fixed: 'right',
    label: '操作'
  }
]

// 自定义
const transformRequestParams = data => {
  // 创建时间
  console.log(data)
  if (data.createTime) {
    data.startCreateTime = data.createTime[0]
    data.endCreateTime = data.createTime[1]
  }

  if (data.lastFollowTime) {
    data.startLastFollowTime = data.lastFollowTime[0]
    data.endLastFollowTime = data.lastFollowTime[1]
  }
  if (data.lastModifiedTime) {
    data.lastModifiedTimeStart = data.lastModifiedTime[0]
    data.lastModifiedTimeEnd = data.lastModifiedTime[1]
  }
}

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

const proTable = ref('')
// 提交成功之后的回调函数
const getList = () => {
  proTable.value?.getTableList()
}

const clueShow = ref(false)
const type = ref('add')
const handleAdd = () => {
  type.value = 'add'
  clueShow.value = true
}
const handleConvertParams = data => {
  data.tags = data.tags.map(item => item.tagId)
}

const handleRevertParams = (data: any) => {
  if (data.tags.length) {
    data.tags = data.tags.map(item => {
      return {
        tagId: item
      }
    })
  }
  data.entryType = '1' // 公海录入
  data.type = '0' // 代表线索
}
const handleEditClue = id => {
  detailShow.value = false
  showDialog({
    title: '编辑',
    customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: ClueForm2, // 表单组件
    getApi: getClueDetail,
    requestParams: id,
    handleConvertParams,
    handleRevertParams,
    submitApi: saveClue, // 提交api
    submitCallback: getList // 提交成功之后的回调函数
  })
}
// handleShowDetail 显示线索详情
const detailShow = ref(false)
const clueId = ref()
const isDivide = ref(false)
const isGet = ref(true)
const handleShowDetail = row => {
  clueId.value = row.id
  isDivide.value = row.isDivide
  isGet.value = row.isGet
  detailShow.value = true
}

// 领取
const handleReceive = async row => {
  await useHandleData(
    receiveZoneClue,
    {
      id: row.id,
      type: '0' // 线索领取
    },
    `确定领取当前线索`
  )
  detailShow.value = false
  console.log('handleReceive')
  proTable.value?.getTableList()
}

// 删除线索
const handleDelete = async id => {
  await useHandleData(deleteClue, id, `删除当前线索`)
  detailShow.value = false
  console.log('handleDelete')
  proTable.value?.getTableList()
}

// 分配线索
const handleDivide = row => {
  showDialog({
    title: '分配线索',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: clueDistribute, // 表单组件
    submitApi: divideClue, // 提交api
    handleConvertParams: data => {
      data.deptIds = row.deptIds
    },
    handleRevertParams: data => {
      data.id = row.id
    },
    submitCallback: getList // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 导出列表功能
const handleExport = async () => {
  const params = Object.assign({}, proTable.value.searchParam)
  if (params.lastFollowTime) {
    params.startLastFollowTime = params.lastFollowTime[0]
    params.endLastFollowTime = params.lastFollowTime[1]
    delete params.lastFollowTime
  }
  if (params.createTime) {
    params.startCreateTime = params.createTime[0]
    params.endCreateTime = params.createTime[1]
    delete params.createTime
  }
  const result = await cusCustomerOrClueClueInSeaListExport(params)
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}
</script>
<style lang="scss" scoped></style>
