<!--
 * @Description: 客户公海
 * @Author: thb
 * @Date: 2023-08-22 09:08:39
 * @LastEditTime: 2023-11-16 10:40:38
 * @LastEditors: thb
-->
<template>
  <ProTable
    ref="proTable"
    title="客户公海"
    :columns="columns"
    :request-api="getClientZoneList"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button :icon="Download" @click="handleExport" v-hasPermi="['material-manage:client-zone:export']">导出</el-button>
    </template>

    <template #companyName="{ row }">
      <span class="blue-text" @click="showDetail(row)">{{ row.companyName }}</span>
    </template>
    <template #operation="{ row }">
      <el-button type="primary" link v-if="isCapableGet(row.isGet, row.deptIds)" @click="handleReceive(row)">领取</el-button>
      <el-button type="primary" link @click="handleDivide(row)" v-if="isCapableDispatch(row.isDivide)">分配</el-button>
    </template>
  </ProTable>
  <ClientForm v-if="clientShow" :type="type" @on-close="clientShow = false" @on-success="getList" />
  <clientDetail
    v-if="detailShow"
    :isSea="true"
    :isDivide="isDivide"
    :isGet="isGet"
    :id="clientId"
    @on-close="detailShow = false"
    @on-edit="handleEditClient"
    @on-receive="handleReceive"
    @on-success="getList"
    @on-delete="handleDelete"
    @on-distribute="handleDivide"
  />
</template>
<script setup lang="tsx">
import { ColumnProps } from '@/components/ProTable/interface'
import { ref } from 'vue'
import { getClientZoneList, cusCustomerOrClueCustomerInSeaListExport } from '@/api/material-manage/client'
import { getZoneSettingList } from '@/api/material-manage/zone-setting'
import { useDic } from '@/hooks/useDic'
import { CirclePlus, Download } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialog'
import ClientForm from './components/client-form.vue'
import { getMyClueList, getSharedClueList, getClueDetail, saveClue } from '@/api/material-manage/clue'
import ClientDetail from '../client-manage/components/client-detail'
import clientForm2 from '../client-manage/components/form.vue'
import { useHandleData } from '@/hooks/useHandleData'
import { getClueZoneList, receiveZoneClue, deleteClue, divideClue } from '@/api/material-manage/clue'
import clueDistribute from '../clue-manage/components/clue-distribute'
import { useDept } from '@/hooks/useDept'
import { cusSourceTree } from '@/api/material-manage/source'

const { proxy } = getCurrentInstance()
const { isCapableGet, isCapableDispatch } = useDept()
const { showDialog } = useDialog()
const { getDic } = useDic()

// 自定义参数
const transformRequestParams = data => {
  // 创建时间
  console.log(data)
  if (data.createTime) {
    data.startCreateTime = data.createTime[0]
    data.endCreateTime = data.createTime[1]
  }

  if (data.lastFollowTime) {
    data.startLastFollowTime = data.lastFollowTime[0]
    data.endLastFollowTime = data.lastFollowTime[1]
  }
  if (data.becomeTime) {
    data.becomeTimeStart = data.becomeTime[0]
    data.becomeTimeEnd = data.becomeTime[1]
  }
  if (data.lastModifiedTime) {
    data.lastModifiedTimeStart = data.lastModifiedTime[0]
    data.lastModifiedTimeEnd = data.lastModifiedTime[1]
  }
}

const getTree = async () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await cusSourceTree({ enable: 1 })
    // 将后端传回的数据结构进行转换
    const revertTreeData = (data = []) => {
      data.forEach(item => {
        item.label = item.name
        item.value = item.id
        item.children = item.child
        if (Array.isArray(item.children) && item.children.length) {
          revertTreeData(item.children)
        }
      })
    }
    revertTreeData(data)
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '序号',
    fixed: 'left',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'companyName',
    width: 300,
    fixed: 'left',
    label: '客户名称',
    search: {
      el: 'input'
    },
    sortable: 'custom',
    sortName: 'ccoc.company_name'
  },
  {
    prop: 'seaId',
    enum: () => {
      return new Promise(async (resolve, reject) => {
        const { data } = await getZoneSettingList({
          pageSize: 1000,
          pageNum: 1,
          status: '1',
          type: '1'
        })
        if (data) {
          resolve({
            data: data.records.map(item => {
              return {
                label: item.name,
                value: item.id
              }
            })
          })
        }
      })
    },

    label: '所属公海',
    width: 150,
    search: {
      el: 'select'
    },
    sortable: 'custom',
    sortName: 'ccoc.sea_id'
  },
  {
    prop: 'contactPhone',
    width: 200,
    label: '手机号',
    search: {
      el: 'input'
    },
    sortable: 'custom',
    sortName: 'ccc.contact_phone'
  },
  {
    prop: 'sourceId',
    width: 200,
    label: '客户来源',
    enum: getTree,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'name',
          children: 'child',
          disabled: data => {
            return data.child
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.sourceName}</span>
    },
    sortable: 'custom',
    sortName: 'source.name'
  },
  {
    prop: 'level',
    width: 200,
    label: '客户等级',
    enum: getDic('customer_level'),
    search: { el: 'select' },
    sortable: 'custom',
    sortName: 'ccoc.level'
  },
  {
    prop: 'followStatus',
    width: 150,
    enum: [
      {
        label: '未跟进',
        value: '0'
      },
      {
        label: '跟进中',
        value: '1'
      },
      {
        label: '已转企业',
        value: '2'
      }
    ],
    label: '跟进状态',
    search: { el: 'select' },
    sortable: 'custom',
    sortName: 'ccoc.follow_status'
  },
  {
    prop: 'becomeTime',
    width: 200,
    label: '成为客户时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'ccoc.become_time'
  },
  {
    prop: 'lastModifiedTime',
    width: 200,
    label: '最近修改时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'ccoc.last_modified_time'
  },
  {
    prop: 'lastFollowTime',
    width: 200,
    label: '最近跟进时间',
    search: {
      el: 'date-picker',
      // span: 1,
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'ccoc.last_follow_time'
  },
  {
    prop: 'tagsName',
    width: 200,
    label: '标签'
  },
  {
    prop: 'remark',
    width: 300,
    label: '备注'
  },
  {
    prop: 'taxNature',
    width: 200,
    label: '税务性质',
    enum: getDic('customer_property'),
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.taxNature || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'ccoc.tax_nature'
  },
  {
    prop: 'createBy',
    width: 150,
    search: {
      el: 'input'
    },
    label: '创建人',
    sortable: 'custom',
    sortName: 'ccoc.create_by'
  },
  {
    prop: 'createTime',
    width: 200,

    search: {
      el: 'date-picker',
      // span: 1,
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    label: '创建时间',
    sortable: 'custom',
    sortName: 'ccoc.create_time'
  },
  {
    prop: 'operation',
    width: 150,
    fixed: 'right',
    label: '操作'
  }
]

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// 新增客户
const type = ref('add')
const clientShow = ref(false)
const handleAdd = () => {
  type.value = 'add'
  clientShow.value = true
}

// getList
const proTable = ref()
const getList = () => {
  detailShow.value = false
  proTable.value?.getTableList()
}
// 显示客户详情弹窗
const detailShow = ref(false)
const clientId = ref()
const isDivide = ref()
const isGet = ref()
const showDetail = (row: any) => {
  clientId.value = row.id
  isDivide.value = row.isDivide
  isGet.value = row.isGet
  detailShow.value = true
}

const handleConvertParams = data => {
  data.tags = data.tags.map(item => item.tagId)
}
const handleEditClient = id => {
  detailShow.value = false
  showDialog({
    title: '编辑',
    customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: clientForm2, // 表单组件
    getApi: getClueDetail,
    requestParams: id,
    handleConvertParams,
    handleRevertParams: data => {
      data.tags = data.tags.map(item => {
        return {
          tagId: item
        }
      })

      data.entryType = '1' // 公海录入
      data.type = '1' // 代表客户
    },
    submitApi: saveClue, // 提交api
    submitCallback: getList // 提交成功之后的回调函数
  })
}
// 领取客户
const handleReceive = async row => {
  await useHandleData(
    receiveZoneClue,
    {
      id: row.id,
      type: '1' // 客户领取
    },
    `确定领取当前客户`
  )
  detailShow.value = false

  proTable.value?.getTableList()
}

// 删除客户
const handleDelete = async id => {
  await useHandleData(deleteClue, id, `删除当前客户`)
  detailShow.value = false
  proTable.value?.getTableList()
}

// 分配客户
const handleDivide = row => {
  showDialog({
    title: '分配客户',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: clueDistribute, // 表单组件
    submitApi: divideClue, // 提交api
    handleConvertParams: data => {
      data.type = '1' // 客户
      data.deptIds = row.deptIds
    },
    handleRevertParams: data => {
      data.id = row.id
    },
    submitCallback: getList // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 导出列表功能
const handleExport = async () => {
  const params = Object.assign({}, proTable.value.searchParam)
  if (params.lastFollowTime) {
    params.startLastFollowTime = params.lastFollowTime[0]
    params.endLastFollowTime = params.lastFollowTime[1]
    delete params.lastFollowTime
  }
  if (params.createTime) {
    params.startCreateTime = params.createTime[0]
    params.endCreateTime = params.createTime[1]
    delete params.createTime
  }
  if (params.becomeTime) {
    params.becomeTimeStart = params.becomeTime[0]
    params.becomeTimeEnd = params.becomeTime[1]
    delete params.becomeTime
  }
  if (params.lastModifiedTime) {
    params.lastModifiedTimeStart = params.lastModifiedTime[0]
    params.lastModifiedTimeEnd = params.lastModifiedTime[1]
    delete params.lastModifiedTime
  }
  const result = await cusCustomerOrClueCustomerInSeaListExport(params)
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}
</script>
<style lang="scss" scoped></style>
