var CarOverlay=T.Overlay.extend({initialize:function(t,i){this.lnglat=t;this.setOptions(i);this.options=i},onAdd:function(t){this.map=t;var i=this.div=document.createElement("div");var e=this.img=document.createElement("img");i.style.position="absolute";i.style.width=this.options.width+"px";i.style.height=this.options.height+"px";i.style.marginLeft=-this.options.width/2+"px";i.style.marginTop=-this.options.height/2+"px";i.style.zIndex=200;e.style.width=this.options.width+"px";e.style.height=this.options.height+"px";e.src=this.options.iconUrl;i.appendChild(e);t.getPanes().overlayPane.appendChild(this.div);this.update(this.lnglat)},onRemove:function(){var t=this.div.parentNode;if(t){t.removeChild(this.div);this.map=null;this.div=null}},CSS_TRANSFORM:function(){var t=document.createElement("div");var i=["transform","WebkitTransform","MozTransform","OTransform","msTransform"];for(var e=0;e<i.length;e++){var s=i[e];if(t.style[s]!==undefined){return s}}return i[0]},setRotate:function(t){this.img.style[this.CSS_TRANSFORM()]="rotate("+t+"deg)"},setLnglat:function(t){this.lnglat=t;this.update()},getLnglat:function(){return this.lnglat},setPos:function(t){this.lnglat=this.map.layerPointToLngLat(t);this.update()},update:function(){var t=this.map.lngLatToLayerPoint(this.lnglat);this.div.style.left=t.x+"px";this.div.style.top=t.y+"px"}});T.CarTrack=function(t,i){this.map=t;this.options.polylinestyle=this.setOptions(this.options.polylinestyle,i.polylinestyle);this.options.carstyle=this.setOptions(this.options.carstyle,i.carstyle);this.options=this.setOptions(this.options,i);this.init()};T.CarTrack.prototype={options:{interval:1e3,carstyle:{display:true,iconUrl:"http://lbs.tianditu.gov.cn/images/openlibrary/car.png",width:52,height:26},polylinestyle:{display:true,color:"red",width:"3",opacity:.8,lineStyle:""}},init:function(){var t=this;if(this.options.speed>0){var i=this.distance(this.options.Datas);this.options.nodeslength=i/this.options.speed}else{this.options.nodeslength=this.options.Datas.length}this.options.Counter=0;this.D3OverLayer=new T.D3Overlay(this.d3init,this.d3redraw,this.options);this.D3OverLayer.lineDatas=[];this.D3OverLayer.dataToLnglat=this.dataToLnglat;this.D3OverLayer.applyLatLngToLayer=this.applyLatLngToLayer;this.D3OverLayer.updateSymbolLine=this.updateSymbolLine;this.receiveData(this.map)},setOptions:function(t,i){for(var e in i){if(e!="polylinestyle"&&e!="carstyle")t[e]=i[e]}return t},clear:function(){this.state=4;this._Remove();delete this},_Remove:function(){this._pause();delete this._timer;this.map.removeOverLay(this.carMarker);this.map.removeOverLay(this.D3OverLayer)},receiveData:function(){var t=this.options;var i=this;if(t.Datas instanceof Array&&t.Datas.length>0){i.map.addOverLay(i.D3OverLayer);i.carMarker=new CarOverlay(i.dataToLnglat(this.options.Datas[0]),i.options.carstyle);if(!this.options.carstyle)i.carMarker.hide();i.map.addOverLay(this.carMarker);i.D3OverLayer.bringToBack()}},dataToLnglat:function(t){if(t instanceof T.LngLat||"lat"in t&&"lng"in t)return t;else{var i=t.geometry.coordinates;var e=new T.LngLat(i[0],i[1]);return e}},bind:function(t,i){var e=Array.prototype.slice;if(t.bind){return t.bind.apply(t,e.call(arguments,1))}},applyLatLngToLayer:function(t){return this.map.lngLatToLayerPoint(this.dataToLnglat(t))},d3init:function(t,i){t.append("path").attr("id","polyline").attr("fill","none").attr("stroke",this.options.polylinestyle.color).attr("opacity",this.options.polylinestyle.opacity).attr("display",this.options.polylinestyle.display?"block":"none");t.append("path").attr("id","dynamicLine").attr("fill","none").attr("stroke","red").attr("opacity",this.options.polylinestyle.opacity).attr("display",this.options.dynamicLine&&this.options.polylinestyle.display?"block":"none")},d3redraw:function(){function t(t,i){var e="",s,a,n,o,r,h;for(s=0,n=t.length;s<n;s++){r=t[s];for(a=0,o=r.length;a<o;a++){h=r[a];e+=(a?"L":"M")+h.x+" "+h.y}e+=i?L.Browser.svg?"z":"x":""}return e||"M0 0"}function i(t,i){var e=[];for(var s=0;s<i.length;s++){e.push(t.lngLatToLayerPoint(i[s]))}return e}var e=t([i(this.map,this.options.Datas)],false);var s=this.lineDatas?this.lineDatas:this.D3OverLayer.lineDatas;var a=t([i(this.map,s)],false);d3.select("path#polyline").attr("d",e).attr("stroke-width",this.options.polylinestyle.width+"px");d3.select("path#dynamicLine").attr("d",a).attr("stroke-width",this.options.polylinestyle.width+"px")},updateSymbolLine:function(){var t=[];for(var i=0;i<this.options.Datas.length-1;i++){var e=this.map.lngLatToLayerPoint(this.options.Datas[i]);var s=this.map.lngLatToLayerPoint(this.options.Datas[i+1]);var a=20;var n=e.distanceTo(s);for(var o=0;o<n/a;o++){var r=(s.x-e.x)*a*o/n;var h=(s.y-e.y)*a*o/n;var l=new T.Point(e.x+r,e.y+h);var p=this.map.layerPointToLngLat(l);t.push(p)}}this.D3OverLayer.SybomlDatas=t},update:function(){this.options.Counter++;var t=d3.select("path#polyline").attr("display",this.options.polylinestyle.display&&!this.options.dynamicLine?"block":"none");var i=this.options.speed>0?Math.ceil(this.options.nodeslength)+1:Math.ceil(this.options.nodeslength);if(this.options.speed>0){var e=t.node().getTotalLength();var s=(this.options.Counter-1)/this.options.nodeslength;var a=s*e;var n=t.node().getPointAtLength(a);this.D3OverLayer.lineDatas=[];if(this.options.Datas[0])this.D3OverLayer.lineDatas.push(this.options.Datas[0]);var o=0;for(var r=0;r<this.options.Datas.length-1;r++){var h=this.map.lngLatToLayerPoint(this.options.Datas[r]);var l=this.map.lngLatToLayerPoint(this.options.Datas[r+1]);var p=h.distanceTo(l);o=o+p;if(a>o)this.D3OverLayer.lineDatas.push(this.options.Datas[r+1]);else{break}}if(this.options.Counter<i){var y=this.map.layerPointToLngLat(n);this.D3OverLayer.lineDatas.push(y)}}else{this.D3OverLayer.lineDatas=this.options.Datas.slice(0,this.options.Counter)}this.carMarker.setLnglat(this.D3OverLayer.lineDatas[this.D3OverLayer.lineDatas.length-1]);if(this.options.Counter>1){var d=this.angle(this.D3OverLayer.lineDatas[this.D3OverLayer.lineDatas.length-2],this.D3OverLayer.lineDatas[this.D3OverLayer.lineDatas.length-1]);this.carMarker.setRotate(d)}else{this.carMarker.setRotate(0)}if(this.options.dynamicLine)this.d3redraw();if(this.options.passOneNode)this.options.passOneNode(this.carMarker.getLnglat(),this.options.Counter,i);if(this.options.Counter>=i){this.options.Counter=0}},distance:function(){var t=0;var i=this.options.Datas;var e=i.length;for(var s=0;s<e-1;s++){var a=this.dataToLnglat(i[s]);var n=this.dataToLnglat(i[s+1]);t=t+this.map.getDistance(a,n)}return t},angle:function(t,i){var e=0;if(i.lng!=t.lng){var s=(i.lat-t.lat)/(i.lng-t.lng),a=Math.atan(s);e=-a*360/(2*Math.PI);if(i.lng<t.lng){e=-e+90+90}else{e=-e}return-e}else{var n=i.lat-t.lat;var o=0;if(n>0)o=-1;else o=1;return-o*90}return},start:function(){if(this.state==4)return;this.state=1;if(this.D3OverLayer&&!this._timer){this._timer=setInterval(this.bind(this.update,this),this.options.interval)}return this},stop:function(){if(this.state==4)return;this.state=2;this._pause();this._Remove();this.init()},_pause:function(){if(this._timer){clearTimeout(this._timer);delete this._timer}return this},pause:function(){if(this.state==4)return;this.state=3;this._pause()}};