<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-11-06 14:10:22
 * @LastEditTime: 2024-02-21 11:16:19
 * @LastEditors: thb
-->
<template>
  <template v-if="list?.length">
    <div class="download-text" v-for="(file, index) in list" :key="index" @click="downloadFile(file)">
      <span class="file-name"> {{ file?.fileNames || '暂无文件' }}</span>
      <span class="blue-text load-btn" @click.stop="handleDownload(file)"> 下载</span>
    </div>
  </template>
  <template v-else>
    <span class="download-text"> 暂无文件 </span>
  </template>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import iFrame from '@/components/iFrame'
import { getFileUrlByOss } from '@/api/file/file.js'
defineProps({
  list: {
    type: Array,
    default: () => {
      return []
    }
  }
})

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

// 下载文件handleDownload
const handleDownload = async file => {
  const { data } = await getFileUrlByOss(file.urls)
  window.open(data)
}
</script>
<style lang="scss" scoped>
.download-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.load-btn {
  flex: 0 0 35px;
  margin-left: 10px;
}
</style>
