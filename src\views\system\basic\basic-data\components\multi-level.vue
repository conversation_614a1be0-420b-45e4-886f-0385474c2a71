<!--
 * @Description:  多层级数据设置
 * @Author: thb
 * @Date: 2023-05-23 11:10:14
 * @LastEditTime: 2023-08-14 16:27:12
 * @LastEditors: thb
-->
<template>
  <div class="wrap">
    <div v-if="list.length" class="wrap-left">
      <div v-for="(level, index) in list" :key="level" class="level-item">
        <div class="item-center">
          {{ '数据名称' + (index + 1) }}
          <el-icon color="red" class="item-cursor" @click.stop="handleDeleteLevel(index)"><Delete /></el-icon>
        </div>
        <!-- 每一层级的数据 -->
        <div class="item-list">
          <!-- 输入框和文本切换 -->
          <div
            v-for="(litem, lindex) in level.list"
            :key="litem"
            class="l-item"
            :class="litem.active ? 'item-active' : ''"
            @click="handleActive(lindex, level)"
          >
            <span v-if="!litem?.isEdit" class="litem-name" @mouseover="litem.isHover = true" @mouseout="litem.isHover = false">
              {{ litem.name }}

              <el-icon v-show="litem.isHover" color="black" class="item-cursor item-fix-edit" @click.stop="litem.isEdit = true"
                ><Edit
              /></el-icon>
              <el-icon
                v-show="litem.isHover"
                color="red"
                class="item-cursor item-fix-delete"
                @click.stop="handleDelete(litem, level)"
                ><Delete
              /></el-icon>
            </span>
            <el-input v-else v-model="litem.name" placeholder="请输入" @blur="handleBlur(litem)"></el-input>
          </div>
        </div>
        <div class="item-center item-cursor" @click="handleAddLevelContent(level, index)">
          <el-icon :size="20" class="add-icon"><CirclePlus /></el-icon>
          <span>添加内容</span>
        </div>
      </div>
    </div>
    <!-- 添加层级按钮 -->
    <div class="wrap-right">
      <div class="right-btn" @click="handleAddLevel">
        <el-icon :size="20"><CirclePlus /></el-icon>
        <span>添加层级</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { CirclePlus, Edit, Delete } from '@element-plus/icons-vue'

const list = ref([])

// 添加层级
const handleAddLevel = () => {
  list.value.push({
    list: []
  })
}

// 添加单个层级下的内容
const handleAddLevelContent = (level, index) => {
  level.list.forEach(item => {
    item.active = false
  })
  const contentItem = {
    name: '',
    // parent:activeItem[0]?activeItem[0].name+'-'+index:'',//之前被激活的name + 序号
    isEdit: true, // 是否编辑
    isHover: false, // 是否被悬浮展示编辑按钮
    active: true
  }
  level.list.push(contentItem)
  console.log('handleAddLevelContent')
  // 找到该level的前一个层级下的激活内容项
  if (index !== 0) {
    const activeItem = list.value[index - 1].list.filter(item => item.active)[0]
    if (!activeItem.children) {
      activeItem.children = []
      activeItem.children.push(contentItem)
    } else {
      activeItem.children.push(contentItem)
    }
  }
  console.log('level', level)
}

const handleActive = (lindex, level) => {
  level.list.forEach((item, index) => {
    if (index === lindex) {
      item.active = true
      if (item.children) {
        // 找到当前level所在层级
        const lindex = list.value.findIndex(item => item === level)
        // 目标层级
        // const targetIndex = lindex + 1
        // 删除目标层级后的数据
        list.value.splice(lindex + 1)
        const newLevel = {
          list: [...item.children] // 防止重复引用
        }
        list.value.push(newLevel)
        handleActive(0, list.value[list.value.length - 1]) // 默认激活第一个元素
      } else {
        const newIndex = list.value.findIndex(item => item === level)
        if (newIndex !== -1) {
          list.value.splice(newIndex + 1)
        }
      }
    } else {
      item.active = false
    }
  })
}
// 删除某个内容项
const handleDelete = (litem, level) => {
  const index = level.list.findIndex(item => item === litem)
  level.list.splice(index, 1)
  // 高亮最后一个内容项
  handleActive(level.list.length - 1, level)
}

// 删除某个层级
const handleDeleteLevel = index => {
  // list.value.splice(index)
  if (index === 0) {
    list.value = []
  } else {
    list.value.splice(index)
    // 删除源数据
    console.log('level', list.value[index - 1])
    const level = list.value[index - 1].list.filter(item => item.active)[0]
    level.children = []
    console.log('list', list.value)
  }
  // console.log('list', list.value)
}

// 输入框失去焦点
const handleBlur = item => {
  if (!item.name) return
  item.isEdit = false
}

const setDefaultList = data => {
  list.value = data
  // 默认高亮第一个元素
  handleActive(0, list.value[0])
}
defineExpose({
  list,
  setDefaultList
})
</script>

<style lang="scss" scoped>
.wrap {
  display: flex;
  width: 100%;
  height: 400px;
  .wrap-left {
    display: flex;
  }
  .wrap-right {
    display: flex;
    // flex: 1;
    align-items: center;
    .right-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100px;
      cursor: pointer;
    }
  }
}
.level-item {
  display: flex;
  flex-direction: column;
  width: 150px;
  height: 100%;
  cursor: pointer;
  &:last-child {
    .item-list {
      border-right: 1px solid black;
    }
  }
}
.item-center {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.litem-name {
  position: relative;
  display: inline-block;
  width: 80%;
  height: 100%;
  overflow: hidden;
  line-height: 25px;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.item-fix-edit {
  position: absolute;
  top: 5px;
  right: 20px;
}
.item-fix-delete {
  position: absolute;
  top: 5px;
  right: 0;
}
.item-list {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  border: 1px solid black;
  border-right: none;
  height: 600px;
  overflow-y: auto;
  .l-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 25px;
  }
  .item-active {
    color: white;
    background-color: chocolate;
  }
}
.item-cursor {
  cursor: pointer;
}
.add-icon {
  vertical-align: bottom;
}
.el-input {
  height: 25px;
}
</style>
