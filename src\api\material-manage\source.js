import request from '@/utils/request'

// "name": "根据id删除",
// "method": "delete",
// "path": "/cusSource/delete",
export const cusSourceDelete = params => {
  return request({
    url: '/cusSource/delete',
    method: 'delete',
    params
  })
}

// "name": "详情",
// "method": "get",
// "path": "/cusSource/detail",
export const cusSourceDetail = params => {
  return request({
    url: '/cusSource/detail',
    method: 'get',
    params
  })
}

// "name": "直投列表查询",
// "method": "get",
// "path": "/cusSource/direct/list",
export const cusSourceDirectlist = params => {
  return request({
    url: '/cusSource/direct/list',
    method: 'get',
    params
  })
}

// "name": "直投保存数据",
// "method": "post",
// "path": "/cusSource/save",
export const cusSourceSave = params => {
  return request({
    url: '/cusSource/direct/save',
    method: 'post',
    data: params
  })
}

// "name": "保存修改",
// "method": "post",
// "path": "/cusSource/saveOrUpdate",
export const cusSourceSaveOrUpdate = params => {
  return request({
    url: '/cusSource/saveOrUpdate',
    method: 'post',
    data: params
  })
}

export const cusSourceEnable = params => {
  return request({
    url: '/cusSource/enable',
    method: 'post',
    params
  })
}

// "name": "树结构查询",
// "method": "get",
// "path": "/cusSource/tree",
export const cusSourceTree = params => {
  params.createTime = undefined
  return request({
    url: '/cusSource/tree',
    method: 'get',
    params
  })
}
