<template>
  <el-tree-select
    v-model="feeType"
    filterable
    :data="productTreeData"
    :props="defaultPopsFunction(row)"
    @current-change="(node, row) => handleSelectChange(node, row)"
    :render-after-expand="false"
    default-expand-all
    :disabled="disabledBool"
    :placeholder="disabledBool ? ' ' : '请选择'"
  />
</template>

<script setup lang="jsx">
import { getBusinessList } from '@/api/business/business'

const emit = defineEmits()
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  disabledBool: {
    type: Boolean,
    default: false
  }
})

const feeType = ref('')
watch(
  () => props.modelValue,
  val => {
    feeType.value = val
  },
  { deep: true, immediate: true }
)

const handleSelectChange = (node, row) => {
  if (node?.type === '业务类型') return
  // console.log('node', node, row)
  emit('update:modelValue', node.id)
  emit('onSelectChange', node)
}

// 获取所有的产品名称
const productTreeData = ref()
const defaultPopsFunction = row => {
  console.log('row', row)
  return {
    value: 'id',
    label: 'name',
    disabled: data => {
      return data?.type === '业务类型' && !data?.children.length
    }
  }
}

const getAllProducts = async () => {
  const { data } = await getBusinessList({
    pageNum: 1,
    pageSize: 10000
  })
  // 将后端传回的数据结构进行转换
  const revertData = []
  data.forEach(item => {
    const obj = {
      contractType: item.contractType,
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      children: []
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach(child => {
        obj.children.push({
          name: child.productName,
          types:
            item.contractType?.split(',')?.map(item => {
              return {
                label: item,
                value: item
              }
            }) || [],
          type: '产品类型',
          activityStatus: child.activityStatus,
          activityQuotation: child.activityQuotation,
          discountTime: child.discountTime,
          feeType: child.feeType,
          quotation: child.quotation,
          isInContract: child.isInContract,
          id: child.id // 产品类型id
        })
      })
    }
  })
  productTreeData.value = revertData || []
}
getAllProducts()
</script>

<style lang="scss" scoped></style>
