<!--
 * @Description: 日记账表单
 * @Author: thb
 * @Date: 2023-09-07 08:51:39
 * @LastEditTime: 2023-09-13 15:44:19
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row>
      <el-col :span="12">
        <el-form-item label="记账日期" prop="receiptDate">
          <el-date-picker
            v-model="formData.receiptDate"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择"
            :disabledDate="disabledDate"
            :disabled="formData.formType === 'edit'"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="支付方式" prop="receiptMethod">
          <el-select v-model="formData.receiptMethod" placeholder="请选择" clearable>
            <el-option v-for="item in receipt_method" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="关联企业" prop="customerName">
          <!-- 点击弹窗出现客户列表  -->
          <div @click="handleShow" style="width: 100%" @mouseenter="showDeleteIcon" @mouseleave="deleteShow = false">
            <el-input v-model="formData.customerName" readonly maxlength="20" placeholder="请输入" />
            <span class="icon close-icon-small" v-if="deleteShow" @click.stop="clearCustomerName"></span>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="12">
      <el-col :span="12">
        <el-form-item label="对方科目" prop="projectId">
          <el-tree-select
            v-model="formData.projectId"
            :data="treeSelectData"
            :props="defaultProps"
            check-strictly
            clearable
            @change="setDefaultAbstract"
            @node-click="handleNodeClickByProject"
            :render-after-expand="false"
        /></el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="业务归属" prop="vestSource">
          <el-tree-select
            v-model="formData.vestSource"
            :props="{
              value: 'id',
              label: 'label',
              children: 'children'
            }"
            :data="treeData"
            @node-click="handleNodeClickByVest"
            check-strictly
            :render-after-expand="false"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="摘要" prop="digest">
          <el-input v-model="formData.digest" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入"
        /></el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="收入(借方)金额" prop="incomeAmount">
          <el-input placeholder="请输入" v-model="formData.incomeAmount" maxlength="20">
            <template #suffix>元</template>
          </el-input>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="付出(贷方)金额" prop="paymentAmount">
          <el-input placeholder="请输入" v-model="formData.paymentAmount" maxlength="20">
            <template #suffix>元</template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            placeholder="请输入"
          /> </el-form-item
      ></el-col>
    </el-row>
  </el-form>

  <tableModal
    v-if="listSelectShow"
    :init-param="{ discard: 0 }"
    rowKey="customerId"
    title="关联客户"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
</template>
<script setup lang="tsx">
import tableModal from '@/components/tableModal'
import { getCurrentInstance, reactive } from 'vue'
import { getCustomers } from '@/api/customer/file'
import { ColumnProps } from '@/components/ProTable/interface'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import { useDic } from '@/hooks/useDic'
import { getBusinessList } from '@/api/business/business'
import { getBookkeepingSubjectTreeList } from '@/api/basicData/basicData'
import { getReviewerTreeData } from '@/api/process/process'
import NumberInput from '@/components/NumberInput'
import { financePaymentList } from '@/api/finance/accounts-receivable'
import { FormValidators } from '@/utils/validate'
import dayjs from 'dayjs'
import { ref } from 'vue'
const { getDic } = useDic()
const formData: any = reactive({
  receiptDate: dayjs().format('YYYY-MM-DD'), // 默认为当前日期
  receiptMethod: '',
  customerName: '',
  customerId: '',
  vestSource: '', // 业务归属id和业务归属类型的组合
  vestId: '', // 业务归属id
  vestName: '', //业务归属name
  vestType: '', // 业务归属类型
  receiptId: '', // 账期id
  projectId: '',
  projectName: '', // 对方科目name
  digest: '',
  paymentAmount: '', // 收入余额
  incomeAmount: '', // 付出余额
  remark: '',
  id: undefined
})
const { proxy } = getCurrentInstance()
const { receipt_method } = proxy.useDict('receipt_method')
const rules = {
  receiptDate: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  receiptMethod: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  projectId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  digest: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur', 'change']
    }
  ],
  incomeAmount: [
    {
      required: false,
      validator: FormValidators.feeNumberPoint,
      trigger: ['blur']
    }
  ],
  paymentAmount: [
    {
      required: false,
      validator: FormValidators.feeNumberPoint,
      trigger: ['blur']
    }
  ]
}
const disabledDate = (time: any) => {
  return time.getTime() > Date.now()
}

const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '300',
    label: '财税顾问'
  },
  {
    prop: 'customerSuccessUserName',
    width: '300',
    label: '客户成功'
  }
]
// 关联企业
const listSelectShow = ref(false)
const handleShow = () => {
  listSelectShow.value = true
}
// 选择单个客户后
const handleSelect = (data: any) => {
  console.log('data', data)
  // 关联客户的数据有 客户名称  客户id(ciId/customerId) 客户编号 联系人 联系电话 所属公司
  const { customerName, customerId, customerNo } = data
  formData.customerName = customerName
  formData.customerId = customerId
  formData.customerNo = customerNo
  if (formData.customerNo && formData.projectId) {
    setAbstractValue({
      customerNo: formData.customerNo,
      productId: formData.projectId // 产品id(对方科目)
    })
  }
}

const setAbstractValue = async (params: { customerNo: string; productId: string }) => {
  const { data } = await financePaymentList(params)
  console.log('摘要', data.records)
  if (data.records && data.records[0]) {
    const { customerName, paymentStartTime, paymentEndTime, paymentId } = data.records[0]
    if (paymentStartTime && paymentEndTime) {
      formData.digest = `${customerName} ${formData.projectName} ${paymentStartTime} - ${paymentEndTime}`
    } else {
      formData.digest = `${customerName} ${formData.projectName} `
    }

    formData.receiptId = paymentId // 账期id
  } else {
    formData.digest = ''
  }
}
// setDefaultAbstract
const setDefaultAbstract = async (value: string) => {
  console.log('setDefaultAbstract', value)
  if (formData.customerNo && value) {
    setAbstractValue({
      customerNo: formData.customerNo,
      productId: value // 产品id(对方科目)
    })
  }
}

const handleNodeClickByVest = (node: any) => {
  formData.vestName = node.label
}
const handleNodeClickByProject = (node: any) => {
  formData.projectName = node.name
}

// showDeleteIcon
const deleteShow = ref(false)
const showDeleteIcon = () => {
  if (formData.customerName && formData.customerId) {
    deleteShow.value = true
  }
}
const clearCustomerName = () => {
  formData.customerName = ''
  formData.customerId = ''
  deleteShow.value = false
}

//treeSelectData

const convertData = (data: any, revertData: any) => {
  data.forEach((item: any) => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      child: [] as any
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach((child: any) => {
        obj.child.push({
          name: child.productName,
          type: '产品类型',
          id: child.id // 产品类型id
        })
      })
    }
  })
}
const defaultProps = { value: 'id', children: 'child', label: 'name' }
const treeSelectData = ref([])
const getTreeSelectData = async () => {
  const { data } = await getBusinessList({
    pageSize: 1000,
    pageNum: 1
  })
  // 将后端传回的数据结构进行转换
  const revertData: any = []
  convertData(data, revertData)
  const result = await getBookkeepingSubjectTreeList({
    enable: 1,
    pageSize: 1000,
    pageNum: 1
  })
  treeSelectData.value = revertData.concat(result.data)
}

getTreeSelectData()

// 获取reviewerTreeData
const setTree = (data: any[]) => {
  data.forEach(item => {
    item.id = item.type + '-' + item.id
    if (Array.isArray(item.children) && item.children.length) {
      setTree(item.children)
    }
  })
}
const treeData = ref([])
const getDeptTree = async () => {
  const { data } = await getReviewerTreeData()
  setTree(data)
  console.log('treeData', data)
  treeData.value = data || []
}

getDeptTree()
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped>
:deep(.el-date-editor) {
  width: 100%;
}
.el-select {
  width: 100%;
}

.icon {
  border: 1px solid #e8e8e8ff;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  position: absolute;
  top: 30%;
  right: 10px;
}
</style>
