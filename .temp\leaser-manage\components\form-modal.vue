<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="600px"
    top="5vh"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-position="top"
      :rules="rules"
      :disabled="['详情'].includes(mode)"
      :hide-required-asterisk="['详情'].includes(mode)"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="出租人名称" prop="xxx1">
            <el-input v-model="formData.xxx1" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="手机号码" prop="xxx2">
            <el-input v-model="formData.xxx2" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="银行" prop="xxx3">
            <el-input v-if="disabled" v-model="formData.xxx3" disabled />
            <el-cascader
              v-else
              style="width: 100%"
              class="select-class"
              v-model="formData.xxx3"
              filterable
              clearable
              :placeholder="disabled ? ' ' : '请选择'"
              :disabled="disabled"
              :props="{
                value: 'name',
                label: 'name',
                children: 'child'
              }"
              :options="bank_list"
              :show-all-levels="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="银行账户" prop="xxx4">
            <el-input v-model="formData.xxx4" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
      <el-button v-if="!['详情'].includes(mode)" type="primary" @click="handleSubmit" :loading="loading"> 保存 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { getClueAppealRecordGetById, postClueAppealRecordSave } from '@/api/material-manage/clue-appeal-record.js'
import { getBankTreeList } from '@/api/basicData/basicData'

const { proxy } = getCurrentInstance()
const emit = defineEmits()

const mode = ref('')
const formRef = ref()
const formData = reactive({})
const visible = ref(true)

const rules = {
  xxx1: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
}

function getDetail(row) {
  getClueAppealRecordGetById({ id: row.id }).then(res => {
    Object.assign(formData, res.data)
  })
}

const onAdd = () => {
  mode.value = '新增'
}
const onDetail = row => {
  getDetail(row)
  mode.value = '详情'
}
const onEdit = row => {
  getDetail(row)
  mode.value = '编辑'
}
const handleClose = () => {
  emit('close')
}

const loading = ref(false)
const handleSubmit = async () => {
  console.log('formData', formData)
  if (loading.value) return
  await formRef.value.validate()
  loading.value = true
  if (formData.id) {
    postClueAppealRecordSave(formData)
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
      .finally(() => {
        loading.value = false
      })
  } else {
    postClueAppealRecordSave(formData)
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}

const bank_list = ref([])
function onGetBasicData() {
  getBankTreeList({ enable: 1 }).then(res => {
    bank_list.value = res.data // 已验证，新增下拉有对应数据
  })
}
onGetBasicData()

defineExpose({
  onAdd,
  onEdit,
  onDetail
})
</script>

<style lang="scss" scoped>
.tips {
  color: #aaa;
}
</style>
