<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-09-25 13:15:41
 * @LastEditTime: 2023-09-28 16:39:50
 * @LastEditors: thb
-->
<template>
  <el-dialog
    v-model="visible"
    title="转单"
    :close-on-click-modal="false"
    :before-close="onHandleClose"
    width="500px"
    destroy-on-close
    append-to-body
  >
    <el-alert
      style="margin-bottom: 10px"
      title="转单后，该办理流程将和您不再存在任何关系，且操作后无法恢复"
      type="warning"
      show-icon
      :closable="false"
    />
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-form-item label="转让接收人" prop="userId">
        <SelectTree style="width: 100%" v-model="formData.userId" placeholder="请选择" clearable @on-node-click="handleChange" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onHandleClose"> 取消 </el-button>
      <el-button type="primary" @click="handleSubmit"> 确认 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { bizDelegate } from '@/api/certificate/certificate'
import SelectTree from '@/components/SelectTree'

const { proxy } = getCurrentInstance()
const emit = defineEmits(['ok'])

const formRef = ref(null)
const formData = reactive({
  taskId: undefined,
  userId: undefined
})

const rules = {
  userId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
}

const visible = ref(false)
const onShow = data => {
  visible.value = true
  formData.taskId = data.taskId
}
const onHandleClose = () => {
  visible.value = false
}
const handleSubmit = () => {
  formRef.value.validate(valid => {
    if (valid) {
      bizDelegate(formData).then(res => {
        if (res.code === 200) {
          proxy.$message.success('转单成功')
          onHandleClose()
          emit('ok')
        }
      })
    } else {
    }
  })
}

const handleChange = node => {
  formData.userId = node.label
}

defineExpose({
  onShow
})
</script>

<style lang="scss" scoped>
:deep(.el-select) {
  width: 100%;
}
</style>
