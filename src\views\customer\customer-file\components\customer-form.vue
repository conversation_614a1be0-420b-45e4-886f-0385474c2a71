<!--
 * @Description: 新增客户弹窗
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-11-27 14:07:31
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="企业名称" prop="customerName">
          <el-input v-model="formData.customerName" maxlength="100" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="formData.contactPerson" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="手机号" prop="contactPhone">
          <el-input v-model="formData.contactPhone" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="财税顾问" prop="mangerUserId">
          <!-- <el-select v-model="formData.manger" placeholder="请选择" clearable @change="handleChangeManger">
            <el-option :label="item.nickName" :value="item.userId" v-for="(item, index) in userList" :key="index" />
          </el-select> -->
          <SelectTree v-model="formData.mangerUserId" placeholder="请选择" clearable @on-node-click="handleChangeManger" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="所属分公司" prop="branchOffice">
          <el-select v-model="formData.branchOffice" placeholder="请选择" clearable>
            <el-option v-for="(option, index) in branch_office" :key="index" :label="option.name" :value="option.name" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="企业状态" prop="customerStatus">
          <el-select v-model="formData.customerStatus" placeholder="请选择" clearable>
            <el-option v-for="(option, index) in customer_status" :key="index" :label="option.label" :value="option.value" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="实际经营地址" prop="address">
          <el-input
            type="textarea"
            maxlength="100"
            v-model="formData.address"
            placeholder="请输入"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="企业性质" prop="customerProperty">
          <el-select v-model="formData.customerProperty" placeholder="请选择" clearable>
            <el-option v-for="(option, index) in customer_property" :key="index" :label="option.label" :value="option.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="从事行业" prop="industry">
          <el-select
            v-model="formData.industry"
            placeholder="请选择"
            clearable
            allow-create
            default-first-option
            filterable
            @change="handleChange"
          >
            <el-option v-for="(option, index) in industry" :key="index" :label="option.label" :value="option.value" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="营业执照" prop="businessFileList">
          <FileUpload :isShowTip="false" v-model="formData.businessFileList" :limit="100" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="开票员" prop="counselor">
          <!-- <el-select v-model="formData.counselor" placeholder="请选择" clearable @change="handleChangeCounselor">
            <el-option :label="item.nickName" :value="item.userId" v-for="(item, index) in userList" :key="index" />
          </el-select> -->
          <SelectTree v-model="formData.counselorUserId" placeholder="请选择" clearable @on-node-click="handleChangeCounselor" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="客户成功" prop="customerSuccess">
          <!-- <el-select v-model="formData.customerSuccess" placeholder="请选择" clearable @change="handleChangeCustomerSuccess">
            <el-option :label="item.nickName" :value="item.userId" v-for="(item, index) in userList" :key="index" />
          </el-select> -->
          <SelectTree
            v-model="formData.customerSuccessUserId"
            placeholder="请选择"
            clearable
            @on-node-click="handleChangeCustomerSuccess"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="主办会计" prop="sponsorAccounting">
          <!-- <el-select v-model="formData.sponsorAccounting" placeholder="请选择" clearable @change="handleChangeSponsorAccounting">
            <el-option :label="item.nickName" :value="item.userId" v-for="(item, index) in userList" :key="index" />
          </el-select> -->
          <SelectTree
            v-model="formData.sponsorAccountingUserId"
            placeholder="请选择"
            clearable
            @on-node-click="handleChangeSponsorAccounting"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import FileUpload from '@/components/FileUpload'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import { FormValidators } from '@/utils/validate'
import { listUser } from '@/api/system/user'
import { getCompanyTreeList } from '@/api/basicData/basicData'
import SelectTree from '@/components/SelectTree'
import { useSetDic } from '@/hooks/useSetDic'
const { setDic } = useSetDic()
const handleChange = value => {
  setDic('industry', value, value, industry)
}
const { proxy } = getCurrentInstance()
const { industry } = proxy.useDict('industry')
const branch_office = ref([])
function onGetBasicData() {
  getCompanyTreeList({ enable: 1 }).then(res => {
    branch_office.value = res.data.records // 未验证
  })
}
onGetBasicData()
const { customer_status } = proxy.useDict('customer_status')
const { customer_property } = proxy.useDict('customer_property')
const formData = reactive({
  customerName: '',
  contactPerson: '',
  contactPhone: '',
  manger: '',
  branchOffice: '',
  address: '',
  customerStatus: '',
  customerProperty: '',
  industry: ''
})

const rules = {
  customerName: [{ required: true, message: '请输入', trigger: 'blur' }],
  contactPerson: [{ required: true, message: '请输入', trigger: 'blur' }],
  contactPhone: [{ message: '请输入正确格式的手机号', validator: FormValidators.mobilePhone, trigger: 'blur' }],
  customerStatus: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  customerProperty: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  // mangerUserId: [
  //   {
  //     required: true,
  //     message: '请选择',
  //     trigger: 'change'
  //   }
  // ],
  branchOffice: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ]
  // address: [{ required: true, message: '请输入', trigger: 'blur' }],
  // customerStatus: [
  //   {
  //     required: true,
  //     message: '请选择',
  //     trigger: 'change'
  //   }
  // ],
  // customerProperty: [
  //   {
  //     required: true,
  //     message: '请选择',
  //     trigger: 'change'
  //   }
  // ],
  // industry: [
  //   {
  //     required: true,
  //     message: '请选择',
  //     trigger: 'change'
  //   }
  // ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

const userList = ref([])
const getUserList = async () => {
  const result = await listUser()
  userList.value = result.rows || []
}
getUserList()
defineExpose({
  // handleValidateForm,
  formData,
  getFormRef
})
const handleChangeManger = node => {
  // formData.mangerUserId = node.id
  formData.manger = node.label
}
// const handleChangeManger = userId => {
//   formData.mangerUserId = userId
//   formData.manger = userList.value.find(item => item.userId === userId).nickName
// }
const handleChangeCounselor = node => {
  // formData.counselorUserId = userId
  // formData.counselor = userList.value.find(item => item.userId === userId).nickName
  formData.counselor = node.label
}
const handleChangeCustomerSuccess = node => {
  // formData.customerSuccessUserId = userId
  formData.customerSuccess = node.label
}
const handleChangeSponsorAccounting = node => {
  // formData.sponsorAccountingUserId = userId
  formData.sponsorAccounting = node.label
}
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
