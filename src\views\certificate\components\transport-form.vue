<template>
  <el-form ref="formRef" :model="data" :rules="rules" label-width="120px" label-position="top">
    <el-row :gutter="24">
      <rowCheck
        v-for="(value, key) in formItemMap"
        :data="data"
        :key="key"
        :label="value"
        :labelKey="key"
        :flagKey="flagMap[key]"
        @on-load-success="validateFormFiled"
        @on-preview="previewFile"
      ></rowCheck>
    </el-row>
  </el-form>
</template>
<script setup>
import { FormValidators } from '@/utils/validate'
import rowCheck from './row-check'
// @ApiModelProperty("驾驶证")
//     private List<CommonBizFile> driversLicenseFileList;

//     @ApiModelProperty("驾驶员身份证")
//     private List<CommonBizFile> driversIdentityFileList;

//     @ApiModelProperty("从业资格证")
//     private List<CommonBizFile> professionalQualificationCertificateFileList;
defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const formItemMap = {
  copyOfLicenseFileList: '执照副本',
  officialSealFileList: '公章',
  driversLicenseFileList: '驾驶证',
  driversIdentityFileList: '驾驶员身份证',
  professionalQualificationCertificateFileList: '从业资格证'
}

const flagMap = {
  professionalQualificationCertificateFileList: 'qualificationFileListFlag'
}
// 劳务派遣表单检验规则
const rules = {
  copyOfLicenseFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  officialSealFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  driversLicenseFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  driversIdentityFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  professionalQualificationCertificateFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
// 预览文件
const emits = defineEmits('on-preview')
const previewFile = file => {
  emits('on-preview', file)
}
const formRef = ref()

// 文件上传后检验
const validateFormFiled = field => {
  formRef.value.validateField(field)
}
defineExpose({
  formRef,
  rules,
  flagMap
})
</script>
<style lang="scss" scoped>
.el-checkbox {
  margin-right: 8px;
}
:deep(.el-col.el-col-12.is-guttered) {
  display: flex;
  align-items: baseline;
  .el-form-item {
    flex: 1;
  }
}
</style>
