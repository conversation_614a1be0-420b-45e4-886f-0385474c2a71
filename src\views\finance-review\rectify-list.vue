<template>
  <ProTable
    :init-param="initParam"
    v-if="initParam.searchStatus === ''"
    ref="proTable"
    title="整改办理"
    row-key="id"
    :columns="columnsExtra"
    :request-api="financeReviewRectifyList"
    :transformRequestParams="transformRequestParams"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.searchStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #auditStatus="{ row }">
      <span v-if="row.auditStatus === 'pending'" style="color: #409eff">待审批</span>
      <span v-else-if="row.auditStatus === 'pass'" style="color: #67c23a">通过</span>
      <span v-else-if="row.auditStatus === 'not_pass'" style="color: #f56c6c">不通过</span>
      <span v-else>--</span>
    </template>
    <template #rectificationTime="{ row }">
      <span
        v-if="
          row.expectCompleteDate &&
          dayjs().add(-1, 'day').valueOf() >= dayjs(row.expectCompleteDate).valueOf() &&
          row.rectificationStatus === 'pending'
        "
        style="color: #f56c6c"
        >已超时</span
      >
      <span
        v-else-if="
          row.expectCompleteDate &&
          row.rectificationTime &&
          dayjs(row.rectificationTime).valueOf() > dayjs(row.expectCompleteDate).valueOf()
        "
        style="color: #f56c6c"
        >{{ row.rectificationTime }}</span
      >
      <span v-else>{{ row.rectificationTime }}</span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button
        v-if="scope.row.rectificationStatus === 'pending' && scope.row.sponsorAccountingUserId === userStore?.user?.userId"
        type="primary"
        link
        @click="handlDeal(scope.row)"
        >办理</el-button
      >
      <el-button v-else type="primary" link @click="handlDetail(scope.row)">详情</el-button>
    </template>
  </ProTable>
  <ProTable
    :init-param="initParam"
    v-if="initParam.searchStatus === 'rectification_pending'"
    ref="proTable"
    title="整改办理"
    row-key="id"
    :columns="columns"
    :request-api="financeReviewRectifyList"
    :transformRequestParams="transformRequestParams"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.searchStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #auditStatus="{ row }">
      <span v-if="row.auditStatus === 'pending'" style="color: #409eff">待审批</span>
      <span v-else-if="row.auditStatus === 'pass'" style="color: #67c23a">通过</span>
      <span v-else-if="row.auditStatus === 'not_pass'" style="color: #f56c6c">不通过</span>
      <span v-else>--</span>
    </template>
    <template #rectificationTime="{ row }">
      <span
        v-if="
          row.expectCompleteDate &&
          dayjs().add(-1, 'day').valueOf() >= dayjs(row.expectCompleteDate).valueOf() &&
          row.rectificationStatus === 'pending'
        "
        style="color: #f56c6c"
        >已超时</span
      >
      <span
        v-else-if="
          row.expectCompleteDate &&
          row.rectificationTime &&
          dayjs(row.rectificationTime).valueOf() > dayjs(row.expectCompleteDate).valueOf()
        "
        style="color: #f56c6c"
        >{{ row.rectificationTime }}</span
      >
      <span v-else>{{ row.rectificationTime }}</span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button
        v-if="scope.row.rectificationStatus === 'pending' && scope.row.sponsorAccountingUserId === userStore?.user?.userId"
        type="primary"
        link
        @click="handlDeal(scope.row)"
        >办理</el-button
      >
      <el-button v-else type="primary" link @click="handlDetail(scope.row)">详情</el-button>
    </template>
  </ProTable>
  <ProTable
    :init-param="initParam"
    v-if="initParam.searchStatus === 'completed'"
    ref="proTable"
    title="整改办理"
    row-key="id"
    :columns="columnsExtra"
    :request-api="financeReviewRectifyList"
    :transformRequestParams="transformRequestParams"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.searchStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #auditStatus="{ row }">
      <span v-if="row.auditStatus === 'pending'" style="color: #409eff">待审批</span>
      <span v-else-if="row.auditStatus === 'pass'" style="color: #67c23a">通过</span>
      <span v-else-if="row.auditStatus === 'not_pass'" style="color: #f56c6c">不通过</span>
      <span v-else>--</span>
    </template>
    <template #rectificationTime="{ row }">
      <span
        v-if="
          row.expectCompleteDate &&
          dayjs().add(-1, 'day').valueOf() >= dayjs(row.expectCompleteDate).valueOf() &&
          row.rectificationStatus === 'pending'
        "
        style="color: #f56c6c"
        >已超时</span
      >
      <span
        v-else-if="
          row.expectCompleteDate &&
          row.rectificationTime &&
          dayjs(row.rectificationTime).valueOf() > dayjs(row.expectCompleteDate).valueOf()
        "
        style="color: #f56c6c"
        >{{ row.rectificationTime }}</span
      >
      <span v-else>{{ row.rectificationTime }}</span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button
        v-if="scope.row.rectificationStatus === 'pending' && scope.row.sponsorAccountingUserId === userStore?.user?.userId"
        type="primary"
        link
        @click="handlDeal(scope.row)"
        >办理</el-button
      >
      <el-button v-else type="primary" link @click="handlDetail(scope.row)">详情</el-button>
    </template>
  </ProTable>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { financeReviewRectifyList, financeReviewGetById } from '@/api/finance-review/finance-review.js'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from './components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
import { useDic } from '@/hooks/useDic'
import useCommonStore from '@/store/modules/common'

const userStore = useUserStore()
// console.log('userStore?.user?.userId', userStore?.user?.userId)
const useCommon = useCommonStore()
const { getDic } = useDic()
const { proxy } = getCurrentInstance()

// 整改状态
// pending 待整改
// rectified 已整改
// not_rectified 不予整改
const rectificationStatusDict = [
  {
    label: '待整改',
    value: 'pending'
  },
  {
    label: '已整改',
    value: 'rectified'
  },
  {
    label: '不予整改',
    value: 'not_rectified'
  }
]
// 审核状态
// pending 待整改
// pass 通过
// not_pass 不通过
const auditStatusDict = [
  {
    label: '待审批',
    value: 'pending'
  },
  {
    label: '通过',
    value: 'pass'
  },
  {
    label: '不通过',
    value: 'not_pass'
  }
]

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}
const initParam = reactive({
  searchStatus: 'rectification_pending'
})
const tabs = ref([
  {
    dictLabel: '全部',
    dicValue: ''
  },
  {
    dictLabel: '待整改',
    dicValue: 'rectification_pending'
  },
  {
    dictLabel: '已完成',
    dicValue: 'completed'
  }
])
const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.searchStatus = e
}
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'customerName',
    label: '关联企业',
    search: { el: 'input' },
    fixed: 'left',
    width: 300
  },
  {
    prop: 'category',
    label: '分类',
    enum: getDic('finance_review_category'),
    search: { el: 'select' },
    width: 150
  },
  {
    prop: 'issue',
    label: '问题',
    width: 200
  },
  {
    prop: 'reason',
    label: '原因',
    width: 200
  },
  {
    prop: 'voucherCode',
    label: '凭证号',
    width: 150
  },
  {
    prop: 'sponsorAccountingUserName',
    label: '做账会计',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'voucherPeriod',
    label: '凭证所属期',
    search: {
      el: 'date-picker',
      props: { type: 'month', valueFormat: 'YYYY-MM' }
    },
    width: 150
  },
  {
    prop: 'reviewDate',
    label: '查账日期',
    search: {
      el: 'date-picker',
      props: { type: 'date', valueFormat: 'YYYY-MM-DD' }
    },
    width: 150
  },
  {
    prop: 'rectificationStatus',
    enum: rectificationStatusDict,
    search: { el: 'select' },
    label: '整改状态',
    width: 150
  },
  {
    prop: 'rectificationTime',
    label: '改账日期',
    width: 150
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 100
  }
]
const columnsExtra = Object.assign([], columns)
columnsExtra.splice(10, 0, {
  prop: 'auditStatus',
  enum: auditStatusDict,
  label: '审批状态',
  width: 150
})
columnsExtra.splice(12, 0, {
  prop: 'completeTime',
  label: '完成时间',
  search: {
    el: 'date-picker',
    props: { type: 'daterange', valueFormat: 'YYYY-MM-DD' }
  },
  width: 150
})

const transformRequestParams = (data: any) => {
  // 完成时间
  if (data.completeTime) {
    data.rectificationCompleteStartDate = data.completeTime[0]
    data.rectificationCompleteEndDate = data.completeTime[1]
  }
}

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handlDeal = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDeal(row)
  })
}

const handlDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

watch(
  () => useCommon.id,
  async () => {
    if (useCommon.id && useCommon.bizType === 'payment_review_record') {
      financeReviewGetById({ id: useCommon.id })
        .then((res: any) => {
          if (res.data.rectificationStatus === 'pending') {
            handlDeal({
              id: useCommon.id
            })
          } else {
            handlDetail({
              id: useCommon.id
            })
          }
        })
        .finally(() => {
          useCommon.clearId()
          useCommon.clearBizType()
        })
    }
  },
  {
    immediate: true
  }
)

watch(
  () => useCommon.todoTaskFlag,
  () => {
    if (useCommon.todoTaskFlag) {
      nextTick(async () => {
        // 接收人为账户使用者
        proTable.value.searchParam.sponsorAccountingUserName = userStore?.user?.nickName
        await proTable.value?.requestResult
        proTable.value?.search()
        useCommon.clearTodoTaskFlag()
      })
    }
  },
  {
    immediate: true
  }
)
// onMounted(() => {
// })
</script>
<style lang="scss" scoped></style>
