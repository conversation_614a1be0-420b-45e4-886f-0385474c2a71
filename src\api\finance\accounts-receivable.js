import request from '@/utils/request'

// 列表查询
export const financePaymentList = params => {
  params.monthTurnover = undefined
  params.priceFluctuationAmount = undefined
  return request({
    url: '/financePayment/list',
    method: 'get',
    params
  })
}

// 根据费用类别去查询账款金额
export const financePaymentGetPaymentamountByFeeType = query => {
  return request({
    url: '/financePayment/getPaymentamountByFeeType',
    method: 'post',
    data: query
  })
}

// 保存数据
export const financePaymentSaveOrUpdate = query => {
  return request({
    url: '/financePayment/saveOrUpdate',
    method: 'post',
    data: query
  })
}

// 收款分析
export const financePaymentCollectionAnalysisSaveOrUpdate = query => {
  return request({
    url: '/financePayment/collectionAnalysis',
    method: 'post',
    data: query
  })
}

// 统计数据查询
export const financePaymentGetStaticData = params => {
  return request({
    url: '/financePayment/getStaticData',
    method: 'get',
    params
  })
}

// 详情
export const financePaymentGetById = params => {
  return request({
    url: '/financePayment/getById',
    method: 'get',
    params
  })
}
// 导入账单
export const uploadPayment = query => {
  return request({
    url: '/financePayment/importPayment',
    method: 'post',
    data: query
  })
}

// 关闭账单
export const postFinancePaymentClose = query => {
  return request({
    url: '/financePayment/close?id=' + query.id,
    method: 'post',
    data: query
  })
}

// 获取结算信息
export const getFinancePaymentGetSettlementInfo = params => {
  return request({
    url: '/financePayment/getSettlementInfo',
    method: 'get',
    params
  })
}

// 根据id删除
export const financePaymentDelete = params => {
  return request({
    url: '/financePayment/delete',
    method: 'delete',
    params
  })
}

// 导出企业档案列表
export const downloadFinancePaymentExport = query => {
  return request({
    url: '/financePayment/export',
    method: 'post',
    data: query
  })
}
