import request from '@/utils/request'

// "name": "列表查询",
// "method": "get",
// "path": "/userCustomerBindRecord/list",
export const getUserCustomerBindRecordList = params => {
  return request({
    url: '/userCustomerBindRecord/list',
    method: 'get',
    params
  })
}

// "name": "保存数据",
// "method": "post",
// "path": "/userCustomerBindRecord/savet",
export const postUserCustomerBindRecordSave = params => {
  return request({
    url: '/userCustomerBindRecord/save',
    method: 'post',
    data: params
  })
}

// "name": "解绑",
// "method": "post",
// "path": "/userCustomerBindRecord/unbind",
export const postUserCustomerBindRecordUnbind = params => {
  return request({
    url: '/userCustomerBindRecord/unbind',
    method: 'post',
    data: params
  })
}
