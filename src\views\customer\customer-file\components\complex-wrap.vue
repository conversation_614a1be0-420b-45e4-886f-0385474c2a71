<!--
 * @Description: 高级新建组件的外层组件
 * @Author: thb
 * @Date: 2023-05-30 08:19:43
 * @LastEditTime: 2023-08-30 10:20:34
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    class="complex-dialog"
    width="1200"
    :close-on-click-modal="false"
    :title="title"
    v-model="visible"
    @close="handleClose"
  >
    <complexForm ref="complexRef" />
    <template #footer>
      <el-button type="primary" @click="saveForm">保存</el-button>
      <el-button type="primary" @click="saveFormCancel">保存并关闭</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import complexForm from './complex-form.vue'
import { getCustomerContactByCiId } from '@/api/customer/file'
const isDisabled = ref(false)
provide('disabled', isDisabled)
const visible = ref(true)
const emits = defineEmits(['on-close'])
const handleClose = () => {
  emits('on-close')
}
const props = defineProps({
  data: Object,
  title: String
})

const complexRef = ref()
const saveForm = () => {
  complexRef.value.saveRemote()
}
// provide('customerName', props.data?.customerName)
const saveFormCancel = async () => {
  const result = await complexRef.value.saveRemote()
  if (result) {
    handleClose()
  }
}
onMounted(() => {
  nextTick(async () => {
    // 单独获取联系人id
    const { data } = await getCustomerContactByCiId(props.data.ciId)
    // 此时联系人只有一个
    let contractMainId, createBy, createTime
    if (Array.isArray(data) && data.length) {
      contractMainId = data[0]?.id
      createBy = data[0]?.createBy
      createTime = data[0]?.createTime
    }

    complexRef.value.setBasicData({
      ...props.data,
      contractMainId,
      createBy,
      createTime
    })
  })
})
</script>
<style lang="scss" scoped>
:deep(.el-date-editor.el-date-editor--date) {
  width: 100%;
}
.el-select {
  width: 100%;
}
</style>
