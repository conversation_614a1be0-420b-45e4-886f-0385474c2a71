<template>
  <div style="width: 100%; padding: 10px">
    <div v-if="!disabled" style="text-align: right; width: 100%; margin-bottom: 10px">
      <el-button type="primary" @click.stop="handleAddRow"> 新增 </el-button>
    </div>
    <el-table :data="commonList" style="width: 100%">
      <el-table-column prop="commonBankName" width="200" label="一般户开户银行">
        <template #default="{ row }">
          <el-tooltip
            :content="row.commonBankName?.length ? row.commonBankName[row.commonBankName.length - 1] : ''"
            v-if="disabled && row.commonBankName?.length"
            effect="light"
            placement="top-start"
          >
            <!-- <el-select :disabled="disabled" v-model="row.commonBankName" :placeholder="disabled ? ' ' : '请选择'" clearable>
                <el-option v-for="(option, index) in common_bank_list" :key="index" :label="option.name" :value="option.name" />
              </el-select> -->
            <div style="width: 100%">
              <el-cascader
                v-model="row.commonBankName"
                filterable
                clearable
                :placeholder="disabled ? ' ' : '请选择'"
                :disabled="disabled"
                :props="{
                  value: 'name',
                  label: 'name',
                  children: 'child'
                }"
                :options="bank_list"
                :show-all-levels="false"
              />
            </div>
          </el-tooltip>
          <!-- <el-select
              v-else
              :disabled="disabled"
              v-model="row.commonBankName"
              :placeholder="disabled ? ' ' : '请选择'"
              clearable
            >
              <el-option v-for="(option, index) in common_bank_list" :key="index" :label="option.name" :value="option.name" />
            </el-select> -->

          <!-- 采用级联选择器 -->
          <el-cascader
            v-else
            v-model="row.commonBankName"
            filterable
            clearable
            :placeholder="disabled ? ' ' : '请选择'"
            :disabled="disabled"
            :props="{
              value: 'name',
              label: 'name',
              children: 'child'
            }"
            :options="bank_list"
            :show-all-levels="false"
          />
        </template>
      </el-table-column>
      <el-table-column prop="commonBankAccount" width="200" label="一般户账号">
        <template #default="{ row }">
          <el-tooltip
            :content="row.commonBankAccount"
            v-if="disabled && row.commonBankAccount"
            effect="light"
            placement="top-start"
          >
            <el-input
              v-model="row.commonBankAccount"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            v-model="row.commonBankAccount"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </template>
      </el-table-column>
      <!-- <el-table-column prop="commonBankFlag" width="200" label="一般户网银">
        <template #default="{ row }">
          <customSelect :disabled="disabled" v-model="row.commonBankFlag" />
        </template>
      </el-table-column> -->
      <el-table-column prop="commonReceiptCardFlag" width="120" label="一般户回单卡">
        <template #default="{ row }">
          <el-select
            :disabled="disabled"
            v-model="row.commonReceiptCardFlag"
            :placeholder="disabled ? ' ' : '请选择'"
            @change="value => handleChange2(value, row)"
            clearable
          >
            <el-option label="无" value="0"></el-option>
            <el-option label="有" value="1"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <!-- 回单卡为有才显示 -->
      <el-table-column prop="commonReceiptCardType" width="150" label="一般户回单卡类型">
        <template #default="{ row }">
          <el-select
            v-if="row.commonReceiptCardFlag === '1'"
            :disabled="disabled"
            v-model="row.commonReceiptCardType"
            :placeholder="disabled ? ' ' : '请选择'"
            @change="value => handleChange3(value, row)"
            clearable
          >
            <el-option label="卡" value="卡"></el-option>
            <el-option label="账号密码" value="账号密码"></el-option>
            <el-option label="自动获取" value="自动获取"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <!-- 回单卡类型为账号密码才显示-->
      <el-table-column prop="commonCardAccount" minWidth="150" label="一般户回单卡账号">
        <template #default="{ row }">
          <template v-if="row.commonReceiptCardType === '账号密码'">
            <el-tooltip
              :content="row.commonCardAccount"
              v-if="disabled && row.commonCardAccount"
              effect="light"
              placement="top-start"
            >
              <el-input
                v-model="row.commonCardAccount"
                maxlength="100"
                :disabled="disabled"
                :placeholder="disabled ? '' : '请输入'"
              />
            </el-tooltip>
            <el-input
              v-else
              v-model="row.commonCardAccount"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="commonCardPassword" minWidth="150" label="一般户回单卡密码">
        <template #default="{ row }">
          <el-input
            v-if="row.commonReceiptCardType === '账号密码' || row.commonReceiptCardType === '卡'"
            v-model="row.commonCardPassword"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" width="100" fixed="right" v-if="!disabled">
        <template #default="{ $index }">
          <el-button type="danger" @click="handleDeleteRow($index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { getBankTreeList } from '@/api/basicData/basicData'
import customSelect from '@/views/customer/customer-file/components/custom-select.vue'
import { watch } from 'vue'

const { proxy } = getCurrentInstance()
const emit = defineEmits()

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  disabled: Boolean
})

// const { bank_list } = proxy.useBasicDict('bank_list')
const bank_list = ref([])
function onGetBasicData() {
  getBankTreeList({ enable: 1 }).then(res => {
    bank_list.value = res.data // 已验证，新增下拉有对应数据
  })
}
onGetBasicData()

const commonList = ref([])

const handleChange2 = (value, row) => {
  if (value === '0') {
    row.commonReceiptCardType = ''
    row.commonCardAccount = ''
    row.commonCardPassword = ''
  }
}

const handleChange3 = (value, row) => {
  if (value !== '账户密码') {
    row.commonCardAccount = ''
    row.commonCardPassword = ''
  }
}

const handleAddRow = () => {
  commonList.value.push({
    commonBankName: [],
    commonBankAccount: '',
    commonBankFlag: '无',
    commonReceiptCardFlag: '无',
    commonReceiptCardType: '',
    commonCardAccount: '',
    commonCardPassword: ''
  })
  // emit('update:modelValue', commonList.value)
}

const handleDeleteRow = index => {
  commonList.value.splice(index, 1)
  // emit('update:modelValue', commonList.value)
}
watch(
  () => props.modelValue,
  () => {
    commonList.value = props.modelValue
  },
  { deep: true, immediate: true }
)
watch(
  () => commonList.value,
  () => {
    emit('update:modelValue', commonList.value)
  },
  { deep: true }
)
</script>

<style lang="scss" scoped></style>
