<!--
 * @Description: 新增或者编辑归档号
 * @Author: thb
 * @Date: 2023-10-30 09:50:15
 * @LastEditTime: 2023-10-31 11:45:18
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="关联企业" prop="customerName">
          <el-input v-model="formData.customerName" disabled maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="合同编号" prop="contractNo">
          <el-input v-model="formData.contractNo" maxlength="20" placeholder="请输入" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="服务产品" prop="productName">
          <el-input v-model="formData.productName" maxlength="20" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8" v-if="formData.startTime">
        <el-form-item :label="formData.contractType === '2' ? '协议开始时间' : '合同起始时间'" prop="startTime">
          <el-date-picker v-model="formData.startTime" format="YYYY-MM" value-format="YYYY-MM" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="8" v-if="formData.monthNum">
        <el-form-item :label="formData.contractType === '2' ? '服务期限' : '服务月份'" prop="monthNum">
          <NumberInput v-if="formData.contractType !== '2'" v-model="formData.monthNum" disabled>
            <template #suffix>
              <div>月</div>
            </template>
          </NumberInput>
          <NumberInput v-else v-model="formData.monthNum" disabled maxlength="20">
            <template #suffix>
              <div>年</div>
            </template></NumberInput
          >
        </el-form-item>
      </el-col>
      <el-col :span="8" v-if="formData.endTime">
        <el-form-item :label="formData.contractType === '2' ? '协议结束时间' : '合同结束时间'" prop="endTime">
          <el-date-picker v-model="formData.endTime" format="YYYY-MM" value-format="YYYY-MM" type="month" disabled />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="8" v-if="formData.contractType === '2'">
        <el-form-item label="以后每年" prop="everyYear">
          <NumberInput v-model="formData.everyYear" maxlength="20" :regFormat="/^(0+)|[^\d]+/g" disabled>
            <template #suffix>
              <div>元</div>
            </template>
          </NumberInput>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="服务费" prop="serviceCost">
          <!-- formData.rowData?.isInContract === '1' 为在合同中定义 需要自己输入 -->
          <NumberInput v-model="formData.serviceCost" maxlength="20" :regFormat="/^(0+)|[^\d]+/g" disabled placeholder="请输入">
            <!-- 如果是一次性收费 -->
            <template #suffix>
              <!-- 一次性收费 -->
              <div v-if="formData.feeType === '0'">元</div>
              <!-- 每年收费 -->
              <div v-if="formData.feeType === '1'">元/年</div>
              <!-- 每月收费 -->
              <div v-if="formData.feeType === '2'">元/月</div>
            </template>
          </NumberInput>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="合同总金额" prop="totalCostCn">
          <el-input v-model="formData.totalCost" disabled> </el-input>
          <div>大写：{{ formData.totalCostCn }}</div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="formData.contractType !== '2'">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" disabled :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" maxlength="1000" />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 新增归档号 -->
    <el-row>
      <el-col :span="8">
        <el-form-item label="归档号" prop="documentNo">
          <el-input v-model="formData.documentNo" length="20"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import { reactive } from 'vue'
import NumberInput from '@/components/NumberInput'
const formData = reactive({
  customerName: '', // 关联客户
  contractNo: '', // 合同编码
  productName: '', //服务产品
  startTime: '', // 合同起始时间
  monthNum: '', // 服务月份
  endTime: '', // 合同结束时间
  contractType: '', // 合同类型
  serviceCost: '', // 服务费
  totalCostCn: '', // 合同总金额中文
  totalCost: '', // 合同总金额
  remark: '', // 备注
  everyYear: '', // 以后每年
  documentNo: '', // 归档号
  feeType: '',
  contractId: undefined
})

const rules = {
  documentNo: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
