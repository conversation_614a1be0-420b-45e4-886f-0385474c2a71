/*
 * @Description: 基础数据的增删改查
 * @Author: thb
 * @Date: 2023-05-23 10:07:01
 * @LastEditTime: 2023-05-24 10:52:19
 * @LastEditors: thb
 */

import request from '@/utils/request'

// 查询基础数据列表
export function getDictList(query) {
  return request({
    url: '/system/baseConfig/list',
    method: 'get',
    params: query
  })
}

// 新增或者编辑基础数据
export function addOrUpdateDict(query) {
  return request({
    url: '/system/baseConfig',
    method: 'post',
    data: query
  })
}

// 删除基础数据
export function deleteDict(id) {
  return request({
    url: '/system/baseConfig/' + id,
    method: 'delete'
  })
}
