<!--
 * @Description: 数据面板right 选择框
 * @Author: thb
 * @Date: 2023-09-04 10:55:56
 * @LastEditTime: 2023-09-04 10:58:23
 * @LastEditors: thb
-->
<template>
  <el-select v-model="selectValue" placeholder="请选择" v-bind="$attr">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>
<script setup>
const enumMap = inject('enumMap')
console.log('inject enumMap', enumMap)
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: () => {
      return []
    }
  }
})

const emits = defineEmits('update:modelValue')
const selectValue = computed({
  get: () => {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  }
})
</script>
<style lang="scss" scoped></style>
