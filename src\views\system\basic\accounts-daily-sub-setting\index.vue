<template>
  <ProTable
    ref="proTable"
    title="日记账科目设置"
    row-key="id"
    :columns="columns"
    :request-api="getBookkeepingSubjectTreeList"
    :dataCallback="dataCallback"
    :pagination="false"
  >
    <template #enable="{ row }">
      <span> <el-switch active-value="1" inactive-value="0" :model-value="row.enable" @click="handleChangeEnable(row)" /></span>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button :icon="Upload" @click="handleImport">导入</el-button>
    </template>

    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handleAdd(scope.row)">新增</el-button>
      <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
      <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
    </template>
  </ProTable>
  <ImportExcel ref="dialogRef" />
</template>
<script setup lang="tsx">
import { ref } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  getBookkeepingSubjectTreeList,
  getBookkeepingSubjectDetail,
  deleteBookkeepingSubject,
  postAddBookkeepingSubject,
  postBookkeepingSubjectEnable,
  postBookkeepingSubjectImportTempUrl,
  postBookkeepingSubjectImport
} from '@/api/basicData/basicData'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus, Upload } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialogFinance'
import Form from './components/form.vue'
import { useHandleData } from '@/hooks/useHandleData'
import ImportExcel from '@/components/ImportExcel/index.vue'

const { proxy } = getCurrentInstance()

// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'name',
    label: '日记账科目',
    search: { el: 'input' }
  },
  {
    prop: 'sort',
    label: '排序'
  },
  {
    prop: 'remark',
    label: '备注'
  },
  {
    prop: 'enable',
    enum: [
      {
        label: '正常',
        value: '1'
      },
      {
        label: '禁用',
        value: '0'
      }
    ],
    search: { el: 'select' },
    label: '状态'
  },
  {
    prop: 'updateTime',
    label: '修改时间'
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right'
  }
]

function renameChildToChildren(items: any) {
  return items.map((item: any) => {
    if (item.child) {
      return {
        ...item,
        children: renameChildToChildren(item.child),
        child: undefined
      }
    }
    return item
  })
}
const dataCallback = (data: any) => {
  if (data) {
    data = renameChildToChildren(data)
    console.log('data', data)
    return data
  }
}

function handleChangeEnable(row: any) {
  console.log('handleChangeEnable', row)
  if (!row) return
  nextTick(() => {
    postBookkeepingSubjectEnable(row).then(() => {
      proxy.$message.success('操作成功')
      proTable.value?.search()
    })
  })
}

// 新增行政区划数据
const handleAdd = (row: any) => {
  showDialog({
    title: '新增',
    customClass: 'medium-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: Form, // 表单组件
    rowFormData: { parentId: row.id },
    submitApi: postAddBookkeepingSubject, // 提交api
    submitCallback: submitCallback // 提交成功之后的回调函数
  })
}

const { showDialog } = useDialog()

const proTable = ref()
const submitCallback = () => {
  proTable.value?.getTableList()
}

// 编辑行政区划数据
const handleEdit = (row: any) => {
  showDialog({
    title: '编辑',
    customClass: 'medium-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: Form, // 表单组件
    requestParams: row.id,
    getApi: getBookkeepingSubjectDetail,
    submitApi: postAddBookkeepingSubject, // 提交api
    submitCallback: submitCallback // 提交成功之后的回调函数
  })
}

// 删除行政区划
const handleDelete = async (row: any) => {
  await useHandleData(deleteBookkeepingSubject, row.id, `删除所选行 ${row.name} 的信息`)
  proTable.value?.getTableList()
}

const dialogRef = ref<InstanceType<typeof ImportExcel> | null>(null)
const handleImport = () => {
  const params = {
    title: '日记账科目',
    tempApi: postBookkeepingSubjectImportTempUrl, // 下载模板接口
    importApi: postBookkeepingSubjectImport,
    getTableList: proTable.value?.getTableList
  }
  dialogRef.value?.acceptParams(params)
}
</script>
<style lang="scss" scoped></style>
