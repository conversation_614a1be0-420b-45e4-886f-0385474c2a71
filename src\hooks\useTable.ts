import { Table } from './interface'
import { reactive, computed, toRefs } from 'vue'

/**
 * @description table 页面操作方法封装
 * @param {Function} api 获取表格数据 api 方法 (必传)
 * @param {Object} initParam 获取数据初始化参数 (非必传，默认为{})
 * @param {Boolean} isPageable 是否有分页 (非必传，默认为true)
 * @param {Function} dataCallBack 对后台返回的数据进行处理的方法 (非必传)
 * */
export const useTable = (
  api?: (params: any) => Promise<any>,
  initParam: object = {},
  isPageable = true,
  dataCallBack?: (data: any) => any,
  requestError?: (error: any) => void,
  transformRequestParams?: (data: any) => void
) => {
  const state = reactive<Table.TableStateProps>({
    // 表格数据
    tableData: [],
    loading: false, // 表格加载数据loading
    // 分页数据
    pageable: {
      // 当前页数
      pageNum: 1,
      // 每页显示条数
      pageSize: 10,
      // 总条数
      total: 0
    },
    // 查询参数(只包括查询)
    searchParam: {},
    // 初始化默认的查询参数
    searchInitParam: {},
    // 总参数(包含分页和查询参数)
    totalParam: {}
  })

  /**
   * @description 分页查询参数(只包括分页和表格字段排序,其他排序方式可自行配置)
   * */
  const pageParam = computed({
    get: () => {
      return {
        pageNum: state.pageable.pageNum,
        pageSize: state.pageable.pageSize
      }
    },
    set: (newVal: any) => {
      console.log('我是分页更新之后的值', newVal)
    }
  })

  /**
   * @description 获取表格数据
   * @return void
   * */
  const getTableList = async () => {
    if (!api) return
    return new Promise(async (resolve, reject) => {
      try {
        // 先把初始化参数和分页参数放到总参数里面
        Object.assign(state.totalParam, initParam, isPageable ? pageParam.value : {})
        transformRequestParams && transformRequestParams(state.totalParam)
        // 手动自定义传参参数
        state.loading = true
        let { data } = await api({
          ...state.searchInitParam,
          ...state.totalParam
        })
        state.loading = false
        dataCallBack && (data = dataCallBack(data))
        state.tableData = isPageable ? data.records || data.rows : data
        // 解构后台返回的分页数据 (如果有分页更新分页信息)
        // const { pageNum, pageSize, total } = data
        const { current, pageNum, pageSize, size, total } = data
        isPageable &&
          updatePageable({
            pageNum: current ? Number(current) : pageNum,
            pageSize: size ? Number(size) : pageSize,
            total: Number(total)
          })
        resolve(data)
      } catch (error) {
        console.log('error', error)

        state.loading = false
        requestError && requestError(error)
        reject(error)
      }
    })
  }

  /**
   * @description 更新查询参数
   * @return void
   * */
  const updatedTotalParam = () => {
    state.totalParam = {}
    // 处理查询参数，可以给查询参数加自定义前缀操作
    const nowSearchParam: { [key: string]: any } = {}
    // 防止手动清空输入框携带参数（这里可以自定义查询参数前缀）
    for (const key in state.searchParam) {
      // * 某些情况下参数为 false/0 也应该携带参数
      if (state.searchParam[key] || state.searchParam[key] === false || state.searchParam[key] === 0) {
        nowSearchParam[key] = state.searchParam[key]
      }
    }
    console.log('nowSearchParam', nowSearchParam)
    Object.assign(state.totalParam, nowSearchParam, isPageable ? pageParam.value : {})
  }

  /**
   * @description 更新分页信息
   * @param {Object} resPageable 后台返回的分页数据
   * @return void
   * */
  const updatePageable = (resPageable: Table.Pageable) => {
    Object.assign(state.pageable, resPageable)
  }

  /**
   * @description 表格数据查询
   * @return void
   * */

  const search = () => {
    state.pageable.pageNum = 1
    updatedTotalParam()
    getTableList()
  }

  /**
   * @description 表格数据重置
   * @return void
   * */
  const keysSpecial = ['incomeAmount', 'paymentAmount', 'monthTurnover', 'priceFluctuationAmount', 'totalCost']
  const reset = () => {
    state.pageable.pageNum = 1
    console.log('===incomeAmount-useTable===', state.searchParam.incomeAmount, Array.isArray(state.searchParam.incomeAmount))

    const boolVo = {}

    keysSpecial.forEach(key => {
      if (Array.isArray(state.searchParam[key])) {
        boolVo[key] = true
      }
    })

    state.searchParam = {}

    keysSpecial.forEach(key => {
      if (boolVo[key]) {
        state.searchParam[key] = []
      }
    })

    // console.log('===state.searchParam===', state.searchParam.incomeAmount);
    // 重置搜索表单的时，如果有默认搜索参数，则重置默认的搜索参数
    // 2023-08-01 新建工单跳转到我发起的列表页面 重置出现冲突，
    // state.searchInitParam = {}

    Object.keys(state.searchInitParam).forEach(key => {
      state.searchParam[key] = state.searchInitParam[key]
    })

    updatedTotalParam()
    getTableList()
  }

  /**
   * @description 每页条数改变
   * @param {Number} val 当前条数
   * @return void
   * */
  const handleSizeChange = (val: number) => {
    state.pageable.pageNum = 1
    state.pageable.pageSize = val
    getTableList()
  }

  /**
   * @description 当前页改变
   * @param {Number} val 当前页
   * @return void
   * */
  // 防抖
  let timer: any = null
  const handleCurrentChange = (val: number) => {
    state.pageable.pageNum = val
    if (timer) {
      clearInterval(timer)
    }
    timer = setTimeout(() => {
      getTableList()
    }, 500)
  }

  return {
    ...toRefs(state),
    getTableList,
    search,
    reset,
    handleSizeChange,
    handleCurrentChange,
    updatedTotalParam
  }
}
