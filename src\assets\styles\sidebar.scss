#app {
  .main-container {
    position: relative;
    height: 100%;
    margin-left: $base-sidebar-width;
    transition: margin-left 0.28s;
  }
  .sidebarHide {
    margin-left: 0 !important;
  }
  .sidebar-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    width: $base-sidebar-width !important;
    height: 100%;
    overflow: hidden;
    font-size: 0;
    background: $base-menu-background;
    box-shadow: 2px 0 6px rgb(0 21 41 / 35%);
    transition: width 0.28s;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }
    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }
    .el-scrollbar__bar.is-vertical {
      right: 0;
    }
    .el-scrollbar {
      height: 100%;
    }
    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px - 56px);
        overflow-y:auto;
      }
    }
    .is-horizontal {
      display: none;
    }
    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }
    .svg-icon {
      // margin-right: 16px;
    }
    .el-menu {
      width: 100% !important;
      height: 100%;
      border: none;
    }
    .el-menu-item,
    .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      font-size: 14px;
      font-family: SourceHanSansCN-Medium, SourceHanSansCN;
      font-weight: 500;
      line-height: 21px;
    }
    .menu-title{
      margin-left: 9px;
    }
    .el-menu-item .el-menu-tooltip__trigger {
      // display: inline-block !important;
      justify-content: center;
      display: flex;
      padding: 16px;
    }

    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      height: 48px;
      &:hover {
        background-color: rgb(0 0 0 / 6%) !important;
      }
    }
    & .theme-dark .is-active > .el-sub-menu__title {
      color: $base-menu-color-active !important;
    }
    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $base-sidebar-width !important;
      &:hover {
        background-color: rgb(0 0 0 / 6%) !important;
      }
    }
    & .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: $base-sub-menu-background !important;
      &:hover {
        background-color: $base-sub-menu-hover !important;
      }
    }
  }
  .hideSidebar {
    .sidebar-container {
      width: 48px !important;
    }
    .main-container {
      margin-left: 48px;
    }
    .submenu-title-noDropdown{
      height: 48px;
       .svg-icon {
        // margin-top: 16px;
      }
    }
    .sub-menu-title-noDropdown {
      position: relative;
      padding: 0 !important;
      .el-tooltip {
        padding: 0 !important;
        .svg-icon {
          // margin-left: 16px;
        }
      }
    }
    .el-sub-menu {
      overflow: hidden;
      & > .el-sub-menu__title {
        padding: 0 !important;
        .svg-icon {
          // margin-left: 16px;
        }
      }
    }
    .el-menu--collapse {
      .el-sub-menu {
        & > .el-sub-menu__title {
          & > span {
            display: inline-block;
            width: 0;
            height: 0;
            overflow: hidden;
            visibility: hidden;
          }
          & > i {
            display: inline-block;
            width: 0;
            height: 0;
            overflow: hidden;
            visibility: hidden;
          }
        }
      }
    }
  }
  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $base-sidebar-width !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0;
    }
    .sidebar-container {
      width: $base-sidebar-width !important;
      transition: transform 0.28s;
    }
    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$base-sidebar-width, 0, 0);
      }
    }
  }
  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      // margin-right: 16px;
    }
  }
  .nest-menu .el-sub-menu > .el-sub-menu__title,
  .el-menu-item {
    height: 48px;
    &:hover {
      // you can use $sub-menuHover
      background-color: rgb(0 0 0 / 6%) !important;
    }
  }

  // the scroll bar appears when the sub-menu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;
    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
