/*
 * @Description: 通用存储
 * @Author: thb
 * @Date: 2023-08-25 13:23:26
 * @LastEditTime: 2024-03-13 10:38:50
 * @LastEditors: thb
 */
const useCommonStore = defineStore('common', {
  state: () => ({
    id: '',
    notObj: {
      id: '',
      notificationId: undefined
    },
    bizType: '',
    orderNo: '', //工单编号
    todoTaskFlag: false // 工作台待办任务
  }),
  actions: {
    setTodoTaskFlag(flag) {
      this.todoTaskFlag = flag
    },
    clearTodoTaskFlag() {
      this.todoTaskFlag = false
    },
    setId(id) {
      console.log('setId', id)
      this.id = id
    },
    setBizType(bizType) {
      this.bizType = bizType
    },
    clearId() {
      this.id = ''
    },
    clearBizType() {
      this.bizType = ''
    },
    setNotice({ id, notificationId }) {
      this.notObj = {
        id,
        notificationId
      }
    },
    setOrderNo(orderNo) {
      this.orderNo = orderNo
    },
    clearOrderNo() {
      this.orderNo = ''
    }
  }
})
export default useCommonStore
