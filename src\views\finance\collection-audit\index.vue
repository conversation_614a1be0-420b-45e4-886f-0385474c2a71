<template>
  <div class="main-wrap">
    <ProTable
      v-if="initParam.type === 1"
      ref="proTable"
      title="收款审核"
      :init-param="initParam"
      :columns="columns"
      :toolButton="false"
      rowKey="id"
      :request-api="getFinanceReceiptGetSubmitListPage"
    >
      <template #tabs>
        <el-radio-group :model-value="initParam.type" @change="handleRadioChange">
          <el-radio-button :label="1">提交记录</el-radio-button>
          <el-radio-button v-hasPermi="['finanace:receipt:check']" :label="2">由我审批</el-radio-button>
        </el-radio-group></template
      >
      <!-- 客户信息详情 -->
      <template #customerName="{ row }">
        <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
      </template>
      <template #isChecked="{ row }">
        <span>{{ row.isChecked !== undefined && collectionIsCheckedArr.find(item => item.value === row.isChecked).label }}</span>
      </template>
      <template #action="{ row }">
        <el-button v-if="initParam.type === 1" type="primary" text @click="handleShowCollectionDetail(row)">详情</el-button>
      </template>
    </ProTable>
    <ProTable
      v-if="initParam.type === 2"
      ref="proTable"
      title="收款审核"
      :init-param="initParam"
      :columns="columnsWithPaymentNo"
      :toolButton="false"
      rowKey="id"
      :request-api="getFinanceReceiptGetCheckListPage"
    >
      <template #tabs>
        <el-radio-group :model-value="initParam.type" @change="handleRadioChange">
          <el-radio-button :label="1">提交记录</el-radio-button>
          <el-radio-button v-hasPermi="['finanace:receipt:check']" :label="2">由我审批</el-radio-button>
        </el-radio-group></template
      >
      <!-- 客户信息详情 -->
      <template #customerName="{ row }">
        <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
      </template>
      <!-- 账单 -->
      <template #paymentNo="{ row }">
        <span class="blue-text" @click="handleShowAccountsDetail(row, row.paymentId)">{{ row.paymentNo }}</span>
      </template>
      <template #isChecked="{ row }">
        <span>{{ row.isChecked !== undefined && collectionIsCheckedArr.find(item => item.value === row.isChecked).label }}</span>
      </template>
      <template #action="{ row }">
        <el-button v-if="row.isChecked === 0" type="primary" text @click="handleShowCollectionAudit(row.id, 1)">通过</el-button>
        <el-button v-if="row.isChecked === 0" type="primary" text @click="handleShowCollectionAudit(row.id, 2)">驳回</el-button>
      </template>
    </ProTable>
  </div>
  <!-- 此处的客户详情，不要有编辑按钮，只是查看的详情 -->
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <div v-show="false"><feeTypeTree></feeTypeTree></div>
</template>

<script setup lang="tsx">
import {
  postFinanceReceiptUpdateCheckStatus,
  getFinanceReceiptGetSubmitListPage,
  getFinanceReceiptGetCheckListPage,
  getFinanceReceiptGetById
} from '@/api/finance/collection-ledger'
import { ref, reactive, nextTick } from 'vue'
import collectionForm from '@/views/finance/collection-ledger/components/form.vue'
import { useDialog } from '@/hooks/useDialogFinance'
import { ColumnProps } from '@/components/ProTable/interface'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import feeTypeTree from '@/views/finance/accounts-receivable/components/fee-type-tree'
import { collectionIsCheckedArr } from '@/utils/constants'
import accountsForm from '@/views/finance/accounts-receivable/components/form.vue'
import { financePaymentSaveOrUpdate, financePaymentGetById } from '@/api/finance/accounts-receivable'
import bus from 'vue3-eventbus'

const { proxy } = getCurrentInstance()

const proTable = ref()

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 })
const tabs = ref([
  {
    dictLabel: '提交记录',
    dicValue: 1
  },
  {
    dictLabel: '由我审批',
    dicValue: 2
  }
])

const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'customerName',
    label: '客户名称',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'feeType',
    label: '费用类别',
    isColShow: false,
    search: {
      render: ({ searchParam }) => {
        return <feeTypeTree vModel={searchParam.feeType} />
      }
    },
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    }
  },
  {
    prop: 'payee',
    width: '100',
    label: '收款人',
    search: { el: 'input' }
  },
  {
    prop: 'receiptAmount',
    width: '100',
    label: '收款金额',
    render: scope => {
      return <span>{scope.row.receiptAmount || 0}元</span>
    }
  },
  {
    prop: 'receiptMethod',
    width: '100',
    label: '收款渠道'
  },
  {
    prop: 'receiptDate',
    width: '180',
    label: '收款时间'
  },
  {
    prop: 'isChecked',
    width: '100',
    label: '审批状态',
    enum: collectionIsCheckedArr,
    search: { el: 'select' }
  },
  {
    prop: 'action',
    label: '操作',
    width: 220,
    isColShow: false,
    fixed: 'right'
  }
]

const columnsWithPaymentNo: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'customerName',
    label: '客户名称',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'feeType',
    label: '费用类别',
    isColShow: false,
    search: {
      render: ({ searchParam }) => {
        return <feeTypeTree vModel={searchParam.feeType} />
      }
    },
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    }
  },
  {
    prop: 'payee',
    width: '100',
    label: '收款人',
    search: { el: 'input' }
  },
  {
    prop: 'receiptAmount',
    width: '100',
    label: '收款金额',
    render: scope => {
      return <span>{scope.row.receiptAmount || 0}元</span>
    }
  },
  {
    prop: 'receiptMethod',
    width: '100',
    label: '收款渠道'
  },
  {
    prop: 'receiptDate',
    width: '180',
    label: '收款时间'
  },
  {
    prop: 'paymentNo',
    width: '150',
    label: '关联账单',
    search: { el: 'input' }
  },
  {
    prop: 'isChecked',
    width: '100',
    label: '审批状态',
    enum: collectionIsCheckedArr,
    search: { el: 'select' }
  },
  {
    prop: 'action',
    label: '操作',
    width: 220,
    isColShow: false,
    fixed: 'right'
  }
]

const { showDialog } = useDialog()
// 处理表单提交参数
// const handleRevertParams = (data: any) => {
//   if (Array.isArray(data.receiptVoucherFile) && data.receiptVoucherFile[0]) {
//     const file = data.receiptVoucherFile[0]
//     data.receiptVoucherFile = {
//       fileSize: file.uploadSize,
//       fileNames: file.newFileName,
//       // bizType: 'dddd', // 假数据
//       uploadBy: file.uploadBy,
//       uploadTime: file.uploadTime,
//       urls: file.url
//     }
//   } else {
//     delete data.receiptVoucherFile
//   }
// }

let isCheckedTemp = 0
const handleShowCollectionAudit = (id: any, status: any) => {
  console.log('id', id)
  isCheckedTemp = status
  showDialog({
    title: status === 1 ? '通过收款' : '驳回收款',
    customClass: 'mini-dialog',
    // cancelButtonText: '关闭',
    // confirmButtonText: '保存',
    rowFormData: { isChecked: status },
    component: collectionForm,
    submitApi: postFinanceReceiptUpdateCheckStatus,
    getApi: getFinanceReceiptGetById,
    requestParams: { id },
    handleRevertParams,
    submitCallback
  })
}

const handleRevertParams = (data: any) => {
  data.isChecked = isCheckedTemp
}

const handleShowCollectionDetail = (row: any) => {
  console.log('row', row)
  showDialog({
    title: `审批详情（${collectionIsCheckedArr.find(item => item.value === row.isChecked).label}）`,
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: collectionForm,
    submitApi: postFinanceReceiptUpdateCheckStatus,
    getApi: getFinanceReceiptGetById,
    requestParams: { id: row.id },
    submitCallback
  })
}

const submitCallback = () => {
  proTable.value?.getTableList()
}
const cancelCallback = () => {
  proTable.value?.getTableList()
}

const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.type = e
}

const getList = () => {
  proTable.value?.getTableList()
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  console.log('handlShowCustomerDetail', id)
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
}
/* 客户详情弹窗 ---end--- */

/* 账单详情弹窗 ---start--- */
const handleShowAccountsDetail = (row: any, id: any) => {
  console.log('id', id)
  showDialog({
    title: '账单详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    confirmButtonText: '编辑',
    // showConfirmButton: 'hide',
    component: accountsForm,
    submitApi: financePaymentSaveOrUpdate,
    getApi: financePaymentGetById,
    requestParams: { id },
    submitCallback,
    cancelCallback
  })
  nextTick(() => {
    bus.emit('paymentNo', row.paymentNo)
    bus.emit('paymentId', row.paymentId || row.id)
  })
}
/* 账单弹窗 ---end--- */
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
