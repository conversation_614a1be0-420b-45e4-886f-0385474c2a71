@import "./variables.module.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./btn.scss";
@import "./ruoyi.scss";
@import "./dialog.scss";
@import "./font.scss";
@import "./icon.scss";
body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
}
label {
  font-weight: 700;
}
html {
  box-sizing: border-box;
  height: 100%;
}
#app {
  height: 100%;
}
*,
*::before,
*::after {
  box-sizing: inherit;
}
.no-padding {
  padding: 0 !important;
}
.padding-content {
  padding: 4px 0;
}
a:focus,
a:active {
  outline: none;
}
a,
a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}
div:focus {
  outline: none;
}
.fr {
  float: right;
}
.fl {
  float: left;
}
.pr-5 {
  padding-right: 5px;
}
.pl-5 {
  padding-left: 5px;
}
.block {
  display: block;
}
.pointer {
  cursor: pointer;
}
.inlineBlock {
  display: block;
}
.clearfix {
  &::after {
    display: block;
    height: 0;
    clear: both;
    font-size: 0;
    visibility: hidden;
    content: " ";
  }
}
aside {
  display: block;
  padding: 8px 24px;
  margin-bottom: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans",
    "Helvetica Neue", sans-serif;
  font-size: 16px;
  line-height: 32px;
  color: #2c3e50;
  background: #eef1f6;
  border-radius: 2px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  a {
    color: #337ab7;
    cursor: pointer;
    &:hover {
      color: rgb(32 160 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 16px;
  width: 100%;
}
.components-container {
  position: relative;
  margin: 30px 50px;
}
.pagination-container {
  margin-top: 30px;
}
.text-center {
  text-align: center;
}
.sub-navbar {
  position: relative;
  width: 100%;
  height: 50px;
  padding-right: 20px;
  line-height: 50px;
  text-align: right;
  background: linear-gradient(
    90deg,
    rgb(32 182 249 / 100%) 0%,
    rgb(32 182 249 / 100%) 0%,
    rgb(33 120 241 / 100%) 100%,
    rgb(33 120 241 / 100%) 100%
  );
  transition: 600ms ease position;
  .subtitle {
    font-size: 20px;
    color: #ffffff;
  }
  &.draft {
    background: #d0d0d0;
  }
  &.deleted {
    background: #d0d0d0;
  }
}
.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
  &:hover {
    color: rgb(32 160 255);
  }
}
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    display: inline-block;
    margin-bottom: 10px;
    vertical-align: middle;
  }
}


.download-text{
  cursor: pointer;
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #2383E7;
}

// disabled下将select框的图标隐藏
.el-select.el-select--default.el-select--disabled{
  .el-input__suffix{
    display: none;
  }
}

// 列表文字样式
.blue-text {
  color: #409eff;
  cursor: pointer;
}


// 隐藏kkfile预览文件下的logo
// iframe{
//   .container+img{
//     display: none;
//   }
// }