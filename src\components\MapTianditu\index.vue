<template>
  <div class="map-container" ref="mapContainer"></div>
</template>

<script setup>
import { onMounted } from 'vue'

const map = shallowRef(null)
const mapContainer = ref()

const props = defineProps({
  zoom: {
    type: Number,
    default: 14
  },
  center: {
    type: Array,
    default: () => [120.750865, 30.762653]
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  // 控件位置
  controlZoomPosition: {
    type: String,
    default: 'bottom-right',
    validator: value => ['top-left', 'top-right', 'bottom-left', 'bottom-right'].includes(value) || ''
  }
})

const emits = defineEmits(['onInit'])

const positionMap = {
  'bottom-left': T_ANCHOR_BOTTOM_LEFT,
  'bottom-right': T_ANCHOR_BOTTOM_RIGHT,
  'top-left': T_ANCHOR_TOP_LEFT,
  'top-right': T_ANCHOR_TOP_RIGHT
}

onMounted(async () => {
  await nextTick()
  map.value = new T.Map(mapContainer.value)
  // 创建缩放平移控件对象
  if (props.controlZoomPosition) {
    const control = new T.Control.Zoom()
    map.value.addControl(control)
    control.setPosition(positionMap[props.controlZoomPosition])
  }
  map.value.centerAndZoom(new T.LngLat(...props.center), props.zoom)
  emits('onInit', map.value, mapContainer)
})
</script>

<style lang="scss" scoped>
.map-container {
  width: v-bind(width);
  height: v-bind(height);
}
// 重置infowindow
:deep(.tdt-infowindow) {
  .tdt-infowindow-tip-container {
    display: none;
  }
  .tdt-infowindow-content-wrapper {
    padding: 0;
  }
  .tdt-infowindow-content {
    box-shadow: none;
    width: auto !important;
    margin: 0;
    line-height: 1;
  }
  .tdt-infowindow-close-button {
    width: 24px;
    height: 24px;
    background-image: url('@/assets/icons/close-icon.png');
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: center;
    position: absolute;
    right: 16px;
    top: 16px;
    color: rgba(255, 255, 255, 0);
    z-index: 100;
    &:hover {
      color: rgba(255, 255, 255, 0) !important;
    }
  }
}
</style>
