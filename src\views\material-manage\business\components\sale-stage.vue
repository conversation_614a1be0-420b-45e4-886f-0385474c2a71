<!--
 * @Description: 销售阶段
 * @Author: thb
 * @Date: 2023-08-22 15:39:00
 * @LastEditTime: 2023-08-28 14:59:42
 * @LastEditors: thb
-->
<template>
  <div class="wrap-container">
    <div class="left">销售阶段</div>
    <div class="right">
      <saleChart class="m-r chart-item" v-for="item in list" :key="item" :text="item" :percentage="stagePercentageMap[item]" />
    </div>
  </div>
</template>
<script setup>
import saleChart from './stage-chart'
const props = defineProps({
  stage: String
})

const { proxy } = getCurrentInstance()

const stagePercentageMap = {
  '需求确认,有明确意向': '20',
  '已报价,待确认': '40',
  异议处理: '70',
  待打款: '90',
  '已收定金,待打尾款': '95',
  赢单: '100',
  输单: '0'
}
const list = ref([])
// 获取销售阶段
watch(
  () => props.stage,
  () => {
    if (props.stage) {
      getStageList(props.stage)
    }
  }
)
const getStageList = stage => {
  if (stage === '输单') {
    list.value = ['输单']
  } else {
    const list1 = Object.keys(stagePercentageMap)
    const index = list1.findIndex(item => item === stage)
    list.value = list1.slice(0, index + 1)
  }
}
</script>
<style lang="scss" scoped>
.wrap-container {
  display: flex;
  height: 120px;
  margin-bottom: 8px;
  .left {
    flex: 0 0 32px;
    background: rgba(35, 131, 231, 0.1);
    border-radius: 4px;
    font-size: 15px;
    padding: 18px 9px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    color: #2383e7;
    margin-right: 8px;
  }
  .right {
    flex: 1;
    display: flex;
  }
}

.chart-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 178px;
  position: relative;
  &::after {
    content: '';
    position: absolute;
    height: 1px;
    width: 134px;
    top: 50px;
    left: 117px;
    border-bottom: 1px dashed #b2b5b9ff;
  }
  &:last-child::after {
    content: '';
    display: none;
  }
}
.m-r {
  margin-right: 12px;

  &:last-child {
    margin-right: 0;
  }
}
</style>
