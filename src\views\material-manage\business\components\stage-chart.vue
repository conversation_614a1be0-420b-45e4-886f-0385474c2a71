<!--
 * @Description: 销售阶段echarts
 * @Author: thb
 * @Date: 2023-08-22 15:44:33
 * @LastEditTime: 2023-08-23 10:20:13
 * @LastEditors: thb
-->
<template>
  <div>
    <div ref="pieRef" class="pie-chart"></div>
    <div class="text">{{ text }}</div>
  </div>
</template>
<script setup>
import * as echarts from 'echarts'

const props = defineProps({
  percentage: {
    type: String,
    default: '20'
  },
  text: {
    type: String,
    default: ''
  }
})
// drawChart
const pieRef = ref()
const drawChart = () => {
  console.log('Number(props.percentage)', Number(props.percentage), 100 - Number(props.percentage))
  let myChart = echarts.init(pieRef.value)
  const option = {
    graphic: {
      elements: [
        {
          type: 'text',
          left: Number(props.percentage) === 100 ? '25%' : '34%',
          top: 'center',
          style: {
            text: props.percentage + '%',
            textAlign: 'center',
            fill: '#2383E7', //文字的颜色
            fontSize: 15,
            fontFamily: 'AlibabaPuHuiTi_2_65_Medium',
            lineHeight: 21
          }
        }
      ]
    },
    series: [
      {
        type: 'pie',
        radius: ['70%', '80%'],

        label: {
          show: false,
          position: 'center'
        },

        data: [
          {
            value: Number(props.percentage),
            itemStyle: {
              color: '#3E9CFFFF'
            }
          },
          {
            value: 100 - Number(props.percentage),
            itemStyle: {
              color: '#E8E8E8FF'
            }
          }
        ]
      }
    ]
  }
  myChart.setOption(option)
}
onMounted(() => {
  drawChart()
})
</script>
<style lang="scss" scoped>
.pie-chart {
  width: 70px;
  height: 70px;
}
.text {
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #6a7697;
}
</style>
