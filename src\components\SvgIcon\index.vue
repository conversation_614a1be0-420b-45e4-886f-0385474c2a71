<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-05-25 22:51:25
 * @LastEditTime: 2023-08-04 11:11:17
 * @LastEditors: thb
-->
<template>
  <svg :class="svgClass" aria-hidden="true">
    <use :xlink:href="iconName" :fill="color" />
  </svg>
</template>

<script>
export default defineComponent({
  props: {
    iconClass: {
      type: String,
      required: true
    },
    className: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    return {
      iconName: computed(() => `#icon-${props.iconClass}`),
      svgClass: computed(() => {
        if (props.className) {
          return `svg-icon ${props.className}`
        }
        return 'svg-icon'
      })
    }
  }
})
</script>

<style scope lang="scss">
.sub-el-icon,
.nav-icon {
  position: relative;
  display: inline-block;
  margin-right: 12px;
  font-size: 15px;
}
.svg-icon {
  position: relative;
  // width: 1em;
  // height: 1em;
  width: 16px;
  height: 16px;
  vertical-align: -2px;
  fill: currentColor;
}
</style>
