<!--
 * @Description:  点击查看更多组件
 * @Author: thb
 * @Date: 2023-07-03 17:08:15
 * @LastEditTime: 2023-07-03 17:22:54
 * @LastEditors: thb
-->
<template>
  <div>
    <div class="question-des" ref="questionDes" v-if="!showMore">
      {{ des }}
    </div>
    <div class="switchBtn" v-if="hasMore" @click="switchFn(1)">查看全部</div>
    <div class="question-des_more" v-if="showMore">{{ des }}</div>
    <div class="switchBtn" v-if="!hasMore && showMore" @click="switchFn(0)">收起</div>
  </div>
</template>
<script setup>
const props = defineProps({
  des: {
    type: String,
    default: ''
  }
})

const questionDes = ref('')

const hasMore = ref(false)
const showMore = ref(false)

const switchFn = type => {
  if (type) {
    showMore.value = true
    hasMore.value = false
  } else {
    showMore.value = false
    hasMore.value = true
  }
}
onMounted(() => {
  nextTick(() => {
    hasMore.value = questionDes.value.clientHeight < questionDes.value.scrollHeight
  })
})
</script>
<style lang="scss" scoped>
.question-des {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: wrap;
  color: textLightGrey;
}

.question-des_more {
  color: textLightGrey;
}

.switchBtn {
  font-size: 12px;
  color: #5fbefe;
  text-align: right;
  margin-bottom: 6px;
  cursor: pointer;
}
</style>
