<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    top="5vh"
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :disabled="disabled">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="姓名" prop="userId">
            <el-select
              style="width: 100%"
              v-model="formData.userId"
              filterable
              :disabled="disabled"
              :placeholder="disabled ? ' ' : '请选择'"
              @change="handleChange"
            >
              <el-option :label="item.nickName" :value="item.userId" v-for="(item, index) in userList" :key="index" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="phonenumber">
            <el-input disabled v-model="formData.phonenumber" maxlength="11" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item>
            <template #label>
              <div class="my-form-item-label">
                <div class="label my-required-label">所属企业</div>
                <div class="button">
                  <el-button type="primary" plain @click="handleAdd">新增</el-button>
                </div>
              </div>
            </template>
            <div class="my-table">
              <ProTable ref="proTable" row-key="id" :columns="columnsForm" :data="formData.customerIdList" :pagination="false">
                <template #operation="scope">
                  <el-button type="danger" link @click="handleDelete(scope.row, scope.$index)">删除</el-button>
                </template>
              </ProTable>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <tableModal
      v-if="listSelectShow"
      title="关联企业"
      :columns="columns"
      rowKey="customerId"
      :init-param="{}"
      :request-api="getCustomers"
      @on-close="listSelectShow = false"
      @on-multiple-select="handleSelect"
      multiple
    />
    <template #footer>
      <el-button type="primary" @click="handleSubmit"> 确认 </el-button>
      <el-button @click="handleClose"> 取消 </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="jsx">
import { FormValidators } from '@/utils/validate'
import tableModal from '@/components/tableModal'
import { getCustomers } from '@/api/customer/file'
import { postUserCustomerBindRecordSave } from '@/api/customer-user/customer-list.js'
import { useDic } from '@/hooks/useDic'
import { listUser } from '@/api/system/user'

const { getDic } = useDic()
const { proxy } = getCurrentInstance()
const emit = defineEmits()

const mode = ref('')

/** 表单 */
const formRef = ref()
const disabled = ref(false)
const formData = reactive({
  id: undefined,
  customerIdList: []
})
const rules = {
  userId: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  phonenumber: [
    {
      required: false,
      validator: FormValidators.mobilePhone,
      trigger: ['blur']
    }
  ]
}
const visible = ref(true)
const onAdd = () => {
  mode.value = '新增授权'
}

const handleClose = () => {
  emit('close')
}
const handleSubmit = () => {
  formRef.value.validate(valid => {
    if (valid) {
      if (!formData.customerIdList.length) {
        return proxy.$modal.msgError('请选择所属企业')
      }
      const formDataTemp = {
        customerIdList: formData.customerIdList.map(item => item.customerId),
        userId: formData.userId
      }
      postUserCustomerBindRecordSave(formDataTemp).then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('新增成功')
          emit('ok')
          handleClose()
        }
      })
    } else {
    }
  })
}

/** 所属企业 */
const columnsForm = [
  { type: 'index', label: '序号', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '企业名称'
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 180
  }
]
const handleAdd = row => {
  handleShow()
}
const handleDelete = (row, index) => {
  formData.customerIdList.splice(index, 1)
}

/** 企业用户列表 */
const userList = ref([])
const getUserList = async () => {
  const result = await listUser({ enterpriseFlag: true, pageSize: 9999 })
  userList.value = result.rows || []
}
getUserList()
function handleChange(e1, e2) {
  console.log('e2', e2)
  formData.phonenumber = userList.value.find(item => item.userId === e1)?.phonenumber
}

/** 客户列表 */
const listSelectShow = ref(false)
const handleSelect = data => {
  console.log('data', data)
  formData.customerIdList.push(...data)
}
const handleShow = () => {
  listSelectShow.value = true
}
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

defineExpose({
  onAdd
})
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  padding: 0;
}
.my-form-item-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.my-required-label {
  &:before {
    content: '*';
    color: var(--el-color-danger);
    margin-right: 4px;
  }
}
</style>
<style lang="scss" scoped>
.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
.my-table {
  // min-height: 300px;
  // max-height: 500px;
  // overflow-y: scroll;
  display: flex;
  :deep(.table-box) {
    .card {
      padding: 0;
      border: none;
      box-shadow: none;
    }
    .table-search {
      margin-top: 15px;
      margin-bottom: 0px;
    }
    .table-main {
      min-height: 200px;
      max-height: 400px;
      .table-header {
        margin-bottom: 0px;
      }
    }
  }
}
</style>
