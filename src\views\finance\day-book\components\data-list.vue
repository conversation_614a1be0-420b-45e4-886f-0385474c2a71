<template>
  <el-row :gutter="24" class="data-list">
    <el-col :span="12">
      <el-row :gutter="24" style="margin-bottom: 12px">
        <el-col :span="24">年初金额：{{ getValue(statisticData.balance) }} 元 </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="6">本年合计： </el-col>
        <el-col :span="6">借方：{{ getValue(statisticData.yearStatics?.incomeAmount) }}元 </el-col>
        <el-col :span="6"> 贷方：{{ getValue(statisticData.yearStatics?.paymentAmount) }}元 </el-col>
        <el-col :span="6"> 余额：{{ getValue(statisticData.yearStatics?.balance) }}元 </el-col>
      </el-row>
    </el-col>
    <el-col :span="12">
      <el-row :gutter="24" style="margin-bottom: 12px">
        <el-col :span="6">本月合计： </el-col>
        <el-col :span="6">借方：{{ getValue(statisticData.monthStatics?.incomeAmount) }}元 </el-col>
        <el-col :span="6"> 贷方：{{ getValue(statisticData.monthStatics?.paymentAmount) }}元 </el-col>
        <el-col :span="6"> 余额：{{ getValue(statisticData.monthStatics?.balance) }}元 </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="6">本日合计：</el-col>
        <el-col :span="6">借方：{{ getValue(statisticData.dayStatics?.incomeAmount) }}元 </el-col>
        <el-col :span="6"> 贷方：{{ getValue(statisticData.dayStatics?.paymentAmount) }}元 </el-col>
        <el-col :span="6"> 余额：{{ getValue(statisticData.dayStatics?.balance) }}元 </el-col>
      </el-row>
    </el-col>
  </el-row>
</template>
<script setup>
import { getDayBookStatisticData } from '@/api/finance/day-book'
const statisticData = ref({})
const getStatisticData = async () => {
  const { data } = await getDayBookStatisticData()
  statisticData.value = data || {}
}
getStatisticData()
const getValue = value => {
  return value === 0 ? 0 : value || '--'
}

const props = defineProps({
  timeStamp: {
    type: String,
    default: ''
  }
})

watch(
  () => props.timeStamp,
  () => {
    getStatisticData()
  }
)
</script>
<style lang="scss" scoped>
.data-list {
  background: #fff;
  margin-left: 0 !important;
  margin-right: 0 !important;
  margin-bottom: 24px;
  padding: 20px;
}
</style>
