<template>
  <dataWrap
    title="收单金额平均客单价"
    class="mid-right"
    ref="wrapRef"
    :selectOptions="selectOptions"
    :request-api="getAveragePrice"
    @on-select="drawChart"
    :charts="charts"
  >
    <template #default>
      <div class="line-chart" ref="lineRef"></div>
    </template>
  </dataWrap>
</template>
<script setup>
import dataWrap from '../../collecting-panel/components/data-wrap'
import { getAveragePrice } from '@/api/panel-data/sale'
import * as echarts from 'echarts'

const selectOptions = [
  {
    prop: 'year',
    type: 'date-picker',
    defaultValue: new Date().getFullYear() + '',
    props: { type: 'year', value: 'YYYY', valueFormat: 'YYYY' }
  }
]
const lineRef = ref()
const wrapRef = ref()
const charts = ref([])
const drawChart = () => {
  console.log('wrapRef', wrapRef.value.panelData)
  let myChart = echarts.init(lineRef.value)
  charts.value[0] = myChart
  const panelData = wrapRef.value.panelData
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['上一年度', '本年度']
    },
    grid: {
      left: '2%',
      top: '12%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },

    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '上一年度',
        type: 'line',

        // data: [20, 42, 60, 20, 20, 50, 90]
        data: panelData.map(item => item.value.lastAmount)
      },
      {
        name: '本年度',
        type: 'line',

        // data: [20, 42, 60, 20, 20, 50, 90],
        data: panelData.map(item => item.value.nowAmount)
      }
    ]
  }
  myChart.setOption(option)
}
onMounted(async () => {
  await wrapRef.value.requestResult
  drawChart()
})
</script>
<style lang="scss" scoped>
.mid-right {
  flex: 1.5;
}
.line-chart {
  // flex: 1;
  width: 100%;
  height: 100%;
  min-height: 151px;
}
</style>
