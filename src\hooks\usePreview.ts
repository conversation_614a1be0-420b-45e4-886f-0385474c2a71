/*
 * @Description:
 * @Author: thb
 * @Date: 2023-06-08 10:47:11
 * @LastEditTime: 2023-10-27 09:03:51
 * @LastEditors: zzzlucas <EMAIL>
 */

// import { Base64 } from 'js-base64'
// const previewUrl = import.meta.env.VITE_APP_FILE_API
// export function usePreview(url: string) {
//   console.log('url', url)
//   return previewUrl + encodeURIComponent(Base64.encode(url))
// }
import { Base64 } from 'js-base64'
import { getFileUrlByOss } from '@/api/file/file.js'
const previewUrl = import.meta.env.VITE_APP_FILE_PREVIEW_API
// const fileRemoteUrl = import.meta.env.VITE_APP_FILE_API || window.location.protocol + '//' + window.location.host
export async function usePreview(url: string) {
  // 走oss url就是fileName
  let { data } = await getFileUrlByOss(url)
  console.log('data-usePreview-1', data)
  // 后端表示阿里云oss不支持url包含+的情况，但因为浏览器自动转义（解码），%2B浏览器里丢进去会重新解码成+
  data = data.replaceAll('+', '%2B')
  console.log('data-usePreview-2', data)
  // const encodeUrl =data.split('?Expires')
  const searchIndex = data.indexOf('?Expires')
  let encodeUrl = ''
  const paramsUrl = data.substring(searchIndex)
  if (searchIndex > -1) {
    encodeUrl = decodeURIComponent(data.substring(0, searchIndex))
  }
  console.log('oss返回结果', data, previewUrl + Base64.encode(encodeURIComponent(encodeUrl + paramsUrl)))
  return previewUrl + Base64.encode(encodeURIComponent(encodeUrl + paramsUrl))
}
