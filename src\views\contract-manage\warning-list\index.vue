<!--
 * @Description: 到期预警
 * @Author: thb
 * @Date: 2023-07-25 11:16:37
 * @LastEditTime: 2023-10-26 08:47:22
 * @LastEditors: thb
-->
<template>
  <ProTable ref="proTable" title="到期预警" :columns="columns" :request-api="getExpirationtWarningList">
    <template #totalCost="{ row }">
      <span> {{ row.totalCost }} 元</span>
    </template>
    <template #contractNo="{ row }">
      <span class="blue-text" @click="handleShowContractDetail(row)">{{ row.contractNo }}</span>
    </template>
    <template #customerName="{ row }">
      <span class="blue-text" @click="handleShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
  </ProTable>

  <customerDetail v-if="customerDetailShow" :id="rowId" :hideActionBtn="true" @on-close="customerDetailShow = false" />
  <contractDetail
    v-if="detailShow"
    :isChange="isChange"
    :id="contractId"
    @on-close="detailShow = false"
    @on-change="handleChange"
  />

  <changeContract v-if="changeShow" :id="contractId" @on-close="changeShow = false" />

  <templateCreate v-if="templateShow" :id="contractId" @on-close="templateShow = false" />
</template>
<script setup lang="tsx">
import { getExpirationtWarningList } from '@/api/basicData/basicData'
import { ColumnProps } from '@/components/ProTable/interface'

import { useCustomer } from '@/hooks/useCustomer'
import { useContract } from '@/hooks/useContract'
const { customerDetailShow, rowId, handleShowCustomerDetail, customerDetail } = useCustomer()
const {
  handleShowContractDetail,
  detailShow,
  contractId,
  isChange,
  changeShow,
  contractDetail,
  changeContract,
  templateShow,
  templateCreate,
  handleChange
} = useContract()
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'contractNo',
    label: '合同编号',
    width: 200,
    fixed: 'left',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'productName',
    width: 300,
    label: '服务产品'
  },
  {
    prop: 'customerName',
    label: '客户名称',
    width: 300,
    search: {
      el: 'input'
    }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: 150,
    search: {
      el: 'input'
    }
  },

  {
    prop: 'contractType',
    width: 200,
    enum: [
      {
        label: '记账合同',
        value: '0'
      },
      {
        label: '一次性合同',
        value: '1'
      },
      {
        label: '地址服务协议合同',
        value: '2'
      }
    ],
    label: '合同类型'
  },
  {
    prop: 'totalCost',
    width: 100,
    label: '合同金额'
  },
  {
    prop: 'startTime',
    width: 150,
    label: '起始时间'
  },
  {
    prop: 'endTime',
    width: 150,
    label: '结束时间'
  },
  {
    prop: 'alertTime',
    width: 150,
    label: '提醒时间'
  },
  {
    prop: 'manger',
    width: 150,
    // fixed: 'right',
    label: '财税顾问'
  }
]
</script>
<style lang="scss" scoped></style>
