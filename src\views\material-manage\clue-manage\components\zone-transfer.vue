<!--
 * @Description: 线索转移至公海
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-08-22 14:10:56
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="选择公海" prop="seaId">
          <el-select v-model="formData.seaId" placeholder="请选择" clearable>
            <el-option
              v-for="item in seaOptions"
              :key="item.id"
              :label="item.name"
              :disabled="item.id === formData.currentSeaId"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import { getZoneSettingList } from '@/api/material-manage/zone-setting'
const formData = reactive({
  seaId: '',
  type: '0', // 线索回收
  id: undefined
})
// 获取公海列表
const seaOptions = ref([])
const getSeaOptions = async () => {
  console.log('getSeaOptions', formData.type)
  const { data } = await getZoneSettingList({
    pageSize: 1000,
    pageNum: 1,
    status: '1',
    type: formData.type // 客户公海 和线索公海要区分的
  })
  seaOptions.value = data.records || []
}
const selectRef = ref()
const rules = {
  seaId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})

onMounted(() => {
  nextTick(() => {
    console.log('formData', formData.type)
    getSeaOptions()
  })
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
