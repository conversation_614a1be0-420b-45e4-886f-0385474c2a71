<!--
 * @Description: 附加材料的form表单
 * @Author: thb
 * @Date: 2023-08-18 13:07:44
 * @LastEditTime: 2023-11-13 09:22:49
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="法人身份证" prop="legalIdentityFileList">
          <FileUpload v-model="formData.legalIdentityFileList" :limit="10" :isShowTip="false" />
        </el-form-item>
      </el-col>
      <!-- 
      <el-col :span="12">
        <el-form-item label="法人身份证反面" prop="legalIdCardBack">
          <ImageUpload v-model="formData.legalIdCardBack" :limit="1" :isShowTip="false" />
        </el-form-item>
      </el-col> -->
      <el-col :span="12">
        <el-form-item label="法人联系方式" prop="legalPhone">
          <el-input v-model="formData.legalPhone" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="监事身份证" prop="supervisorIdentityFileList">
          <FileUpload v-model="formData.supervisorIdentityFileList" :limit="10" :isShowTip="false" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="监事联系方式" prop="supervisorPhone">
          <el-input v-model="formData.supervisorPhone" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="12">
        <el-form-item label="监事身份证反面" prop="supervisorIdCardBack">
          <ImageUpload v-model="formData.supervisorIdCardBack" :limit="1" :isShowTip="false" />
        </el-form-item>
      </el-col> -->
    </el-row>

    <!-- <el-row :gutter="24"> </el-row> -->
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="股份比例" prop="share">
          <el-input v-model="formData.share" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="其他附件" prop="otherDocumentFileList"
          ><FileUpload v-model="formData.otherDocumentFileList" :limit="100" :isShowTip="false" />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 新增股东信息表格 -->
    <ShareholderList
      ref="shareholderListRef"
      :formData="{
        tableData: formData.shareholderInfoList,
        rules: shareRules
      }"
    />
  </el-form>
</template>
<script setup>
import ImageUpload from '@/components/ImageUpload'
import FileUpload from '@/components/FileUpload'
import { FormValidators } from '@/utils/validate'
import ShareholderList from './shareholder-list'
const formData = ref({
  id: undefined,
  legalIdentityFileList: [],
  supervisorIdentityFileList: [],

  legalPhone: '',

  supervisorPhone: '',
  share: '',
  otherDocumentFileList: [],
  shareholderInfoList: []
})

const shareRules = {
  shareholderName: [
    {
      required: false,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  shareholderPhone: [
    {
      required: false,
      validator: FormValidators.mobilePhone,
      trigger: ['blur']
    }
  ],
  shareholderFileList: [
    {
      required: false,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
const rules = {
  legalPhone: [
    {
      required: false,
      validator: FormValidators.allPhone
    }
  ],
  supervisorPhone: [
    {
      required: false,
      validator: FormValidators.allPhone
    }
  ]
}

// 表单检验方法
const formRef = ref()
const shareholderListRef = ref()
// 校验不满足原有表单的rules校验规则
const getFormRef = () => {
  return {
    validate: async callback => {
      // 原有表单的校验以及 formTable表单的校验
      const validBase = await formRef.value.validate()
      const validExtra = await shareholderListRef.value.formTableRef.handleValidate()
      if (!validBase || !validExtra) {
        if (callback) {
          callback(false)
        } else {
          return false
        }
      } else {
        if (callback) {
          callback(true)
        } else {
          return true
        }
      }
    }
  }
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
