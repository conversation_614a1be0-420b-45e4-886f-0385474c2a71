<!--
 * @Description: 公海线索
 * @Author: thb
 * @Date: 2023-08-15 09:43:55
 * @LastEditTime: 2023-11-15 15:47:50
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    :width="active === 0 ? 800 : 1200"
    :title="type === 'add' ? '新增线索' : '编辑线索'"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <!-- <processView /> -->
    <el-steps :active="active" align-center finish-status="success">
      <el-step title="步骤1" />
      <el-step title="步骤2" />
    </el-steps>

    <el-form ref="stepOneRef" v-show="active === 0" :model="formData" :rules="rules" label-position="top">
      <el-row :gutter="24" justify="center">
        <el-col :span="6">
          <el-form-item label="选择目标公海" prop="seaId">
            <el-select v-model="formData.seaId" placeholder="请选择" clearable>
              <el-option
                v-for="item in seaOptions"
                :key="item.id"
                :disabled="includeDept(item.deptIds)"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <clueForm ref="clueFormRef" v-show="active === 1" />

    <template #footer>
      <template v-if="active === 0">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleStepAfter">下一步</el-button>
      </template>
      <template v-if="active === 1">
        <el-button type="primary" @click="handleStepBefore">上一步</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </template>
    </template>
  </el-dialog>
</template>
<script setup>
import { getZoneSettingList } from '@/api/material-manage/zone-setting'
import clueForm from '../clue-manage/components/clue-form'
import { saveClue } from '@/api/material-manage/clue'
import { useDept } from '@/hooks/useDept'
const { includeDept } = useDept()
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success', 'on-edit'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number,
  type: {
    type: String,
    default: 'add' // 'add'表示新增 'update'表示编辑
  }
})
const active = ref(0)
// 获取公海列表
const seaOptions = ref([])
const getSeaOptions = async () => {
  const { data } = await getZoneSettingList({
    pageSize: 1000,
    pageNum: 1,
    status: '1',
    type: '0'
  })
  seaOptions.value = data.records || []
}
getSeaOptions()
const formData = ref({})
const rules = {
  seaId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
// 执行下一步
const stepOneRef = ref()
const handleStepAfter = async () => {
  // 执行下一步之前先校验
  const result = await stepOneRef.value.validate()
  if (result) {
    active.value = 1
  }
}

const handleStepBefore = () => {
  active.value = 0
}

const clueFormRef = ref()
const { proxy } = getCurrentInstance()
const handleSubmit = () => {
  clueFormRef.value.getFormRef().validate(async valid => {
    if (valid) {
      // console.log('clueFormRef.value', clueFormRef.value.formData)

      const result = await saveClue({
        seaId: formData.value.seaId,
        ...clueFormRef.value.formData,
        entryType: '1', // 公海录入
        type: '0', //代表线索
        tags: clueFormRef.value.formData.tags.map(item => {
          return {
            tagId: item
          }
        })
      })
      if (result.code === 200) {
        proxy.$modal.msgSuccess(`保存成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`保存失败!`)
      }
    }
  })
}
</script>
<style lang="scss" scoped></style>
