<!--
 * @Description: 资料上传form 表单
 * @Author: thb
 * @Date: 2023-09-28 15:24:07
 * @LastEditTime: 2023-10-19 15:15:35
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :hide-required-asterisk="disabled" :model="data" :rules="rules" label-width="120px" label-position="top">
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="许可证名称" prop="permitName">
          <el-input v-model="data.permitName" length="20" :disabled="disabled"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="证件到期日" prop="expireDate">
          <el-date-picker
            v-model="data.expireDate"
            :disabled="disabled"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="date"
            :placeholder="disabled ? ' ' : '请选择'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="证件年检日" prop="inspectAnnuallyDate">
          <el-date-picker
            v-model="data.inspectAnnuallyDate"
            :disabled="disabled"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="date"
            :placeholder="disabled ? ' ' : '请选择'"
          /> </el-form-item
      ></el-col>
    </el-row>
  </el-form>
</template>
<script setup>
defineProps({
  data: Object
})

const disabled = inject('disabled')
// 许可证资料上传校验规则
const rules = {
  permitName: [
    {
      required: true,
      message: '请输入',
      trigger: ['change']
    }
  ]
}
const formRef = ref()
const validateForm = async () => {
  const result = await formRef.value.validate(valid => {
    if (valid) {
      console.log('检验成功')
    } else {
      console.log('校验失败')
      return false
    }
  })
  return result
}

const validateCheckedForm = async () => {
  return true
}

defineExpose({
  validateForm,
  validateCheckedForm
})
</script>
<style lang="scss" scoped>
:deep(.el-date-editor) {
  width: 100%;
}
</style>
