<!--
 * @Description: 通知公告
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-08-25 11:58:35
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="isDisabled">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="标题" prop="title">
          <el-input v-model="formData.title" maxlength="20" :disabled="isDisabled" :placeholder="isDisabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="发送范围" prop="scopeType">
          <el-radio-group
            v-model="formData.scopeType"
            @change="handleChange"
            :disabled="formData.type === 'detail' || formData.type === 'edit'"
          >
            <el-radio label="1">全部用户</el-radio>
            <el-radio label="2">指定范围</el-radio>
          </el-radio-group>
          <el-tree-select
            ref="selectRef"
            :disabled="formData.type === 'detail' || formData.type === 'edit'"
            v-if="formData.scopeType === '2'"
            v-model="formData.scopeDeptsList"
            :data="deptList"
            placeholder="请选择"
            value-key="deptId"
            multiple
            check-strictly
            :props="defaultProps"
            show-checkbox
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="内容" prop="content"
          ><WEditor v-model="formData.content" :disabled="isDisabled" @on-change="handleChangeContent" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="附件" v-if="!isDisabled" prop="notificationFiles" class="file-list">
          <FileUpload
            v-model="formData.notificationFiles"
            :limit="5"
            :fileSize="20"
            :fileType="['jpg', 'jpeg', 'bmp', 'png', 'gif', 'txt', 'doc', 'docx', 'rar', 'zip', 'pdf', 'xls', 'xlsx']"
            tipMessage="支持扩展名：jpg、jpeg、bmp、png、gif、txt、doc、docx、rar、zip、pdf、xls、xlsx ，单个文件不能超过20MB"
          />
        </el-form-item>
        <template v-else>
          <template v-if="formData.notificationFiles && formData.notificationFiles.length">
            <el-form-item label="附件" class="file-list">
              <div
                class="download-text"
                v-for="(file, index) in formData.notificationFiles"
                :key="index"
                @click="downloadFile(file)"
              >
                {{ (file.fileNames && file.fileNames) || '暂无文件' }}
              </div>
            </el-form-item>
          </template>
        </template>
      </el-col>
    </el-row>
  </el-form>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>

<script setup>
import { reactive } from 'vue'
import WEditor from '@/components/WEditor'
import FileUpload from '@/components/FileUpload'
import { listDept } from '@/api/system/dept'
import iFrame from '@/components/iFrame'
const formData = reactive({
  title: '',
  scopeType: '1',
  content: '<p><br></p>',
  scopeDeptsList: [],
  notificationFiles: [],
  id: undefined
})

const isDisabled = computed(() => {
  return formData.type === 'detail'
})
// radio切换
const handleChange = value => {
  if (value === '2') {
    formData.scopeDeptsList = []
  }
}

const validateDept = (rule, value, callback) => {
  if (formData.scopeType === '2' && !formData.scopeDeptsList.length) {
    callback(new Error('请选择部门'))
  } else {
    callback()
  }
}

const validateContent = (rule, value, callback) => {
  // 编辑器内容为空时默认为<p><br></p>
  if (value === '<p><br></p>') {
    callback(new Error('请输入'))
  } else {
    callback()
  }
}

// 校验编辑器内容
const handleChangeContent = () => {
  if (formData.content) {
    formRef.value.validateField('content')
  }
}
const rules = {
  title: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  scopeType: [
    {
      required: true,
      validator: validateDept,
      trigger: ['blur', 'change']
    }
  ],
  content: [
    {
      required: true,
      message: '请输入',
      validator: validateContent,
      trigger: ['blur']
    }
  ]
}
/** 查询部门列表 */
const { proxy } = getCurrentInstance()
const deptList = ref([])
const defaultProps = {
  value: 'deptId',
  label: 'deptName',
  children: 'children'
}
function getDeptList() {
  listDept().then(response => {
    deptList.value = proxy.handleTree(response.data, 'deptId')

    console.log('deptList', deptList.value)
  })
}

getDeptList()

const previewShow = ref(false)
const previewUrl = ref('')
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select.el-select--default {
  width: 100%;
}

:deep(.file-list) {
  .el-form-item__content {
    flex-direction: column;
    align-items: start;
  }
}
</style>
