<template>
  <div class="main-wrap">
    <ProTable
      ref="proTable"
      title="列表"
      :init-param="initParam"
      :columns="columns"
      :toolButton="false"
      rowKey="id"
      :request-api="bizList"
    >
      <template #tabs>
        <el-radio-group :model-value="initParam.bizStatus" @change="handleRadioChange">
          <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group></template
      >
      <template #customerName="{ row }">
        <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
      </template>
      <template #code="{ row }">
        <span>{{ row.code }}</span>
      </template>
      <template #contractNo="{ row }">
        <span class="blue-text" @click="handlShowContractDetail(row.contractId)">{{ row.contractNo }}</span>
      </template>
      <template #action="{ row }">
        <span class="blue-text" @click="handleDetail(row)">办理</span>
      </template>
    </ProTable>
  </div>
  <formModal ref="formModalRef" :productName="productName" actionType="myList" @ok="getList" />
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <contractDetail v-if="contractDetailShow" :id="contractId" :hideActionBtn="true" @on-close="contractDetailShow = false" />

  <fromModalNew v-if="formShow" actionType="myList" :productName="productName" :id="id" @on-close="handleClose" />
</template>

<script setup lang="tsx">
import { bizList } from '@/api/certificate/certificate'
import { ref, reactive, nextTick, onMounted } from 'vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { useRoute, useRouter } from 'vue-router'
import formModal from './components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import contractDetail from '@/views/contract-manage/contract-list/components/contract-detail.vue'
import { bizStageArr, bizTypeArr, bizStatusArr } from '@/utils/constants'
import useCommonStore from '@/store/modules/common'
import fromModalNew from './components/form-modal-new'
const useCommon = useCommonStore()

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const initParam = reactive({
  bizStatus: '',
  myTaskFlag: 1
})
const tabs = [
  {
    label: '全部',
    value: ''
  }
].concat(bizStatusArr)
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'code',
    label: '流程编号',
    fixed: 'left',
    minWidth: '200',
    search: { el: 'input' }
  },
  {
    prop: 'bizType',
    label: '业务名称',
    minWidth: '200',
    enum: bizTypeArr,
    render: scope => {
      let label = bizTypeArr.filter(item => item.value === scope.row.bizType)[0]?.label
      if (scope.row.changeSubject) {
        label = label + `(${scope.row.changeSubject} )`
      }

      if (scope.row.productName?.includes('单办证')) {
        label = label + `(单办证)`
      }
      return <span>{label || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerName',
    label: '企业名称',
    minWidth: '200',

    search: { el: 'input', order: 1 }
  },
  {
    prop: 'customerNo',
    label: '企业编号',
    minWidth: '200',
    search: { el: 'input' }
  },
  {
    prop: 'handleUserName',
    label: '当前办理人',
    minWidth: '200'
  },
  {
    prop: 'counselorName',
    label: '财税顾问',
    minWidth: '200'
  },
  {
    prop: 'creatorName',
    label: '创建人',
    minWidth: '200'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: '200'
  },
  {
    prop: 'updateTime',
    label: '最近更新时间',
    minWidth: '200'
  },
  // 完成用时
  {
    prop: 'days',
    label: '完成用时',
    minWidth: '200',
    render: scope => {
      return <span>{scope.row.days ? `${scope.row.days}天` : '--'}</span>
    }
  },
  {
    prop: 'contractNo',
    label: '关联合同',
    minWidth: '200'
  },

  {
    prop: 'bizStage',
    label: '办理阶段',
    fixed: 'right',
    enum: bizStageArr,
    // render: scope => {
    //   return <span>{bizStageArr[scope.bizStage] || '--'}</span>
    // },
    minWidth: '200'
  },
  {
    prop: 'action',
    label: '操作',
    minWidth: '200',
    fixed: 'right'
  }
]

const proTable = ref()
function getList() {
  proTable.value?.getTableList()
}

function handleRadioChange(e: any) {
  proTable.value.pageable.pageNum = 1
  initParam.bizStatus = e
}

const formModalRef = ref()
const formShow = ref(false)
const id = ref()

const productName = ref('')
function handleDetail(row: any) {
  productName.value = row.productName
  const types = ['domestic_business_registration', 'foreign_business_registration', 'bank_account_open']
  if (types.includes(row.bizType) || row.bizType === 'bank_account_open' || row.bizType === 'business_license_register') {
    formModalRef.value?.onShow(row)
  } else {
    id.value = row.id
    formShow.value = true
  }
}

const handleClose = () => {
  formShow.value = false
  getList()
}
const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
}
/* 客户详情弹窗 ---end--- */

/* 合同详情弹窗 ---start--- */
const contractDetailShow = ref(false)
const contractId = ref()
const handlShowContractDetail = (id: number) => {
  contractDetailShow.value = true
  contractId.value = id
}
/* 合同弹窗 ---end--- */

// onMounted(() => {
//   if (useCommon.id) {
//     handleDetail({ id: useCommon.id })
//     //立马clear useCommon id
//     useCommon.clearId()
//   }
// })

watch(
  () => useCommon.id,
  () => {
    console.log('watching', useCommon.id)
    nextTick(() => {
      if (useCommon.id) {
        handleDetail({ id: useCommon.id, bizType: useCommon.bizType })
        //立马clear useCommon id
        useCommon.clearId()
        useCommon.clearBizType()
      }
    })
  },
  {
    immediate: true
  }
)

watch(
  () => useCommon.todoTaskFlag,
  () => {
    if (useCommon.todoTaskFlag) {
      nextTick(() => {
        handleRadioChange('processing')
        useCommon.clearTodoTaskFlag()
      })
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
