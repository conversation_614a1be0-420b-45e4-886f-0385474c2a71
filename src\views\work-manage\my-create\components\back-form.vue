<!--
 * @Description: 完成工单表单
 * @Author: thb
 * @Date: 2023-07-19 08:41:17
 * @LastEditTime: 2023-07-19 14:10:52
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="回退说明" prop="feedbackOrDescription">
          <el-input
            v-model="formData.feedbackOrDescription"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 6 }"
            maxlength="1000"
            placeholder=" 请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import SelectTree from '@/components/SelectTree'
const formData = ref({
  feedbackOrDescription: ''
})

const rules = {
  feedbackOrDescription: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped></style>
