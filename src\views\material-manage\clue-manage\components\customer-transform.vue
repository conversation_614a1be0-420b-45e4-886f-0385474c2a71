<!--
 * @Description: 转为客户
 * @Author: thb
 * @Date: 2023-05-30 09:56:01
 * @LastEditTime: 2023-11-27 14:14:37
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center title="转为客户" width="1200" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <p>转化类型</p>
    <el-radio-group v-model="type">
      <el-radio label="新建客户">新建客户</el-radio>
      <el-radio label="关联已有客户">关联已有客户</el-radio>
    </el-radio-group>
    <el-form ref="formRef" :model="detail" v-if="type === '新建客户'" :rules="rules" label-position="top">
      <Collapse title="基础信息">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="客户名称" prop="companyName">
              <el-input v-model="detail.companyName" length="20" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="手机号" prop="contactPhone">
              <el-input v-model="detail.contactPhone" length="20" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户来源" prop="sourceId">
              <el-tree-select
                filterable
                v-model="detail.sourceId"
                :data="clue_source"
                check-strictly
                :render-after-expand="false"
                default-expand-all
                placeholder="请选择"
                :props="{
                  value: 'id',
                  label: 'name',
                  children: 'child',
                  disabled: (data, node) => {
                    return data.child
                  }
                }"
                clearable
                @current-change="(node, nodeData) => handleSelectChange(node, nodeData)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="detail.sourceId === '3'">
            <el-form-item label="介绍来源" prop="introductionCustomerName">
              <el-input
                @click="handleListSelectShow"
                readonly
                v-model="detail.introductionCustomerName"
                maxlength="50"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户等级" prop="level">
              <el-select v-model="detail.level" placeholder="请选择" clearable>
                <el-option :label="item.label" :value="item.value" v-for="(item, index) in customer_level" :key="index" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="detail.remark"
                length="1000"
                type="textarea"
                placeholder="请输入"
                :autosize="{ minRows: 2, maxRows: 4 }"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="税务性质" prop="taxNature">
              <el-select v-model="detail.taxNature" placeholder="请选择" clearable>
                <el-option v-for="(item, index) in customer_property" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签" prop="tags">
              <el-select v-model="detail.tags" multiple placeholder="请选择" clearable>
                <el-option
                  v-for="(item, index) in tagsList"
                  :disabled="item.status === '0'"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                /> </el-select
            ></el-form-item>
          </el-col>
        </el-row>
      </Collapse>
      <Collapse title="更多信息">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="行业" prop="industry">
              <el-select
                v-model="detail.industry"
                placeholder="请选择"
                filterable
                allow-create
                default-first-option
                @change="handleChange"
              >
                <el-option v-for="(item, index) in industry" :key="index" :label="item.label" :value="item.value" /> </el-select
            ></el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="电话" prop="phone">
              <el-input v-model="detail.phone" maxlength="20" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="detail.email" maxlength="20" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="地区" prop="area">
              <el-input v-model="detail.area" maxlength="20" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="详细地址" prop="address">
              <el-input v-model="detail.address" maxlength="100" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
      </Collapse>
    </el-form>
    <div class="table-wrap" v-if="type === '关联已有客户'">
      <ProTable ref="proTable" @select="handleSingleSelect" rowKey="customerId" :columns="columns" :request-api="getClientList">
      </ProTable>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit(formRef)">保存</el-button>
    </template>
  </el-dialog>
  <tableModal
    v-if="listSelectShow"
    rowKey="customerId"
    title="介绍来源"
    :columns="columnsCustomers"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
</template>
<script setup lang="tsx">
import { getClientList, associateExistClient } from '@/api/material-manage/client'
import Collapse from '@/components/Collapse'
import { getClueTagList } from '@/api/material-manage/tag'
import { FormValidators } from '@/utils/validate'
import { transformCustomerFromClue } from '@/api/material-manage/clue'
import { cloneDeep } from 'lodash'
import { ColumnProps } from '@/components/ProTable/interface'
import { useDic } from '@/hooks/useDic'
import { useSetDic } from '@/hooks/useSetDic'
import { cusSourceTree } from '@/api/material-manage/source'
import tableModal from '@/components/tableModal'
import { getCustomers } from '@/api/customer/file'

const { setDic } = useSetDic()
const handleChange = value => {
  setDic('industry', value, value, industry)
}

const { getDic } = useDic()
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}
const type = ref('新建客户')
const { proxy } = getCurrentInstance()

// const { clue_source } = proxy.useDict('clue_source')
const clue_source = ref([])
cusSourceTree({ enable: 1 }).then(res => {
  clue_source.value = res.data
})
const { industry } = proxy.useDict('industry')
const { customer_property } = proxy.useDict('customer_property')
const { customer_level } = proxy.useDict('customer_level')
const tagsList = ref([])
const getTagsList = async () => {
  const { data } = await getClueTagList({
    pageSize: 10000,
    pageNum: 1
  })
  tagsList.value = data.records || []
}
getTagsList()
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

// const detail = computed(() => {
//   return {
//     ...props.data,
//     tags: props.data.tags?.map(item => item.tagId)
//   }
// })
const detail = ref({})
detail.value = {
  ...props.data,
  tags: props.data.tags?.map(item => item.tagId)
}

// console.log('detail', detail.value)

// const detail1 = ref({
//   ...cloneDeep(props.detail)
// })

const rules = {
  // contactName: [
  //   {
  //     required: true,
  //     message: '请输入',
  //     trigger: ['blur']
  //   }
  // ],
  contactPhone: [
    {
      required: true,
      validator: (rules, value, callback) => {
        if (value) {
          FormValidators.mobilePhone(rules, value, callback)
        } else {
          callback(new Error('请输入'))
        }
      },
      trigger: ['blur']
    }
  ],
  phone: [
    {
      required: false,
      message: '请输入正确的电话格式',
      validator: FormValidators.allPhone,
      trigger: ['blur']
    }
  ],
  email: [
    {
      required: false,
      message: '请输入正确的邮箱格式',
      validator: FormValidators.email,
      trigger: ['blur']
    }
  ],
  // qq: [
  //   {
  //     required: false,
  //     message: '请输入正确格式的qq账号',
  //     validator: FormValidators.qq,
  //     trigger: ['blur']
  //   }
  // ],
  sourceId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  introductionCustomerName: [
    {
      required: true,
      message: '请选择',
      trigger: ['change', 'blur']
    }
  ],
  level: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  companyName: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  remark: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ]
}

const formRef = ref()

const handleSubmit = async formEl => {
  if (type.value === '新建客户') {
    if (!formEl) return
    await formEl.validate(async valid => {
      if (valid) {
        if (type.value === '新建客户') {
          const result = await transformCustomerFromClue({
            ...detail.value,
            tags: detail.value.tags.map(item => {
              if (item !== null && typeof item === 'object') {
                return {
                  tagId: item.tagId
                }
              } else {
                return {
                  tagId: item
                }
              }
            })
          })
          if (result.code === 200) {
            proxy.$modal.msgSuccess(`转为客户成功!`)
            handleClose()
            emits('on-success')
          } else {
            proxy.$modal.msgError(`转为客户失败!`)
          }
        }
      } else {
      }
    })
  } else {
    // 关联已有客户
    if (!selectRow) {
      // 没有选择客户, 提示应该选择客户
      proxy.$modal.msgWarning('请选择某一行!')
      return
    } else {
      // 说明选择了某一个客户
      const result1 = await associateExistClient({
        clueId: detail.value.id,
        customerId: selectRow.id
      })
      if (result1.code === 200) {
        proxy.$modal.msgSuccess(`转为客户成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`转为客户失败!`)
      }
    }
  }
}
// 关联已有客户
// 单选
let selectRow = null
const proTable = ref()
const handleSingleSelect = (selection, row) => {
  selectRow = row
  proTable.value?.clearSelection()
  if (selection.length === 0) return
  proTable.value?.toggleRowSelection(row, true)
}
const getTree = async () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await cusSourceTree({ enable: 1 })
    // 将后端传回的数据结构进行转换
    const revertTreeData = (data = []) => {
      data.forEach(item => {
        item.label = item.name
        item.value = item.id
        item.children = item.child
        if (Array.isArray(item.children) && item.children.length) {
          revertTreeData(item.children)
        }
      })
    }
    revertTreeData(data)
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '序号',

    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'companyName',
    width: 300,
    label: '客户名称'
  },
  {
    prop: 'contactPhone',
    width: 150,
    label: '手机号'
  },
  {
    prop: 'sourceId',
    width: 200,
    label: '客户来源',
    enum: getTree,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'name',
          children: 'child',
          disabled: data => {
            return data.child
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.sourceName}</span>
    }
  },
  {
    prop: 'level',
    width: 150,
    label: '客户等级'
  },
  {
    prop: 'followStatus',
    width: 150,
    // enum: getDic('follow_status'),
    enum: [
      {
        label: '未跟进',
        value: '0'
      },
      {
        label: '跟进中',
        value: '1'
      },
      {
        label: '已转企业',
        value: '2'
      }
    ],
    search: {
      el: 'select'
    },
    label: '跟进状态'
  },

  {
    prop: 'taxNature',
    width: 150,
    fixed: 'right',
    label: '税务性质',
    render: scope => {
      return <span>{scope.row.taxNature || '--'}</span>
    }
  }
]

/** 选择线索来源 */
function handleSelectChange(node, nodeData) {
  // console.log('handleSelectChange', node)
  detail.value.sourceName = node.name
}

/** 客户列表弹窗显示 */
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  listSelectShow.value = true
}
const handleSelect = data => {
  console.log('data', data)
  detail.value.introductionCustomerName = data.customerName
  detail.value.introductionCustomerId = data.customerId
}
const columnsCustomers = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
.table-wrap {
  min-height: 400px;
  display: flex;
}

:deep(.el-table) {
  .el-table__header {
    thead tr {
      th:nth-child(1) .el-checkbox {
        display: none;
      }
      th:nth-child(2) .el-checkbox {
        display: none;
      }
    }
  }
}
</style>
