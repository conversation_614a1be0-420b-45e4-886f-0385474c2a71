/*
 * @Description: 日记账
 * @Author: thb
 * @Date: 2023-09-06 15:28:37
 * @LastEditTime: 2023-09-08 16:02:50
 * @LastEditors: thb
 */
import request from '@/utils/request'
// 获取日记账列表
export const getDayBooKList = params => {
  params.incomeAmount = undefined
  params.paymentAmount = undefined
  return request({
    url: '/financeJournal/list',
    method: 'get',
    params
  })
}
// 删除日记账
export const deleteDayBook = id => {
  return request({
    url: '/financeJournal/delete',
    method: 'delete',
    params: {
      id
    }
  })
}
// 新增或者编辑日记账
export const addOrUpdateDayBook = data => {
  return request({
    url: '/financeJournal/saveOrUpdate',
    method: 'post',
    data
  })
}
// 获取数据初期金额
export const getInitialAmount = params => {
  return request({
    url: '/financeJournal/getInitialAmount',
    method: 'get',
    params
  })
}
// 保存数据初期金额
export const saveOrUpdateInitialAmount = data => {
  return request({
    url: '/financeJournal/saveOrUpdateInitialAmount',
    method: 'post',
    data
  })
}
// 获取日记账详情
export const getDayBookById = id => {
  return request({
    url: '/financeJournal/getById',
    method: 'get',
    params: {
      id
    }
  })
}
// 获取日记数据列表
export const getDayBookStatisticData = () => {
  return request({
    url: '/financeJournal/topStatistics',
    method: 'get'
  })
}

// 获取日记列表每次查询的本月合计
export const getDayBookStatisticDataByMonth = params => {
  if (params.incomeAmount) {
    params.incomeAmountMin = params.incomeAmount[0]
    params.incomeAmountMax = params.incomeAmount[1]
    params.incomeAmount = undefined
  }
  if (params.paymentAmount) {
    params.paymentAmountMin = params.paymentAmount[0]
    params.paymentAmountMax = params.paymentAmount[1]
    params.paymentAmount = undefined
  }
  return request({
    url: '/financeJournal/currentMonthTotal',
    method: 'get',
    params
  })
}
// 导入模板
export const journalImport = data => {
  return request({
    url: '/financeJournal/import',
    method: 'post',
    data
  })
}

// 获取日记账列表
export const getDayBooKListAnalysis = params => {
  params.incomeAmount = undefined
  params.paymentAmount = undefined
  return request({
    url: '/financeJournalAnalysis/list',
    method: 'get',
    params
  })
}
// 获取数据初期金额
export const getInitialAmountAnalysis = params => {
  return request({
    url: '/financeJournalAnalysis/getInitialAmount',
    method: 'get',
    params
  })
}
// 保存数据初期金额
export const saveOrUpdateInitialAmountAnalysis = data => {
  return request({
    url: '/financeJournalAnalysis/saveOrUpdateInitialAmount',
    method: 'post',
    data
  })
}
