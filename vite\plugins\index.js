/*
 * @Description:
 * @Author: thb
 * @Date: 2023-05-19 15:10:43
 * @LastEditTime: 2023-11-08 10:37:11
 * @LastEditors: thb
 */
import vue from '@vitejs/plugin-vue'

import createAutoImport from './auto-import'

import createSvgIcon from './svg-icon'
import createCompression from './compression'
// import createSetupExtend from './setup-extend'
import vueJsx from '@vitejs/plugin-vue-jsx'
import eslintPlugin from 'vite-plugin-eslint'
export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue()]
  vitePlugins.push(createAutoImport())
  // vitePlugins.push(createSetupExtend())
  vitePlugins.push(createSvgIcon(isBuild))
  isBuild && vitePlugins.push(...createCompression(viteEnv))
  vitePlugins.push(vueJsx())
  vitePlugins.push(eslintPlugin())
  return vitePlugins
}
