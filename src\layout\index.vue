<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <Sidebar v-if="!sidebar.hide" class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <!-- <navbar @set-layout="setLayout" /> -->
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <settings ref="settingRef" />
    </div>
  </div>
</template>

<script setup>
import { useWindowSize } from '@vueuse/core'
import Sidebar from './components/Sidebar/index.vue'
import { AppMain, Navbar, Settings, TagsView } from './components'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import { getToken } from '@/utils/auth'
import { useSocket } from '@/utils/websocket'
import { ElNotification } from 'element-plus'

import { useCheck } from '@/hooks/useCheck'
import useCommonStore from '@/store/modules/common'

import { useRouter } from 'vue-router'
const router = useRouter()
const useCommon = useCommonStore()
const { routerCheckDetail } = useCheck(useCommon)
const settingsStore = useSettingsStore()

const theme = computed(() => settingsStore.theme)
// const sideTheme = computed(() => settingsStore.sideTheme)
const sidebar = computed(() => useAppStore().sidebar)
const device = computed(() => useAppStore().device)
const needTagsView = computed(() => settingsStore.tagsView)
const fixedHeader = computed(() => settingsStore.fixedHeader)

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}))

const { width } = useWindowSize()
const WIDTH = 992 // refer to Bootstrap's responsive design

watchEffect(() => {
  if (device.value === 'mobile' && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false })
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile')
    useAppStore().closeSideBar({ withoutAnimation: true })
  } else {
    useAppStore().toggleDevice('desktop')
  }
})

function handleClickOutside() {
  useAppStore().closeSideBar({ withoutAnimation: false })
}

const settingRef = ref(null)
function setLayout() {
  settingRef.value.openSetting()
}

const socket = ref(null) // websocket实例
const host = import.meta.env.MODE === 'development' ? `${import.meta.env.VITE_WS_URL}` : window.location.host
const head = location.protocol === 'https:' ? 'wss' : 'ws'
const initWebSocket = () => {
  if (['***********', '**************'].includes(window.location.host)) return
  console.log('initWebSocket')
  const options = {
    // window.location.host
    url: `${head}://${host}/ws/eventNotify`,
    query: {
      // params
      token: getToken()
    }
  }
  socket.value = useSocket(options)

  socket.value.subscribe('open', () => {
    console.log('Websocket连接成功!')
    const greet = 'hello'
    // 发送打招呼消息
    socket.value.sendMessage(greet)
  })

  socket.value.subscribe('close', reason => {
    console.log('WebSocket连接关闭!', reason)
  })
  socket.value.subscribe('message', result => {
    console.log('WebSocket接收到消息:', result)
    //提示用户
    nextTick(() => {
      ElNotification({
        title: result.title,
        // message: result.content,
        message: h('p', null, [
          h('span', null, result.content),
          h(
            'span',
            {
              class: 'message-text',
              // vNode绑定点击事件
              onclick: () => {
                // console.log('h函数点击事件触发')
                // routerToMessageList()
                if (result.messageType === '提醒') {
                  routerCheckDetail({
                    ...result,
                    extraDataVO: JSON.parse(result.extraData)
                  })
                } else {
                  // 公告
                  const { id, notificationId } = result
                  useCommon.setNotice({
                    id,
                    notificationId
                  })
                  // 存储好后跳转
                  router.push('/message/my-message')
                }
              }
            },
            '请查看'
          )
        ]),
        // duration: 100000, // 假数据
        type: 'warning'
      })
    })
  })
  socket.value.subscribe('error', err => {
    console.log('WebSocket捕获错误:', err)
  })
  socket.value.subscribe('reconnect', _socket => {
    console.log('WebSocket断开重连:', _socket)
    socket.value = _socket
  })
}

// 初始化websocket
initWebSocket()

onUnmounted(() => {
  // 关闭websocket
  console.log('closeSocket')
  socket.value.closeSocket()
})
</script>

<style lang="scss" scoped>
@import '@/assets/styles/mixin.scss';
@import '@/assets/styles/variables.module.scss';
.app-wrapper {
  @include clearfix;

  position: relative;
  width: 100%;
  height: 100%;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}
.drawer-bg {
  position: absolute;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background: #000000;
  opacity: 0.3;
}
.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}
.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}
.sidebarHide .fixed-header {
  width: 100%;
}
.mobile .fixed-header {
  width: 100%;
}
</style>
<style>
.message-text {
  color: #409eff;
  cursor: pointer;
  margin-left: 10px;
}
</style>
