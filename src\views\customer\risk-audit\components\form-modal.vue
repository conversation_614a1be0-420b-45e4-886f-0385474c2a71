<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    top="5vh"
    destroy-on-close
  >
    <template #footer v-if="['新建风险客户', '编辑风险客户'].includes(mode)">
      <el-button @click="handleClose"> 关闭 </el-button>
      <el-button v-if="!['详情'].includes(mode)" :loading="loading" type="primary" @click="handleSubmit"> 保存 </el-button>
    </template>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      :hide-required-asterisk="['详情'].includes(mode)"
    >
      <el-row :gutter="24">
        <el-col :span="!['新建风险客户', '编辑风险客户'].includes(mode) ? 8 : 12">
          <el-form-item label="关联企业" prop="customerName">
            <!-- 点击弹窗出现客户列表  -->
            <el-input
              @click="handleListSelectShow"
              :disabled="!['新建风险客户'].includes(mode)"
              v-model="formData.customerName"
              readonly
              maxlength="20"
              :placeholder="!['新建风险客户', '编辑风险客户'].includes(mode) ? '' : '请输入'"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="!['新建风险客户', '编辑风险客户'].includes(mode) ? 8 : 12">
          <el-form-item label="企业编号" prop="customerNo">
            <el-input disabled v-model="formData.customerNo" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="!['新建风险客户', '编辑风险客户'].includes(mode) ? 8 : 12">
          <el-form-item label="风险原因" prop="reason">
            <el-tree-select
              :disabled="!['新建风险客户', '编辑风险客户'].includes(mode)"
              style="width: 100%"
              :modelValue="formData.reason"
              :data="reasonDict"
              default-expand-all
              :render-after-expand="false"
              @node-click="handleNodeClick"
            />
            <!-- v-model="formData.reason" -->
          </el-form-item>
        </el-col>
        <el-col
          :span="!['新建风险客户', '编辑风险客户'].includes(mode) ? 8 : 12"
          v-if="['新建风险客户', '编辑风险客户'].includes(mode)"
        >
          <el-form-item label="会计主管" prop="sponsorAccountingManagerUserId">
            <SelectTree
              :disabled="!['新建风险客户', '编辑风险客户'].includes(mode)"
              style="width: 100%"
              v-model="formData.sponsorAccountingManagerUserId"
              :placeholder="!['新建风险客户', '编辑风险客户'].includes(mode) ? '' : '请选择'"
              clearable
              @on-node-click="handleChange"
            />
          </el-form-item>
        </el-col>
        <el-col
          :span="!['新建风险客户', '编辑风险客户'].includes(mode) ? 8 : 12"
          v-if="!['新建风险客户', '编辑风险客户'].includes(mode)"
        >
          <el-form-item label="创建人" prop="createBy">
            <el-input
              :disabled="!['新建风险客户', '编辑风险客户'].includes(mode)"
              v-model="formData.createBy"
              :placeholder="!['新建风险客户', '编辑风险客户'].includes(mode) ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
        <el-col
          :span="!['新建风险客户', '编辑风险客户'].includes(mode) ? 8 : 12"
          v-if="!['新建风险客户', '编辑风险客户'].includes(mode)"
        >
          <el-form-item label="创建时间" prop="createTime">
            <el-input
              :disabled="!['新建风险客户', '编辑风险客户'].includes(mode)"
              v-model="formData.createTime"
              :placeholder="!['新建风险客户', '编辑风险客户'].includes(mode) ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="!['新建风险客户', '编辑风险客户'].includes(mode) ? 8 : 12">
          <el-form-item label="附件" prop="fileList">
            <FileUploadBiz
              v-if="!!['新建风险客户', '编辑风险客户'].includes(mode)"
              v-model="formData.fileList"
              :limit="100"
              :isShowTip="false"
              :fileType="['doc', 'xls', 'txt', 'pdf', 'docx', 'xlsx', 'jpg', 'png', 'jpeg', 'mp3', 'm4a', 'wav', 'amr']"
            />
            <fileList v-else isDownload :list="formData.fileList" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="说明" prop="remark">
            <el-input
              :disabled="!['新建风险客户', '编辑风险客户'].includes(mode)"
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 20 }"
              v-model="formData.remark"
              :placeholder="!['新建风险客户', '编辑风险客户'].includes(mode) ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-collapse v-model="activeCollapse" accordion v-if="['详情', '办理', '流失清理'].includes(mode)">
      <el-collapse-item title="风险审核流程" name="0">
        <el-steps direction="vertical" :active="activeDeal" finish-status="success" :space="40">
          <el-step
            title="Step 1"
            @click="handleDealStep(index)"
            v-for="(item, index) in formData.auditNodeList"
            :key="index"
            :status="formData.auditNodeList[index].handleTime ? 'success' : ''"
          >
            <template #title>
              <div class="title-con">
                <div class="title">
                  <span>{{ item.stage }}</span
                  ><span v-if="item.handleUserName">：{{ item.handleUserName }}</span>
                </div>
                <div class="tips">
                  <div
                    :style="
                      dayjs(formData.auditNodeList[index].expectTime).valueOf() <
                      dayjs(formData.auditNodeList[index].handleTime).valueOf()
                        ? 'color:#F56C6C'
                        : 'color:#67C23A'
                    "
                    v-if="formData.auditNodeList[index].handleTime"
                  >
                    {{ formData.auditNodeList[index].handleTime }}
                  </div>
                  <div v-else-if="formData.auditNodeList[index].expectTime">
                    请在
                    {{ formData.auditNodeList[index].expectTime }}
                    前完成{{ item.stage.substring(4, item.stage.length) }}
                  </div>
                  <div
                    style="color: #f56c6c; float: right"
                    v-if="
                      !formData.auditNodeList[index].handleTime &&
                      dayjs(formData.auditNodeList[index].expectTime).valueOf() < dayjs().valueOf() &&
                      item.stage === formData.stage
                    "
                  >
                    已超时
                  </div>
                </div>
              </div>
            </template>
            <template #description v-if="activeDeal === index">
              <div class="form-con">
                <el-form ref="formRef_deal" :model="formData" :rules="rules" label-position="top">
                  <el-row :gutter="24">
                    <el-col :span="24" v-if="stepArr[index]?.formStructure.includes('情况说明')">
                      <el-form-item label="情况说明" :prop="`auditNodeList[${index}].remark`">
                        <el-input
                          :disabled="formData.stage !== item.stage || ['详情'].includes(mode)"
                          type="textarea"
                          :autosize="{ minRows: 5, maxRows: 20 }"
                          v-model="formData.auditNodeList[index].remark"
                          placeholder=""
                        />
                      </el-form-item>
                    </el-col>
                    <el-col
                      :span="12"
                      v-if="
                        stepArr[index]?.formStructure.includes('回访方式') ||
                        stepArr[index]?.formStructure.includes('下户方式') ||
                        formData.auditNodeList[index].visitCustomerType
                      "
                    >
                      <el-form-item :label="stepArr[index]?.formStructure[0]" :prop="`auditNodeList[${index}].visitCustomerType`">
                        <el-select
                          :disabled="formData.stage !== item.stage || ['详情'].includes(mode)"
                          v-model="formData.auditNodeList[index].visitCustomerType"
                          style="width: 100%"
                        >
                          <el-option label="电话" value="电话" />
                          <el-option label="微信" value="微信" />
                          <el-option label="上门" value="上门" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="stepArr[index]?.formStructure.includes('附件')">
                      <el-form-item label="附件" :prop="`auditNodeList[${index}].fileList`">
                        <FileUploadBiz
                          v-if="formData.stage === item.stage && ['办理'].includes(mode)"
                          v-model="formData.auditNodeList[index].fileList"
                          :limit="100"
                          :isShowTip="false"
                          @on-load-success="validateFormField(`auditNodeList[${index}].fileList`)"
                          :fileType="
                            stepArr[index]?.mayRadio
                              ? ['doc', 'xls', 'txt', 'pdf', 'docx', 'xlsx', 'jpg', 'png', 'jpeg', 'mp3', 'm4a', 'wav', 'amr']
                              : ['doc', 'xls', 'txt', 'pdf', 'docx', 'xlsx', 'jpg', 'png', 'jpeg']
                          "
                        />
                        <fileList v-else isDownload :list="formData.auditNodeList[index].fileList" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="stepArr[index]?.formStructure.includes('备注')">
                      <el-form-item label="备注" :prop="`auditNodeList[${index}].remark`">
                        <el-input
                          :disabled="formData.stage !== item.stage || ['详情'].includes(mode)"
                          type="textarea"
                          :autosize="{ minRows: 5, maxRows: 20 }"
                          v-model="formData.auditNodeList[index].remark"
                          placeholder=""
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="stepArr[index]?.formStructure.includes('办理结果')">
                      <el-form-item label="办理结果" :prop="`auditNodeList[${index}].continueHandle`">
                        <el-radio-group
                          v-model="formData.auditNodeList[index].continueHandle"
                          :disabled="formData.stage !== item.stage || ['详情'].includes(mode)"
                        >
                          <el-radio :label="true">继续流转</el-radio>
                          <el-radio :label="false">终止提报</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="stepArr[index]?.formStructure.includes('审批结果')">
                      <el-form-item label="审批结果" :prop="`auditNodeList[${index}].auditStatus`">
                        <el-radio-group
                          v-model="formData.auditNodeList[index].auditStatus"
                          :disabled="formData.stage !== item.stage || ['详情'].includes(mode)"
                        >
                          <el-radio label="pass">通过（客户流失）</el-radio>
                          <el-radio label="not_pass">不通过</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="stepArr[index]?.formStructure.includes('审批意见')">
                      <el-form-item label="审批意见" :prop="`auditNodeList[${index}].remark`">
                        <el-input
                          :disabled="formData.stage !== item.stage || ['详情'].includes(mode)"
                          type="textarea"
                          :autosize="{ minRows: 5, maxRows: 20 }"
                          v-model="formData.auditNodeList[index].remark"
                          placeholder=""
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="button-con-space" v-if="formData.stage === item.stage && ['办理'].includes(mode)">
                    <div>
                      <!-- todo 因为是同一个组件，能否再创建一个实例 -->
                      <el-button
                        v-if="stepArr[index]?.formStructure.includes('编辑提报信息')"
                        type="primary"
                        plain
                        @click="handleEditSelf"
                        >编辑提报信息</el-button
                      >
                      <el-button
                        v-if="stepArr[index]?.formStructure.includes('回退')"
                        type="danger"
                        plain
                        @click="handleRollback(formData.auditNodeList[index].id)"
                        >回退</el-button
                      >
                    </div>
                    <div>
                      <el-button @click="handleClose">取消 </el-button>
                      <el-button type="primary" :loading="loading" plain @click="handleDealStepSave"> 保存 </el-button>
                      <el-button type="primary" :loading="loading" @click="handleDealStepSubmit"> 提交 </el-button>
                    </div>
                  </div>
                </el-form>
              </div>
            </template>
          </el-step>
        </el-steps>
      </el-collapse-item>
      <el-collapse-item
        title="流失清理流程"
        name="1"
        v-if="['出纳确认', '工作清理', '正式停账', '已完成'].includes(formData.stage)"
      >
        <el-form
          ref="formRef_clear"
          :model="formData"
          :rules="rules"
          label-position="top"
          :hide-required-asterisk="['详情'].includes(mode)"
        >
          <el-steps direction="vertical" :active="activeClear" finish-status="success" :space="40">
            <el-step
              title="出纳确认"
              @click="handleClearStep(0)"
              :status="formData.loseHandleNodeList?.[0]?.handleTime ? 'success' : ''"
            >
              <template #title>
                <div class="title-con">
                  <div class="title">
                    <span>出纳确认</span
                    ><span v-if="formData.loseHandleNodeList?.[0]?.handleUserName"
                      >：{{ formData.loseHandleNodeList?.[0]?.handleUserName }}</span
                    >
                  </div>
                  <div class="tips">
                    <div v-if="formData.loseHandleNodeList?.[0]?.handleTime">
                      {{ formData.loseHandleNodeList[0].handleTime }}
                    </div>
                  </div>
                </div>
              </template>
              <template #description v-if="activeClear === 0 && formData.clearRecord">
                <div class="form-con">
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item label="费用清理方式" :prop="`clearRecord.expenseClearType`">
                        <el-select
                          :disabled="!['流失清理'].includes(mode) || formData.stage !== '出纳确认'"
                          v-model="formData.clearRecord.expenseClearType"
                          style="width: 100%"
                        >
                          <el-option label="补钱" value="补钱" />
                          <el-option label="退款" value="退款" />
                          <el-option label="无" value="无" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="附件" :prop="`fileList`">
                        <FileUploadBiz
                          v-if="['流失清理'].includes(mode) && formData.stage === '出纳确认'"
                          v-model="formData.loseHandleNodeList[0].fileList"
                          :limit="100"
                          :isShowTip="false"
                        />
                        <fileList v-else isDownload :list="formData.loseHandleNodeList[0].fileList" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="费用清理说明" :prop="`expenseClearRemark`">
                        <el-input
                          :disabled="!['流失清理'].includes(mode) || formData.stage !== '出纳确认'"
                          type="textarea"
                          :autosize="{ minRows: 5, maxRows: 20 }"
                          v-model="formData.clearRecord.expenseClearRemark"
                          placeholder=""
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="button-con" v-if="['流失清理'].includes(mode) && formData.stage === '出纳确认'">
                    <el-button @click="handleClose">取消 </el-button>
                    <el-button type="primary" :loading="loading" plain @click="handleClearStepSave"> 保存 </el-button>
                    <el-button type="primary" :loading="loading" @click="handleClearStepSubmit"> 提交 </el-button>
                  </div>
                </div>
              </template>
            </el-step>
            <el-step
              title="工作清理"
              @click="handleClearStep(1)"
              :status="formData.loseHandleNodeList?.[1]?.handleTime ? 'success' : ''"
            >
              <template #title>
                <div class="title-con">
                  <div class="title">
                    <span>工作清理</span
                    ><span v-if="formData.loseHandleNodeList?.[1]?.handleUserName"
                      >：{{ formData.loseHandleNodeList?.[1]?.handleUserName }}</span
                    >
                  </div>
                  <div class="tips">
                    <div v-if="formData.loseHandleNodeList?.[1]?.handleTime">
                      {{ formData.loseHandleNodeList?.[1].handleTime }}
                    </div>
                  </div>
                </div>
              </template>
              <template #description v-if="activeClear === 1">
                <div class="form-con">
                  <el-row :gutter="24">
                    <el-col :span="24">
                      <el-form-item label="" prop="stageNameList_0">
                        <el-checkbox
                          :disabled="!['流失清理'].includes(mode) || formData.stage !== '工作清理'"
                          :class="
                            !['流失清理'].includes(mode) || formData.stage !== '工作清理'
                              ? 'my-required-label my-required-label-extra-hide'
                              : 'my-required-label'
                          "
                          v-model="formData.stageNameList_0"
                          label="变更办税员"
                        /><span class="time">{{ formData.stageNameList_0_time }}</span>
                      </el-form-item></el-col
                    >
                    <el-col :span="24">
                      <el-form-item prop="stageNameList_1">
                        <el-checkbox
                          :disabled="!['流失清理'].includes(mode) || formData.stage !== '工作清理'"
                          :class="
                            !['流失清理'].includes(mode) || formData.stage !== '工作清理'
                              ? 'my-required-label my-required-label-extra-hide'
                              : 'my-required-label'
                          "
                          v-model="formData.stageNameList_1"
                          label="变更地址"
                        /><span class="time">{{ formData.stageNameList_1_time }}</span>
                      </el-form-item></el-col
                    >
                    <el-col :span="24">
                      <el-form-item prop="stageNameList_2">
                        <el-checkbox
                          :disabled="!['流失清理'].includes(mode) || formData.stage !== '工作清理'"
                          :class="
                            !['流失清理'].includes(mode) || formData.stage !== '工作清理'
                              ? 'my-required-label my-required-label-extra-hide'
                              : 'my-required-label'
                          "
                          v-model="formData.stageNameList_2"
                          label="通报工商"
                        /><span class="time">{{ formData.stageNameList_2_time }}</span>
                      </el-form-item></el-col
                    >
                    <el-col :span="24">
                      <el-form-item prop="stageNameList_3">
                        <el-checkbox
                          :disabled="!['流失清理'].includes(mode) || formData.stage !== '工作清理'"
                          :class="
                            !['流失清理'].includes(mode) || formData.stage !== '工作清理'
                              ? 'my-required-label my-required-label-extra-hide'
                              : 'my-required-label'
                          "
                          v-model="formData.stageNameList_3"
                          label="通报税务"
                        /><span class="time">{{ formData.stageNameList_3_time }}</span>
                      </el-form-item></el-col
                    >
                  </el-row>
                  <div class="button-con" v-if="['流失清理'].includes(mode) && formData.stage === '工作清理'">
                    <el-button @click="handleClose">取消 </el-button>
                    <el-button type="primary" :loading="loading" plain @click="handleClearStepSave"> 保存 </el-button>
                    <el-button type="primary" :loading="loading" @click="handleClearStepSubmit"> 提交 </el-button>
                  </div>
                </div>
              </template>
            </el-step>
            <el-step
              title="正式停账"
              @click="handleClearStep(2)"
              :status="formData.loseHandleNodeList?.[2]?.handleTime ? 'success' : ''"
            >
              <template #title>
                <div class="title-con">
                  <div class="title">
                    <span>正式停账</span
                    ><span v-if="formData.loseHandleNodeList?.[2]?.handleUserName"
                      >：{{ formData.loseHandleNodeList?.[2]?.handleUserName }}</span
                    >
                  </div>
                  <div class="tips">
                    <div v-if="formData.loseHandleNodeList?.[2]?.handleTime">
                      {{ formData.loseHandleNodeList?.[2].handleTime }}
                    </div>
                  </div>
                </div>
              </template>
              <template #description v-if="activeClear === 2">
                <div class="form-con">
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item label="停止结果" :prop="`clearRecord.stopResult`">
                        <el-select
                          :disabled="['详情'].includes(mode)"
                          v-model="formData.clearRecord.stopResult"
                          style="width: 100%"
                        >
                          <el-option label="注销" value="注销" />
                          <el-option label="迁移" value="迁移" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="" prop="">
                        <el-checkbox
                          :disabled="!['流失清理'].includes(mode) || formData.stage !== '正式停账'"
                          :class="
                            !['流失清理'].includes(mode) || formData.stage !== '正式停账'
                              ? 'my-required-label my-required-label-extra-hide'
                              : 'my-required-label'
                          "
                          v-model="formData.stageNameList_10"
                          label="资料交接"
                        /><span class="time">{{ formData.stageNameList_10_time }}</span>
                      </el-form-item></el-col
                    >
                  </el-row>
                  <div class="button-con" v-if="['流失清理'].includes(mode) && formData.stage === '正式停账'">
                    <el-button @click="handleClose">取消 </el-button>
                    <el-button type="primary" :loading="loading" plain @click="handleClearStepSave"> 保存 </el-button>
                    <el-button type="primary" :loading="loading" @click="handleClearStepSubmit"> 提交 </el-button>
                  </div>
                </div>
              </template>
            </el-step>
          </el-steps>
        </el-form>
      </el-collapse-item>
    </el-collapse>
  </el-dialog>
  <tableModal
    :init-param="{ discard: 0 }"
    v-if="listSelectShow"
    rowKey="customerId"
    title="关联企业"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
</template>

<script setup lang="jsx">
import { getCustomers } from '@/api/customer/file'
import tableModal from '@/components/tableModal'
import {
  riskCustomerSave,
  riskCustomerUpdate,
  riskCustomerAuditNodeComplete,
  riskCustomerAuditNodeSave,
  riskCustomerGetById,
  riskCustomerLoseHandleAccountCloseComplete,
  riskCustomerLoseHandleAccountCloseSave,
  riskCustomerLoseHandleCashierConfirmComplete,
  riskCustomerLoseHandleCashierConfirmSave,
  riskCustomerLoseHandleWorkClearComplete,
  riskCustomerLoseHandleWorkClearSave,
  riskCustomerRollback
} from '@/api/customer/risk.js'
import SelectTree from '@/components/SelectTree'
import { reasonDict } from '@/utils/constants.js'
import fileList from '@/components/FileList'
import FileUploadBiz from '@/components/FileUploadBiz'
import { useDic } from '@/hooks/useDic'
import dayjs from 'dayjs'
import { ElMessageBox } from 'element-plus'

const { getDic } = useDic()
const { proxy } = getCurrentInstance()
const emit = defineEmits()

const visible = ref(true)
const mode = ref('')
const activeCollapse = ref('')
const loading = ref(false)
const formRef = ref()
const formRef_deal = ref()
const formRef_clear = ref()
const formData = reactive({
  id: undefined
})
const rules = {
  customerName: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  reason: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  sponsorAccountingManagerUserId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  remark: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  // 'auditNodeList.*.fileList' 不支持这种写法
  'auditNodeList[0].fileList': [{ required: true, message: '请上传', trigger: ['blur', 'change'] }],
  'auditNodeList[1].visitCustomerType': [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  'auditNodeList[2].fileList': [{ required: true, message: '请上传', trigger: ['blur', 'change'] }],
  'auditNodeList[3].visitCustomerType': [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  'auditNodeList[4].remark': [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  'auditNodeList[5].remark': [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  'auditNodeList[0].continueHandle': [{ required: true, message: '请选择', trigger: ['change'] }],
  'auditNodeList[1].continueHandle': [{ required: true, message: '请选择', trigger: ['change'] }],
  'auditNodeList[2].continueHandle': [{ required: true, message: '请选择', trigger: ['change'] }],
  'auditNodeList[3].continueHandle': [{ required: true, message: '请选择', trigger: ['change'] }],
  'auditNodeList[4].continueHandle': [{ required: true, message: '请选择', trigger: ['change'] }],
  'auditNodeList[5].continueHandle': [{ required: true, message: '请选择', trigger: ['change'] }],
  'clearRecord.expenseClearType': [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  // stageNameList_0: [{ required: true, message: '请确认', trigger: ['blur', 'change'] }],
  // stageNameList_1: [{ required: true, message: '请确认', trigger: ['blur', 'change'] }],
  // stageNameList_2: [{ required: true, message: '请确认', trigger: ['blur', 'change'] }],
  // stageNameList_3: [{ required: true, message: '请确认', trigger: ['blur', 'change'] }],
  'clearRecord.stopResult': [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
  // stageNameList_10: [{ required: true, message: '请确认', trigger: ['blur', 'change'] }]
}

/** deal step逻辑 */
const activeDeal = ref(0)
let stepArr = []
const stepArr_origin = [
  { stage: '财税顾问回访', formStructure: ['附件', '备注', '办理结果', '编辑提报信息'], mayRadio: true },
  { stage: '财税顾问下户', formStructure: ['下户方式', '附件', '备注', '办理结果', '回退'], mayRadio: true },
  { stage: '会计主管回访', formStructure: ['附件', '备注', '办理结果', '回退'], mayRadio: true },
  { stage: '会计主管下户', formStructure: ['下户方式', '附件', '备注', '办理结果', '回退'], mayRadio: true },
  { stage: '客户经理审批', formStructure: ['情况说明', '附件', '办理结果', '回退'] },
  { stage: '会计经理审批', formStructure: ['情况说明', '附件', '办理结果', '回退'] },
  { stage: '总经理审批', formStructure: ['审批结果', '审批意见'] }
]
const stepArr_new = [
  { stage: '客户成功下户', formStructure: ['附件', '备注', '办理结果', '编辑提报信息'], mayRadio: true },
  { stage: '财税顾问回访', formStructure: ['回访方式', '附件', '备注', '办理结果', '回退'], mayRadio: true },
  { stage: '会计主管回访', formStructure: ['附件', '备注', '办理结果', '回退'], mayRadio: true },
  { stage: '会计主管下户', formStructure: ['下户方式', '附件', '备注', '办理结果', '回退'], mayRadio: true },
  { stage: '客户经理审批', formStructure: ['情况说明', '附件', '办理结果', '回退'] },
  { stage: '会计经理审批', formStructure: ['情况说明', '附件', '办理结果', '回退'] },
  { stage: '总经理审批', formStructure: ['审批结果', '审批意见'] }
]
const stepArr_new_noManger = [
  { stage: '客户成功下户', formStructure: ['附件', '备注', '办理结果', '编辑提报信息'], mayRadio: true },
  { stage: '会计主管回访', formStructure: ['附件', '备注', '办理结果', '回退'], mayRadio: true },
  { stage: '会计主管下户', formStructure: ['下户方式', '附件', '备注', '办理结果', '回退'], mayRadio: true },
  { stage: '客户经理审批', formStructure: ['情况说明', '附件', '办理结果', '回退'] },
  { stage: '会计经理审批', formStructure: ['情况说明', '附件', '办理结果', '回退'] },
  { stage: '总经理审批', formStructure: ['审批结果', '审批意见'] }
]
function mergeStepArrAndAuditNodeList(auditNodeList) {
  // 创建一个新数组，用于存放结果
  const mergedArray = []
  if (auditNodeList?.[0]?.stage === '财税顾问回访') {
    stepArr = stepArr_origin
  } else if (auditNodeList?.[1]?.stage === '会计主管回访') {
    stepArr = stepArr_new_noManger
  } else {
    stepArr = stepArr_new
  }
  // 有”会计主管回访“但没有”财税顾问回访“
  // if (auditNodeList?.[1]?.stage === '会计主管回访') {
  //   stepArr.splice(1, 1)
  //   console.log('mergeStepArrAndAuditNodeList-stepArr', stepArr)
  // }
  // 遍历 stepArr 数组
  stepArr.forEach(stepItem => {
    // 从 auditNodeList 中查找与当前 stepItem 相匹配的元素
    const auditItem = auditNodeList.find(audit => audit.stage === stepItem.stage)
    // 如果在 auditNodeList 中找到了匹配的元素，则添加到结果数组中
    if (auditItem) {
      mergedArray.push(auditItem)
    } else {
      // 如果没有找到匹配的元素，则添加原始的 stepItem
      mergedArray.push(stepItem)
    }
  })
  // 注释该行会对老流程产生影响吗？
  // if (auditNodeList?.[1]?.stage === '会计主管回访') {
  //   mergedArray.splice(1, 1)
  // }
  console.log('mergeStepArrAndAuditNodeList-mergedArray', mergedArray)
  return mergedArray
}
function handleDealStep(index) {
  console.log('handleDealStep', index, formData.auditNodeList)
  const indexTemp = stepArr.findIndex(item => item.stage === formData.stage)
  if (indexTemp !== -1 && index > indexTemp) {
    proxy.$message.warning('未达到该步骤')
    return
  }
  activeDeal.value = index
}
const handleDealStepSave = async () => {
  console.log('formData', formData)
  if (loading.value) return
  const formDataTemp = formData.auditNodeList[activeDeal.value]
  // await formRef_deal.value[0].validate()
  loading.value = true
  riskCustomerAuditNodeSave(formDataTemp)
    .then(res => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess('保存成功')
        emit('ok')
        getDetail({ id: formData.id })
      }
    })
    .finally(() => {
      loading.value = false
    })
}
const handleDealStepSubmit = async () => {
  console.log('formData', formData)
  // console.log('formRef_deal.value', formRef_deal.value)
  if (loading.value) return
  const formDataTemp = formData.auditNodeList[activeDeal.value]
  await formRef_deal.value[0].validate()
  loading.value = true
  riskCustomerAuditNodeComplete(formDataTemp)
    .then(res => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess('保存成功')
        emit('ok')
        handleClose()
      }
    })
    .finally(() => {
      loading.value = false
    })
}

/** clear step 逻辑 */
const activeClear = ref(0)
const StepArrClear = [{ stage: '出纳确认' }, { stage: '工作清理' }, { stage: '正式停账' }]
function handleClearStep(index) {
  console.log('handleClearStep', index)
  const indexTemp = StepArrClear.findIndex(item => item.stage === formData.stage)
  if (indexTemp !== -1 && index > indexTemp) {
    proxy.$message.warning('未达到该步骤')
    return
  }
  activeClear.value = index
}
const handleClearStepSave = async () => {
  if (loading.value) return
  switch (formData.stage) {
    case '出纳确认':
      {
        const formDataTemp = {
          id: formData.loseHandleNodeList[0].id,
          expenseClearRemark: formData.clearRecord.expenseClearRemark,
          expenseClearType: formData.clearRecord.expenseClearType,
          fileList: formData.loseHandleNodeList[0].fileList
        }
        loading.value = true
        riskCustomerLoseHandleCashierConfirmSave(formDataTemp)
          .then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess('保存成功')
              emit('ok')
              getDetail({ id: formData.id })
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
      break
    case '工作清理':
      {
        const formDataTemp = {
          id: formData.loseHandleNodeList[1].id,
          stageNameList: [
            formData.stageNameList_0 ? '变更办税员' : undefined,
            formData.stageNameList_1 ? '变更地址' : undefined,
            formData.stageNameList_2 ? '通报工商' : undefined,
            formData.stageNameList_3 ? '通报税务' : undefined
          ]
        }
        loading.value = true
        riskCustomerLoseHandleWorkClearSave(formDataTemp)
          .then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess('保存成功')
              emit('ok')
              getDetail({ id: formData.id })
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
      break
    case '正式停账':
      {
        const formDataTemp = {
          id: formData.loseHandleNodeList[2].id,
          stageNameList: [formData.stageNameList_10 ? '资料交接' : undefined],
          stopResult: formData.clearRecord.stopResult
        }
        loading.value = true
        riskCustomerLoseHandleAccountCloseSave(formDataTemp)
          .then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess('保存成功')
              emit('ok')
              getDetail({ id: formData.id })
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
      break
  }
}
const handleClearStepSubmit = async () => {
  if (loading.value) return
  switch (formData.stage) {
    case '出纳确认':
      {
        const formDataTemp = {
          id: formData.loseHandleNodeList[0].id,
          expenseClearRemark: formData.clearRecord.expenseClearRemark,
          expenseClearType: formData.clearRecord.expenseClearType,
          fileList: formData.loseHandleNodeList[0].fileList
        }
        await formRef_clear.value.validate()
        loading.value = true
        riskCustomerLoseHandleCashierConfirmComplete(formDataTemp)
          .then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess('保存成功')
              emit('ok')
              handleClose()
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
      break
    case '工作清理':
      {
        const formDataTemp = {
          id: formData.loseHandleNodeList[1].id,
          stageNameList: [
            formData.stageNameList_0 ? '变更办税员' : undefined,
            formData.stageNameList_1 ? '变更地址' : undefined,
            formData.stageNameList_2 ? '通报工商' : undefined,
            formData.stageNameList_3 ? '通报税务' : undefined
          ]
        }
        await formRef_clear.value.validate()
        let returnFlag = false
        console.log('formDataTemp.stageNameList', formDataTemp.stageNameList)
        for (const item of formDataTemp.stageNameList) {
          if (!item) {
            proxy.$message.warning(`请完成当前步骤所有内容后提交`)
            returnFlag = true
            break
          }
        }
        if (returnFlag) return
        loading.value = true
        riskCustomerLoseHandleWorkClearComplete(formDataTemp)
          .then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess('保存成功')
              emit('ok')
              handleClose()
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
      break
    case '正式停账':
      {
        const formDataTemp = {
          id: formData.loseHandleNodeList[2].id,
          stageNameList: [formData.stageNameList_10 ? '资料交接' : undefined],
          stopResult: formData.clearRecord.stopResult
        }
        await formRef_clear.value.validate()
        let returnFlag = false
        for (const item of formDataTemp.stageNameList) {
          if (!item) {
            proxy.$message.warning(`请完成当前步骤所有内容后提交`)
            returnFlag = true
            break
          }
        }
        if (returnFlag) return
        loading.value = true
        riskCustomerLoseHandleAccountCloseComplete(formDataTemp)
          .then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess('保存成功')
              emit('ok')
              handleClose()
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
      break
  }
}

/** main逻辑 */
const getDetail = async (row, onRefreshPartBool) => {
  // console.log('getDetail', row)
  await riskCustomerGetById({ id: row.id }).then(res => {
    if (onRefreshPartBool) {
      const keys = [
        'customerName',
        'customerNo',
        'reason',
        'sponsorAccountingManagerUserId',
        'createBy',
        'createTime',
        'fileList',
        'remark'
      ]
      keys.forEach(key => {
        formData[key] = res.data[key]
      })
      return
    }
    activeDeal.value = res.data.auditNodeList.length - 1
    if (res.data.loseHandleNodeList?.length) {
      activeClear.value = res.data.loseHandleNodeList.length - 1
    }
    const auditNodeList = mergeStepArrAndAuditNodeList(res.data.auditNodeList)
    Object.assign(formData, res.data, { auditNodeList: auditNodeList })
    if (formData.auditNodeList?.[6]?.auditStatus === null) {
      formData.auditNodeList[6].auditStatus = 'pass'
    }
    if (formData?.clearRecord?.workClearStageList?.length) {
      const stageNameListArr = ['变更办税员', '变更地址', '通报工商', '通报税务']
      formData.clearRecord.workClearStageList.forEach(item => {
        if (item.completeTime) {
          const index = stageNameListArr.indexOf(item.stageName)
          formData[`stageNameList_${index}`] = true
          formData[`stageNameList_${index}_time`] = item.completeTime
        }
      })
    }
    if (formData?.clearRecord?.accountStopStageList?.length) {
      formData.clearRecord.accountStopStageList.forEach((item, index) => {
        if (item.completeTime) {
          formData[`stageNameList_10`] = true
          formData[`stageNameList_10_time`] = item.completeTime
        }
      })
    }
    // formData.auditNodeList[0].expectTime = '2023-12-25 15:37:01'
    // console.log('auditNodeList', auditNodeList)
    // console.log('formData.auditNodeList', formData.auditNodeList)
  })
}
const onAdd = row => {
  mode.value = '新建风险客户'
}
const onEdit = row => {
  getDetail(row)
  mode.value = '编辑风险客户'
}
const onDetail = row => {
  getDetail(row)
  mode.value = '详情'
}
const onRefresh = row => {
  getDetail(row, true)
  mode.value = '办理'
}
const onDeal = row => {
  getDetail(row)
  activeCollapse.value = '0'
  mode.value = '办理'
}
const onClear = row => {
  getDetail(row)
  activeCollapse.value = '1'
  mode.value = '流失清理'
}
const handleClose = () => {
  emit('close')
}

const handleEditSelf = () => {
  // console.log('handleEditSelf')
  emit('edit-self', { id: formData.id })
}

const handleRollback = nodeId => {
  ElMessageBox.confirm('确定要回退到上一步骤吗', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      riskCustomerRollback({ nodeId }).then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess(res.msg)
          emit('ok')
          handleClose()
          // setTimeout(() => {
          //   // 后端处理完数据需要一点耗时
          //   getDetail({ id: formData.id })
          // }, 1000)
        } else {
          proxy.$modal.msgError(res.msg)
        }
      })
    })
    .catch(() => {})
}

const handleSubmit = async () => {
  console.log('formData', formData)
  await formRef.value.validate()
  if (formData.id) {
    riskCustomerUpdate(formData).then(res => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess('保存成功')
        emit('ok')
        emit('update-self', { id: formData.id })
        handleClose()
      }
    })
  } else {
    riskCustomerSave(formData).then(res => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess('保存成功')
        emit('ok')
        handleClose()
      }
    })
  }
}

/** 客户列表弹窗显示 */
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  listSelectShow.value = true
}
const handleSelect = data => {
  formData.customerName = data.customerName
  formData.customerNo = data.customerNo
  formData.customerId = data.customerId
}
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },
  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  // {
  //   prop: 'manger',
  //   width: '150',
  //   label: '会计主管'
  // },
  // {
  //   prop: 'manger',
  //   width: '150',
  //   label: '客户经理'
  // },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

/** 风险原因选择 */
const handleNodeClick = (node, node1) => {
  console.log('node', node, node1)
  if (!node1.parent.data.value) return
  const txt = node1.parent.data.value + '/' + node1.data.value
  // console.log('txt', txt)
  formData.reason = txt
}

/** 会计主管选择 */
const handleChange = node => {
  // console.log('node', node)
  formData.sponsorAccountingManagerUserId = node.id
}

// 文件上传后检验
const validateFormField = field => {
  formRef_deal.value[0].validateField(field)
}

defineExpose({
  onAdd,
  onEdit,
  onRefresh,
  onDetail,
  onDeal,
  onClear
})
</script>

<style lang="scss" scoped>
:deep(.el-collapse-item__header) {
  font-size: 16px;
  font-weight: bold;
}
:deep(.el-step__title) {
  cursor: pointer;
}
:deep(.upload-file) {
  width: 100%;
}
:deep(.el-step__description) {
  padding-right: 0;
}
.title-con {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  .title {
    cursor: pointer;
  }
  .tips {
    font-weight: normal;
  }
}
.form-con {
  padding-top: 15px;
  padding-bottom: 15px;

  .button-con-space {
    display: flex;
    justify-content: space-between;
  }
  .button-con {
    display: flex;
    justify-content: flex-end;
  }
}

.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
.el-button--danger.is-plain {
  --el-button-text-color: var(--el-color-danger);
  --el-button-bg-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-danger);
  }
}
.time {
  margin-left: 20px;
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #b2b5b9;
  line-height: 20px;
}

.my-required-label {
  :deep(.el-checkbox__label) {
    &:before {
      content: '*';
      color: var(--el-color-danger);
      margin-right: 4px;
    }
  }
}
.my-required-label-extra-hide {
  :deep(.el-checkbox__label) {
    &:before {
      content: '';
    }
  }
}
</style>
