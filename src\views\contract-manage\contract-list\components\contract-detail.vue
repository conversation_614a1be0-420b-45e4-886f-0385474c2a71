<!--
 * @Description: 合同详情
 * @Author: thb
 * @Date: 2023-07-07 14:39:22
 * @LastEditTime: 2024-03-05 13:21:53
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    title="合同详情"
    :width="isPermission ? 1200 : 800"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <template v-if="isPermission">
      <div class="c-top f-al">
        <!-- 合同图标 -->
        <span class="icon book-icon"></span>
        <span class="title">{{ detail.contractNo }}</span>
        <!-- 审批记录图标 -->
        <span class="icon review-icon"></span> <span class="blue-text review" @click="reviewShow = true">审批记录</span>
        <span class="title-small">财税顾问：{{ detail.manager }}</span>
        <span v-if="detail.documentNo" class="title-small"
          >归档号：{{ detail.documentNo }}

          <el-icon :size="14" color="#0368CD" class="cursor-point" @click="editDocumentNo"><EditPen /></el-icon
        ></span>
      </div>
      <!-- <p v-if="isChange" class="p-t">
        变更自:
        <span class="text-color" @click="handleChangeDetail"> {{ detail.originContractNo || detail?.contractNo }}</span>
      </p> -->
      <div class="container">
        <div class="left">
          <!-- oss收费 先注释 -->
          <iframe :src="urlPreview" frameborder="no" style="width: 100%; height: 100%" scrolling="auto" />
        </div>
        <div class="right">
          <!-- 根据合同类型去动态渲染对应的合同表单组件 -->
          <component
            :isShow="false"
            :isDisabled="true"
            :allDisabled="true"
            :rowChange="true"
            :is="formMap[detail.contractTypeName]"
            v-model="detail"
          />
        </div>
      </div>
    </template>
    <!-- 头部信息 -->

    <template v-else> <checkPermission @on-success="handleClose" /> </template>

    <template #footer>
      <div class="footer">
        <div class="footer-left">
          <template v-if="isPermission">
            <!-- 变更自 -->
            <span v-if="isChange" class="m-r">
              变更自:
              <span class="text-color" @click="handleChangeDetail"> {{ detail.originContractNo || detail?.contractNo }}</span>
            </span>

            <template v-if="detail.type === '1' || detail?.fileList?.length > 1">
              <span>合同版本：</span>
              <el-select class="m-r" style="width: 300px" v-model="fileCurrent" @change="handleChangePreview">
                <el-option v-for="item in detail.fileList" :key="item.urls" :label="item.fileNames" :value="item.urls" />
              </el-select>
              <!-- <el-icon :size="24" color="#409EFF" @click="handleUpdateFile"><Upload /></el-icon> -->
              <span class="icon upload-icon-sm m-r" @click="handleUpdateFile"></span>
              <span @click="handleUpdateFile" class="blue-text text-color m-r">更新合同</span>
              <div class="my-upload-file" style="display: none"><FileUpload @on-load-success="onLoadSuccess" /></div>
            </template>
          </template>
        </div>

        <div class="footer-right">
          <template v-if="isPermission">
            <!-- 功能按钮 -->
            <el-button type="primary" @click="handleBack" v-if="isOrigin"> 返回 </el-button>
            <!-- 合同续签--只有执行中和已到期合同是可以的contractStatus 同时周期性的 -->

            <el-button
              type="primary"
              v-if="(contractStatus === '4' || contractStatus === '5') && detail.feeType !== '0'"
              @click="contractRenewal"
            >
              续签
            </el-button>

            <el-button
              type="primary"
              @click="handleChange"
              v-if="detail.isIntention !== '1' && detail.contractStatus === '4' && !hideActionBtn && !isBorrow"
            >
              变更
            </el-button>
            <el-button
              type="primary"
              @click="handleTranslate"
              v-if="detail.isIntention === '1' && detail.contractStatus === '4' && !hideActionBtn && !isBorrow"
            >
              转为正式合同
            </el-button>
            <!-- 终止合同的功能 -->
            <el-button
              type="primary"
              v-if="detail.contractId && (detail.contractStatus === '4' || detail.contractStatus === '5')"
              @click="endContract"
            >
              终止合同
            </el-button>
            <el-button @click="handleDownload"> 下载合同 </el-button>
            <el-button @click="handlePrint"> 打印合同 </el-button></template
          >

          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
  <!-- 审批记录的弹窗 -->
  <el-dialog align-center title="审批记录" width=" 800" :close-on-click-modal="false" v-model="reviewShow">
    <!-- 审批流程 -->
    <processSet
      v-if="detail.reviewList?.length"
      type="detail"
      :isReview="true"
      :isHorizontal="true"
      :createTime="detail.createTime"
      v-model="detail.reviewList"
    />
    <template #footer>
      <el-button @click="reviewShow = false">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 终止合同弹窗 -->
  <el-dialog align-center title="提示" width="400" :close-on-click-modal="false" v-model="stopShow">
    <template v-if="isExist">
      <el-alert title="当前合同有关联的有效账单，无法进行终止操作" type="warning" show-icon :closable="false"
    /></template>
    <template v-else>
      <el-alert title="是否确认终止该合同？" type="warning" show-icon :closable="false" />
      <p class="text">执行后将无法撤回该操作</p>
    </template>
    <template #footer>
      <template v-if="isExist">
        <el-button @click="stopShow = false">关闭</el-button>
      </template>
      <template v-else>
        <el-button @click="stopShow = false">关闭</el-button>
        <el-button type="primary" @click="submitStopContract">确认</el-button>
      </template>
    </template>
  </el-dialog>
</template>
<script setup>
import { usePreview } from '@/hooks/usePreview'
import {
  getContractDetailById,
  checkContractDetailIsPermission,
  customerContractAddFile,
  updateDocumentNo,
  checkContractBillExisted,
  stopContract
} from '@/api/contract/contract'
import { getFileUrlByOss } from '@/api/file/file.js'
import { Download } from '@element-plus/icons-vue'
import accountingForm from './accounting-form.vue'
import oneOff from './one-off.vue'
import addressService from './address-service.vue'
import checkPermission from './check-permission.vue'
import Collapse from '@/components/Collapse'
import { cloneDeep } from 'lodash'
import processSet from '@/views/process-manage/contract-review/components/process-set.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { EditPen } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialog'
import documentNoForm from './documentNo-form'
import printJS from 'print-js'
const iframeRef = ref()

const downloadFileWExt = ['pdf', 'jpg', 'png', 'jpeg']
const handlePrint = async () => {
  // const url = detail.value?.file?.urls
  if (fileCurrent.value) {
    const fileName = fileCurrent.value.split('.')
    const fileExt = fileName[fileName.length - 1]
    if (downloadFileWExt.includes(fileExt)) {
      const { data } = await getFileUrlByOss(fileCurrent.value)
      // 判断
      printJS({
        printable: data,
        type: fileExt === 'pdf' ? 'pdf' : 'image'
      })
    } else {
      proxy.$modal.msgWarning(`目前只支持pdf、jpg、png、jpeg格式的文件打印!`)
    }
  }
}
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success', 'on-change', 'on-translate', 'on-terminate', 'edit-documnetNo'])
const handleClose = () => {
  emits('on-close')
}

const reviewShow = ref(false)

// editDocumentNo 编辑归档号
const { showDialog } = useDialog()
const editDocumentNo = () => {
  showDialog({
    title: '合同归档',
    component: documentNoForm,
    customClass: 'document-dialog',
    getApi: checkContractDetailIsPermission,
    requestParams: detail.value.contractId,
    submitApi: updateDocumentNo,
    submitCallback: () => {
      getContractDetail(props.id)
      emits('edit-documnetNo')
    }
    // handleRevertParams
  })
}
const props = defineProps({
  id: String,
  hideActionBtn: Boolean,
  isChange: {
    type: Boolean,
    default: false
  },
  contractStatus: {
    type: String,
    default: ''
  }
})
const isBorrow = ref(false)
const detail = ref({})
const originDetail = ref({})
const urlPreview = ref('')
const reviewList = ref([])
const fileCurrent = ref('')

const isPermission = ref(false)
// 获取原合同的详情
const getContractOriginDetail = async id => {
  const { data } = await checkContractDetailIsPermission(id)
  if (data) {
    // 是否被借阅，是借阅的不能变更,
    isBorrow.value = !data.isChange
    // 有权限
    const { isInContract, feeType, salesRevenue } = data
    const salesSplit = salesRevenue?.split('/') || ''

    originDetail.value =
      Object.assign(data, {
        salesRevenue: salesSplit.length === 2 && salesSplit[0] === '其他' ? salesSplit[1] : salesRevenue,
        rowData: {
          isInContract,
          feeType
        }
      }) || {}

    // reviewList.value = data.reviewList || []
    isOrigin.value = true
    detail.value = originDetail.value
    // 后续恢复以下注释
    if (detail.value.type === '1' || detail.value?.fileList?.length > 1) {
      const item = detail.value?.fileList.at(-1)
      fileCurrent.value = item?.urls
      handlePreview(fileCurrent.value)
    } else {
      handlePreview(data?.file?.urls)
    }
    // if (data?.file?.urls) {
    //   const previewUrl = await usePreview(data?.file?.urls)
    //   urlPreview.value = previewUrl
    // }
  } else {
    // 没有权限进行提示
    ElMessageBox.alert('没有该合同查看权限', '提示')
  }
}
const baseDetail = ref({})
const getContractDetail = async id => {
  const { data } = await checkContractDetailIsPermission(id)
  if (data) {
    isPermission.value = true
    // 是否被借阅，是借阅的不能变更,
    isBorrow.value = !data.isChange
    // 如果权限存在
    const { isInContract, feeType, salesRevenue } = data
    const salesSplit = salesRevenue?.split('/') || ''

    detail.value =
      Object.assign(data, {
        salesRevenue: salesSplit.length === 2 && salesSplit[0] === '其他' ? salesSplit[1] : salesRevenue,
        rowData: {
          isInContract,
          feeType
        }
      }) || {}
    // 存储baseDetail
    baseDetail.value = cloneDeep(detail.value)
    // reviewList.value = data.reviewList || []
    // 后续恢复以下注释
    if (detail.value.type === '1' || detail.value?.fileList?.length > 1) {
      const item = detail.value?.fileList.at(-1)
      fileCurrent.value = item?.urls
      handlePreview(fileCurrent.value)
    } else {
      handlePreview(data?.file?.urls)
    }
    // if (data?.file?.urls) {
    //   const previewUrl = await usePreview(data?.file?.urls)
    //   urlPreview.value = previewUrl
    // }
  } else {
    isPermission.value = false
  }
}
// 查看原合同详情
const isOrigin = ref(false)
const handleChangeDetail = async () => {
  //如果是变更合同
  if (props.isChange && !Object.keys(originDetail.value).length) {
    await getContractOriginDetail(detail.value.originId)
  } else {
    console.log('originDetail.value', originDetail.value)
    isOrigin.value = true
    detail.value = originDetail.value
  }
  // 后续恢复以下注释
  if (detail.value.type === '1' || detail.value?.fileList?.length > 1) {
    const item = originDetail.value?.fileList.at(-1)
    fileCurrent.value = item?.urls
    handlePreview(fileCurrent.value)
  } else {
    handlePreview(originDetail.value?.file?.urls)
  }
  // if (originDetail.value?.file?.urls) {
  //   const previewUrl = await usePreview(data?.file?.urls)
  //   urlPreview.value = previewUrl
  // }
}
// 返回
const handleBack = async () => {
  isOrigin.value = false
  console.log('baseDetail', baseDetail.value)
  detail.value = baseDetail.value
  // 后续恢复以下注释
  if (detail.value.type === '1' || detail.value?.fileList?.length > 1) {
    const item = detail.value?.fileList.at(-1)
    fileCurrent.value = item?.urls
    handlePreview(fileCurrent.value)
  } else {
    handlePreview(baseDetail.value?.file?.urls)
  }
  // if (baseDetail.value?.file?.urls) {
  //   const previewUrl = await usePreview(data?.file?.urls)
  //   urlPreview.value = previewUrl
  // }
}

const handleChangePreview = () => {
  handlePreview(fileCurrent.value)
}

const handlePreview = async url => {
  if (url) {
    const previewUrl = await usePreview(url)
    urlPreview.value = previewUrl
  }
}

const handleUpdateFile = () => {
  document.querySelector('div.my-upload-file input[type="file"]').click()
}

const onLoadSuccess = fileList => {
  // console.log('fileList', fileList)
  fileList[0].urls = fileList[0].url
  fileList[0].fileNames = fileList[0].fileName
  const formData = Object.assign({}, { contractId: props.id, file: fileList[0] })
  customerContractAddFile(formData).then(res => {
    if (res.code === 200) {
      ElMessage.success('更新成功')
      getContractDetail(props.id)
    }
  })
}

const formMap = {
  记账合同: accountingForm,
  一次性合同: oneOff,
  地址服务协议合同: addressService
}
// 变更合同
const handleChange = () => {
  emits('on-change', props.id, detail.value.type)
  handleClose()
}
// 转为正式合同
const handleTranslate = () => {
  emits('on-translate', props.id, detail.value.type)
  handleClose()
}
// 下载合同
const handleDownload = async () => {
  // const url = detail.value?.file?.urls
  // console.log('ulr', detail.value, detail.value?.file?.urls)
  if (fileCurrent.value) {
    const { data } = await getFileUrlByOss(fileCurrent.value)
    window.open(data)
  }
}
// 终止合同
const stopShow = ref(false)
const isExist = ref(false)
const endContract = async () => {
  const { data } = await checkContractBillExisted({
    contractId: props.id
  })
  // 如果data为true 说明是存在有效账单
  isExist.value = data
  if (data) {
    stopShow.value = true
  } else {
    stopShow.value = true
  }
}
const { proxy } = getCurrentInstance()
// 二次确认终止合同
const submitStopContract = async () => {
  const result = await stopContract({
    contractId: props.id
  })
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`终止合同成功!`)
    // 关闭合同详情
    handleClose()
    emits('on-terminate')
  } else {
    proxy.$modal.msgSuccess(`终止合同失败!`)
  }
}

// 合同续签
const contractRenewal = () => {
  emits('on-change', props.id, detail.value.type, true)
  handleClose()
}
onMounted(() => {
  if (props.id) {
    // 获取详情接口
    getContractDetail(props.id)
  }
})
</script>
<style lang="scss" scoped>
.my-filelist-line {
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
}

:deep(.el-select) {
  width: 100%;
}
.f-al {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  .right-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
  }
}

.title {
  font-size: 18px;
  color: black;
  margin-right: 8px;
  margin-left: 8px;
}
.review {
  flex: 1;
  margin-left: 8px;
}
.title-small {
  font-size: 16px;
  margin-left: 10px;
  color: black;
}

.container {
  display: flex;
  .left {
    flex: 1;
    margin-right: 16px;
  }
  .right {
    flex: 1;
  }
}

.download {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #409eff;
  cursor: pointer;
}

:deep(.el-collapse-item__content) {
  line-height: normal;
}
.collapse {
  margin-top: 40px;
}

.p-t {
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
  cursor: pointer;
}
.text-color {
  font-size: 16px;
  // font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #2383e7ff;
}
.text {
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #b2b5b9;
}
.cursor-point {
  cursor: pointer;
  vertical-align: middle;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .footer-left {
    flex: 1;
    align-items: center;
    display: flex;
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: #6a7697;
  }
}

.m-r {
  margin-right: 8px;
}
.el-alert {
  background: rgba(255, 204, 54, 0.2);
  border-radius: 4px;
  margin-bottom: 12px;
  :deep(.el-alert__title) {
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: #333333;
  }
}
</style>

<style>
#iframe {
  @media print {
    display: block;
  }
}
</style>
