<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-10-25 08:21:21
 * @LastEditTime: 2024-01-30 09:36:56
 * @LastEditors: thb
-->
<template>
  <workIndex :columns="columns" :requestApi="getWorkListByToDo" workType="todo" orderStatus="0" :go="go" />
</template>
<script setup>
import workIndex from './my-create.vue'
import { getWorkListByToDo } from '@/api/work/work'

const props = defineProps({
  go: {
    type: Boolean,
    default: false
  }
})

const columns = [
  {
    prop: 'orderTypeName',
    label: '工单类型',
    minWidth: 300,
    align: 'left'
  },
  {
    prop: 'createBy',
    minWidth: 200,
    label: '发起人',
    align: 'left'
  },
  {
    prop: 'createTime',
    minWidth: 200,
    label: '发起时间',
    align: 'left'
  },
  {
    prop: 'isUrgent',
    minWidth: 100,
    enum: [
      {
        value: '0',
        label: '一般'
      },
      {
        value: '1',
        label: '紧急'
      }
    ],
    label: '紧急状态',
    align: 'left'
  }
]
</script>
<style lang="scss" scoped></style>
