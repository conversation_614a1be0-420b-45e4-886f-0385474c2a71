<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-06-14 13:50:04
 * @LastEditTime: 2023-11-03 09:22:17
 * @LastEditors: thb
-->
<!-- 文件导入状态 -->
<template>
  <ProTable ref="proTable" title="导入进度" :columns="columns" :request-api="fileImportList">
    <template #action="{ row }">
      <el-button type="primary" v-if="row.status === 2" link @click="handleCheck(row)">失败原因</el-button>
      <!-- todo 此处其实不只是customer:import-process:export对应的下载，但每一项都填也不对 -->
      <el-button
        v-hasPermi="['customer:import-process:export']"
        v-if="row.opType === 'download' && row.status === 1"
        @click="downloadFile(row.fileName)"
        >下载文件</el-button
      >
    </template>
    <template #opType="{ row }">
      <span>
        {{ row.opType === 'download' ? '导出' : '导入' }}
      </span>
    </template>
  </ProTable>

  <el-dialog align-center width="400" title="失败原因" :close-on-click-modal="false" v-model="visible">
    <el-form ref="formRef" :model="formData" label-position="top">
      <el-form-item prop="discardReason">
        <el-input v-model="formData.reason" disabled type="textarea" :autosize="{ minRows: 6, maxRows: 10 }" />
      </el-form-item>
    </el-form>
    <!-- <template #footer>
      <el-button @click="visible = false">关闭</el-button>
    </template> -->
  </el-dialog>
</template>
<script setup lang="tsx">
import { ColumnProps } from '@/components/ProTable/interface'
import { fileImportList } from '@/api/customer/file'
import { getFileUrlByOss } from '@/api/file/file.js'
// 下载文件
const downloadFile = async fileName => {
  let { data } = await getFileUrlByOss(fileName)
  window.open(data)
}
const statusMap = {
  0: '',
  1: 'success',
  2: 'danger'
}
const textMap = {
  0: '进行中',
  1: '成功',
  2: '失败'
}
const columns: ColumnProps<any>[] = [
  {
    prop: 'fileName',
    label: '文件名称',
    search: { el: 'input' }
  },
  {
    prop: 'opType',
    label: '类型',
    width: 100
  },
  {
    prop: 'createBy',
    label: '操作者',
    width: 150
  },
  {
    prop: 'createTime',
    label: '操作时间',
    width: 200,
    search: {
      el: 'date-picker',
      props: { type: 'date', valueFormat: 'YYYY-MM-DD' }
    }
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    enum: [
      {
        label: '进行中',
        value: 0
      },
      {
        label: '成功',
        value: 1
      },
      {
        label: '失败',
        value: 2
      }
    ],
    search: { el: 'select' },
    render: scope => {
      return <el-tag type={statusMap[scope.row.status]}>{textMap[scope.row.status]}</el-tag>
    }
  },
  {
    prop: 'action',
    label: '操作',
    width: 200
  }
]
// 查看错误原因
const visible = ref(false)
const formData = ref({})
const handleCheck = row => {
  visible.value = true
  formData.value.reason = row.reason
}
</script>
<style lang="scss" scoped></style>
