<!--
 * @Description: 新增地址申请
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-12-04 08:46:31
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="isDisabled">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="企业名称" prop="customerName">
          <div style="width: 100%; cursor: pointer" @click="listShow = true">
            <el-input v-model="formData.customerName" maxlength="20" readonly disabled />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="申请区域" prop="supplier">
          <el-select v-model="formData.supplier" disabled clearable placeholder=" ">
            <el-option
              :label="item.supplier"
              :value="item.id"
              v-for="(item, index) in addressList"
              :disabled="item.enable === '0'"
              :key="index"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="关联账单" prop="paymentNo">
          <el-input v-model="formData.paymentNo" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="提交时间" prop="createTime">
          <el-input v-model="formData.createTime" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="formData.remark" disabled maxlength="1000" />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 审批通过还是不通过 -->
    <el-row :gutter="24" v-if="!isDisabled">
      <el-col :span="24">
        <el-form-item label="审批结果">
          <el-radio-group v-model="formData.status" @change="changeClear" :disabled="isDisabled">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 通过的详情多余表单项 -->
    <template v-if="(formData.status === '1' || formData.status === '2') && isDisabled">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="审批人" prop="reviewPerson">
            <el-input v-model="formData.reviewPerson" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审批时间" prop="reviewTime">
            <el-input v-model="formData.reviewTime" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    <template v-if="formData.status === '1'">
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="详细地址" prop="address">
            <el-input
              v-model="formData.address"
              :disabled="isDisabled"
              maxlength="1000"
              :placeholder="isDisabled ? ' ' : '请输入'"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="地址成本" prop="addressCost">
            <NumberInput v-model="formData.addressCost" :disabled="isDisabled" :placeholder="isDisabled ? '' : '请输入'">
              <template #suffix> 元 </template>
            </NumberInput>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="毛利" prop="grossProfit">
            <NumberInput v-model="grossProfit" disabled placeholder=" ">
              <template #suffix> 元 </template>
            </NumberInput>
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <!-- 不通过的选择项 -->
    <template v-if="formData.status === '2'">
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="不通过原因" prop="reason">
            <el-input
              v-model="formData.reason"
              :disabled="isDisabled"
              maxlength="1000"
              :placeholder="isDisabled ? ' ' : '请输入'"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
          /></el-form-item>
        </el-col>
      </el-row>
    </template>
  </el-form>
</template>

<script setup lang="tsx">
import { reactive } from 'vue'
import NumberInput from '@/components/NumberInput'
import { getAddressProviderList } from '@/api/address-provider'
const { proxy } = getCurrentInstance()
const { fee_type } = proxy.useDict('fee_type')
const formData = reactive({
  customerName: '',
  customerId: '',
  address: '',
  supplier: '',
  addressCost: '',
  reviewPerson: '',
  reviewTime: '',
  status: '',
  paymentNo: '',
  paymentId: '',
  grossProfit: '',
  supplierId: '',
  reason: '',
  paymentAmount: '',
  id: undefined,
  createTime: '',
  remark: ''
})

const grossProfit = computed(() => {
  if (!formData.addressCost) {
    return ''
  } else {
    return formData.paymentAmount - formData.addressCost
  }
})
watch(grossProfit, () => {
  formData.grossProfit = grossProfit.value
})
const isDisabled = ref(false)
watch(
  formData,
  () => {
    if (formData.isDisabled) {
      isDisabled.value = true
    }
  },
  {
    immediate: true
  }
)

const rules = {
  address: [{ required: true, message: '请输入', trigger: 'blur' }],
  reason: [{ required: true, message: '请输入', trigger: 'blur' }],
  addressCost: [{ required: true, message: '请输入', trigger: 'blur' }]
}
const addressList = ref([])
const getAddressProviders = async () => {
  const { data } = await getAddressProviderList({
    page: 1,
    pageSize: 1000,
    enable: '1'
  })
  addressList.value = data.records || []
}
getAddressProviders()

const changeClear = value => {
  formData.address = ''
  formData.reason = ''
  formData.addressCost = ''
}
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
:deep(.el-date-editor) {
  width: 100%;
}
</style>
