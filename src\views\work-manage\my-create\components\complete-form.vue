<!--
 * @Description: 指派工单表单
 * @Author: thb
 * @Date: 2023-07-19 08:41:17
 * @LastEditTime: 2023-12-20 08:43:21
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="完成反馈" prop="feedbackOrDescription">
          <el-input
            v-model="formData.feedbackOrDescription"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 6 }"
            maxlength="1000"
            placeholder=" 请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 清税证明 -->
    <el-row :gutter="24" v-if="formData.orderTypeName === '税务注销'">
      <el-col :span="24">
        <el-form-item label="清税证明" prop="taxClearanceList">
          <FileUpload
            :isShowTip="false"
            v-model="formData.taxClearanceList"
            :limit="100"
            @on-load-success="validateLoadSuccess('taxClearanceList')"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="相关附件">
          <ImageUpload v-model="formData.fileList" :isShowTip="false" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import ImageUpload from '@/components/ImageUpload'
import FileUpload from '@/components/FileUpload'
const formData = ref({
  fileList: [],
  orderTypeName: '', // 工单类型
  feedbackOrDescription: ''
})

const rules = {
  feedbackOrDescription: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  taxClearanceList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

const validateLoadSuccess = field => {
  console.log('field', field)
  formRef.value.validateField(field)
}
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped>
.el-select.el-select--default {
  width: 100%;
}
</style>
