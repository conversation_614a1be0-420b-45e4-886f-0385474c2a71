<template>
  <ProTable
    :init-param="initParam"
    ref="proTable"
    title="内部交接"
    row-key="id"
    :columns="columns"
    :request-api="materialHandoverRecordList"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.handoverStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
    </template>
    <template #handoverNo="{ row }">
      <span class="blue-text" @click="handlDetail(row)">{{ row.handoverNo }}</span>
    </template>
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #detailList="{ row }">
      <span v-for="(item, index) in row.detailList" :key="item.id">
        <span>{{ item.category }}</span>
        <span v-if="index < row.detailList.length - 1">，</span>
      </span>
    </template>
    <template #handoverStatus="{ row }">
      <span :class="[statusMap[row.handoverStatus]?.['class']]">{{ statusMap[row.handoverStatus]?.['label'] }}</span>
    </template>
    <template #action="{ row }">
      <el-button
        type="primary"
        link
        v-if="row.handoverStatus === 'pending' && row.recipientUserId === userStore?.user?.userId"
        @click="confirmReceipt(row)"
        >确认接受</el-button
      >
    </template>
  </ProTable>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal
    v-if="formModalShow"
    :confirmFlag="confirmFlag"
    :handoverStatus="handoverStatus"
    ref="formModalRef"
    @close="formModalClose"
    @ok="getList"
  />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { materialHandoverRecordList } from '@/api/customer/material.js'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from './components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
import { useDic } from '@/hooks/useDic'
import useCommonStore from '@/store/modules/common'
import { CirclePlus } from '@element-plus/icons-vue'

const userStore = useUserStore()
// console.log('userStore?.user?.userId', userStore?.user?.userId)
const useCommon = useCommonStore()
const { getDic } = useDic()
const { proxy } = getCurrentInstance()

const tabs = ref([
  {
    dictLabel: '全部',
    dicValue: ''
  },
  {
    dictLabel: '待接收',
    dicValue: 'pending'
  },
  {
    dictLabel: '已完成',
    dicValue: 'accept'
  },
  {
    dictLabel: '拒绝接受',
    dicValue: 'refuse'
  }
])

const statusMap: any = {
  pending: {
    label: '待接收',
    class: 'default-status'
  },
  accept: {
    label: '已完成',
    class: 'success-status'
  },
  refuse: {
    label: '拒绝接受',
    class: 'danger-status'
  }
}
const handleRadioChange = value => {
  proTable.value.pageable.pageNum = 1
  initParam.handoverStatus = value
}
const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}
const initParam = reactive({ handoverStatus: '' })
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'handoverNo',
    label: '交接单号',
    minWidth: 150
  },
  {
    prop: 'customerName',
    label: '企业名称',
    search: { el: 'input' },
    minWidth: 300
  },
  {
    prop: 'customerNo',
    label: '企业编号',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'operatorUserName',
    label: '创建人',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'recipientUserName',
    label: '接收人',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'detailList',
    label: '交接内容',
    minWidth: 280
  },
  {
    prop: 'operateTime',
    label: '创建时间',
    width: 180
  },
  {
    prop: 'handoverStatus',
    label: '交接状态',
    width: 180
  },
  {
    prop: 'action',
    label: '操作',
    width: 150
  }
]

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
  handoverStatus.value = ''
  confirmFlag.value = false
}

const handleAdd = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onAdd()
  })
}

const handoverStatus = ref('')
const handlDetail = (row: any) => {
  formModalShow.value = true
  handoverStatus.value = row.handoverStatus
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}

const confirmFlag = ref(false)
const confirmReceipt = (row: any) => {
  formModalShow.value = true
  confirmFlag.value = true
  handoverStatus.value = row.handoverStatus
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

// onMounted(() => {
// })

watch(
  () => useCommon.todoTaskFlag,
  () => {
    if (useCommon.todoTaskFlag) {
      nextTick(async () => {
        // 接收人为账户使用者
        proTable.value.searchParam.recipientUserName = userStore?.user?.nickName
        // handleRadioChange('pending')
        initParam.handoverStatus = 'pending'
        await proTable.value?.requestResult
        proTable.value?.search()

        useCommon.clearTodoTaskFlag()
      })
    }
  },
  {
    immediate: true
  }
)
</script>
<style lang="scss" scoped>
.default-status {
  color: #409eff;
}

.danger-status {
  color: #f56c6c;
}

.success-status {
  color: #0ec27f;
}
</style>
