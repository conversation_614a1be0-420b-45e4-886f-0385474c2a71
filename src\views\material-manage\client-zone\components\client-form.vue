<template>
  <el-dialog
    align-center
    width="800"
    :title="type === 'add' ? '新增客户' : '编辑客户'"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <el-steps :active="active" align-center finish-status="success">
      <el-step title="步骤1" />
      <el-step title="步骤2" />
    </el-steps>

    <el-form ref="stepOneRef" v-show="active === 0" :model="formData" :rules="rules" label-position="top">
      <el-row :gutter="24" justify="center">
        <el-col :span="6">
          <el-form-item label="选择目标公海" prop="seaId">
            <el-select v-model="formData.seaId" placeholder="请选择" clearable>
              <el-option
                v-for="item in seaOptions"
                :key="item.id"
                :disabled="includeDept(item.deptIds)"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <ClientForm ref="clientFormRef" v-show="active === 1" />

    <template #footer>
      <template v-if="active === 0">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleStepAfter" pain>下一步</el-button>
      </template>
      <template v-if="active === 1">
        <el-button type="primary" @click="handleStepBefore" pain>上一步</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </template>
    </template>
  </el-dialog>
</template>
<script setup>
import { getZoneSettingList } from '@/api/material-manage/zone-setting'
import ClientForm from '../../client-manage/components/form.vue'
import { saveClue } from '@/api/material-manage/clue'
import { useDept } from '@/hooks/useDept'
const { includeDept } = useDept()

const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success', 'on-edit'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number,
  type: {
    type: String,
    default: 'add' // 'add'表示新增 'update'表示编辑
  }
})
const active = ref(0)

const formData = ref({})
const rules = {
  seaId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
// 获取公海列表
const seaOptions = ref([])
const getSeaOptions = async () => {
  const { data } = await getZoneSettingList({
    pageSize: 1000,
    pageNum: 1,
    status: '1',
    type: '1'
  })
  seaOptions.value = data.records || []
}
getSeaOptions()
// 执行下一步
const stepOneRef = ref()
const handleStepAfter = async () => {
  // 执行下一步之前先校验
  const result = await stepOneRef.value.validate()
  if (result) {
    active.value = 1
  }
}

const handleStepBefore = () => {
  active.value = 0
}
const clientFormRef = ref()
const { proxy } = getCurrentInstance()
const handleSubmit = () => {
  clientFormRef.value.getFormRef().validate(async valid => {
    if (valid) {
      const result = await saveClue({
        ...clientFormRef.value.formData,
        entryType: '1', // 公海录入
        type: '1', //代表客户
        seaId: formData.value.seaId,
        tags: clientFormRef.value.formData.tags.map(item => {
          return {
            tagId: item
          }
        })
      })
      if (result.code === 200) {
        proxy.$modal.msgSuccess(`保存成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`保存失败!`)
      }
    }
  })
}
</script>
<style lang="scss" scoped></style>
