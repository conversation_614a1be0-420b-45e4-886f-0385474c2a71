<template>
  <div class="my-form">
    <el-steps v-if="!route.query.id" :active="active" align-center finish-status="success">
      <el-step title="步骤1" description="基础信息" />
      <el-step title="步骤2" description="模板制作" />
      <el-step title="步骤3" description="模板预览" />
    </el-steps>
    <div class="detail-info" v-if="route.query.id">
      <h3>{{ formData.tempName }}</h3>
    </div>
    <!-- formData.tempName-{{ formData.tempName }} -->
    <div class="my-step" v-show="active === 0 || route.query.id">
      <el-form
        style="width: 100%"
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="top"
        :disabled="route.query.id"
        :hide-required-asterisk="route.query.id"
      >
        <el-row :gutter="24">
          <el-col :span="8"
            ><el-form-item label="模板名称" prop="tempName"> <el-input v-model="formData.tempName"></el-input> </el-form-item
          ></el-col>
          <el-col :span="8"
            ><el-form-item label="合同类型" prop="contractType">
              <el-select style="width: 100%" v-model="formData.contractType">
                <el-option v-for="item in contractTypeArr" :key="item.value" :label="item.label" :value="item.value" />
              </el-select> </el-form-item
          ></el-col>
          <el-col :span="8"
            ><el-form-item label="审批流程" prop="flowId">
              <el-select style="width: 100%" v-model="formData.flowId">
                <el-option v-for="item in contractReviewList" :key="item.id" :label="item.contractTypeName" :value="item.id" />
              </el-select> </el-form-item
          ></el-col>
          <el-col :span="24"
            ><el-form-item label="备注" prop="remark"> <el-input v-model="formData.remark"></el-input> </el-form-item
          ></el-col>
          <el-col :span="8" v-if="!route.query.id"
            ><el-form-item label="上传文件" prop="fileTemp">
              <FileUpload v-model="formData.fileTemp" :isShowTip="false" /> </el-form-item
          ></el-col>
        </el-row>
      </el-form>
    </div>
    <div class="my-step" v-show="[1, 2].includes(active) || route.query.id">
      <div class="left">
        <!-- 如果加载速度慢，就在切换到这步前提前加载 -->
        <div v-if="route.query.id" class="my-office-docx" v-html="formData.htmlStr"></div>
        <vue-office-docx v-else ref="docxRef" class="my-office-docx" :src="docx" />
      </div>
      <div class="right my-office-form" v-if="!route.query.id">
        <el-form :model="fieldForm" :disabled="active !== 1 || route.query.id">
          <el-table :data="fieldForm" max-height="700">
            <el-table-column label="字段名称" width="300" align="center">
              <template #default="scope">
                <el-form-item>
                  <el-select
                    style="width: 80%"
                    :disabled="scope.row.disabledCode"
                    v-model="scope.row.fieldName"
                    @change="e => handleChangeFieldFormItem(scope, e)"
                  >
                    <el-option
                      v-for="item in fieldList"
                      :disabled="
                        item.disabled ||
                        (item.type === 'select' && fieldForm.findIndex(itemx => itemx.fieldName === item.fieldName) !== -1)
                      "
                      :key="item.fieldName"
                      :label="item.fieldName"
                      :value="item.fieldName"
                    />
                  </el-select>
                  <el-icon
                    v-if="['select', 'select_text'].includes(scope.row.type) && active === 1"
                    class="el-icon"
                    style="margin-left: 5px; cursor: pointer"
                    @click="handleAddFormItemSelect(scope)"
                    ><CirclePlus
                  /></el-icon>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column v-if="!route.query.id && active === 1" label="操作" width="120" align="center">
              <template #default="scope"
                ><el-form-item>
                  <span v-if="currentMode === 'development'">disabledCode-{{ scope.row.disabledCode }}</span>
                  <el-button
                    v-if="![2, 4].includes(scope.row.disabledCode)"
                    type="primary"
                    link
                    @click="handleConfirmFieldItem(scope)"
                    >确定</el-button
                  >
                  <el-button v-if="![3, 4].includes(scope.row.disabledCode)" type="danger" link @click="handleDelFormItem(scope)"
                    >删除</el-button
                  ></el-form-item
                >
              </template>
            </el-table-column>
          </el-table>
        </el-form>
        <template v-if="active === 1">
          <el-button style="margin-top: 10px" @click="handleAddFormItem">添加字段</el-button>
          <!-- <el-button @click="handleLogForm">打印表单数据</el-button> -->
          <!-- <el-button @click="handleLogHtml">打印HTML数据</el-button> -->
          <!-- <el-alert title="选择下拉字段后，在左侧选中字段对应位置，并点击“确定”按钮" type="warning" /> -->
        </template>
      </div>
      <div class="right my-office-form" v-if="route.query.id">
        <el-table :data="fieldList" max-height="700">
          <el-table-column label="字段名称" width="300" align="center" prop="fieldName"> </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="form-submit-btn">
      <template v-if="route.query.id">
        <el-button type="danger" @click="handleDelete">删除</el-button>
        <el-button @click="handleCancel">返回</el-button>
      </template>
      <template v-else>
        <el-button type="primary" @click="handleStepPre" v-if="active > 0">上一步</el-button>
        <el-button type="primary" @click="handleStepAfter" v-if="active < 2">下一步</el-button>
        <el-button @click="handleReset" v-if="active === 0">清空</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="active === 2" :loading="loading">保存</el-button>
        <el-button @click="handleCancel" v-if="active === 2">取消</el-button>
      </template>
    </div>
  </div>
</template>

<script setup>
import { getContractReviewList } from '@/api/process/process.js'
import {
  getContractTempGetFieldList,
  postContractTempSaveOrUpdate,
  getContractTempGetById,
  deleteContractTempDelete
} from '@/api/contract-template/contract-template.js'
import VueOfficeDocx from '@vue-office/docx'
import '@vue-office/docx/lib/index.css'
import { nextTick, onMounted, reactive, watch } from 'vue'
import { getFileUrlByOss } from '@/api/file/file.js'
import { useRoute, useRouter } from 'vue-router'
import { contractTypeArr } from '@/utils/constants.js'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const currentMode = import.meta.env.MODE

const contractReviewList = ref([])
function onGetContractReviewList() {
  getContractReviewList({ type: 0, enable: 1 }).then(res => {
    contractReviewList.value = res.data.records
  })
}
onGetContractReviewList()

const fieldCodeChange = [
  // {
  //   fieldCode: 'contractName',
  //   disabled: true
  // },
  {
    fieldCode: 'productId',
    disabled: true
  },
  {
    fieldCode: 'monthNum',
    disabled: true
  },
  {
    fieldCode: 'ciId',
    type: 'text',
    num: 2 // 甲方有两处要填
  },
  {
    fieldCode: 'customerNo',
    disabled: true,
    type: 'text'
  },
  {
    fieldCode: 'remark',
    type: 'text',
    placeholderStr: '_'
  },
  {
    fieldCode: 'otherRemark',
    placeholderStr: '_'
  },
  {
    fieldCode: 'declare',
    type: 'select'
  },
  {
    fieldCode: 'isEstablish',
    type: 'select'
  },
  {
    fieldCode: 'payrollService',
    type: 'select'
  }
  // {
  //   fieldName: '其他费用',
  //   fieldCode: 'otherCost'
  // }
]
const fieldCodeExtra = new Map()
fieldCodeExtra.set('otherSalesText', {
  fieldName: '销售收入 - 其他',
  fieldCode: 'otherSalesText'
})
fieldCodeExtra.set('totalCost', {
  fieldName: '合同总金额',
  fieldCode: 'totalCost'
})
fieldCodeExtra.set('serviceCostUnit', {
  fieldName: '服务费单位',
  fieldCode: 'serviceCostUnit'
})
fieldCodeExtra.set('contactAddress', {
  // 合同逻辑在某次修改后舍弃了显示的该项填写，但是逻辑上还是需要的，在这里补上
  fieldName: '办理人联系地址',
  fieldCode: 'contactAddress'
})
// fieldCodeExtra.set('softwareFeeUnit', {
//   fieldName: '账册软件费单位',
//   fieldCode: 'softwareFeeUnit'
// })
fieldCodeExtra.set('productOneOff', {
  fieldName: '办理业务',
  fieldCode: 'productOneOff',
  disabledCode: 2,
  type: 'selectSpecial'
})
fieldCodeExtra.set('productOneOffOption', [
  {
    fieldName: '注册业务',
    fieldCode: 'productOneOffOption_register',
    disabledCode: 3,
    type: 'selectOption',
    extraType: 'no_del'
  },
  {
    fieldName: '注册业务-备注',
    fieldCode: 'productOneOffOption_remark_register',
    disabledCode: 3,
    type: 'text',
    extraType: 'no_del',
    placeholderStr: '_'
  },
  {
    fieldName: '变更业务',
    fieldCode: 'productOneOffOption_change',
    disabledCode: 3,
    type: 'selectOption',
    extraType: 'no_del'
  },
  {
    fieldName: '变更业务-备注',
    fieldCode: 'productOneOffOption_remark_change',
    disabledCode: 3,
    type: 'text',
    extraType: 'no_del',
    placeholderStr: '_'
  },
  {
    fieldName: '注销业务',
    fieldCode: 'productOneOffOption_cancel',
    disabledCode: 3,
    type: 'selectOption',
    extraType: 'no_del'
  },
  {
    fieldName: '注销业务-备注',
    fieldCode: 'productOneOffOption_remark_cancel',
    disabledCode: 3,
    type: 'text',
    extraType: 'no_del',
    placeholderStr: '_'
  },
  {
    fieldName: '许可证业务',
    fieldCode: 'productOneOffOption_licence',
    disabledCode: 3,
    type: 'selectOption',
    extraType: 'no_del'
  },
  {
    fieldName: '许可证业务-备注',
    fieldCode: 'productOneOffOption_remark_licence',
    disabledCode: 3,
    type: 'text',
    extraType: 'no_del',
    placeholderStr: '_'
  },
  {
    fieldName: '银行业务',
    fieldCode: 'productOneOffOption_bank',
    disabledCode: 3,
    type: 'selectOption',
    extraType: 'no_del'
  },
  {
    fieldName: '银行业务-备注',
    fieldCode: 'productOneOffOption_remark_bank',
    disabledCode: 3,
    type: 'text',
    extraType: 'no_del',
    placeholderStr: '_'
  },
  {
    fieldName: '其他业务',
    fieldCode: 'productOneOffOption_other',
    disabledCode: 3,
    type: 'selectOption',
    extraType: 'no_del'
  },
  {
    fieldName: '其他业务-备注',
    fieldCode: 'productOneOffOption_remark_other',
    disabledCode: 3,
    type: 'text',
    extraType: 'no_del',
    placeholderStr: '_'
  }
])

const fieldList = ref([])

const rules = {
  tempName: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  flowId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  fileTemp: [{ required: true, message: '请上传', trigger: ['blur', 'change'] }],
  contractType: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
}

const formRef = ref(null)
const active = ref(0)
const formData = reactive({
  tempName: undefined,
  contractType: undefined,
  flowId: undefined,
  remark: undefined,
  fileTemp: undefined,
  // status: 1, // 临时通过
  enable: 1
})
const initItem = {
  fieldName: undefined,
  fieldCode: undefined,
  type: 'text',
  originDiv: undefined,
  disabledCode: 0 // 0 1下拉框option不可更改 2不可确定
  // disabledCode: 0 // 0 1下拉框option不可更改 2不可确定 3不可删除 4不可确定且不可删除
  // showConfirm: true,
  // showDel: true,
  // disabled showConfirm showDel 也麻烦
}
const fieldForm = reactive([Object.assign({}, initItem)])
const docxRef = ref(null)
// let docx = 'https://501351981.github.io/vue-office/examples/dist/static/test-files/test.docx'
let docx = ''

function getDetail() {
  if (route.query.id) {
    getContractTempGetById({ id: route.query.id }).then(res => {
      Object.assign(formData, res.data)
    })
  }
}
getDetail()

function handleAddFormItem() {
  fieldForm.push(Object.assign({}, initItem))
}
function handleChangeFieldFormItem(scope, e) {
  const temp = fieldList.value.find(item => item.fieldName === e)
  fieldForm[scope.$index] = Object.assign(scope.row, temp)
  console.log('handleChangeFieldFormItem', scope.row, e)
  if (scope.row.type === 'select' && !scope.row.fieldName.includes(' - 选项')) {
    scope.row.disabledCode = 2 // 0 -> 2
  }
  if (scope.row.fieldName === '办理业务') {
    fieldForm.splice(scope.$index + 1, 0, ...fieldCodeExtra.get('productOneOffOption'))
  }
  // console.log('fieldForm', fieldForm)
}
function handleAddFormItemSelect(scope) {
  // 删除后新增的优化选项几 --> 新增option后时前列状态1的全部变为状态3
  // 选项隔一个如何新增到对应位置 --> 找到最新一个自己的option然后获取index、splice
  const fieldNameTemp = scope.row.fieldName.split(' - ')[0]
  // console.log('handleAddFormItemSelect', scope.row, fieldForm, fieldNameTemp)
  // 为了区分开“销售收入-其他”，不属于“销售收入”一般选项的绑定arr
  const arr = fieldForm.filter(item => item?.fieldName?.includes(fieldNameTemp) && !item?.fieldName?.includes('其他'))
  const indexLast = fieldForm.findIndex(item => item === arr[arr.length - 1]) // 获取select最后一项在fieldForm中的位置
  console.log('handleAddFormItemSelect', JSON.parse(JSON.stringify(arr)), indexLast)
  arr.forEach(item => {
    if (indexLast === 0) {
      // 选项本身变为状态4
      item.disabledCode = 4 // 2 -> 4
    } else {
      if (item.disabledCode === 1) {
        item.disabledCode = 3 // 1 -> 3
      }
      if (item.disabledCode === 2) {
        item.disabledCode = 4 // 2 -> 4
      }
    }
  })
  const rowTemp = Object.assign({}, scope.row, {
    originDiv: undefined,
    fieldName: arr[0].fieldName + ` - 选项${arr.length}`,
    fieldCode: arr[0].fieldCode + `_${arr.length}`,
    disabledCode: 1,
    type: 'selectOption'
  })
  fieldForm.splice(indexLast + 1, 0, rowTemp)
  // console.log('arr', arr)
  // console.log('fieldForm', fieldForm)
}

function handleDelFormItem(scope) {
  const spans = docxRef.value.$el.getElementsByTagName('span') // spans可能不支持filter
  const spanTempArr = []
  if (['selectSpecial'].includes(scope.row.type)) {
    for (const span of spans) {
      if (fieldCodeExtra.get('productOneOffOption').some(item => span.getAttribute('fieldCode') === item.fieldCode)) {
        spanTempArr.push(span)
      }
    }
    for (const span of spanTempArr) {
      for (const item of fieldCodeExtra.get('productOneOffOption')) {
        if (span.getAttribute('fieldCode') === item.fieldCode) {
          fieldForm.find(itemx => itemx.fieldCode === span.getAttribute('fieldCode')).disabledCode = 3
          span.outerHTML = fieldForm.find(itemx => itemx.fieldCode === span.getAttribute('fieldCode')).originDiv
        }
      }
    }
    fieldForm.splice(scope.$index, fieldCodeExtra.get('productOneOffOption').length + 1) // +1是因为包括上级
    return
  }
  for (const span of spans) {
    if (span.getAttribute('fieldCode') === fieldForm[scope.$index].fieldCode) {
      spanTempArr.push(span)
    }
  }
  for (const span of spanTempArr) {
    // console.log('handleDelFormItem-span', span, fieldForm[scope.$index], scope.row) //  fieldForm[scope.$index] 等价于 scope.row
    if (parseInt(span.getAttribute('fieldTimeStamp')) === fieldForm[scope.$index].fieldTimeStamp) {
      span.outerHTML = fieldForm[scope.$index].originDiv
    }
  }
  if (['select', 'selectOption'].includes(scope.row.type)) {
    const fieldNameTemp = scope.row.fieldName.split(' - ')[0]
    const arr = fieldForm.filter(item => item?.fieldName?.includes(fieldNameTemp) && !item?.fieldName?.includes('其他'))
    console.log('handleDelFormItem', JSON.parse(JSON.stringify(arr)), arr.length - 1)
    if (arr.length > 2) {
      // 代表删去后至少剩选项一
      if (arr[arr.length - 2].disabledCode === 4) {
        // 不可确定且不可删除
        arr[arr.length - 2].disabledCode = 2 // 4 -> 2
      }
      if (arr[arr.length - 2].disabledCode === 3) {
        // 未确定且不可删除
        arr[arr.length - 2].disabledCode = 1 // 3 -> 1
      }
    } else if (arr.length > 1) {
      // 代表删去后剩选项本身
      arr[arr.length - 2].disabledCode = 2 // 4 -> 2
    }
  }
  fieldForm.splice(scope.$index, 1)
}
function handleConfirmFieldItem(scope) {
  if (!fieldForm[scope.$index].fieldCode) {
    proxy.$modal.msgWarning(`请先选择字段名称`)
    return
  }
  const fieldTimeStamp = new Date().getTime()
  const selection = window.getSelection()
  if (!selection.rangeCount) return
  const range = selection.getRangeAt(0)
  console.log('range', range)
  // 检查是否有fieldCode属性
  // 当前所选内容的父节点 xx
  // 当前所选内容的父节点下的所有后代节点 xx
  // 当前所选内容正好有fieldCode或者内容里有fieldCode，转换为string后indexOf判断
  const fragment = range.cloneContents()
  const div = document.createElement('div')
  div.appendChild(fragment)
  const allElements = div.getElementsByTagName('*')
  const hasFieldCode = Array.from(allElements).some(function (element) {
    return element.hasAttribute('fieldCode')
  })
  if (hasFieldCode) {
    proxy.$modal.msgWarning(`此处已有绑定字段`)
    return
  }
  const selectedContent = range.cloneContents()
  const tempDiv = document.createElement('div')
  tempDiv.appendChild(selectedContent)
  fieldForm[scope.$index].originDiv = tempDiv.innerHTML // 原本选中的 DOM 内容
  fieldForm[scope.$index].fieldTimeStamp = fieldTimeStamp // 增加唯一标识，在表单相重复时重复区分删除的表单项
  const selectedText = range.toString()
  if (!selectedText) {
    proxy.$modal.msgWarning(`请在左侧合同中选中字段对应位置`)
    return
  }
  // □✔√☑
  let placeholderStr
  const span = document.createElement('span')
  span.style.backgroundColor = 'lightblue'
  span.style.color = 'black'
  if (['select', 'selectOption'].includes(scope.row.type)) {
    placeholderStr = fieldForm[scope.$index].placeholderStr || '□'
    span.textContent = placeholderStr
    span.setAttribute('fieldCode', fieldForm[scope.$index].fieldCode) // 添加 fieldCode 属性
    const fieldNameTemp = scope.row.fieldName.split(' - ')[0]
    const arr = fieldForm.filter(item => item?.fieldName?.includes(fieldNameTemp) && !item?.fieldName?.includes('其他'))
    const indexArr = arr.findIndex(item => item.fieldName === fieldForm[scope.$index].fieldName) // 获取select当前操作项在select_arr中的位置
    // console.log('handleConfirmFieldItem', JSON.parse(JSON.stringify(arr)), indexArr)
    if (['no_del'].includes(scope.row.extraType)) {
      fieldForm[scope.$index].disabledCode = 4 // 3 -> 4
    } else if (indexArr === arr.length - 1) {
      // select_arr的最后一项要显示删除
      fieldForm[scope.$index].disabledCode = 2 // 1 -> 2
    } else {
      fieldForm[scope.$index].disabledCode = 4 // 3 -> 4
    }
  } else {
    placeholderStr = fieldForm[scope.$index].placeholderStr || '__'
    span.textContent = placeholderStr.repeat(selectedText.length)
    span.setAttribute('fieldCode', fieldForm[scope.$index].fieldCode) // 添加 fieldCode 属性
    if (['no_del'].includes(scope.row.extraType)) {
      fieldForm[scope.$index].disabledCode = 4 // 3 -> 4
    } else {
      fieldForm[scope.$index].disabledCode = 2 // 1 -> 2
    }
  }
  span.setAttribute('fieldTimeStamp', fieldTimeStamp)
  range.deleteContents()
  range.insertNode(span)
  selection.removeAllRanges() // 移除选区
  selection.addRange(range) // 添加新的选区
}

function handleStepPre() {
  active.value--
}

function handleStepAfter() {
  // console.log('formData', JSON.stringify(formData))
  formRef.value.validate(async valid => {
    if (valid) {
      active.value++
    }
  })
}
function handleReset() {
  formRef.value.resetFields()
}

const loading = ref(false)
async function handleSubmit() {
  if (loading.value) return
  loading.value = true
  fieldForm.forEach(element => {
    element.originDiv = undefined
  })
  formData.fieldForm = fieldForm
  formData.htmlStr = docxRef.value.$el.innerHTML
  // console.log('handleSubmit', formData)
  // localStorage.setItem('contractTemplateFormData', JSON.stringify(formData))
  // localStorage.setItem('contractTemplateInnerHTML', JSON.stringify(docxRef.value.$el.innerHTML))
  formData.htmlStr = await convertImgToBase64InHtml(formData.htmlStr)
  postContractTempSaveOrUpdate(formData)
    .then(res => {
      if (res.code === 200) {
        proxy.$message.success('保存成功')
        // 暂时注释
        if (route.query.redirect) {
          router.push(route.query.redirect)
        } else {
          router.push('/contract-template/list')
        }
      }
    })
    .finally(() => {
      loading.value = false
    })
}
function handleCancel() {
  router.push('/contract-template/list')
}

function handleDelete() {
  proxy.$modal
    .confirm('是否确认删除该合同模板?')
    .then(function () {
      return deleteContractTempDelete(route.query.id).then(res => {
        if (res.code === 200) {
          proxy.$message.success('删除成功')
          router.replace('/contract-template/list')
        }
      })
    })
    .catch(() => {})
}

function overwriteArrayAWithArrayB(arrayA, arrayB) {
  const mapB = new Map(arrayB.map(item => [item.fieldCode, item]))
  return arrayA.map(itemA => {
    if (mapB.has(itemA.fieldCode)) {
      return { ...itemA, ...mapB.get(itemA.fieldCode) }
    }
    return itemA
  })
}

watch(
  () => formData.contractType,
  val => {
    if (!val) return
    getContractTempGetFieldList({ contractType: val }).then(res => {
      res.data = overwriteArrayAWithArrayB(res.data, fieldCodeChange)
      const index_totalCost = res.data.findIndex(item => item.fieldCode === 'totalCostCn')
      if (index_totalCost !== -1) {
        res.data[index_totalCost].fieldName = '合同总金额(大写)'
        res.data.splice(index_totalCost, 0, fieldCodeExtra.get('totalCost')) // 添在合同总金额（大写）的前面
      }
      const index_salesRevenue = res.data.findIndex(item => item.fieldCode === 'salesRevenue')
      if (index_salesRevenue !== -1) {
        res.data.splice(index_salesRevenue + 1, 0, fieldCodeExtra.get('otherSalesText'))
      }
      if (['0'].includes(formData.contractType)) {
        // contractType -- 0 记账合同 1 一次性合同 2 地址服务协议合同
        const index_serviceCost = res.data.findIndex(item => item.fieldCode === 'serviceCost')
        if (index_serviceCost !== -1) {
          res.data.splice(index_serviceCost + 1, 0, fieldCodeExtra.get('serviceCostUnit'))
        }
        // const index_softwareFee = res.data.findIndex(item => item.fieldCode === 'softwareFee')
        // if (index_softwareFee !== -1) {
        //   res.data.splice(index_softwareFee + 1, 0, fieldCodeExtra.get('softwareFeeUnit'))
        // }
      }
      if (['1'].includes(formData.contractType)) {
        res.data.splice(0, 0, fieldCodeExtra.get('productOneOff'))
      }
      if (['2'].includes(formData.contractType)) {
        const index_branchOffice = res.data.findIndex(item => item.fieldCode === 'branchOffice')
        if (index_branchOffice !== -1) {
          res.data.splice(index_branchOffice + 1, 0, fieldCodeExtra.get('contactAddress'))
        }
      }
      fieldList.value = res.data
    })
  }
  // { immediate: true }
)

watch(
  () => formData.fileTemp,
  async () => {
    if (formData.fileTemp && formData.fileTemp[0]?.url) {
      formRef.value.validateField('fileTemp')
      const { data } = await getFileUrlByOss(formData.fileTemp[0]?.url)
      docx = data
      // console.log('docx', docx)
    }
  }
)

watch(
  () => active.value,
  async val => {
    if (val === 1) {
      // await nextTick()
      // setTimeout(() => {
      //   docxRef.value.addEventListener('mousedown', function (e) {
      //     console.log('mousedown')
      //     mousedownX = e.pageX
      //   })
      //   docxRef.value.addEventListener('mouseup', function (e) {
      //     console.log('mouseup')
      //     const mouseupX = e.pageX
      //     const deltaX = mouseupX - mousedownX
      //     const fontSize = window.getComputedStyle(docxRef.value).fontSize
      //     const numOfChars = Math.floor(deltaX / parseFloat(fontSize))
      //     const str = 'x'.repeat(numOfChars)
      //     console.log(str) // log the string for testing
      //   })
      // }, 1000)
    }
  }
)

async function convertImgToBase64InHtml(htmlString) {
  const parser = new DOMParser()
  const htmlDoc = parser.parseFromString(htmlString, 'text/html')
  const imgTags = htmlDoc.getElementsByTagName('img')

  for (let img of imgTags) {
    const blobUrl = img.src

    const response = await fetch(blobUrl)
    const blob = await response.blob()

    const reader = new FileReader()
    reader.readAsDataURL(blob)

    await new Promise(resolve => {
      reader.onloadend = function () {
        const base64data = reader.result
        img.src = base64data
        resolve()
      }
    })
  }

  return htmlDoc.documentElement.outerHTML
}

// function handleMoveRow(index, direction) {
//   const newIndex = index + direction
//   if (newIndex >= 0 && newIndex < fieldForm.length) {
//     const itemToMove = fieldForm.splice(index, 1)[0]
//     fieldForm.splice(newIndex, 0, itemToMove)
//   }
// }

// 调试第二页使用
// onMounted(async () => {
//   Object.assign(formData, {
//     tempName: '1',
//     contractType: '1',
//     flowId: '1686543455852367874',
//     fileTemp: [
//       {
//         msg: '操作成功',
//         uploadBy: '若依',
//         uploadSize: '13',
//         fileName: '20240327090208A007_一次性合同 - 微调修正0728.docx',
//         code: 200,
//         newFileName: '20240327090208A007_一次性合同 - 微调修正0728.docx',
//         uploadTime: '2024-03-27 09:02:08',
//         url: '20240327090208A007_一次性合同 - 微调修正0728.docx',
//         originalFilename: '一次性合同 - 微调修正0728.docx',
//         name: '20240327090208A007_一次性合同 - 微调修正0728.docx',
//         fileNames: '20240327090208A007_一次性合同 - 微调修正0728.docx',
//         urls: '20240327090208A007_一次性合同 - 微调修正0728.docx'
//       }
//     ],
//     enable: 1
//   })
//   await nextTick()
//   // console.log('formData.value', formData.value)
//   if (formData.fileTemp && formData.fileTemp[0]?.url) {
//     const { data } = await getFileUrlByOss(formData.fileTemp[0]?.url)
//     docx = data
//     // console.log('docx', docx)
//   }
//   active.value++
// })
</script>

<style lang="scss" scoped>
.my-form {
  padding-top: 10px;
  // height: calc(100% - 56px);
  width: 1160px;
  margin: 0 auto;
}
.detail-info {
  width: 1160px;
  margin: 0 auto;
}
.my-step {
  width: 1160px;
  margin: 10px auto 0;
  display: flex;
  justify-content: center;
  .left {
    .my-office-docx {
      height: 780px;
      // padding: 10px;
      overflow-y: scroll;
      width: 860px;
      padding-bottom: 80px;
    }
  }
  .right {
  }
  .my-office-form {
    // height: 780px;
    // overflow-y: scroll;
    padding: 0 10px;
  }
}

.form-submit-btn {
  height: 56px;
  line-height: 56px;
  bottom: 0;
  right: 0;
  left: 0;
  position: fixed;
  z-index: 2;
  text-align: right;
  box-shadow: 0 -1px 2px rgb(0 0 0 / 3%);
  background: #fff;
  border-top: 1px solid #e8e8e8;
  padding: 0 24px;
}
</style>
