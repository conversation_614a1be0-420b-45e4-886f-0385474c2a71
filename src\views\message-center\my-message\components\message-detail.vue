<!--
 * @Description: 消息弹窗
 * @Author: thb
 * @Date: 2023-08-03 16:58:24
 * @LastEditTime: 2023-08-22 09:02:00
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center title="详情" width="800" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <template v-if="!isDelete">
      <p class="title">{{ detail?.title }}</p>
      <div class="tip">
        <el-icon><User /></el-icon>
        <span class="name">{{ detail.createBy }}</span>
        <span>发布时间：</span>
        <span>{{ detail.createTime }}</span>
      </div>
      <p v-html="detail?.content"></p>
      <template v-if="detail.notificationFiles?.length">
        <div class="text-tag">附件</div>
        <div class="download-text" v-for="(file, index) in detail.notificationFiles" :key="index" @click="handleDownload(file)">
          {{ file.fileNames }}
        </div>
      </template></template
    >
    <template v-else> 公告已删除 </template>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { getMessageNoticeDetail } from '@/api/message-center/message-center'
import { getFileUrlByOss } from '@/api/file/file.js'
import { setMessageIsRead } from '@/api/message-center/message-center'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: String,
  messageId: String
})

const detail = ref({})
const isDelete = ref(false)
// 获取message详情
const getDetail = async () => {
  const { data } = await getMessageNoticeDetail(props.id)
  if (data) {
    detail.value = data || {}
    // 查看详情后同时标记为已读
    await setMessageIsRead([props.messageId])
  } else {
    // 删除
    isDelete.value = true
    await setMessageIsRead([props.messageId])
  }
}
const handleDownload = async file => {
  const url = file?.urls
  console.log('ulr', file?.urls)
  if (url) {
    const { data } = await getFileUrlByOss(url)
    window.open(data)
  }
}
onMounted(() => {
  getDetail()
})
</script>
<style lang="scss" scoped>
.tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .name {
    flex: 1;
  }
}

.text-tag {
  font-size: 16px;
  font-weight: 600;
  position: relative;
  margin-left: 10px;
  margin-bottom: 24px;
  &::before {
    content: '';
    width: 4px;
    position: absolute;
    top: 0;
    left: -10px;
    bottom: 0;
    background: #409eff;
  }
}

.title {
  font-size: 18px;
  color: #333;
  font-weight: 600;
}
</style>
