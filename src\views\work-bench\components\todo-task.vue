<template>
  <div class="wrap-list">
    <div class="list-item" v-for="item in taskList" :key="item">
      <div class="title">
        <span class="name">
          {{ item.title }}
        </span>
        <span class="text" @click="checkDetail(item)">查看</span>
        <img src="@/assets/icons/arrow-right.png" @click="checkDetail(item)" class="img-icon" />
      </div>
      <div class="number">
        <div class="num">
          <span>
            {{ item.num }}
          </span>
          <span class="unit">项</span>
        </div>

        <img :src="item.icon" class="item-icon" />
      </div>
    </div>
  </div>
</template>
<script setup>
import { getTodoTask } from '@/api/bentch/bentch.js'
import orderIcon from '@/assets/icons/order-icon.png'
import handoverIcon from '@/assets/icons/handover-icon.png'
import cardIcon from '@/assets/icons/card-icon.png'
import riskIcon from '@/assets/icons/risk-icon.png'
import cleanIcon from '@/assets/icons/clean-icon.png'
import financeIcon from '@/assets/icons/finance-icon.png'
import contractIcon from '@/assets/icons/contract-icon.png'
import { useRouter } from 'vue-router'
import useCommonStore from '@/store/modules/common'
import { getWorkListByToDo } from '@/api/work/work'
import { materialHandoverRecordList } from '@/api/customer/material.js'
import { bizList } from '@/api/certificate/certificate'
import { riskCustomerAuditList } from '@/api/customer/risk.js'
import { riskCustomerLoseHandleList } from '@/api/customer/risk.js'
import { financeReviewRectifyList } from '@/api/finance-review/finance-review.js'
import { getContractReviewListByUser } from '@/api/contract/contract'
import useUserStore from '@/store/modules/user'
const userStore = useUserStore()
const commonStore = useCommonStore()
const router = useRouter()
const taskList = ref([
  {
    title: '工单待办',
    url: '/work-manage/my-deal',
    icon: orderIcon,
    num: 0
  },
  {
    title: '内部交接',
    url: '/customer/material-handover',
    icon: handoverIcon,
    num: 0
  },
  {
    title: '办证任务',
    url: '/certificate/my-list',
    icon: cardIcon,
    num: 0
  },
  {
    title: '风险审核',
    url: '/customer/risk-audit',
    icon: riskIcon,
    num: 0
  },
  {
    title: '流失清理',
    url: '/customer/risk-clear',
    icon: cleanIcon,
    num: 0
  },
  {
    title: '财务审核',
    url: '/finance-review/rectify',
    icon: financeIcon,
    num: 0
  },
  {
    title: '合同审批',
    url: '/contract-manage/contract-judge',
    icon: contractIcon,
    num: 0
  }
])

const iconMap = {
  工单待办: {
    url: '/work-manage/my-deal',
    icon: orderIcon,
    api: getWorkListByToDo,
    params: {
      tabType: 'assignToMe',
      pageNum: 1,
      pageSize: 10
    }
  },
  内部交接: {
    url: '/customer/material-handover',
    icon: handoverIcon,
    api: materialHandoverRecordList,
    params: {
      recipientUserName: userStore.user.nickName,
      pageNum: 1,
      pageSize: 10,
      handoverStatus: 'pending'
    }
  },
  办证任务: {
    url: '/certificate/my-list',
    icon: cardIcon,
    api: bizList,
    params: {
      bizStatus: 'processing',
      myTaskFlag: 1,
      pageNum: 1,
      pageSize: 10
    }
  },
  风险审核: {
    url: '/customer/risk-audit',
    icon: riskIcon,
    api: riskCustomerAuditList
  },
  流失清理: {
    url: '/customer/risk-clear',
    icon: cleanIcon,
    api: riskCustomerLoseHandleList
  },
  财务审核: {
    url: '/finance-review/rectify',
    icon: financeIcon,
    api: financeReviewRectifyList,
    params: {
      sponsorAccountingUserName: userStore.user.nickName,
      pageNum: 1,
      pageSize: 10,
      searchStatus: 'rectification_pending'
    }
  },
  合同审批: {
    url: '/contract-manage/contract-judge',
    icon: contractIcon,
    api: getContractReviewListByUser,
    params: {
      tabType: 1,
      pageNum: 1,
      reviewStatus: 0,
      pageSize: 10
    }
  }
}
const getTask = () => {
  taskList.value.forEach(async task => {
    const api = iconMap[task.title]?.api
    const params = iconMap[task.title]?.params
    if (api) {
      const { data } = await api(params || {})
      if (data.total) {
        task.num = data.total
      }
    }
  })
}
getTask()

// 跳转路由
const { proxy } = getCurrentInstance()
const checkDetail = item => {
  const routers = router.getRoutes().map(item => item.path)
  console.log('routers', routers)
  if (routers.includes(item.url)) {
    router.push({
      path: item.url
    })
    commonStore.setTodoTaskFlag(true)
  } else {
    // 警告暂无权限
    proxy.$modal.msgWarning('当前暂无访问权限')
  }
}
</script>
<style lang="scss" scoped>
.img-icon {
  width: 20px;
  height: 20px;
  margin-left: 8px;
  cursor: pointer;
}
.item-icon {
  width: 48px;
  height: 48px;
}
.wrap-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  .list-item {
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    padding: 22px 20px 16px 20px;
    display: flex;
    flex-direction: column;
    gap: 14px;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: SourceHanSansCN, SourceHanSansCN;

      .name {
        font-weight: 500;
        font-size: 18px;
        color: #30343a;
        font-style: normal;
        flex: 1;
      }
      .text {
        font-weight: 400;
        font-size: 14px;
        color: #2383e7;
        font-style: normal;
        cursor: pointer;
      }
    }
    .number {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: SourceHanSansCN, SourceHanSansCN;
      .num {
        font-family: Arial, Arial;
        font-weight: normal;
        font-size: 36px;
        color: #30343a;
        font-style: normal;
        flex: 1;
      }
      .unit {
        font-size: 16px;
      }
    }
  }
}
</style>
