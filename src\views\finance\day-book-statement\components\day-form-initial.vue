<!--
 * @Description: 日记账表单
 * @Author: thb
 * @Date: 2023-09-07 08:51:39
 * @LastEditTime: 2023-09-13 15:44:19
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="账户" prop="receiptMethod">
          <el-select style="width: 100%" v-model="formData.receiptMethod" placeholder="请选择">
            <el-option v-for="item in receipt_method" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="月份" prop="yearMonth">
          <el-date-picker
            style="width: 100%"
            :clearable="false"
            v-model="formData.yearMonth"
            type="month"
            format="YYYY-MM"
            value-format="YYYY-MM"
            placeholder="请选择"
            :disabledDate="disabledDate"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="收入(借方)金额" prop="incomeAmount">
          <el-input placeholder="请输入" v-model="formData.incomeAmount" maxlength="20">
            <template #suffix>元</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="付出(贷方)金额" prop="paymentAmount">
          <el-input placeholder="请输入" v-model="formData.paymentAmount" maxlength="20">
            <template #suffix>元</template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="tsx">
import { getInitialAmountAnalysis } from '@/api/finance/day-book'
import { getCurrentInstance, reactive, watch } from 'vue'
import { useDic } from '@/hooks/useDic'
import { FormValidators } from '@/utils/validate'
import dayjs from 'dayjs'
import { ref } from 'vue'

const formData: any = reactive({
  paymentAmount: '', // 收入余额
  incomeAmount: '', // 付出余额
  yearMonth: '',
  id: undefined
})
const { proxy } = getCurrentInstance()
const { receipt_method } = proxy.useDict('receipt_method')

// getInitialAmount
watch(
  () => [formData.yearMonth, formData.receiptMethod],
  () => {
    console.log('formData.yearMonth', formData.yearMonth)
    if (formData.yearMonth) {
      getInitialAmountAnalysis({ yearMonth: formData.yearMonth, receiptMethod: formData.receiptMethod }).then(res => {
        if (res.code === 200) {
          formData.paymentAmount = res.data.paymentAmount
          formData.incomeAmount = res.data.incomeAmount
        }
      })
    }
  }
)

const rules = {
  receiptMethod: [
    {
      required: true,
      message: '请选择',
      trigger: ['blur']
    }
  ],
  yearMonth: [
    {
      required: true,
      message: '请选择',
      trigger: ['blur']
    }
  ],
  incomeAmount: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    },
    {
      required: true,
      validator: FormValidators.feeNumberPoint,
      trigger: ['blur']
    }
  ],
  paymentAmount: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    },
    {
      required: true,
      validator: FormValidators.feeNumberPoint,
      trigger: ['blur']
    }
  ]
}
const disabledDate = (time: any) => {
  return time.getTime() > Date.now()
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped>
:deep(.el-date-editor) {
  width: 100%;
}
.el-select {
  width: 100%;
}

.icon {
  border: 1px solid #e8e8e8ff;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  position: absolute;
  top: 30%;
  right: 10px;
}
</style>
