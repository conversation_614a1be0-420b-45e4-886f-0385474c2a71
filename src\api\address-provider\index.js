import request from '@/utils/request'
export const getAddressProviderList = params => {
  return request({
    url: '/addressSupplier/list',
    method: 'get',
    params
  })
}

export const saveUpdateAddressProvider = data => {
  return request({
    url: '/addressSupplier/saveOrUpdate',
    method: 'post',
    data
  })
}

export const getAddressProviderDetailById = id => {
  return request({
    url: '/addressSupplier/getById',
    method: 'get',
    params: {
      id
    }
  })
}

export const removeAddressProvider = id => {
  return request({
    url: '/addressSupplier/delete',
    method: 'delete',
    params: {
      id
    }
  })
}
export const changeProviderStatus = id => {
  return request({
    url: '/addressSupplier/enable',
    method: 'post',
    params: {
      id
    }
  })
}
