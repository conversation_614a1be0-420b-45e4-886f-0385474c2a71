<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-position="top"
    :hide-required-asterisk="[1, 3].includes(disabledCode)"
  >
    <template v-if="![5, 6].includes(disabledCode)">
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="客户名称" prop="customerName">
            <!-- 点击弹窗出现客户列表  -->
            <div @click="handleListSelectShow" style="width: 100%">
              <el-input
                :disabled="disabledCode"
                v-model="formData.customerName"
                readonly
                maxlength="20"
                :placeholder="disabledCode ? '' : '请输入'"
                style="width: 100%"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户编码" prop="customerNo">
            <el-input disabled v-model="formData.customerNo" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="disabledCode !== 4">
          <el-form-item label="关联合同" prop="contractNo">
            <!-- 点击弹窗出现合同列表  -->
            <div @click="handleListSelectShowContract" style="width: 100%">
              <el-input
                :disabled="[1, 2, 3].includes(disabledCode)"
                v-model="formData.contractNo"
                readonly
                :placeholder="[1, 2, 3].includes(disabledCode) ? '' : '请输入'"
                style="width: 100%"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="disabledCode !== 4">
          <el-form-item label="关联账单" prop="paymentNo">
            <!-- 点击弹窗出现账单列表  -->
            <div @click="handleListSelectShowPayment" style="width: 100%">
              <el-input
                :disabled="[1, 2, 3].includes(disabledCode)"
                v-model="formData.paymentNo"
                readonly
                :placeholder="[1, 2, 3].includes(disabledCode) ? '' : '请输入'"
                style="width: 100%"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="费用类别" prop="feeType">
            <!-- 为什么这里disabled可以直接传入且生效，是在form中做了什么处理吗？ -->
            <feeTypeTree disabledBool v-model="formData.feeType" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="账款金额" prop="fundAmount">
            <el-input disabled v-model="formData.fundAmount" placeholder="">
              <template #suffix>元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="![1, 2, 3].includes(disabledCode)">
          <el-form-item label="待收金额" prop="receivableAmount">
            <el-input disabled v-model="formData.receivableAmount" placeholder="">
              <template #suffix>元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="收款人" prop="payeeId">
            <el-select filterable @change="handleChangePayeeId" disabled v-model="payeeShow" placeholder="请选择" clearable>
              <el-option :label="item.nickName" :value="item.userId" v-for="(item, index) in userList" :key="index" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收款金额" prop="receiptAmount">
            <el-input :disabled="[1, 2, 3].includes(disabledCode)" v-model.trim="formData.receiptAmount" placeholder="">
              <!-- onkeyup="value = value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" -->
              <!-- @input="handleValidateOtherReceiptAmount" -->
              <template #suffix>元</template>
            </el-input>
            <!-- {{ formData.receiptAmount }} -->
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="[1, 2, 3].includes(disabledCode) && formData.isChecked === 1">
          <el-form-item label="收款渠道" prop="receiptMethod">
            <el-select
              :disabled="[1, 2, 3].includes(disabledCode)"
              v-model="formData.receiptMethod"
              :placeholder="[1, 2, 3].includes(disabledCode) ? ' ' : '请选择'"
              clearable
            >
              <el-option :label="item.label" :value="item.value" v-for="(item, index) in receipt_method" :key="index" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="[1, 2, 3].includes(disabledCode) && formData.isChecked === 1">
          <el-form-item label="记账摘要" prop="digest">
            <el-input disabled v-model="formData.digest" placeholder="" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="收款备注" prop="mark">
            <el-input
              maxlength="1000"
              :disabled="[1, 2, 3].includes(disabledCode)"
              v-model="formData.mark"
              type="textarea"
              :placeholder="[1, 2, 3].includes(disabledCode) ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="收款凭证" prop="receiptVoucherFile">
            <FileUpload v-if="![1, 2, 3].includes(disabledCode)" v-model="formData.receiptVoucherFile" :isShowTip="false" />
            <span
              :alt="formData.receiptVoucherFile?.fileNames"
              class="download-text"
              @click="downloadFile(formData.receiptVoucherFile)"
              v-else
            >
              {{ (formData.receiptVoucherFile && formData.receiptVoucherFile?.fileNames) || '暂无文件' }}</span
            >
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="收款时间" prop="receiptDate">
            <el-date-picker
              style="width: 100%"
              :disabled="[1, 2, 3].includes(disabledCode)"
              v-model="formData.receiptDate"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              type="date"
              :placeholder="[1, 2, 3].includes(disabledCode) ? ' ' : '请选择'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="disabledCode === 1 && formData.isChecked !== 0">
          <el-form-item label="提交时间" prop="createTime">
            <el-input disabled v-model="formData.createTime" placeholder="" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <!-- 未审核且审批详情打开不展示 -->
        <!-- 未审核且收款审批打开展示 formData.isChecked === 1 -->
        <el-col :span="24" v-if="disabledCode === 2">
          <el-form-item label="通过" prop="isChecked">
            <el-radio-group :disabled="disabledCode === 3" v-model="formData.isChecked">
              <el-radio v-for="(item, index) in collectionIsCheckedArr" :key="index" :label="item.value">{{
                item.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="disabledCode === 3 && formData.isChecked !== 0">
          <el-form-item label="审批人" prop="checkPerson">
            <el-input disabled v-model="formData.checkPerson" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="disabledCode === 3 && formData.isChecked !== 0">
          <el-form-item label="提交时间" prop="createTime">
            <el-input disabled v-model="formData.createTime" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="formData.isChecked === 2">
          <el-form-item label="驳回原因" prop="rejectReason">
            <el-input
              :disabled="[1, 3].includes(disabledCode)"
              v-model="formData.rejectReason"
              :autosize="{ minRows: 2, maxRows: 4 }"
              :placeholder="[1, 3].includes(disabledCode) ? '' : '请输入'"
              type="textarea"
              maxlength="1000"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    <template v-if="disabledCode === 5">
      <!-- 为什么这里面还要写disabledCode === 3的逻辑...忘了 -->
      <el-form-item label="记账记录" prop="journalName">
        <div @click="handleListSelectShowJournal" style="width: 100%">
          <el-input
            v-model="formData.journalName"
            readonly
            :placeholder="disabledCode === 3 ? '' : '请选择'"
            style="width: 100%"
          />
        </div>
      </el-form-item>
    </template>
    <template v-if="disabledCode === 6">
      <el-form-item label="驳回原因" prop="rejectReason">
        <el-input
          :disabled="disabledCode === 3"
          v-model="formData.rejectReason"
          :autosize="{ minRows: 2, maxRows: 4 }"
          :placeholder="disabledCode === 3 ? '' : '请输入'"
          type="textarea"
          maxlength="1000"
        />
      </el-form-item>
    </template>
  </el-form>
  <tableModal
    :init-param="{ discard: 0 }"
    v-if="listSelectShow"
    title="关联客户"
    rowKey="customerId"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelectCustomer"
  />
  <tableModal
    :init-param="initParamContract"
    v-if="listSelectShowContract"
    title="关联合同"
    :columns="columnsContract"
    :request-api="getContractList"
    @on-close="listSelectShowContract = false"
    @on-select="handleSelectContract"
  />
  <tableModal
    :init-param="initParamPayment"
    v-if="listSelectShowPayment"
    title="关联账单"
    :columns="columnsPayment"
    :request-api="financePaymentList"
    :tableRowClassName="tableRowClassName"
    @on-close="listSelectShowPayment = false"
    @on-select="handleSelectPayment"
  />
  <tableModal
    :init-param="initParamJournal"
    v-if="listSelectShowJournal"
    :title="titleJournal"
    :columns="columnsJournal"
    :request-api="getDayBooKList"
    :tableRowClassName="tableRowClassName"
    @on-close="listSelectShowJournal = false"
    @on-select="handleSelectJournal"
  />
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>

<script setup lang="jsx">
import { getFinanceReceiptGetFinanceAssociatedPayment } from '@/api/finance/collection-ledger'
import { getCustomers } from '@/api/customer/file'
import { getContractList } from '@/api/contract/contract'
import { getDayBooKList } from '@/api/finance/day-book'
import { financePaymentList } from '@/api/finance/accounts-receivable'
import { listUser } from '@/api/system/user'
import { reactive, watch } from 'vue'
import tableModal from '@/components/tableModal'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import feeTypeTree from '@/views/finance/accounts-receivable/components/fee-type-tree.vue'
import useUserStore from '@/store/modules/user'
import { receiveStatusArr } from '@/utils/constants'
import iFrame from '@/components/iFrame'
import { useDic } from '@/hooks/useDic'
import dayjs from 'dayjs'

const { getDic } = useDic()
const userStore = useUserStore()
const { proxy } = getCurrentInstance()
const { receipt_method } = proxy.useDict('receipt_method')
console.log('receipt_method', receipt_method)
const { customer_status } = proxy.useDict('customer_status')

const tableRowClassName = ({ row, rowIndex }) => {
  if (row?.paymentStatus === 'close') {
    return 'disabled-row'
  }
  if (row?.receiptId) {
    return 'disabled-row'
  }
  return ''
}

const collectionIsCheckedArr = [
  {
    label: '通过',
    value: 1
  },
  {
    label: '驳回',
    value: 2
  }
]

const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

const columnsContract = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    width: '300',
    label: '客户名称'
  },
  {
    prop: 'customerNo',
    width: '150',
    label: '客户编号'
  },
  {
    prop: 'contractNo',
    width: '150',
    label: '合同编号',
    search: { el: 'input' }
  },
  {
    prop: 'productName',
    width: '250',
    label: '服务产品'
  },
  {
    prop: 'contractType',
    width: '150',
    label: '合同类型',
    enum: [
      {
        label: '记账合同',
        value: '0'
      },
      {
        label: '一次性合同',
        value: '1'
      },
      {
        label: '地址服务协议合同',
        value: '2'
      }
    ],
    search: { el: 'select' }
  },
  {
    prop: 'totalCost',
    width: '150',
    label: '合同金额'
  },
  {
    prop: 'startTime',
    width: '100',
    label: '起始时间'
  },
  {
    prop: 'endTime',
    width: '100',
    label: '结束时间'
  },
  {
    prop: 'manager',
    width: '100',
    label: '财税顾问'
  }
]
const columnsPayment = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'paymentNo',
    label: '账单编号',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'feeType',
    label: '费用类别',
    isColShow: false,
    search: {
      render: ({ searchParam }) => {
        return <feeTypeTree vModel={searchParam.feeType} />
      }
    },
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    }
  },
  {
    prop: 'receiveStatus',
    label: '收款情况',
    enum: receiveStatusArr,
    search: { el: 'select' }
  },
  {
    prop: 'paymentAmount',
    label: '账款金额', // 账款金额->账款金额总计
    isColShow: false,
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'allReceiptAmount',
    label: '已付款金额', // 已收款->已付款金额
    isColShow: false,
    render: scope => {
      return <span>{scope.row.allReceiptAmount >= 0 ? `${scope.row.allReceiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'allReceivableAmount',
    label: '应待收款',
    isColShow: false,
    render: scope => {
      // 思考：hooks下如何篡改列表项数据
      return <span>{scope.row.allReceivableAmount >= 0 ? `${scope.row.allReceivableAmount}元` : '--'}</span>
    }
  }
]
const columnsJournal = [
  {
    type: 'selection',
    fixed: 'left',
    width: 80,
    selectable: row => {
      // selectable未传递到内部，未生效
      return false
    }
  },
  {
    prop: 'receiptMethod',
    width: 150,
    label: '支付方式'
    // enum: getDic('receipt_method'),
    // search: {
    //   el: 'select'
    // }
  },
  {
    prop: 'projectName',
    label: '对方科目',
    width: 200
  },
  {
    prop: 'digest',
    width: 200,
    label: '摘要'
    // search: {
    //   el: 'input'
    // }
  },
  {
    prop: 'vestId',
    label: '业务归属',
    render: scope => {
      return <span>{scope.row.vestName || '--'}</span>
    }
    // enum: () => {
    //   return new Promise(async (resolve, reject) => {
    //     const { data } = await getReviewerTreeData()
    //     resolve({
    //       data
    //     })
    //   })
    // },
    // search: {
    //   el: 'tree-select',
    //   props: {
    //     'check-strictly': true,
    //     props: {
    //       value: 'id',
    //       label: 'label',
    //       children: 'children'
    //     }
    //   }
    // }
  },
  {
    prop: 'remark',
    label: '备注'
  }
]

const disabledCode = ref(0)
// "customerId": 0,
// "contractId": 0,
// "createBy": "string",
// "createTime": "2023-06-28T07:36:49.707Z",
// "customerName": "string",
// "customerNo": "string",
// "feeType": "string",
// "fundAmount": 0,
// "id": 0,
// "isDeleted": false,
// "isChecked": 0,
// "mark": "string",
// "payee": "string",
// "paymentId": 0,
// "receiptAmount": 0,
// "receiptMethod": "string",
// "receiptVoucherFile": {
//   "bizType": "string",
//   "fileKey": "string",
//   "fileNames": "string",
//   "fileSize": 0,
//   "id": 0,
//   "isDeleted": false,
//   "mainId": 0,
//   "uploadBy": "string",
//   "uploadTime": "2023-06-28T07:36:49.707Z",
//   "urls": "string"
// },
// "receivableAmount": 0,
// "rejectReason": "string",
// "updateBy": "string",
// "updateTime": "2023-06-28T07:36:49.707Z"
const formData = reactive({
  digest: undefined,
  checkPerson: undefined,
  customerId: undefined,
  contractId: undefined,
  contractNo: undefined,
  createBy: undefined,
  createTime: undefined,
  receiptDate: undefined,
  customerName: undefined,
  customerNo: undefined,
  feeType: undefined,
  fundAmount: undefined,
  id: undefined,
  isDeleted: undefined,
  isChecked: undefined,
  mark: undefined,
  payee: undefined,
  payeeId: undefined,
  paymentId: undefined,
  paymentNo: undefined, // 前端加的
  receiptAmount: undefined,
  receiptMethod: undefined,
  receiptVoucherFile: undefined,
  receivableAmount: undefined,
  rejectReason: undefined,
  updateBy: undefined,
  updateTime: undefined
})

const rules = {
  customerName: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  paymentNo: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  payeeId: [{ required: true, message: '请输入', trigger: ['blur'] }],
  receiptAmount: [
    { required: true, message: '请输入', trigger: ['blur', 'change'] },
    {
      pattern: /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,2})?$/,
      message: '请输入正确的金额',
      trigger: 'blur'
    }
  ],
  receiptMethod: [{ required: true, message: '请选择', trigger: ['blur'] }],
  receiptDate: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  isChecked: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  journalName: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  rejectReason: [{ required: true, message: '请输入', trigger: ['blur'] }]
}

// const handleValidateOtherReceiptAmount = value => {
//   formData.receiptAmount = value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
// }

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

// 客户列表弹窗显示
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  if (disabledCode.value) return
  listSelectShow.value = true
  console.log('listSelectShow', listSelectShow.value)
}
const handleSelectCustomer = data => {
  // console.log('data', data)
  if (formData.customerId === data.customerId) return
  formData.customerName = data.customerName
  formData.customerNo = data.customerNo
  formData.customerId = data.customerId
  formData.contractNo = undefined
  formData.contractId = undefined
  formData.paymentId = undefined
  formData.paymentNo = undefined
  formData.feeType = undefined
  formData.fundAmount = undefined
  formData.receivableAmount = undefined
}
// 合同列表弹窗显示
const initParamContract = reactive({})
const listSelectShowContract = ref(false)
const handleListSelectShowContract = () => {
  if (disabledCode.value) return
  if (!formData.customerId) {
    proxy.$modal.msgWarning(`请先选择客户`)
    return
  }
  initParamContract.customerId = formData.customerId
  listSelectShowContract.value = true
}
const handleSelectContract = data => {
  // console.log('data', data)
  formData.contractNo = data.contractNo
  formData.contractId = data.contractId
}
// 账单列表弹窗显示
const initParamPayment = reactive({})
const listSelectShowPayment = ref(false)
const handleListSelectShowPayment = () => {
  if (disabledCode.value) return
  if (!formData.customerId) {
    proxy.$modal.msgWarning(`请先选择客户`)
    return
  }
  initParamPayment.customerId = formData.customerId
  listSelectShowPayment.value = true
}
const handleSelectPayment = data => {
  if (formData.paymentId === data.id) return
  formData.paymentId = data.id
  formData.paymentNo = data.paymentNo
  formData.contractNo = undefined
  formData.contractId = undefined
  if (data.contractId && data.contractNo) {
    formData.contractNo = data.contractNo
    formData.contractId = data.contractId
  }
  // getFinanceReceiptGetFinanceAssociatedPayment({ paymentId: formData.paymentId }).then(res => {
  //   formData.feeType = res.data.feeType
  //   formData.fundAmount = res.data.paymentAmount
  //   formData.receivableAmount = res.data.receivableAmount
  //   // formData.allReceiptAmount = res.data.allReceiptAmount
  // })
}

/** 关联记账弹窗显示 */
const initParamJournal = reactive({})
const listSelectShowJournal = ref(false)
const titleJournal = ref('关联记账')
const handleListSelectShowJournal = () => {
  titleJournal.value = `关联记账（${formData.customerName} ${formData.receiptDate}收款记录）`
  initParamJournal.initialAmountFlag = false
  initParamJournal.customerId = formData.customerId
  initParamJournal.receiptDate = formData.receiptDate
  initParamJournal.incomeAmount = formData.receiptAmount
  initParamJournal.yearMonth = dayjs(formData.receiptDate).format('YYYY-MM')
  listSelectShowJournal.value = true
}
const handleSelectJournal = data => {
  // console.log('data', data)
  if (data.receiptId) return
  formData.journalName = data.digest
  formData.digest = data.digest
  formData.journalId = data.id
  titleJournal.value = '关联记账'
}

watch(
  () => formData.paymentId,
  val => {
    // console.log('watch-1', val)
    if ([0, 4].includes(disabledCode.value) && val) {
      // console.log('watch-2')
      getFinanceReceiptGetFinanceAssociatedPayment({ paymentId: val }).then(res => {
        formData.feeType = res.data.feeType
        formData.fundAmount = res.data.paymentAmount
        formData.receivableAmount = res.data.receivableAmount
        // formData.allReceiptAmount = res.data.allReceiptAmount
      })
    }
  },
  { deep: true, immediate: true }
)

defineExpose({
  disabledCode,
  formData,
  getFormRef
})

const handleChangePayeeId = val => {
  console.log('handleChangePayeeId', val)
  formData.payeeId = val
  formData.payee = userList.value?.find(item => item.userId === val)?.nickName
}

const userList = ref([])
const getUserList = async () => {
  const result = await listUser({ pageSize: 9999, pageNum: 1 })
  userList.value = result.rows || []
  if (!formData.payeeId && !formData.payee) {
    formData.payee = userStore.user.nickName
    formData.payeeId = userStore.user.userId
  }
  // console.log('getUserList', formData.payee, formData.payeeId)
}

onMounted(() => {
  getUserList()
})

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

const payeeShow = computed(() => {
  return formData.payeeId || formData.payee
})

watch(
  () => formData.receiptMethod,
  val => {
    if (val) {
      formRef.value.validateField('receiptMethod')
    }
  }
)
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
.download-text {
  max-width: 100%;
  padding: 0 5px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
</style>
