<!--
 * @Description: 新增业务表格
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-07-14 15:41:57
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="业务类型" prop="typeName">
          <el-input v-model="formData.typeName" maxlength="10" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="业务代码" prop="code">
          <el-input v-model="formData.code" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="合同类型" prop="contractType">
          <el-select v-model="formData.contractType" multiple placeholder="请选择">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in typeList" :key="index" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="显示排序" prop="sort">
          <NumberInput v-model="formData.sort" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import NumberInput from '@/components/NumberInput'
// const { proxy } = getCurrentInstance()

const formData = reactive({
  typeName: '',
  code: '',
  sort: '',
  contractType: [],
  id: undefined
})

// typeList
const typeList = [
  {
    label: '记账合同',
    value: '记账合同'
  },
  {
    label: '一次性合同',
    value: '一次性合同'
  },
  {
    label: '地址服务协议合同',
    value: '地址服务协议合同'
  }
]

const handleCodeValidate = (rule, value, callback) => {
  console.log(rule, value, callback)
  const regx = /^[A-Za-z0-9_-]+$/
  if (regx.test(value)) {
    callback()
  } else {
    callback(new Error('只允许字母、数字、-和下划线'))
  }
}
const rules = {
  typeName: [{ required: true, message: '请输入', trigger: 'blur' }],
  code: [{ required: true, trigger: 'blur', validator: handleCodeValidate }],
  contractType: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  sort: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入'
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
