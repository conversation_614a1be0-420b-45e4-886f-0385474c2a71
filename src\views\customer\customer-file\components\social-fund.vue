<!--
 * @Description: 社保公积金组件
 * @Author: thb
 * @Date: 2023-05-29 13:45:09
 * @LastEditTime: 2023-11-14 10:26:55
 * @LastEditors: thb
-->
<template>
  <el-form :model="formData" ref="formRef" label-position="top" :hide-required-asterisk="disabled">
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="社保开户">
          <el-radio-group v-model="formData.socialAccountFlag" :disabled="disabled">
            <el-radio label="0" size="large">否</el-radio>
            <el-radio label="1" size="large">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="社保账号">
          <el-tooltip
            :content="formData.socialAccount"
            v-if="disabled && formData.socialAccount"
            effect="light"
            placement="top-start"
          >
            <el-input
              v-model="formData.socialAccount"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            v-model="formData.socialAccount"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="社保密码">
          <el-input
            v-model="formData.socialPassword"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="社保开户附件">
          <FileUpload v-if="!disabled" v-model="formData.socialAccountOpenFileList" :limit="100" :isShowTip="false" />
          <!-- <span class="download-text" v-else @click="downloadFile(formData.socialAccountOpenFile)">
            {{ (formData.socialAccountOpenFile && formData.socialAccountOpenFile?.fileNames) || '暂无文件' }}</span
          > -->
          <fileList :list="formData.socialAccountOpenFileList" v-else />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="公积金开户">
          <el-radio-group v-model="formData.fundAccountFlag" :disabled="disabled">
            <el-radio label="0" size="large">否</el-radio>
            <el-radio label="1" size="large">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="公积金账户">
          <el-tooltip
            :content="formData.fundAccount"
            v-if="disabled && formData.fundAccount"
            effect="light"
            placement="top-start"
          >
            <el-input
              v-model="formData.fundAccount"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            v-model="formData.fundAccount"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="公积金密码">
          <el-input
            v-model="formData.fundPassword"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="公积金开户附件">
          <FileUpload v-if="!disabled" v-model="formData.fundAccountOpenFileList" :limit="100" :isShowTip="false" />
          <!-- <span class="download-text" v-else @click="downloadFile(formData.fundAccountOpenFile)">
            {{ (formData.fundAccountOpenFile && formData.fundAccountOpenFile?.fileNames) || '暂无文件' }}</span
          > -->
          <fileList :list="formData.fundAccountOpenFileList" v-else />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import { saveCustomerSocialFund, getCustomerSocialFundByCiId } from '@/api/customer/file'

import { useRemote } from '@/hooks/useRemote'
import iFrame from '@/components/iFrame'
import fileList from '@/components/FileList'
// import { downloadFile } from '@/utils/common'
const disabled = inject('disabled')
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const emits = defineEmits(['update:modelValue', 'on-edit'])
const formData = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})

const getDetail = async () => {
  const { data } = await getCustomerSocialFundByCiId(props.modelValue.ciId)
  formData.value = Object.assign(formData.value, data)
}
watch(
  disabled,
  () => {
    if (disabled.value) {
      getDetail()
    }
  },
  {
    immediate: true
  }
)
// 监听是否是编辑
const edit = inject('isEdit')
watch(
  edit,
  async () => {
    if (edit.value) {
      await getDetail()
      emits('on-edit')
    }
  },
  {
    immediate: true
  }
)
const saveRemote = async () => {
  const id = await useRemote(
    saveCustomerSocialFund,
    formData.value,
    ['socialAccountOpenFile', 'fundAccountOpenFile'],
    '社保公积金',
    ['socialAccountOpenFileList', 'fundAccountOpenFileList']
  )
  formData.value.fundId = id
  return id
}
// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}
defineExpose({
  saveRemote
})
</script>
<style lang="scss" scoped></style>
