/*
 * @Description: 收款面板api
 * @Author: thb
 * @Date: 2023-09-04 13:08:23
 * @LastEditTime: 2023-09-12 13:30:43
 * @LastEditors: thb
 */
import request from '@/utils/request'
// 新增户数
export const getAccountNumbers = params => {
  return request({
    url: '/kanban/payment/newCustomer',
    method: 'get',
    params
  })
}

// 获取存量户数、注销户数、流失户数、迁移户数
export const getAccountTypesNumbers = () => {
  return request({
    url: '/kanban/payment/customerChangeStatistics',
    method: 'get'
  })
}

// 获取服务客户记账单价
export const getServiceClientMoney = params => {
  return request({
    url: '/kanban/payment/agencyBookkeepingUnitPrice',
    method: 'get',
    params
  })
}
// 获取当月回款率分析
export const getCurrentMonthRate = params => {
  return request({
    url: '/kanban/payment/paymentRecoveryAnalysis',
    method: 'get',
    params
  })
}

// 获取月营业额明细
export const getMonthMoneyDetail = params => {
  return request({
    url: '/kanban/payment/monthlySalesBreakdown',
    method: 'get',
    params
  })
}
