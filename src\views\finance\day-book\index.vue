<!--
 * @Description: 日记账
 * @Author: thb
 * @Date: 2023-09-06 15:11:27
 * @LastEditTime: 2023-10-30 08:35:30
 * @LastEditors: thb
-->
<template>
  <div class="main-wrap">
    <dataList :timeStamp="timeStamp" />
    <ProTable
      ref="proTable"
      title="日记账"
      :init-param="initParam"
      :columns="columns"
      :toolButton="false"
      :request-api="getDayBooKList"
      :data-callback="handleSearch"
      :transformRequestParams="transformRequestParams"
      @sort-change="sortChange"
    >
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
        <el-button :icon="Upload" @click="handleImport">导入</el-button>
      </template>
      <template #index="{ $index }">
        <span>
          {{ $index + 1 }}
        </span>
      </template>
      <template #yearMonth="{ row }">
        <span>{{ row.receiptDate ? row.receiptDate.slice(0, 7) : '--' }}</span>
      </template>
      <template #day="{ row }">
        <span>{{ row.receiptDate ? row.receiptDate.slice(8) : '--' }}</span>
      </template>
      <template #projectName="{ row }">
        <span>{{ row.projectName || '--' }}</span>
      </template>
      <template #digest="{ row }">
        <span v-if="row.digest === '期初金额'" class="blue-text" @click="handleEditInitial()">{{ row.digest || '--' }}</span>
        <span v-else>{{ row.digest || '--' }}</span>
      </template>
      <!-- balance -->
      <template #balance="{ row }">
        <span>{{ row.balance }}</span>
      </template>
      <!-- 收款单 -->
      <template #receiptNo="{ row }">
        <span v-if="row.receiptId" class="blue-text" @click="handleShowCollectionDetail(row.receiptId)">{{ row.receiptNo }}</span>
        <span v-else>--</span>
      </template>
      <template #action="{ row }">
        <el-button :disabled="row.receiptId" type="primary" text @click="handleEdit(row)">编辑</el-button>
        <el-button :disabled="row.receiptId" type="danger" text @click="handleDelete(row)">删除</el-button>
      </template>
    </ProTable>
    <el-row class="footer">
      <el-col :span="12">
        <el-row :gutter="24">
          <el-col :span="6">合计：</el-col>
          <el-col :span="6">借方：{{ footerData.incomeAmount === 0 ? 0 : footerData.incomeAmount || '--' }} 元</el-col>
          <el-col :span="6"> 贷方：{{ footerData.paymentAmount === 0 ? 0 : footerData.paymentAmount || '--' }} 元</el-col>
          <el-col :span="6"> 余额：{{ footerData.balance === 0 ? 0 : footerData.balance || '--' }} 元</el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
  <div v-show="false">
    <InputRange></InputRange>
  </div>
  <ImportExcel ref="dialogRef" :yearShow="true" />
</template>
<script setup lang="tsx">
import { reactive, ref, watch } from 'vue'
import { ColumnProps } from '@/components/ProTable/interface'
import {
  getDayBooKList,
  deleteDayBook,
  addOrUpdateDayBook,
  getDayBookById,
  journalImport,
  saveOrUpdateInitialAmount
} from '@/api/finance/day-book'
import { CirclePlus, Upload } from '@element-plus/icons-vue'
import ImportExcel from '@/components/ImportExcel/index.vue'
import { getBusinessList } from '@/api/business/business'
import { getBookkeepingSubjectTreeList } from '@/api/basicData/basicData'
import { useHandleData } from '@/hooks/useHandleData'
import { useDialog } from '@/hooks/useDialog'
import { useDialog as useDialogFinance } from '@/hooks/useDialogFinance'
import dayjs from 'dayjs'
import dayForm from './components/day-form'
import dayFormInitial from './components/day-form-initial'
import dataList from './components/data-list'
import { getDayBookStatisticDataByMonth } from '@/api/finance/day-book'
import { getReviewerTreeData } from '@/api/process/process'
import { useDic } from '@/hooks/useDic'
import collectionForm from '@/views/finance/collection-ledger/components/form.vue'
import { getFinanceReceiptGetById } from '@/api/finance/collection-ledger'
import InputRange from '@/views/finance/day-book-statement/components/input-range'

const { getDic } = useDic()
const { proxy } = getCurrentInstance()
const { receipt_method } = proxy.useDict('receipt_method')

const { showDialog } = useDialog()
const { showDialog: showDialogFinance } = useDialogFinance()
const initParam = reactive({ initialAmountFlag: true })

const convertData = (data: any, revertData: any) => {
  data.forEach((item: any) => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      child: [] as any
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach((child: any) => {
        obj.child.push({
          name: child.productName,
          type: '产品类型',
          id: child.id // 产品类型id
        })
      })
    }
  })
}
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '',
    width: 60
  },
  {
    prop: 'yearMonth',
    label: '月',
    width: 150,
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'day',
    label: '日',
    width: 100,
    search: {
      el: 'date-picker',
      props: {
        type: 'date',
        valueFormat: 'DD',
        format: 'DD'
      }
    }
  },
  {
    prop: 'receiptMethod',
    width: 150,
    label: '支付方式',
    enum: getDic('receipt_method'),
    search: {
      el: 'select'
    },
    sortable: 'custom',
    sortName: 'journal.receipt_method'
  },
  {
    prop: 'projectName',
    label: '对方科目',
    width: 200,
    enum: () => {
      return new Promise(async (resolve, reject) => {
        const { data } = await getBusinessList({
          pageSize: 1000,
          pageNum: 1
        })
        // 将后端传回的数据结构进行转换
        const revertData: any = []
        convertData(data, revertData)
        const result = await getBookkeepingSubjectTreeList({
          enable: 1,
          pageSize: 1000,
          pageNum: 1
        })
        console.log('result', result.data)
        resolve({
          data: revertData.concat(result.data)
        })
      })
    },
    fieldNames: {
      label: 'name',
      // value: 'id',
      value: 'name',
      children: 'child'
    },
    search: {
      el: 'tree-select',
      props: {
        'check-strictly': true
      }
    },
    sortable: 'custom',
    sortName: 'journal.project_id'
  },
  {
    prop: 'digest',
    minWidth: 200,
    label: '摘要',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'incomeAmount',
    width: 180,
    label: '收入(借方)金额(元)',
    search: {
      render: ({ searchParam }) => {
        console.log(
          '===incomeAmount-index.vue-render===',
          searchParam.incomeAmount,
          searchParam.incomeAmount && JSON.parse(JSON.stringify(searchParam.incomeAmount))
        )
        return <InputRange vModel={searchParam.incomeAmount} />
      }
    },
    sortable: 'custom',
    sortName: 'journal.income_amount'
  },
  {
    prop: 'paymentAmount',
    width: 180,
    label: '付出(贷方)金额(元)',
    search: {
      render: ({ searchParam }) => {
        return <InputRange vModel={searchParam.paymentAmount} />
      }
    },
    sortable: 'custom',
    sortName: 'journal.payment_amount'
  },
  {
    prop: 'balance',
    width: 150,
    label: '余额(元)'
  },
  {
    prop: 'vestId',
    width: 150,
    label: '业务归属',
    render: scope => {
      return <span>{scope.row.vestName || '--'}</span>
    },
    enum: () => {
      return new Promise(async (resolve, reject) => {
        const { data } = await getReviewerTreeData()
        resolve({
          data
        })
      })
    },
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        // 'check-strictly': true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children'
        }
      }
    },
    sortable: 'custom',
    sortName: 'journal.vest_id'
  },
  {
    prop: 'remark',
    width: 150,
    label: '备注'
  },
  {
    prop: 'receiptNo',
    width: 150,
    label: '收款单号'
  },
  {
    prop: 'action',
    fixed: 'right',
    width: 180,
    label: '操作'
  }
]

const proTable = ref()
const transformRequestParams = (data: any) => {
  if (data.incomeAmount) {
    data.incomeAmountMin = data.incomeAmount[0]
    data.incomeAmountMax = data.incomeAmount[1]
  }

  if (data.paymentAmount) {
    data.paymentAmountMin = data.paymentAmount[0]
    data.paymentAmountMax = data.paymentAmount[1]
  }
}
const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

const dialogRef = ref<InstanceType<typeof ImportExcel> | null>(null)
const handleImport = () => {
  const params = {
    title: '日记账',
    tempApi: '/financeJournal/importTemp', // 下载模板接口
    importApi: journalImport, // 导入家口
    getTableList: proTable.value?.getTableList
  }
  dialogRef.value?.acceptParams(params)
}

// 删除日记账
const handleDelete = async (row: any) => {
  await useHandleData(deleteDayBook, row.id, `删除所选${row.receiptDate}  日记账信息`)
  proTable.value?.getTableList()
  // getFooterData()
  // timeStamp.value = new Date().getTime() + ''
}
const timeStamp = ref('')
// 新增日记账
const handleAdd = () => {
  showDialog({
    title: '新增',
    customClass: 'day-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: dayForm, // 表单组件
    // handleConvertParams: data => {
    //   // 处理业务归属
    //   data.deptSource = data.deptId + '-' + data.deptType
    // }, // 详情接口数据自定义
    handleRevertParams: data => {
      // 处理业务归属
      if (data.vestSource) {
        data.vestType = data.vestSource.split('-')[0]
        data.vestId = data.vestSource.split('-')[1]
      }
    }, //提交参数自定义
    submitApi: addOrUpdateDayBook, // 提交api
    submitCallback: (data: any) => {
      proTable.value.searchParam['yearMonth'] = data['receiptDate'].slice(0, 7)
      proTable.value.searchParam['day'] = data['receiptDate'].slice(8)
      proTable.value?.search()
      // getFooterData()
      // timeStamp.value = new Date().getTime() + ''
    } // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 编辑日记账
const handleEdit = (row: any) => {
  showDialog({
    title: '编辑',
    customClass: 'day-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: dayForm, // 表单组件
    handleConvertParams: data => {
      // 处理业务归属
      data.formType = 'edit'
      if (data.vestId && data.vestName) {
        // data.vestSource = data.vestId + '-' + data.vestName
        data.vestSource = data.vestType + '-' + data.vestId
      }
      if (data.incomeAmount === 0) {
        data.incomeAmount = ''
      }
      if (data.paymentAmount === 0) {
        data.paymentAmount = ''
      }
    }, // 详情接口数据自定义
    handleRevertParams: data => {
      // 处理业务归属
      console.log('handleRevertParams', data)
      if (data.vestSource) {
        data.vestType = data.vestSource.split('-')[0]
        data.vestId = data.vestSource.split('-')[1]
      }
    }, //提交参数自定义
    submitApi: addOrUpdateDayBook, // 提交api
    requestParams: row.id,
    getApi: getDayBookById,
    submitCallback: (data: any) => {
      proTable.value.searchParam['yearMonth'] = data['receiptDate'].slice(0, 7)
      proTable.value.searchParam['day'] = data['receiptDate'].slice(8)
      proTable.value?.search()
    } // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

const handleEditInitial = () => {
  // console.log('handleEditInitial', proTable.value.searchParam['yearMonth'])
  showDialog({
    title: '编辑期初金额',
    customClass: 'day-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: dayFormInitial, // 表单组件
    handleConvertParams: data => {
      data.yearMonth = proTable.value.searchParam['yearMonth']
      if (data.incomeAmount === 0) {
        data.incomeAmount = ''
      }
      if (data.paymentAmount === 0) {
        data.paymentAmount = ''
      }
    },
    submitApi: saveOrUpdateInitialAmount, // 提交api
    submitCallback: (data: any) => {
      proTable.value.searchParam['yearMonth'] = data['yearMonth']
      proTable.value?.search()
    }
  })
}

const footerData = ref<any>({})
const getFooterData = async () => {
  // console.log('proTable.value.searchParam-1', JSON.parse(JSON.stringify(proTable.value.searchParam)))
  // 为什么从proTable.value.searchParam改为{...proTable.value.searchParam}解除引用关系就不会影响到searchParam在useTable里的变化
  const { data } = await getDayBookStatisticDataByMonth({ ...proTable.value.searchParam })
  // console.log('proTable.value.searchParam-2', JSON.parse(JSON.stringify(proTable.value.searchParam)))
  footerData.value = data || {}
}
// 自定义扩展搜索查询列表功能
const handleSearch = data => {
  getFooterData()
  timeStamp.value = new Date().getTime() + ''
  return data
}

/** 展示收款单 */
const handleShowCollectionDetail = (id: any) => {
  console.log('id', id)
  showDialogFinance({
    title: '收款详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: collectionForm,
    getApi: getFinanceReceiptGetById,
    requestParams: { id }
  })
}

watch(
  () => proTable.value?.searchParam['yearMonth'],
  val => {
    // console.log('val', val)
    proTable.value.searchParam['day'] = undefined
  },
  { deep: true }
)
</script>
<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}

.footer {
  background: #fff;
  margin-left: 0 !important;
  margin-right: 0 !important;
  margin-bottom: 24px;
  padding: 20px;
}
</style>
