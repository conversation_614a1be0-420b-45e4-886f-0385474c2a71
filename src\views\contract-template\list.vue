<template>
  <div class="main-wrap">
    <ProTable
      ref="proTable"
      title="模板管理"
      :init-param="initParam"
      :columns="columns"
      :toolButton="false"
      rowKey="id"
      :request-api="getContractTempList"
    >
      <template #tempName="{ row }">
        <span class="blue-text" @click="handleDetail(row)">{{ row.tempName }}</span>
      </template>
      <template #enable="{ row }">
        <span> <el-switch active-value="1" inactive-value="0" :model-value="row.enable" @click="handleChangeEnable(row)" /></span>
      </template>
      <template #tableHeader="scope">
        <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
        <el-button type="danger" :icon="Delete" @click="handleDelete(scope.selectedListIds)">删除</el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx">
import {
  getContractTempList,
  postContractTempEnable,
  deleteContractTempDeleteByIds
} from '@/api/contract-template/contract-template'
import { ref, reactive, nextTick } from 'vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { contractTypeArr } from '@/utils/constants.js'
import { useHandleData } from '@/hooks/useHandleData'

// 使用状态0-停用 1-启用
const enableArr = [
  { value: 0, label: '停用' },
  { value: 1, label: '启用' }
]
// 状态0-待审批 1-通过 2-驳回
const statusArr = [
  { value: 0, label: '待审批' },
  { value: 1, label: '通过' },
  { value: 2, label: '驳回' }
]

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const proTable = ref()

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({})

const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'tempName',
    label: '模板名称',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'contractType',
    label: '合同类型',
    width: '200',
    isColShow: false,
    enum: contractTypeArr,
    search: { el: 'select' }
  },
  {
    prop: 'remark',
    label: '备注',
    width: '200',
    isColShow: false
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: '200',
    isColShow: false
  },
  {
    prop: 'updateBy',
    label: '更新人',
    width: '200',
    isColShow: false
  },
  {
    prop: 'enable',
    label: '使用状态',
    width: '200',
    isColShow: false,
    enum: enableArr,
    search: { el: 'select' }
  }
]

function handleAdd() {
  console.log('handleAdd', route)
  router.push({ path: `/contract-template/form`, query: { redirect: route.fullPath } })
}

const handleDelete = async (ids: string[]) => {
  if (!ids.length) return
  await useHandleData(deleteContractTempDeleteByIds, ids, '删除所选合同模板信息')
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
}
function handleDetail(row: any) {
  console.log('handleDetail', row)
  router.push({ path: `/contract-template/detail`, query: { id: row.id } })
}

function handleChangeEnable(row: any) {
  console.log('handleChangeEnable', row)
  if (!row) return
  nextTick(() => {
    // row.enable = row.enable === '1' ? '0' : '1'
    // row.id = parseInt(row.id)
    postContractTempEnable(row).then(() => {
      proxy.$message.success('操作成功')
      proTable.value?.search()
    })
  })
}
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
