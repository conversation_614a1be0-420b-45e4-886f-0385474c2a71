<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    top="5vh"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      :hide-required-asterisk="['库存详情'].includes(mode)"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="关联企业" prop="customerName">
            <!-- 点击弹窗出现客户列表  -->
            <div style="width: 100%">
              <el-input
                disabled
                v-model="formData.customerName"
                readonly
                maxlength="20"
                :placeholder="['库存详情'].includes(mode) ? '' : '请输入'"
                style="width: 100%"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业编号" prop="customerNo">
            <el-input disabled v-model="formData.customerNo" placeholder="" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="tit-line">
      <div class="tit">库存明细</div>
      <el-checkbox v-if="['库存详情'].includes(mode)" v-model="formData.myFilterFlag" label="隐藏库存为0的资料" />
    </div>
    <div class="my-table">
      <ProTable
        v-if="mode"
        ref="proTable"
        title="资料库存"
        row-key="id"
        :columns="!['库存详情'].includes(mode) ? columnsMaterialStock : columnsMaterialStock_detail"
        :data="formData.recordList"
        :pagination="false"
      >
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button v-if="scope.row.stockNum > 0" type="danger" link @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </ProTable>
    </div>
    <template v-if="['库存详情'].includes(mode)">
      <div class="tit-line">
        <div class="tit">操作记录</div>
      </div>
      <el-radio-group v-model="initParam.type">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group>
      <!-- 此处列表不变动，使用v-show切换 -->
      <div class="my-table" v-show="initParam.type === 0">
        <ProTable
          ref="proTable1"
          title="入库记录"
          :isShowSearch="false"
          :init-param="initParam"
          :columns="columns1"
          :toolButton="false"
          rowKey="id"
          :request-api="materialInboundRecordDetailList"
          :requestAuto="false"
        >
          <template #handoverNo="{ row }">
            <span class="blue-text" @click="handlDetail(row)">{{ row.handoverNo }}</span>
          </template>
        </ProTable>
      </div>
      <div class="my-table" v-show="initParam.type === 1">
        <ProTable
          ref="proTable2"
          title="交接记录"
          :isShowSearch="false"
          :init-param="initParam"
          :columns="columns2"
          :toolButton="false"
          rowKey="id"
          :request-api="materialHandoverRecordList"
          :requestAuto="false"
        >
          <template #handoverNo="{ row }">
            <span class="blue-text" @click="handlDetail(row)">{{ row.handoverNo }}</span>
          </template>
          <template #detailList="{ row }">
            <span v-for="(item, index) in row.detailList" :key="item.id">
              <span>{{ item.category }}</span>
              <span v-if="index < row.detailList.length - 1">，</span>
            </span>
          </template>
        </ProTable>
      </div>
      <div class="my-table" v-show="initParam.type === 2">
        <ProTable
          ref="proTable3"
          title="删除记录"
          :isShowSearch="false"
          :init-param="initParam"
          :columns="columns3"
          :toolButton="false"
          rowKey="id"
          :request-api="materialStockRecordDeleteRecordList"
          :requestAuto="false"
        >
          <template #handoverNo="{ row }">
            <span class="blue-text" @click="handlDetail(row)">{{ row.handoverNo }}</span>
          </template>
        </ProTable>
      </div>
    </template>

    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
    </template>
  </el-dialog>
  <tableModal
    :init-param="{ discard: 0 }"
    v-if="listSelectShow"
    rowKey="customerId"
    title="关联企业"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" />
</template>

<script setup lang="jsx">
import { getCustomers } from '@/api/customer/file'
import tableModal from '@/components/tableModal'
import FormTable from '@/components/FormTable'
import { materialCategoryArr } from '@/utils/constants.js'
import {
  materialStockRecordDelete,
  materialStockRecordGetById,
  materialInboundRecordDetailList,
  materialHandoverRecordList,
  materialStockRecordDeleteRecordList
} from '@/api/customer/material.js'
import { useHandleData } from '@/hooks/useHandleData'
import formModal from '@/views/customer/material-handover/components/form-modal.vue'

import { useDic } from '@/hooks/useDic'
import { nextTick, watch } from 'vue'
const { getDic } = useDic()

const { proxy } = getCurrentInstance()
const emit = defineEmits()

const visible = ref(true)
const disabled = ref(false)
const mode = ref('')

const formRef = ref()
const formData = reactive({
  id: undefined,
  myFilterFlag: true
})
const rules = {
  customerName: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
}

const getDetail = async row => {
  // console.log('getDetail', row)
  await materialStockRecordGetById({ id: row.customerId, mergeFlag: ['编辑库存'].includes(mode.value) ? false : true }).then(
    res => {
      Object.assign(formData, res.data)
      formData.recordListTemp = Object.assign([], formData.recordList)
      myFilterFunc()
    }
  )
}

const onDetail = row => {
  mode.value = '库存详情'
  getDetail(row)
  initParam.customerId = row.customerId
  nextTick(() => {
    proTable1.value?.getTableList()
  })
}
const onEdit = row => {
  mode.value = '编辑库存'
  getDetail(row)
}
const handleClose = () => {
  emit('close')
}
const handleDelete = async row => {
  await useHandleData(materialStockRecordDelete, { id: row.id }, `确认删除${row.category}的库存`)
  getDetail(formData)
  emit('ok')
}

/** 库存明细列表 */
const columnsMaterialStock = [
  {
    prop: 'category',
    width: '150',
    label: '资料类型'
  },
  {
    prop: 'stockNum',
    width: '150',
    label: '库存数量'
  },
  {
    prop: 'custodianUserName',
    width: '150',
    label: '当前保管人'
  },
  {
    prop: 'location',
    width: '150',
    label: '当前位置'
  },
  {
    prop: 'inboundDate',
    width: '150',
    label: '入库月份'
  },
  {
    prop: 'remark',
    minWidth: '150',
    label: '备注'
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 100
  }
]
const columnsMaterialStock_detail = [
  {
    prop: 'category',
    width: '150',
    label: '资料类型'
  },
  {
    prop: 'stockNum',
    width: '150',
    label: '库存数量'
  },
  {
    prop: 'custodianUserName',
    width: '150',
    label: '当前保管人'
  },
  {
    prop: 'remark',
    minWidth: '150',
    label: '备注'
  }
]
const dataCallback = data => {
  console.log('data', data)
  return data
}

function myFilterFunc() {
  if (formData.myFilterFlag && ['库存详情'].includes(mode.value)) {
    formData.recordListTemp = Object.assign([], formData.recordList)
    formData.recordList = formData.recordList.filter(item => item.stockNum > 0)
  } else {
    formData.recordList = Object.assign([], formData.recordListTemp)
  }
}
watch(
  () => [formData.myFilterFlag],
  () => {
    myFilterFunc()
  }
)

/** 客户列表弹窗显示 */
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  if (['库存详情'].includes(mode.value)) return
  listSelectShow.value = true
}
const handleSelect = data => {
  formData.customerName = data.customerName
  formData.customerNo = data.customerNo
  formData.customerId = data.customerId
}
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },
  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

/** 操作记录 */
const tabs = [
  { dicValue: 0, dictLabel: '入库记录' },
  { dicValue: 1, dictLabel: '交接记录' },
  { dicValue: 2, dictLabel: '删除记录' }
]
const proTable1 = ref(null)
const proTable2 = ref(null)
const proTable3 = ref(null)
const initParam = reactive({ customerId: formData.customerId, type: 0, handoverStatus: 'accept' })
const columns1 = [
  {
    prop: 'operatorUserName',
    search: { el: 'input' },
    label: '操作人'
  },
  {
    prop: 'category',
    search: { el: 'select' },
    enum: materialCategoryArr,
    label: '资料类型'
  },
  {
    prop: 'num',
    label: '入库数量'
  },
  {
    prop: 'location',
    label: '入库位置'
  },
  {
    prop: 'inboundDate',
    label: '入库月份'
  },
  {
    prop: 'remark',
    minWidth: '300',
    label: '备注'
  },
  {
    prop: 'operateTime',
    width: '180',
    label: '操作时间'
  }
]
const columns2 = [
  {
    prop: 'handoverNo',
    label: '交接单号'
  },
  {
    prop: 'operatorUserName',
    search: { el: 'input' },
    label: '操作人'
  },
  {
    prop: 'category',
    search: { el: 'select' },
    enum: materialCategoryArr,
    isShow: false,
    label: '资料类型'
  },
  {
    prop: 'recipientUserName',
    search: { el: 'input' },
    label: '接收人'
  },
  {
    prop: 'detailList',
    minWidth: '300',
    label: '交接内容'
  },
  {
    prop: 'operateTime',
    width: '180',
    label: '操作时间'
  }
]
const columns3 = [
  {
    prop: 'operatorUserName',
    search: { el: 'input' },
    label: '操作人'
  },
  {
    prop: 'remark',
    minWidth: '300',
    label: '备注'
  },
  {
    prop: 'operateTime',
    width: '180',
    label: '操作时间'
  }
]
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handlDetail = row => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}

defineExpose({
  onDetail,
  onEdit
})
</script>

<style lang="scss" scoped>
.my-table {
  // min-height: 300px;
  // max-height: 500px;
  // overflow-y: scroll;
  display: flex;
  :deep(.table-box) {
    .card {
      padding: 0;
      border: none;
      box-shadow: none;
    }
    .table-search {
      margin-top: 15px;
      margin-bottom: 0px;
    }
    .table-main {
      min-height: 200px;
      max-height: 400px;
      .table-header {
        margin-bottom: 0px;
      }
    }
  }
}
.tit-line {
  color: #333;
  margin-top: 5px;
  margin-bottom: 10px;
  vertical-align: middle;
  line-height: 32px;
  height: 32px;
  &:before,
  &:after {
    content: '';
    display: table;
  }
  &:after {
    clear: both;
  }
  .tit {
    float: left;
    font-size: 16px;
    font-weight: bold;
    margin-right: 16px;
  }
}
:deep(.el-table .cell) {
  padding: 0 12px 0 0;
}
:deep(.el-form-item--default) {
  .el-form-item__content {
    width: 100%;
  }
}
.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
</style>
