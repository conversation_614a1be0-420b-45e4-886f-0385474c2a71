<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    top="5vh"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      :hide-required-asterisk="['详情'].includes(mode)"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="关联企业" prop="customerName">
            <!-- 点击弹窗出现客户列表  -->
            <div @click="handleListSelectShow" style="width: 100%">
              <el-input
                :disabled="['详情'].includes(mode)"
                v-model="formData.customerName"
                readonly
                maxlength="20"
                :placeholder="['详情'].includes(mode) ? '' : '请输入'"
                style="width: 100%"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业编号" prop="customerNo">
            <el-input disabled v-model="formData.customerNo" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="['详情'].includes(mode)">
          <el-form-item label="创建时间" prop="operateTime">
            <el-input disabled v-model="formData.operateTime" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="['详情'].includes(mode)">
          <el-form-item label="操作人" prop="operatorUserName">
            <el-input disabled v-model="formData.operatorUserName" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="['详情'].includes(mode) && formData.handoverRecordNo">
          <el-form-item label="交接单号" prop="handoverRecordNo">
            <el-input disabled v-model="formData.handoverRecordNo" placeholder="" />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="tit-line">
        <div class="tit">入库明细</div>
        <div v-if="!['详情'].includes(mode)" style="float: right">
          <el-button plain type="primary" @click="handleAdd">新增</el-button>
        </div>
      </div>
      <FormTable ref="formTableRef" :formData="formData" :option="option">
        <!-- @blur="handleBlurInput"  -->
        <template #category="{ row }">
          <el-select v-model="row.category" filterable :disabled="['详情'].includes(mode)">
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in materialCategoryArr"
              :key="index"
            /> </el-select
        ></template>
        <template #num="{ row }">
          <el-input-number :min="1" v-model="row.num" placeholder="请输入" :disabled="['详情'].includes(mode)"></el-input-number>
        </template>
        <template #location="{ row }"
          ><el-input
            v-model="row.location"
            :placeholder="['详情'].includes(mode) ? '' : '请输入'"
            :disabled="['详情'].includes(mode)"
          ></el-input
        ></template>
        <template #inboundDate="{ row }"
          ><el-date-picker
            style="width: 100%"
            v-model="row.inboundDate"
            format="YYYY-MM"
            value-format="YYYY-MM"
            type="month"
            placeholder="请选择"
            :disabled="['详情'].includes(mode)"
        /></template>
        <template #remark="{ row }"
          ><el-input
            style="width: 100%"
            v-model="row.remark"
            :placeholder="['详情'].includes(mode) ? '' : '请输入'"
            :disabled="['详情'].includes(mode)"
          ></el-input
        ></template>
        <template #action="{ row, $index }">
          <el-button
            :disabled="['详情'].includes(mode) || formData.tableData?.length === 1"
            type="danger"
            text
            @click="handleDelete(row, $index)"
            >删除</el-button
          >
        </template>
      </FormTable>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
      <el-button v-if="!['详情'].includes(mode)" type="primary" @click="handleSubmit" :loading="loading"> 保存 </el-button>
    </template>
  </el-dialog>
  <tableModal
    :init-param="{ discard: 0 }"
    v-if="listSelectShow"
    rowKey="customerId"
    title="关联企业"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
</template>

<script setup lang="jsx">
import { getCustomers } from '@/api/customer/file'
import tableModal from '@/components/tableModal'
import FormTable from '@/components/FormTable'
import { materialCategoryArr } from '@/utils/constants.js'
import { materialInboundRecordSave, materialInboundRecordGetById } from '@/api/customer/material.js'

import { useDic } from '@/hooks/useDic'
const { getDic } = useDic()

const { proxy } = getCurrentInstance()
const emit = defineEmits()

const visible = ref(true)
const disabled = ref(false)
const mode = ref('')

const initItem = {
  category: '',
  inboundDate: '',
  location: '',
  num: '',
  remark: ''
}
const formRef = ref()
const formData = reactive({
  id: undefined,
  tableData: [Object.assign({}, initItem)],
  rules: {
    category: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    num: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
  }
})
const rules = {
  customerName: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
}

const getDetail = async row => {
  // console.log('getDetail', row)
  await materialInboundRecordGetById({ id: row.id }).then(res => {
    Object.assign(formData, res.data, { tableData: res.data.detailList }, { operatorUserName: row.operatorUserName })
  })
}
const onAdd = row => {
  mode.value = '新建入库'
}
const onDetail = row => {
  getDetail(row)
  mode.value = '详情'
}
const handleClose = () => {
  emit('close')
}

const loading = ref(false)
const handleSubmit = async () => {
  console.log('formData', formData)
  if (loading.value) return
  formData.inboundDetailList = formData.tableData
  await formRef.value.validate()
  if (await formTableRef.value.handleValidate()) {
    loading.value = true
    materialInboundRecordSave(formData)
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}

/** 入库明细表格 */
const formTableRef = ref(null)
const option = [
  {
    prop: 'category',
    label: '类型',
    width: '150px'
  },
  {
    prop: 'num',
    label: '数量',
    width: '180px'
  },
  {
    prop: 'location',
    label: '入库位置',
    width: '200px'
  },
  {
    prop: 'inboundDate',
    label: '入库月份',
    width: '150px'
  },
  {
    prop: 'remark',
    label: '备注',
    minWidth: '150px'
  },
  {
    prop: 'action',
    label: '操作',
    width: '100px'
  }
]
// 新增行
const handleAdd = () => {
  if (formData?.tableData?.length) {
    formData.tableData.push(Object.assign({}, initItem))
  } else {
    formData.tableData = [Object.assign({}, initItem)]
  }
}
// 删除行
const handleDelete = (row, index) => {
  formData.tableData.splice(index, 1)
}

/** 客户列表弹窗显示 */
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  if (['详情'].includes(mode.value)) return
  listSelectShow.value = true
}
const handleSelect = data => {
  formData.customerName = data.customerName
  formData.customerNo = data.customerNo
  formData.customerId = data.customerId
}
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },
  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

defineExpose({
  onAdd,
  onDetail
})
</script>

<style lang="scss" scoped>
.tit-line {
  color: #333;
  margin-bottom: 5px;
  font-weight: bold;

  &:before,
  &:after {
    content: '';
    display: table;
  }

  &:after {
    clear: both;
  }

  .tit {
    float: left;
    font-size: 16px;
  }
}

:deep(.el-table .cell) {
  padding: 0 12px 0 0;
}

:deep(.el-form-item--default) {
  .el-form-item__content {
    width: 100%;
  }
}

.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);

  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
</style>
