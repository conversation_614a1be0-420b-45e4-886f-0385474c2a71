<!--
 * @Description: 分配线索
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-11-16 10:28:26
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item :label="formData.type === '0' ? '线索接收人' : '客户接收人'" prop="currentUserId">
          <SelectTree
            ref="selectRef"
            v-model="formData.currentUserId"
            placeholder="请选择"
            value-key="id"
            :render-after-expand="false"
            :defaultProps="defaultProps"
            show-checkbox
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import SelectTree from '@/components/SelectTree'
import useUserStore from '@/store/modules/user'
const formData = reactive({
  currentUserId: '',
  type: '0', //'0'代表线索分配,默认为线索分配，'1'为客户分配
  id: undefined
})

const defaultProps = {
  value: 'id',
  label: 'label',
  children: 'children',
  disabled: (data, node) => {
    // 2023-11-16 需求增加 分配线索时之会分配给该公海下的部门
    // 如果 该公海下没有部门  还是按照之前的逻辑
    // 但是该公海下如果有部门 则外加会分配给该公海下的部门
    const isDisabled = data.type === '0' && Array.isArray(data.children) && data.children.length === 0
    if (!formData.deptIds) {
      return isDisabled
    } else {
      return (
        isDisabled ||
        (data.type === '0' && !formData.deptIds.includes(data.id)) ||
        (data.type === '1' && !formData.deptIds.includes(data.parentId))
      )
    }
  }
}
const selectRef = ref()
const rules = {
  currentUserId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
