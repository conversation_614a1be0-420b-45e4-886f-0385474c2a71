<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-position="top"
    :disabled="disabled"
    :hide-required-asterisk="disabled"
  >
    <!-- 【calPaymentAmount-{{ calPaymentAmount }}】 【disabledPaymentAmount-{{ disabledPaymentAmount }}】 -->
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="客户名称" prop="customerName">
          <!-- 点击弹窗出现客户列表  -->
          <div @click="handleListSelectShow" style="width: 100%">
            <el-input
              v-model="formData.customerName"
              readonly
              maxlength="20"
              :placeholder="disabled ? '' : '请输入'"
              style="width: 100%"
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="客户编码" prop="customerNo">
          <el-input disabled v-model="formData.customerNo" placeholder="" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="关联合同" prop="contractNo">
          <!-- 点击弹窗出现合同列表  -->
          <div @click="handleListSelectShowContract" style="width: 100%">
            <el-input
              clearable
              v-model="formData.contractNo"
              readonly
              :placeholder="disabled ? '' : '请输入'"
              style="width: 100%"
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="费用类别" prop="feeType">
          <!-- formData.feeType:{{ formData.feeType }} -->
          <feeTypeTree :disabledBool="disabled" v-model="formData.feeType" @on-select-change="handleSelectChange" />
        </el-form-item>
      </el-col>
      <el-col :span="8" v-if="calPaymentAmount">
        <el-form-item label="优惠" prop="discount">
          <el-select v-model="formData.discount" @change="handleChangeDiscount">
            <el-option label="无优惠" value="无优惠" />
            <el-option label="金额优惠" value="金额优惠" :disabled="formData.isInContract === '1'" />
            <el-option label="时长优惠" value="时长优惠" />
            <el-option label="活动优惠" value="活动优惠" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="calPaymentAmount && formData.discount === '金额优惠'" :span="8">
        <el-form-item label="优惠金额" prop="discountAmount">
          <el-input v-model.trim="formData.discountAmount" :placeholder="disabled ? '' : '请输入'">
            <!-- onkeyup="value = value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" -->
            <template #suffix>元</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col v-if="calPaymentAmount && formData.discount === '时长优惠'" :span="8">
        <el-form-item label="优惠时长" prop="discountTime">
          <el-input v-model="formData.discountTime" :placeholder="disabled ? '' : '请输入'">
            <template #suffix>月</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col v-if="calPaymentAmount && formData.discount === '活动优惠'" :span="8">
        <el-form-item label="活动优惠" prop="activityTxt">
          <el-input
            v-model="formData.activityTxt"
            clearable
            :disabled="flase"
            placeholder="请输入"
            @click="handleShowActivity"
            @clear="handleClearActivity"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8" v-if="calPaymentAmount">
        <el-form-item label="账期开始时间" prop="paymentStartTime">
          <el-date-picker
            :clearable="false"
            style="width: 100%"
            v-model="formData.paymentStartTime"
            type="month"
            format="YYYY/MM"
            value-format="YYYY-MM-DD"
            :disabled="disabled"
            :placeholder="disabled ? ' ' : '请选择'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8" v-if="calPaymentAmount">
        <el-form-item label="账期" prop="paymentDate">
          <el-input
            v-model.trim="formData.paymentDate"
            :disabled="formData.activityId && formData.activityDiscountTime"
            :placeholder="disabled ? '' : '请输入'"
          >
            <!-- onkeyup="value=value.replace(/[^\d]/g,'')" -->
            <template #suffix>月</template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="账款金额" prop="paymentAmount">
          <el-input :disabled="disabledPaymentAmount" v-model.trim="formData.paymentAmount" placeholder="">
            <!-- onkeyup="value = value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" -->
            <template #suffix>元</template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8" v-if="calPaymentAmount">
        <el-form-item label="账期结束时间" prop="paymentEndTime">
          <el-date-picker
            style="width: 100%"
            v-model="formData.paymentEndTime"
            disabled
            type="month"
            format="YYYY/MM"
            value-format="YYYY-MM-DD"
            placeholder=""
          />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="8" v-if="feeTypeAccounts.includes('记账')">
        <el-form-item label="软件账册费" prop="softwareFeeUnitPrice">
          <el-input v-model.trim="formData.softwareFeeUnitPrice" :placeholder="disabled ? '' : '请输入'">
            <template #suffix>{{
              feeTypeBusiness === '0' ? '元' : feeTypeBusiness === '1' ? '元/年' : feeTypeBusiness === '2' ? '元/月' : ''
            }}</template>
          </el-input>
        </el-form-item>
      </el-col> -->
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="是否赠送礼物" prop="isGift">
          <el-radio-group v-model="formData.isGift">
            <el-radio label="否">否</el-radio>
            <el-radio label="是">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="16">
        <el-form-item label="备注" prop="mark">
          <el-input
            :disabled="formData.activityId"
            maxlength="1000"
            v-model="formData.mark"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <tableModal
    :init-param="{ discard: 0 }"
    v-if="listSelectShow"
    rowKey="customerId"
    title="关联客户"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
  <tableModal
    :init-param="initParamContract"
    v-if="listSelectShowContract"
    title="关联合同"
    :columns="columnsContract"
    :request-api="getContractList"
    @on-close="listSelectShowContract = false"
    @on-select="handleSelectContract"
  />
  <tableModal
    v-if="listSelectShowActivity"
    :init-param="{ productId: formData.productId }"
    rowKey="activityId"
    title="活动优惠"
    :columns="columnsActivity"
    :request-api="getActivityListByProductId"
    @on-close="listSelectShowActivity = false"
    @on-select="handleSelectActivity"
  />
  <div class="my-table" v-if="disabled">
    <ProTable
      ref="proTable"
      title="收款台账"
      :isShowSearch="false"
      :init-param="initParam"
      :columns="collectionColumns"
      :toolButton="false"
      rowKey="id"
      :request-api="getFinanceReceiptList"
    >
      <!-- 收款单 -->
      <template #receiptNo="{ row }">
        <span class="blue-text" @click="handleShowCollectionDetail(row.id)">{{ row.receiptNo }}</span>
      </template>
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="handleRelateCollection(formData)">关联收款</el-button>
      </template>
    </ProTable>
  </div>
  <div class="my-title-line" v-if="disabled && feeTypeAccounts.includes('记账')">
    <span class="my-title">余额结算</span>
    <el-button type="primary" :icon="CircleClose" @click="handleCloseCollection()">关闭账单</el-button>
  </div>
  <div class="table-main" v-if="disabled && feeTypeAccounts.includes('记账')">
    <el-table :data="remainingSumData" style="width: 100%">
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column prop="settlementAmount" label="结算金额" minWidth="240">
        <template #default="{ row }">
          <span v-if="row.settlementAmount">{{ row.settlementAmount }}元 </span></template
        >
      </el-table-column>
      <el-table-column prop="operationUserName" label="操作人" minWidth="120" />
      <el-table-column prop="operationTime" label="操作时间" minWidth="120" />
      <el-table-column prop="remark" label="备注" minWidth="120">
        <template #default="{ row }">
          <span v-if="row.remark">{{ row.remark }}元 </span>
          <span v-esle>--</span>
        </template>
      </el-table-column>
      <template #append v-if="!remainingSumData.length">
        <slot name="append"> </slot>
      </template>
      <template #empty v-if="!remainingSumData.length">
        <div class="table-empty">
          <slot name="empty">
            <img src="@/assets/images/notData.png" alt="notData" />
            <div>暂无数据</div>
          </slot>
        </div>
      </template>
    </el-table>
  </div>
</template>

<script setup lang="jsx">
import {
  financePaymentGetPaymentamountByFeeType,
  getFinancePaymentGetSettlementInfo,
  postFinancePaymentClose,
  financePaymentGetById
} from '@/api/finance/accounts-receivable'
import { getFinanceReceiptList, postFinanceReceiptSaveOrUpdate, getFinanceReceiptGetById } from '@/api/finance/collection-ledger'
import { getCustomers } from '@/api/customer/file'
import { getContractList } from '@/api/contract/contract'
import { getBusinessProductGetById } from '@/api/business/business'
import { getActivityListByProductId } from '@/api/business/business'
import { nextTick, reactive, watch } from 'vue'
import dayjs from 'dayjs'
import tableModal from '@/components/tableModal'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import feeTypeTree from '@/views/finance/accounts-receivable/components/fee-type-tree.vue'
import { CirclePlus, CircleClose } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialogFinance'
import collectionForm from '@/views/finance/collection-ledger/components/form.vue'
import bus from 'vue3-eventbus'
import { useDic } from '@/hooks/useDic'
import { changeNumMoneyToChinese } from '@/utils/index.js'

const { getDic } = useDic()
// import { FormValidators } from '@/utils/validate'
const { proxy } = getCurrentInstance()

// const { customer_status } = proxy.useDict('customer_status')
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

const columnsContract = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    width: '300',
    label: '客户名称'
  },
  {
    prop: 'customerNo',
    width: '150',
    label: '客户编号'
  },
  {
    prop: 'contractNo',
    width: '150',
    label: '合同编号',
    search: { el: 'input' }
  },
  {
    prop: 'productName',
    width: '250',
    label: '服务产品'
  },
  {
    prop: 'contractType',
    width: '150',
    label: '合同类型',
    enum: [
      {
        label: '记账合同',
        value: '0'
      },
      {
        label: '一次性合同',
        value: '1'
      },
      {
        label: '地址服务协议合同',
        value: '2'
      }
    ],
    search: { el: 'select' }
  },
  {
    prop: 'totalCost',
    width: '150',
    label: '合同金额'
  },
  {
    prop: 'startTime',
    width: '100',
    label: '起始时间'
  },
  {
    prop: 'endTime',
    width: '100',
    label: '结束时间'
  },
  {
    prop: 'manager',
    width: '100',
    label: '财税顾问'
  }
]

const disabled = ref(false)
//   "contractId": 0,
//   "createBy": "string",
//   "createTime": "2023-06-21T10:33:15.777Z",
//   "customerName": "string",
//   "customerNo": "string",
//   "discount": "string",
//   "discountAmount": 0,
//   "discountRate": 0,
//   "discountTime": 0,
//   "discountTimeRate": 0,
//   "feeType": "string",
//   "id": 0,
//   "isGift": "string",
//   "mark": "string",
//   "paymentAmount": 0,
//   "paymentDate": 0,
//   "paymentEndTime": "2023-06-21T10:33:15.777Z",
//   "paymentStartTime": "2023-06-21T10:33:15.777Z",
//   "productFeeType": "string",
//   "productId": "string",
//   "productQuotation": "string",
//   "updateBy": "string",
//   "updateTime": "2023-06-21T10:33:15.777Z"
const formData = reactive({
  contractId: undefined,
  contractNo: undefined,
  createBy: '',
  createTime: undefined,
  customerId: undefined,
  customerName: '',
  customerNo: '',
  discount: '无优惠',
  discountAmount: undefined,
  discountRate: undefined,
  discountTime: undefined,
  discountTimeRate: undefined,
  feeType: '',
  feetypeIsSingle: undefined,
  id: undefined,
  isGift: '否',
  mark: '',
  paymentAmount: undefined,
  paymentDate: undefined,
  paymentEndTime: undefined,
  paymentStartTime: undefined,
  productFeeType: '', // 0-一次性收费 1-每年收费 2-每月收费
  productId: '',
  productQuotation: '',
  updateBy: '',
  updateTime: undefined,
  isActivity: undefined, // ********废弃该字段
  softwareFeeUnitPrice: undefined, // ********废弃该字段
  outsourcedAccountingBizFlag: undefined,
  activityId: undefined,
  activityTxt: undefined,
  activityDiscountTime: undefined,
  activityQuotation: undefined
})

const disabledPaymentAmount = ref(true) // true：账款金额不可手填输入
const calPaymentAmount = ref(false) // true：会触发计算账款金额
const handleSelectChange = node => {
  console.log('handleSelectChange', node)
  formData.discount = '无优惠'
  clearPartPayment()
  if (node.type === '业务类型') return
  formData.productId = node.id
  formData.productFeeType = node.feeType
  formData.productQuotation = node.quotation
  formData.isInContract = node.isInContract
  // 下方判断不能写在nextSelectChange，会影响到详情打开时的原始数据
  // if (node.activityStatus === '1') {
  //   if (node.discountTime) {
  //     formData.discount = '时长优惠'
  //     formData.discountTime = node.discountTime
  //   }
  //   formData.paymentDate = 12
  //   nextTick(() => {
  //     // watch里内容会被触发，所以晚点再变更值
  //     // 要确保watch-main触发时候，isActivity已经改变
  //     formData.isActivity = true
  //   })
  // } else {
  //   formData.discount = '无优惠'
  //   formData.discountTime = undefined
  //   formData.paymentDate = undefined
  //   nextTick(() => {
  //     formData.isActivity = false
  //   })
  // }
  nextSelectChange(node)
}

// const nodeTemp = {}
const nextSelectChange = node => {
  console.log('nextSelectChange', node)
  // Object.assign(nodeTemp, node)
  if (!formData.discount) formData.discount = '无优惠'
  if (node.types) {
    // 新增或编辑，从处理过的产品tree里获取记账标记types
    if (node.types.findIndex(item => item.value.includes('记账')) !== -1) {
      feeTypeAccounts.value = '记账'
      feeTypeBusiness.value = node.feeType
    } else {
      feeTypeAccounts.value = ''
      feeTypeBusiness.value = ''
    }
  } else {
    // 打开详情，从账单详情获取记账标记outsourcedAccountingBizFlag
    if (formData.outsourcedAccountingBizFlag) {
      feeTypeAccounts.value = '记账'
      feeTypeBusiness.value = node.feeType
    }
  }
  // feeType就是收费类型 0-一次性收费（一次性业务） 1-每年收费（周期性业务） 2-每月收费（周期性业务），然而，在这个表单逻辑里feeType是具体的产品（费用类别的产品id）
  // 如果产品报价无，但合同中有定义，那么是一次性收费，并且自己输入
  // isInContract -- 0 不在合同中定义 1 在合同中定义
  // isInContract === '1' 账款金额不计算、可输入
  if (node.feeType === '0' && node.quotation) {
    // node.feeType === '0' && node.quotation 等价于 node.feeType === '0' && isInContract === '0'
    // 代入报价的一次性收费
    disabledPaymentAmount.value = true
    calPaymentAmount.value = false
    nextTick(() => {
      // 会被watch触发的formData.paymentAmount = undefined逻辑覆写数值，所以nextTick
      if (disabled.value) {
        return
      }
      formData.feetypeIsSingle = 1
      formData.paymentAmount = node.quotation // 详情的时候，不要让这个值覆盖账单里的正确值
    })
  } else if (node.feeType === '0' && !node.quotation) {
    //node.feeType === '0' && !node.quotation  等价于 node.feeType === '0' && isInContract === '1'
    // 合同中定义的一次性收费
    formData.feetypeIsSingle = 1
    disabledPaymentAmount.value = false
    calPaymentAmount.value = false
  } else {
    // 年付、月付，需要计算
    formData.feetypeIsSingle = 0
    disabledPaymentAmount.value = true
    calPaymentAmount.value = true
    console.log('1111')
    nextTick(() => {
      // 在合同中定义的且没有活动,才允许手动输入金额
      // if (node.isInContract === '1' && formData.discount !== '活动优惠') {
      //   console.log('2222')
      //   disabledPaymentAmount.value = false
      //   calPaymentAmount.value = true
      // }
    })
  }
}

/** 活动优惠---start  */
const columnsActivity = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'activityQuotation',
    label: '活动报价',
    width: '100',
    render: scope => {
      return <span>{scope.row.activityQuotation || '--'}元</span>
    }
  },
  {
    prop: 'activityDiscountTime',
    label: '时长',
    width: '100',
    render: scope => {
      return <span>{scope.row.activityDiscountTime || '--'}月</span>
    }
  },
  {
    prop: 'remark',
    label: '备注',
    minWidth: '300'
  }
]
const listSelectShowActivity = ref(false)
const handleShowActivity = () => {
  formRef.value.validateField('paymentStartTime')
  // 如果是详情状态 不需要显示弹窗
  if (disabled.value) return
  if (!formData.paymentStartTime) {
    return proxy.$modal.msgWarning('请先选择账期开始时间')
  }
  if (!formData.productId) {
    return proxy.$modal.msgWarning('请先选择服务产品')
  }
  listSelectShowActivity.value = true
}
const handleSelectActivity = data => {
  console.log('data', data)
  const { activityId, activityQuotation, activityDiscountTime } = data
  const activityTxt = activityDiscountTime ? `${activityQuotation}元，${activityDiscountTime}月` : `${activityQuotation}元`
  Object.assign(formData, {
    activityId,
    activityTxt,
    activityQuotation, // 临时挂载到formData中用于后续处理数据
    activityDiscountTime: activityDiscountTime, // 临时挂载到formData中用于后续处理数据
    paymentDate: activityDiscountTime
  })
  formData.mark = getRemarkTxt()
  formRef.value.validateField('activityTxt')
}
const handleClearActivity = () => {
  clearPartPayment()
}
watch(
  () => [formData.activityId, formData.paymentStartTime, formData.paymentDate],
  () => {
    if (!disabled.value) formData.mark = getRemarkTxt()
  }
)
const getRemarkTxt = () => {
  console.log('getRemarkTxt', formData.activityQuotation)
  const t1 = formData.activityQuotation
    ? `活动价 <${formData.activityQuotation}元>（人民币：<${changeNumMoneyToChinese(formData.activityQuotation)}>）`
    : ``
  const t2 = formData.paymentDate
    ? `时间<${dayjs(formData.paymentStartTime).format('YYYY-MM')}>至<${dayjs(formData.paymentStartTime)
        .add(formData.paymentDate, 'month')
        .add(-1, 'day')
        .format('YYYY-MM')}>`
    : ``
  const remark = formData.paymentStartTime && formData.activityId ? (t1 && t2 ? `${t1}，${t2}` : t1 ? t1 : t2 ? t2 : ``) : ``
  return remark
}
/** 活动优惠---end  */

function handleChangeDiscount() {
  clearPartPayment()
}

function clearPartPayment() {
  formData.discountAmount = undefined
  formData.discountTime = undefined
  if (formData.activityId) formData.mark = undefined
  formData.activityId = undefined
  formData.activityTxt = undefined
  // formData.paymentStartTime = undefined
  formData.paymentDate = undefined
  formData.paymentAmount = undefined
  formData.paymentEndTime = undefined
}

// // 修改任意优惠都会让活动价失效
// watch(
//   () => [formData.discount, formData.discountTime, formData.paymentDate],
//   () => {
//     console.log('watch-formData.discount, formData.discountTime, formData.paymentDate')
//     nextSelectChange(nodeTemp)
//   }
// )

watch(
  () => formData.id,
  async () => {
    console.log('watch-formData.id')
    if (formData.id && formData.productId) {
      const res = await getBusinessProductGetById(formData.productId)
      nextSelectChange(res.data)
    }
  }
)

// formData.feeType
watch(
  () => [
    formData.activityId,
    formData.paymentStartTime,
    formData.paymentDate,
    formData.discount,
    formData.discountAmount,
    formData.discountTime
  ],
  // watchEffect(
  async () => {
    console.log(
      'watch-formData.paymentStartTime, formData.paymentDate, formData.discount, formData.discountAmount, formData.discountTime'
    )
    if (formData.paymentStartTime && formData?.paymentDate > 0) {
      if (disabled.value) {
        calPaymentAmount.value = true
        return
      }
      if (formData.discount === '金额优惠' && !formData.discountAmount) return
      if (formData.discount === '时长优惠' && !formData.discountTime) return
      if (formData.discount === '活动优惠' && !formData.activityId) return
      if (formData.isInContract === '1' && formData.discount !== '活动优惠') {
        disabledPaymentAmount.value = false
      }
      formData.paymentEndTime = dayjs(formData.paymentStartTime)
        .add(formData.paymentDate, 'month')
        .add(formData.discount === '时长优惠' ? formData.discountTime : 0, 'month') // 优惠时长体现为延长账期结束时间
        .add(-1, 'day')
        .format('YYYY-MM-DD')
      // console.log('formData.paymentEndTime', formData.paymentEndTime)
      if (
        calPaymentAmount.value &&
        (formData.isInContract !== '1' || formData.activityId) &&
        (await formRef.value.validateField('paymentDate'))
      ) {
        const data = {
          discount: formData.discount,
          discountAmount: formData.discountAmount,
          discountTime: formData.discountTime,
          paymentDate: formData.paymentDate,
          paymentStartTime: formData.paymentStartTime,
          productFeeType: formData.productFeeType,
          productId: formData.productId,
          productQuotation: formData.productQuotation,
          activityId: formData.activityId
        }
        financePaymentGetPaymentamountByFeeType(data).then(res => {
          formData.paymentAmount = res.data
          formRef.value.validateField('paymentAmount')
        })
      } else {
        formData.paymentAmount = undefined
      }
    } else {
      // 失去月付年付的账款金额、账期结束时间的必要计算条件时，置空
      // 详情时候不要触发
      if (disabled.value) {
        return
      }
      formData.paymentAmount = undefined
      formData.paymentEndTime = undefined
    }
  }
)

const validatePaymentDate = (rule, value, callback) => {
  if (parseInt(formData.productFeeType) === 1 && value % 12 !== 0 && (!formData.activityId || !formData.activityDiscountTime)) {
    callback(new Error('收费类型是按年收费，账期只能是12的倍数'))
  } else {
    callback()
  }
}

const rules = {
  customerName: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  feeType: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  discountAmount: [
    { required: true, message: '请输入', trigger: 'blur' },
    {
      pattern: /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,2})?$/,
      message: '请输入正确的金额',
      trigger: 'blur'
    }
  ],
  discountTime: [{ required: true, message: '请输入', trigger: 'blur' }],
  activityTxt: [{ required: true, message: '请输入', trigger: 'blur' }],
  paymentStartTime: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  paymentDate: [
    { required: true, message: '请输入', trigger: ['blur'] },
    { validator: validatePaymentDate, trigger: ['blur'] },
    {
      pattern: /^[1-9]\d*$/,
      message: '请输入账期数字',
      trigger: ['blur']
    }
  ],
  paymentAmount: [
    {
      pattern: /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,2})?$/,
      message: '请输入正确的金额',
      trigger: ['blur']
    },
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

// 客户列表弹窗显示
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  if (disabled.value) return
  listSelectShow.value = true
}
const handleSelect = data => {
  // console.log('data', data)
  formData.customerName = data.customerName
  formData.customerNo = data.customerNo
  formData.customerId = data.customerId
  formData.contractNo = undefined
  formData.contractId = undefined
}
// 合同列表弹窗显示
const initParamContract = reactive({})
const listSelectShowContract = ref(false)
const handleListSelectShowContract = () => {
  if (disabled.value) return
  if (!formData.customerId) {
    proxy.$modal.msgWarning(`请先选择客户`)
    return
  }
  initParamContract.customerId = formData.customerId
  listSelectShowContract.value = true
}
const handleSelectContract = data => {
  // console.log('data', data)
  formData.contractNo = data.contractNo
  formData.contractId = data.contractId
}

defineExpose({
  disabled,
  formData,
  getFormRef
})

/* 关联收款单 start */
// const paymentNo = inject('paymentNo')
const paymentNoFun = e => {
  console.log('bus-paymentNo', e)
  initParam.paymentNo = e
}
bus.on('paymentNo', paymentNoFun)

const proTable = ref()
const initParam = reactive({})

const collectionColumns = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'receiptNo',
    label: '收款单编号',
    width: '200',
    isColShow: false
  },
  {
    prop: 'payee',
    width: '100',
    label: '收款人'
  },
  {
    prop: 'receivableAmount',
    width: '100',
    label: '待收金额',
    render: scope => {
      return <span>{scope.row.receivableAmount || 0}元</span>
    }
  },
  {
    prop: 'receiptAmount',
    width: '100',
    label: '收款金额',
    render: scope => {
      return <span>{scope.row.receiptAmount || 0}元</span>
    }
  },
  {
    prop: 'receiptDate',
    width: '180',
    label: '收款时间'
  },
  {
    prop: 'receiptMethod',
    width: '100',
    label: '收款渠道'
  },
  // {
  //   prop: 'xx',
  //   width: '120',
  //   label: '收款凭证xx'
  // },
  {
    prop: 'mark',
    // width: '100',
    label: '收款备注'
  }
]
const { showDialog } = useDialog()
const handleRelateCollection = row => {
  console.log(row)
  showDialog({
    title: '关联收款',
    component: collectionForm,
    customClass: 'customer-dialog',
    rowFormData: {
      customerName: row.customerName,
      customerId: row.customerId,
      customerNo: row.customerNo,
      paymentId: row.id,
      paymentNo: row.paymentNo
    },
    submitApi: postFinanceReceiptSaveOrUpdate,
    submitCallback,
    handleRevertParams: handleRevertParamsCollection
  })
}
// 处理表单提交参数
const handleRevertParamsCollection = data => {
  if (Array.isArray(data.receiptVoucherFile) && data.receiptVoucherFile[0]) {
    const file = data.receiptVoucherFile[0]
    data.receiptVoucherFile = {
      fileSize: file.uploadSize,
      fileNames: file.newFileName,
      // bizType: 'dddd', // 假数据
      uploadBy: file.uploadBy,
      uploadTime: file.uploadTime,
      urls: file.url
    }
  } else {
    delete data.receiptVoucherFile
  }
}
const submitCallback = () => {
  setTimeout(() => {
    proxy.$modal.msgSuccess(`新增收款审核通过后将完成关联，待审核收款可在收款审核中查看`)
  }, 1000)
}
const handleShowCollectionDetail = id => {
  console.log('id', id)
  showDialog({
    title: '收款详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: collectionForm,
    getApi: getFinanceReceiptGetById,
    requestParams: { id }
  })
}
/* 关联收款单 end */

/* 余额结算 start */
const handleCloseCollection = () => {
  proxy.$modal
    .confirm('是否确认关闭该账单?')
    .then(function () {
      return postFinancePaymentClose({ id: formData.id }).then(res => {
        if (res.code === 200) {
          proxy.$message.success(res.msg)
          getFinancePaymentGetSettlementInfo({ paymentId: initParam.paymentId }).then(res => {
            remainingSumData.value = res.data
          })
          financePaymentGetById({ id: initParam.paymentId }).then(res => {
            Object.assign(formData, res.data)
          })
        }
      })
    })
    .catch(() => {})
}

const remainingSumData = ref([])
const paymentIdFun = e => {
  console.log('bus-paymentId', e)
  initParam.paymentId = e
  getFinancePaymentGetSettlementInfo({ paymentId: initParam.paymentId }).then(res => {
    remainingSumData.value = res.data
  })
}
bus.on('paymentId', paymentIdFun)

const feeTypeBusiness = ref('')
const feeTypeAccounts = ref('')
/* 余额结算 end */

onBeforeUnmount(() => {
  bus.off('paymentNo', paymentNoFun)
  bus.off('paymentId', paymentIdFun)
})
</script>
<style lang="scss">
.my-title-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  margin-bottom: 10px;
  .my-title {
    font-weight: bold;
    font-size: 16px;
    color: #333;
  }
}
.my-table {
  min-height: 300px;
  display: flex;
  .el-table__append-wrapper {
  }
  .card {
    box-sizing: border-box;
    padding: 0;
    overflow-x: hidden;
    background-color: none;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }
}
</style>
<style lang="scss" scoped>
.el-select {
  width: 573px;
}
:deep(.el-table__append-wrapper) {
  min-height: 110px;
}
</style>
