/*
 * @Description:
 * @Author: thb
 * @Date: 2023-06-16 17:32:09
 * @LastEditTime: 2023-06-16 17:37:01
 * @LastEditors: thb
 */
import translations from './translationsGerman'

export default function customTranslate(template: any, replacements: any) {
  replacements = replacements || {}

  // Translate
  template = translations[template as keyof typeof translations] || template

  // Replace
  return template.replace(/{([^}]+)}/g, function (_: any, key: any) {
    return replacements[key] || '{' + key + '}'
  })
}
