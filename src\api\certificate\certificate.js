// 业务管理api
import request from '@/utils/request'

// 会计通知
export function bizAccountantNotify(query) {
  return request({
    url: '/licenseBizTask/accountantNotify',
    method: 'post',
    data: query
  })
}
// 指派人员处理
export function bizAssign(query) {
  return request({
    url: '/licenseBizTask/assign',
    method: 'post',
    data: query
  })
}
// 银行开户业务
export function bizBankAccountOpen(query) {
  return request({
    url: '/licenseBizTask/bankAccountOpen',
    method: 'post',
    data: query
  })
}
// 银行开户业务
export function bizBankAccountOpenComplete(query) {
  return request({
    url: '/licenseBizTask/bankAccountOpenComplete',
    method: 'post',
    data: query
  })
}

// 数据收集阶段请求处理
export function bizDataCollection(query) {
  return request({
    url: '/licenseBizTask/dataCollection',
    method: 'post',
    data: query
  })
}
// 数据收集阶段请求处理
export function bizDataCollectionComplete(query) {
  return request({
    url: '/licenseBizTask/dataCollectionComplete',
    method: 'post',
    data: query
  })
}
// 信息上传
export function bizDataUpload(query) {
  return request({
    url: '/licenseBizTask/dataUpload',
    method: 'post',
    data: query
  })
}
// 信息上传
export function bizDataUploadComplete(query) {
  return request({
    url: '/licenseBizTask/dataUploadComplete',
    method: 'post',
    data: query
  })
}
// 转单
export function bizDelegate(query) {
  return request({
    url: '/licenseBizTask/delegate',
    method: 'post',
    data: query
  })
}
// 废弃
export function bizDeprecate(query) {
  return request({
    url: '/licenseBizTask/deprecate?id=' + query.id,
    method: 'post'
  })
}
// 详情
export function bizGetById(params) {
  return request({
    url: '/licenseBizTask/getById',
    method: 'get',
    params
  })
}
// 列表
export function bizList(params) {
  return request({
    url: '/licenseBizTask/list',
    method: 'get',
    params
  })
}
// 办理进度汇报
export function bizProcessReport(query) {
  return request({
    url: '/licenseBizTask/processReport',
    method: 'post',
    data: query
  })
}

// 办理进度汇报
export function bizProcessReportComplete(query) {
  return request({
    url: '/licenseBizTask/processReportComplete',
    method: 'post',
    data: query
  })
}
// 许可证资料收集保存
export const saveLicenseDataCollection = data => {
  return request({
    url: '/licenseBizTask/permitDataCollection',
    method: 'post',
    data
  })
}

// 许可证资料收集提交
export const submitLicenseDataCollection = data => {
  return request({
    url: '/licenseBizTask/permitDataCollectionComplete',
    method: 'post',
    data
  })
}
// 许可证办理进度汇报保存
export const saveLicenseProcessReport = data => {
  return request({
    url: '/licenseBizTask/permitProcessReport',
    method: 'post',
    data
  })
}

// 许可证办理进度汇报提交
export const submitLicenseProcessReport = data => {
  return request({
    url: '/licenseBizTask/permitProcessReportComplete',
    method: 'post',
    data
  })
}
// 许可证信息上传保存
export const saveLicenseDataUpload = data => {
  return request({
    url: '/licenseBizTask/permitDataUpload',
    method: 'post',
    data
  })
}

// 许可证信息上传提交
export const submitLicenseDataUpload = data => {
  return request({
    url: '/licenseBizTask/permitDataUploadComplete',
    method: 'post',
    data
  })
}
//  saveLogoutDataUpload,
// 工商注销保存
export const saveLogoutDataUpload = data => {
  return request({
    url: '/licenseBizTask/businessCancellation',
    method: 'post',
    data
  })
}
// 工商注销提交
//  submitLogoutDataUpload
export const submitLogoutDataUpload = data => {
  return request({
    url: '/licenseBizTask/businessCancellationComplete',
    method: 'post',
    data
  })
}
// 银行注销保存
export const saveBankLogoutDataUpload = data => {
  return request({
    url: '/licenseBizTask/businessCancellationProcessReport',
    method: 'post',
    data
  })
}
// 银行注销提交

export const submitBankLogoutDataUpload = data => {
  return request({
    url: '/licenseBizTask/businessCancellationProcessReportComplete',
    method: 'post',
    data
  })
}

// 工商变更进度保存
export const saveBusinessProcessChange = data => {
  return request({
    url: '/licenseBizTask/changeCollection',
    method: 'post',
    data
  })
}
// 工商变更进度提交

export const submitBusinessProcessChange = data => {
  return request({
    url: '/licenseBizTask/changeCollectionComplete',
    method: 'post',
    data
  })
}
// 工商资料更新保存
export const saveBusinessUpdateChange = data => {
  return request({
    url: '/licenseBizTask/dataUpdates',
    method: 'post',
    data
  })
}
// 工商资料更新提交

export const submitBusinessUpdateChange = data => {
  return request({
    url: '/licenseBizTask/dataUpdatesComplete',
    method: 'post',
    data
  })
}

// 列表导出文件

export const certificateExport = query => {
  return request({
    url: '/licenseBizTask/export',
    method: 'post',
    data: query
  })
}
