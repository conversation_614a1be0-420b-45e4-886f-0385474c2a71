<!--
 * @Description: 普通新建合同(区别于模板创建)
 * @Author: thb
 * @Date: 2023-06-20 10:22:40
 * @LastEditTime: 2024-03-04 14:47:06
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    class="normal-contract"
    title="新增合同"
    width="1200"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <!-- 签约业务 -->
    <!-- <p class="p-title">签约业务</p> -->
    <!-- 产品选择区 -->
    <FormTable :formData="formTableData" :option="option">
      <template #name="{ row }">
        <el-tree-select
          v-model="row.id"
          :ref="el => setTreeSelectRef(el, row.id)"
          filterable
          :data="productTreeData"
          :props="defaultPopsFunction(row)"
          node-key="id"
          @current-change="(node, nodeData) => handleSelectChange(node, nodeData, row)"
          :render-after-expand="false"
        />
      </template>
      <template #contractType="{ row }">
        <el-select v-model="row.contractType" placeholder="请选择" @change="value => handleChange(value, row)">
          <el-option
            :label="item.label"
            :value="item.label"
            :disabled="item.label === '一次性合同' && (row.feeType === '1' || row.feeType === '2')"
            v-for="(item, index) in row?.types"
            :key="index"
          />
        </el-select>
      </template>
      <template #action="{ row, $index }">
        <el-button type="primary" text @click="handleAdd">新增</el-button>
        <el-button type="danger" text @click="handleDelete(row, $index)" :disabled="formTableData.tableData.length === 1"
          >删除</el-button
        >
      </template>
    </FormTable>
    <!-- 合同表单项 -->
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane :label="tab.label" :name="tab.productionId" v-for="tab in tabs" :key="tab">
        <!-- 不同的合同类型对应不同的表单 -->
        <component
          :is="tabMap[tab.type]"
          :isAssociated="isAssociated"
          :ref="el => setCompRef(el, tab.productionId)"
          v-model="tab.data"
        />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button type="primary" @click="handleSubmit">保存信息</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
// import { abandonCustomer } from '@/api/customer/file'
import { getBusinessList } from '@/api/business/business'
import { saveBatchContract } from '@/api/contract/contract'
import FormTable from '@/components/FormTable'
import accountingForm from './accounting-form.vue'
import oneOff from './one-off.vue'
import addressService from './address-service.vue'
import { changeFileData } from '@/utils/common'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  associated: {
    type: Object,
    default: () => {
      return {}
    }
  },
  productIds: {
    type: Array,
    default: () => {
      return []
    }
  }
})

// 判断是否已经被关联客户了,默认为false 不关联
const isAssociated = ref(false)
if (Object.keys(props.associated).length) {
  isAssociated.value = true
}
// const { proxy } = getCurrentInstance()
// 批量保存 注意: 要检验每个合同下的表单
// 动态组件的ref绑定
const compRefs = {}
const setCompRef = (el, id) => {
  if (el) {
    compRefs[id] = el
  }
  console.log('treeSelectRefs', compRefs)
}
const handleSubmit = async () => {
  console.log('compRef', compRefs)
  if (!tabs.value.length) {
    // 说明没有合同，提示创建合同
    proxy.$modal
      .confirm(`请选择合同中的服务产品及合同类型后再次尝试创建合同!`)
      .then(() => {})
      .catch(() => {})
    return
  }
  // 遍历循环compRefs 进行校验
  for (let [productionId, comRef] of Object.entries(compRefs)) {
    console.log('key', productionId)
    console.log('value', comRef)
    const result = await comRef.validateForm()
    console.log('result', result)
    if (!result) {
      // 如果检验没成功，需要提示
      const tabName = tabs.value.filter(item => item.productionId === productionId)[0]
      proxy.$modal
        .confirm(`${tabName?.label}校验有误，请仔细检查！`)
        .then(() => {})
        .catch(() => {})
      return
    }
  }

  // 执行到这儿说明全部校验成功触发 批量保存接口
  const batchData = tabs.value.map(item => {
    if (item.data.rowData?.contractType === '记账合同') {
      // 如果是记账合同
      item.data.startTime = item.data.startTime + '-01'
      item.data.endTime = item.data.endTime + '-01'
    }
    return {
      ...item.data,
      file: Array.isArray(item.data.file) && item.data.file[0] ? changeFileData(item.data.file) : undefined,
      ciId: item.data.customerId,
      type: '0', // 录入合同,
      salesRevenue: item.data.salesRevenue === '其他' ? '其他/' + (item.data.otherSalesText || '') : item.data.salesRevenue
      // changeStartTime: item.data.changeStartTime ? item.data.changeStartTime + '-01' : ''
    }
  })
  console.log('batchData', batchData)
  const data = await saveBatchContract(batchData)
  if (data.code === 200) {
    // 说明批量保存成功
    proxy.$modal.msgSuccess(`保存成功!`)
    handleClose()
    emits('on-success')
  } else {
    proxy.$modal.msgError(`保存失败!`)
  }
}
const formTableData = ref({
  tableData: [
    {
      name: '',
      id: '', // productId
      contractType: ''
    }
  ]
})

// watch(
//   () => props.productIds,
//   () => {
//     console.log(' props.productIds', props.productIds, productTreeData.value)
//     formTableData.value.tableData = props.productIds.map(id => {
//       return {
//         name: '',
//         id: id, // productId
//         productionId: id, // productId
//         contractType: ''
//       }
//     })
//   },
//   {
//     immediate: true
//   }
// )
const option = [
  {
    prop: 'name',
    label: '产品名称'
  },
  {
    prop: 'contractType',
    label: '合同类型'
  },
  {
    prop: 'action',
    label: '操作'
  }
]

// 获取所有的产品名称
const productTreeData = ref()
const defaultPopsFunction = row => {
  return {
    value: 'id',
    label: 'name',
    disabled: data => {
      return (
        (data?.type === '业务类型' && !data?.children.length) ||
        (data?.type === '产品类型' && row.id !== data.id && formTableData.value.tableData.map(item => item.id).includes(data.id))
      )
    }
  }
}

const getAllProducts = async () => {
  const { data } = await getBusinessList({
    pageNum: 1,
    pageSize: 10000
  })
  // 将后端传回的数据结构进行转换
  const revertData = []
  data.forEach(item => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      children: []
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach(child => {
        obj.children.push({
          name: child.productName,
          types:
            item.contractType?.split(',')?.map(item => {
              return {
                label: item,
                value: item
              }
            }) || [],
          type: '产品类型',
          quotation: child.quotation, // 合同报价
          isInContract: child.isInContract, // 是否在合同中定义
          feeType: child.feeType, //收费类型
          id: child.id // 产品类型id
        })
      })
    }
  })

  console.log('revertData', revertData)
  productTreeData.value = revertData || []
}

// 产品下拉框选择事件
const treeSelectRefs = {}
const setTreeSelectRef = (el, id) => {
  if (el) {
    treeSelectRefs[id] = el
  }
}
const handleSelectChange = (node, nodeData, row) => {
  const ids = formTableData.value.tableData.filter(item => item.productionId).map(item => item.productionId)
  // 如果row.id 已经存在且tab下已经有该产品id
  const tabIds = tabs.value.map(item => item.productionId)
  // 判断是否对已经创建的产品合同进行修改产品

  let isCancel = false
  if (row.productionId && tabIds.includes(row.productionId) && node.type === '产品类型' && node.id !== row.productionId) {
    // 如果修改则需要提示
    proxy.$modal
      .confirm('请注意,更改产品将不保存已填写的信息!')
      .then(() => {
        // 确认更改 则需要删除原有的产品合同
        const tabIndex = tabs.value.findIndex(item => item.productionId === row.productionId)
        tabs.value.splice(tabIndex, 1)
        delete compRefs[row.productionId]
        console.log('compRefs', compRefs)
        // 清空当前的合同类型
        row.contractType = ''
        row.types = node.types || []
        row.productionId = node.id // 产品id
        row.name = node.name // 产品名称
        row.quotation = node.quotation // 合同报价
        row.isInContract = node.isInContract // 是否在合同中定义
        row.feeType = node.feeType // 收费类型
        // activeName默认前一个tab
        activeName.value = tabs.value[tabIndex === 0 ? 0 : tabIndex - 1].productionId
      })
      .catch(action => {
        // 不更改
        // row.id = row.productionId
        // 使用setCurrentKey 设置当前选择的节点id
        console.log('取消')
        if (action === 'cancel') {
          isCancel = true
          treeSelectRefs[row.productionId].setCurrentKey(row.productionId)
          row.id = row.productionId
        }
      })

    return
  }
  if (isCancel) return
  if (!ids.includes(node.id) && node.type === '产品类型') {
    row.types = node.types || []
    row.productionId = node.id // 产品id
    row.name = node.name // 产品名称
    row.quotation = node.quotation // 合同报价
    row.isInContract = node.isInContract // 是否在合同中定义
    row.feeType = node.feeType // 收费类型
    // 清空当前的合同类型
    row.contractType = ''
  }
}

// 新增行
const handleAdd = () => {
  formTableData.value.tableData.push({
    name: '',
    contractType: ''
  })
}

// 删除行
const handleDelete = (row, index) => {
  if (row.contractType) {
    // 如果该行的合同类型已经存在
    proxy.$modal
      .confirm('请注意,删除该行将不保存已填写的信息!')
      .then(() => {
        // 确认更改
        // 删除已经存在的tab
        const tabIndex = tabs.value.findIndex(item => item.productionId === row.productionId)
        tabs.value.splice(tabIndex, 1)
        formTableData.value.tableData.splice(index, 1)
        // activeName默认前一个tab
        activeName.value = tabs.value[tabIndex === 0 ? 0 : tabIndex - 1].productionId
        // 删除之后需要将相应的tab绑定的ref绑定
        delete compRefs[row.productionId]
      })
      .catch(error => {
        console.log('error', error)
      })
  } else {
    formTableData.value.tableData.splice(index, 1)
  }
}

// 表格行中选择合同类型后触发的选择事件产生tab
const { proxy } = getCurrentInstance()
const contractTypeMap = {
  记账合同: '0',
  一次性合同: '1',
  地址服务协议合同: '2'
}
const handleChange = (value, row) => {
  activeName.value = row.productionId
  // 判断目前tabs中id集合中是否存在已有的id
  const ids = tabs.value.map(item => item.productionId)
  console.log('ids', ids)
  console.log('rowId', row.productionId)
  if (!ids.includes(row.productionId)) {
    if (props.associated) {
      // 如果存在父组件传递的关联客户
      const {
        customerName,
        customerId,
        customerNo,
        contactPerson,
        contactPhone,
        branchOffice,
        address,
        companyPerson,
        companyAddress,
        companyPhone
      } = props.associated
      const tabData = {
        label: value + `(${row.name})`,
        type: value,
        data: {
          priceChangeFlag: false,
          productId: row.productionId,
          rowData: row, // 存储对应id下的row数据
          productName: row.name,
          contractType: contractTypeMap[value], // 合同类型
          serviceCost: row.quotation, // 服务费
          declare: value === '记账合同' ? '1' : undefined, // 如果是记账合同默认为'0',
          isEstablish: value === '记账合同' ? '0' : undefined, // 如果是记账合同默认为'0',
          customerName, // 关联客户名称,
          customerId, // 客户id
          customerNo, //客户编码
          contactPerson, // 联系人
          contactPhone, // 联系电话
          branchOffice, // 所属公司
          companyPerson, // 所属公司联系人
          companyAddress, // 所属公司地址
          companyPhone // 所属公司联系方式
        }, // 对应合同的表单数据存储
        productionId: row.productionId // 产品id
      }
      if (value === '地址服务协议合同') {
        tabData.data.contactAddress = address
        tabData.data.companyName = customerName // 拟注册成立企业名称
        tabData.data.legalPerson = contactPerson // 拟注册成立企业法定代表人姓名
        tabData.data.legalPhone = contactPhone // 拟注册成立企业法定代表人联系电话

        Object.assign(tabData.data, {
          companyAddress: '',
          companyPerson: contactPerson,
          companyPhone: contactPhone,
          contactPhone: ''
        })
      }
      if (value === '一次性合同') {
        tabData.data.address = address
      }
      tabs.value.push(tabData)
    } else {
      tabs.value.push({
        label: value + `(${row.name})`,
        type: value,
        data: {
          productId: row.productionId,
          rowData: row, // 存储对应id下的row数据
          productName: row.name,
          priceChangeFlag: false,
          contractType: contractTypeMap[value], // 合同类型
          serviceCost: row.quotation, // 服务费
          declare: value === '记账合同' ? '1' : undefined, // 如果是记账合同默认为'0'
          isEstablish: value === '记账合同' ? '0' : undefined // 如果是记账合同默认为'0'
        }, // 对应合同的表单数据存储
        productionId: row.productionId // 产品id
      })
      console.log('tabs', tabs.value)
    }

    // nextTick(() => {
    //   tabs.value.forEach(e => (compRef.value[e.productionId] = ref(null)))
    // })
  } else {
    // 修改产品对应的合同类型情况 需要提示
    console.log('已经存在对应的id')
    proxy.$modal
      .confirm('请注意,更改合同类型将不保存已填写的信息!')
      .then(() => {
        // 确认更改
        // 替换tab以及tab下的内容
        const tab = tabs.value.filter(item => item.productionId == row.productionId)[0]
        if (tab) {
          tab.label = value + `(${row.name})`
          tab.type = value
          // 修改原有数据
          // 将原有已填数据置空
          tab.data = {
            productId: row.productionId,
            priceChangeFlag: false,
            rowData: row, // 存储对应id下的row数据
            productName: row.name,
            contractType: contractTypeMap[value], // 合同类型
            serviceCost: row.quotation, // 服务费

            declare: value === '记账合同' ? '1' : undefined // 如果是记账合同默认为'0'
          }
          // tab.data.contractType = contractTypeMap[value]
          // tab.data.declare = value === '记账合同' ? '0' : undefined // 如果是记账合同默认为'0'
        }
      })
      .catch(() => {
        // 不更改
        const tab = tabs.value.filter(item => item.productionId == row.productionId)[0]
        if (tab) {
          row.contractType = tab.label.split('(')[0]
        }
      })
  }
}
// tabs
const activeName = ref()
const tabs = ref([])

const tabMap = {
  记账合同: accountingForm,
  一次性合同: oneOff,
  地址服务协议合同: addressService
}
// tabClick
const handleTabClick = () => {}
// 根据节点id 查询对应id节点
const getTreeNodeById = (treeList, id) => {
  for (let i = 0; i < treeList.length; i++) {
    let node = treeList[i]
    if (node.id === id) {
      return node
    } else {
      if (node.children && node.children.length > 0) {
        let resNode = getTreeNodeById(node.children, id)
        if (resNode) {
          return resNode
        }
      }
    }
  }
}
onMounted(async () => {
  await getAllProducts()
  // 如果存在新建档案后的ids
  if (props.productIds && props.productIds.length > 0) {
    console.log('productTreeData', productTreeData.value)
    const nodeList = []
    props.productIds.forEach(id => {
      const node = getTreeNodeById(productTreeData.value, id)
      nodeList.push(node)
    })
    formTableData.value.tableData = nodeList.map(node => {
      return {
        types: node.types || [],
        id: node.id,
        productionId: node.id, // 产品id
        name: node.name, // 产品名称
        quotation: node.quotation, // 合同报价
        isInContract: node.isInContract, // 是否在合同中定义
        feeType: node.feeType // 收费类型
      }
    })
    console.log('tableData', formTableData.value.tableData)
  }
})
</script>
<style lang="scss" scoped>
.p-title {
  font-size: 18px;
  font-weight: 700;
  margin-top: 0;
  color: #2a2f35;
}
:deep(.el-select) {
  width: 100%;
}
</style>
