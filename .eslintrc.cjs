// @see: http://eslint.cn
require('@rushstack/eslint-patch/modern-module-resolution')
module.exports = {
  root: true,

  env: {
    browser: true,
    node: true,
    es6: true
  },
  // 指定如何解析语法
  parser: 'vue-eslint-parser',
  // 优先级低于 parse 的语法解析配置
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaVersion: 2020,
    sourceType: 'module',
    jsxPragma: 'React',
    ecmaFeatures: {
      jsx: true
    }
  },
  // 继承某些已有的规则
  extends: ['plugin:vue/vue3-recommended', 'plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended'],
  /**
   * "off" 或 0    ==>  关闭规则
   * "warn" 或 1   ==>  打开的规则作为警告（不影响代码执行）
   * "error" 或 2  ==>  规则作为一个错误（代码不能执行，界面报错）
   */
  rules: {
    // eslint (http://eslint.cn/docs/rules)
    'no-var': 'error', // 要求使用 let 或 const 而不是 var
    'no-multiple-empty-lines': ['error', { max: 1 }], // 不允许多个空行
    'prefer-const': 'off', // 使用 let 关键字声明但在初始分配后从未重新分配的变量，要求使用 const
    'no-use-before-define': 'off', // 禁止在 函数/类/变量 定义之前使用它们
    'no-irregular-whitespace': 'off', // 禁止不规则的空白
    'vue/valid-define-emits': 'off',
    'prettier/prettier': [
      'error',
      {
        singleQuote: true, // 使用单引号
        trailingComma: 'none', // 在对象或数组最后一个元素不加逗号
        tabWidth: 2, // 缩进长度
        useTabs: false, // 是否Tab缩进代替空格
        semi: false, // 是否在末尾添加分号
        endOfLine: 'auto', // 结束行形式
        bracketSpacing: true, // 在对象前后添加空格-eg: { foo: bar }
        arrowParens: 'avoid' // 单参数箭头函数参数周围不使用圆括号
        // singleAttributePerLine: true, // html标签属性换行
        // bracketSameLine: true // 多属性html标签的‘>’不折行放置
      }
    ],
    '@typescript-eslint/no-this-alias': 'off',
    // typeScript (https://typescript-eslint.io/rules)
    '@typescript-eslint/no-unused-vars': 'off', // 禁止定义未使用的变量
    '@typescript-eslint/prefer-ts-expect-error': 'error', // 禁止使用 @ts-ignore
    '@typescript-eslint/no-inferrable-types': 'off', // 可以轻松推断的显式类型可能会增加不必要的冗长
    '@typescript-eslint/no-namespace': 'off', // 禁止使用自定义 TypeScript 模块和命名空间。
    '@typescript-eslint/no-explicit-any': 'off', // 禁止使用 any 类型
    '@typescript-eslint/ban-types': 'off', // 禁止使用特定类型
    '@typescript-eslint/explicit-function-return-type': 'off', // 不允许对初始化为数字、字符串或布尔值的变量或参数进行显式类型声明
    '@typescript-eslint/no-var-requires': 'off', // 不允许在 import 语句中使用 require 语句
    '@typescript-eslint/no-empty-function': 'off', // 禁止空函数
    '@typescript-eslint/no-use-before-define': 'off', // 禁止在变量定义之前使用它们
    '@typescript-eslint/ban-ts-comment': 'off', // 禁止 @ts-<directive> 使用注释或要求在指令后进行描述
    '@typescript-eslint/no-non-null-assertion': 'off', // 不允许使用后缀运算符的非空断言(!)
    '@typescript-eslint/explicit-module-boundary-types': 'off', // 要求导出函数和类的公共类方法的显式返回和参数类型

    // vue (https://eslint.vuejs.org/rules)
    'vue/script-setup-uses-vars': 'error', // 防止<script setup>使用的变量<template>被标记为未使用，此规则仅在启用该no-unused-vars规则时有效。
    'vue/v-slot-style': 'error', // 强制执行 v-slot 指令样式
    'vue/no-mutating-props': 'off', // 不允许组件 prop的改变
    'vue/no-v-html': 'off', // 禁止使用 v-html
    'vue/custom-event-name-casing': 'off', // 为自定义事件名称强制使用特定大小写
    'vue/attributes-order': 'off', // vue api使用顺序，强制执行属性顺序
    'vue/one-component-per-file': 'off', // 强制每个组件都应该在自己的文件中
    // "vue/html-closing-bracket-newline": "off", // 在标签的右括号之前要求或禁止换行
    'vue/max-attributes-per-line': 'off', // 强制每行的最大属性数
    'vue/multiline-html-element-content-newline': 'off', // 在多行元素的内容之前和之后需要换行符
    'vue/singleline-html-element-content-newline': 'off', // 在单行元素的内容之前和之后需要换行符
    'vue/attribute-hyphenation': 'off', // 对模板中的自定义组件强制执行属性命名样式
    'vue/require-default-prop': 'off', // 此规则要求为每个 prop 为必填时，必须提供默认值
    'vue/multi-word-component-names': 'off' // 要求组件名称始终为 “-” 链接的单词
  }
  // rules: {
  //   'handle-callback-err': 'off',
  //   'vue/valid-define-emits': 'off',
  //   'no-unused-vars': 'off',
  //   'vue/no-mutating-props': 'off',
  //   'prettier/prettier': [
  //     'error',
  //     {
  //       singleQuote: true, // 使用单引号
  //       trailingComma: 'none', // 在对象或数组最后一个元素不加逗号
  //       tabWidth: 2, // 缩进长度
  //       useTabs: false, // 是否Tab缩进代替空格
  //       semi: false, // 是否在末尾添加分号
  //       endOfLine: 'auto', // 结束行形式
  //       bracketSpacing: true, // 在对象前后添加空格-eg: { foo: bar }
  //       arrowParens: 'avoid', // 单参数箭头函数参数周围不使用圆括号
  //       singleAttributePerLine: true, // html标签属性换行
  //       bracketSameLine: true // 多属性html标签的‘>’不折行放置
  //     }
  //   ],
  //   'vue/multi-word-component-names': 'off',
  //   'vue/html-closing-bracket-newline': 'off',
  //   'vue/singleline-html-element-content-newline': 'off',
  //   'vue/multiline-html-element-content-newline': 'off',
  //   'vue/no-v-html': 'off',
  //   'vue/require-prop-types': 'off',
  //   'vue/no-unused-components': 1,
  //   'accessor-pairs': 2,
  //   'arrow-spacing': [
  //     2,
  //     {
  //       before: true,
  //       after: true
  //     }
  //   ],
  //   'block-spacing': [2, 'always'],
  //   'brace-style': [
  //     2,
  //     '1tbs',
  //     {
  //       allowSingleLine: true
  //     }
  //   ],
  //   camelcase: [
  //     0,
  //     {
  //       properties: 'always'
  //     }
  //   ],
  //   'comma-dangle': 'off',
  //   'comma-spacing': [
  //     2,
  //     {
  //       before: false,
  //       after: true
  //     }
  //   ],
  //   'comma-style': [2, 'last'],
  //   'constructor-super': 2,
  //   curly: [2, 'multi-line'],
  //   'dot-location': [2, 'property'],
  //   'eol-last': 2,
  //   eqeqeq: 'off',
  //   'generator-star-spacing': [
  //     2,
  //     {
  //       before: true,
  //       after: true
  //     }
  //   ],
  //   'jsx-quotes': [2, 'prefer-single'],
  //   'key-spacing': [
  //     1,
  //     {
  //       beforeColon: false,
  //       afterColon: true
  //     }
  //   ],
  //   'keyword-spacing': [
  //     2,
  //     {
  //       before: true,
  //       after: true
  //     }
  //   ],
  //   'new-cap': [
  //     2,
  //     {
  //       newIsCap: true,
  //       capIsNew: false
  //     }
  //   ],
  //   'new-parens': 2,
  //   'no-array-constructor': 'off',
  //   'no-prototype-builtins': 'off',
  //   'no-caller': 2,
  //   'no-console': 'off',
  //   'no-class-assign': 2,
  //   'no-cond-assign': 2,
  //   'no-const-assign': 2,
  //   'no-control-regex': 0,
  //   'no-delete-var': 2,
  //   'no-dupe-args': 2,
  //   'no-dupe-class-members': 2,
  //   'no-dupe-keys': 2,
  //   'no-duplicate-case': 2,
  //   'no-empty-character-class': 2,
  //   'no-empty-pattern': 2,
  //   'no-empty': 'off',
  //   'no-eval': 2,
  //   'no-ex-assign': 2,
  //   'no-extend-native': 2,
  //   'no-extra-bind': 2,
  //   'no-extra-boolean-cast': 2,
  //   'no-extra-parens': [2, 'functions'],
  //   'no-fallthrough': 2,
  //   'no-floating-decimal': 2,
  //   'no-func-assign': 2,
  //   'no-implied-eval': 'off',
  //   'no-inner-declarations': [2, 'functions'],
  //   'no-invalid-regexp': 2,
  //   'no-irregular-whitespace': 2,
  //   'no-iterator': 2,
  //   'no-label-var': 2,
  //   'no-labels': [
  //     2,
  //     {
  //       allowLoop: false,
  //       allowSwitch: false
  //     }
  //   ],
  //   'no-lone-blocks': 2,
  //   'no-mixed-spaces-and-tabs': 2,
  //   'no-multi-spaces': 2,
  //   'no-multi-str': 2,
  //   'no-multiple-empty-lines': [
  //     2,
  //     {
  //       max: 1
  //     }
  //   ],
  //   'no-native-reassign': 2,
  //   'no-negated-in-lhs': 2,
  //   'no-new-object': 2,
  //   'no-new-require': 2,
  //   'no-new-symbol': 2,
  //   'no-new-wrappers': 2,
  //   'no-obj-calls': 2,
  //   'no-octal': 2,
  //   'no-octal-escape': 2,
  //   'no-path-concat': 2,
  //   'no-proto': 2,
  //   'no-redeclare': 2,
  //   'no-regex-spaces': 2,
  //   'no-return-assign': [2, 'except-parens'],
  //   'no-self-assign': 2,
  //   'no-self-compare': 2,
  //   'no-sequences': 2,
  //   'no-shadow-restricted-names': 2,
  //   'no-sparse-arrays': 2,
  //   'no-this-before-super': 2,
  //   'no-throw-literal': 2,
  //   'no-trailing-spaces': 2,
  //   'no-undef': 0,
  //   'no-undef-init': 2,
  //   'no-unexpected-multiline': 2,
  //   'no-unmodified-loop-condition': 2,
  //   'no-unneeded-ternary': [
  //     2,
  //     {
  //       defaultAssignment: false
  //     }
  //   ],
  //   'no-unreachable': 2,
  //   'no-unsafe-finally': 2,
  //   'no-useless-call': 2,
  //   'no-useless-computed-key': 2,
  //   'no-useless-constructor': 2,
  //   'no-useless-escape': 0,
  //   'no-whitespace-before-property': 2,
  //   'no-with': 2,
  //   'one-var': [
  //     2,
  //     {
  //       initialized: 'never'
  //     }
  //   ],
  //   'operator-linebreak': [
  //     2,
  //     'after',
  //     {
  //       overrides: {
  //         '?': 'before',
  //         ':': 'before'
  //       }
  //     }
  //   ],
  //   'padded-blocks': [2, 'never'],
  //   quotes: [
  //     1,
  //     'single',
  //     {
  //       avoidEscape: true,
  //       allowTemplateLiterals: true
  //     }
  //   ],
  //   semi: [1, 'never'],
  //   'semi-spacing': [
  //     1,
  //     {
  //       before: false,
  //       after: true
  //     }
  //   ],
  //   'space-before-blocks': [1, 'always'],
  //   'space-in-parens': [1, 'never'],
  //   'space-infix-ops': 1,
  //   'space-unary-ops': [
  //     1,
  //     {
  //       words: true,
  //       nonwords: false
  //     }
  //   ],
  //   'spaced-comment': [
  //     1,
  //     'always',
  //     {
  //       markers: [
  //         'global',
  //         'globals',
  //         'eslint',
  //         'eslint-disable',
  //         '*package',
  //         '!',
  //         ','
  //       ],
  //       line: { markers: ['/'] }
  //     }
  //   ],
  //   'template-curly-spacing': [1, 'never'],
  //   'use-isnan': 2,
  //   'valid-typeof': 2,
  //   'wrap-iife': [2, 'any'],
  //   'yield-star-spacing': [2, 'both'],
  //   yoda: [2, 'never'],
  //   'prefer-const': 1,
  //   'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,
  //   // 'object-curly-spacing': [
  //   //   2,
  //   //   'always',
  //   //   {
  //   //     objectsInObjects: false
  //   //   }
  //   // ],
  //   'array-bracket-spacing': [2, 'never']
  // }
}
