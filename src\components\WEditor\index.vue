<!--
 * @Description: Editor编辑器
 * @Author: thb
 * @Date: 2023-08-02 09:54:50
 * @LastEditTime: 2023-08-22 08:55:52
 * @LastEditors: thb
-->
<template>
  <div style="border: 1px solid #ccc">
    <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
    <Editor
      style="height: 300px; overflow-y: hidden; word-break: break-all"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @on-created="handleCreated"
      @on-change="handleChange"
    />
  </div>
</template>
<script setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { getToken } from '@/utils/auth'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const editorRef = shallowRef()
watch(
  () => props.disabled,
  () => {
    // 如果为true,// 需要禁用editor
    if (props.disabled) {
      nextTick(() => {
        editorRef.value.disable()
      })
    }
  },
  {
    immediate: true
  }
)

const emits = defineEmits(['update:modelValue', 'on-change'])
const valueHtml = computed({
  get: () => {
    return props.modelValue
  },
  set: newVal => {
    emits('update:modelValue', newVal)
  }
})
const mode = ref('default')
// 编辑器实例，必须用 shallowRef
// 内容 HTML
// const valueHtml = ref('')

// 模拟 ajax 异步获取内容
onMounted(() => {
  // setTimeout(() => {
  //   valueHtml.value = '<p>模拟 Ajax 异步设置内容</p>'
  // }, 1500)
})

const toolbarConfig = {}
const editorConfig = {
  placeholder: '请输入内容...',
  MENU_CONF: {
    uploadImage: {
      server: import.meta.env.VITE_APP_BASE_API + '/common/upload',
      customInsert(res, insertFn) {
        insertFn(import.meta.env.VITE_APP_FILE_API + res.url)
      },
      fieldName: 'file',
      headers: {
        Authorization: 'Bearer ' + getToken()
      }
    }
  }
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const handleCreated = editor => {
  editorRef.value = editor // 记录 editor 实例，重要！
}

// 内容区域变化change事件
const handleChange = () => {
  console.log('handleChange')
  emits('on-change')
}
</script>
<style lang="scss" scoped></style>
