<!--
 * @Description: 公海配置
 * @Author: thb
 * @Date: 2023-07-25 11:16:37
 * @LastEditTime: 2023-11-22 13:24:53
 * @LastEditors: thb
-->
<template>
  <ProTable ref="proTable" title="公海配置" :columns="columns" :request-api="getZoneSettingList">
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button type="primary" @click="handleSet">保有量设置</el-button>
    </template>
    <!-- duration -->
    <template #duration="{ row }">
      <span v-if="row.duration"> 超过{{ row.duration }}天 </span>
      <span v-else> -- </span>
    </template>
    <template #operation="{ row }">
      <el-button type="primary" link @click="handleDetail(row)">详情</el-button>

      <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>

      <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
    </template>
  </ProTable>
</template>
<script setup lang="tsx">
import {
  getZoneSettingList,
  saveZoneSetting,
  getZoneSettingDetail,
  deleteZoneSetting,
  changeZoneSettingStatus
} from '@/api/material-manage/zone-setting'
import { getSeaInventory, saveOrUpdateSeaInventory } from '@/api/material-manage/client'
import { ColumnProps } from '@/components/ProTable/interface'
import zoneForm from './components/zone-form'
import { useDic } from '@/hooks/useDic'
import { CirclePlus } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialog'
import { useHandleData } from '@/hooks/useHandleData'
import privateSet from './components/private-set'
const { getDic } = useDic()
const { showDialog } = useDialog()
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '序号',
    width: 100,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'operation',
    width: 200,
    label: '操作'
  },
  {
    prop: 'type',
    label: '公海类型',
    width: 150,
    enum: getDic('zone_setting'),
    search: {
      el: 'select'
    }
  },
  {
    prop: 'name',
    label: '公海名称',
    width: 300,
    search: {
      el: 'input'
    }
  },
  {
    prop: 'manageName',
    width: 100,
    label: '公海管理员'
  },
  {
    prop: 'duration',
    width: 100,
    label: '回收规则'
  },
  {
    prop: 'remark',
    label: '备注'
  },
  {
    prop: 'status',
    width: 150,
    label: '状态',
    enum: [
      {
        label: '正常',
        value: '1'
      },
      {
        label: '停用',
        value: '0'
      }
    ],
    render: scope => {
      return (
        <>
          {
            <el-switch
              model-value={scope.row.status}
              active-text={scope.row.status === '1' ? '正常' : '停用'}
              active-value={'1'}
              inactive-value={'0'}
              onClick={() => changeStatus(scope.row)}
            />
          }
        </>
      )
    }
  },
  {
    prop: 'updateTime',
    fixed: 'right',
    width: 200,
    label: '修改时间'
  }
]

const proTable = ref('')
// 提交成功之后的回调函数
const submitCallback = () => {
  proTable.value?.getTableList()
}
// 新增公海配置
const handleAdd = () => {
  showDialog({
    title: '新增',
    customClass: 'zone-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: zoneForm, // 表单组件
    submitApi: saveZoneSetting, // 提交api
    submitCallback // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 编辑公海配置
const handleEdit = (row: any) => {
  showDialog({
    title: '编辑',
    customClass: 'zone-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: zoneForm, // 表单组件
    handleConvertParams: data => {
      data.deptIds = data.deptIds.map(deptId => deptId + '')
    },
    getApi: getZoneSettingDetail,
    requestParams: row.id,
    submitApi: saveZoneSetting, // 提交api
    submitCallback // 提交成功之后的回调函数
  })
}

// 查看公海配置详情
const handleDetail = row => {
  showDialog({
    title: '编辑',
    customClass: 'zone-dialog',
    cancelButtonText: '关闭',

    showConfirmButton: false,
    component: zoneForm, // 表单组件
    getApi: getZoneSettingDetail,
    handleConvertParams: data => {
      data.deptIds = data.deptIds.map(deptId => deptId + '')
      data.zoneType = 'detail'
    },
    requestParams: row.id
  })
}
// 删除公海配置
const handleDelete = async (row: any) => {
  await useHandleData(deleteZoneSetting, row.id, `删除所选公海 ${row.name} 信息`)
  proTable.value?.getTableList()
}

// 修改状态
const changeStatus = async (row: any) => {
  await useHandleData(changeZoneSettingStatus, row.id, `切换【${row.name}】状态`)
  proTable.value?.getTableList()
}

const handleRevertParams = data => {
  const clueList = data.clueList
  const clientList = data.clientList
  data.clueStaffNum = clueList[0].limit
  data.clueAdminNum = clueList[1].limit
  data.clueStaffStatus = clueList[0].status
  data.clueAdminStatus = clueList[1].status
  data.cusStaffNum = clientList[0].limit
  data.cusAdminNum = clientList[1].limit
  data.cusStaffStatus = clientList[0].status
  data.cusAdminStatus = clientList[1].status
  const statusList = [
    'clueStaffStatus',
    'clueAdminStatus',
    'cusStaffStatus',
    'cusAdminStatus',
    'clueList',
    'clientList',
    'clueSeaId',
    'cusSeaId'
  ]

  for (const [key, value] of Object.entries(data)) {
    if (!statusList.includes(key)) {
      data[key] = value ? Number(value) : value
    }
  }
}
// 保有量设置功能
const handleSet = () => {
  showDialog({
    title: '保有量设置',
    customClass: 'zone-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: privateSet, // 表单组件
    getApi: getSeaInventory,
    submitApi: saveOrUpdateSeaInventory, // 提交api
    handleConvertParams: data => {
      data.clueList = [
        {
          name: '员工',
          limit: data.clueStaffNum,
          status: data.clueStaffStatus
        },
        {
          name: '管理者',
          limit: data.clueAdminNum,
          status: data.clueAdminStatus
        }
      ]
      data.clientList = [
        {
          name: '员工',
          limit: data.cusStaffNum,
          status: data.cusStaffStatus
        },
        {
          name: '管理者',
          limit: data.cusAdminNum,
          status: data.cusAdminStatus
        }
      ]
    },
    handleRevertParams // 处理提交参数
  })
}
</script>
<style lang="scss" scoped></style>
