/*
 * @Description:
 * @Author: thb
 * @Date: 2023-05-19 15:10:43
 * @LastEditTime: 2023-11-17 10:15:48
 * @LastEditors: thb
 */
import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'
import { config_private } from './vite_config_private.js'
import http from 'http'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 80,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        '/dev-api': {
          target: config_private.url, // 此处地址从另一文件中获取，该文件默认被忽略不提交
          // http://vue.ruoyi.vip/prod-api/
          changeOrigin: true,
          rewrite: p => p.replace(/^\/dev-api/, ''),
          agent: new http.Agent({ keepAlive: true, keepAliveMsecs: 20000 })
        },
        '/prod-api': {
          // target: 'http://************:8080', //宇涛
          target: 'http://**************:20780/', // 线上
          // http://vue.ruoyi.vip/prod-api/
          changeOrigin: true,
          rewrite: p => p.replace(/^\/prod-api/, '')
        }
      }
    },
    // fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: atRule => {
                if (atRule.name === 'charset') {
                  atRule.remove()
                }
              }
            }
          }
        ]
      }
    }
  }
})
