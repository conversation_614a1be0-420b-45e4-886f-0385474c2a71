/*
 * @Description:
 * @Author: thb
 * @Date: 2023-07-24 11:00:08
 * @LastEditTime: 2023-07-27 09:02:17
 * @LastEditors: thb
 */
import request from '@/utils/request'

// /contractTemp/delete 根据id删除
export function deleteContractTempDelete(id) {
  return request({
    url: '/contractTemp/delete',
    method: 'delete',
    params: {
      id
    }
  })
}

// /contractTemp/deleteByIds 批量删除
export function deleteContractTempDeleteByIds(ids) {
  return request({
    url: '/contractTemp/deleteByIds?ids=' + ids.join(','),
    method: 'delete'
  })
}

// /contractTemp/enable 状态设置
export const postContractTempEnable = query => {
  return request({
    url: `/contractTemp/enable?id=${query.id}`,
    method: 'post',
    data: query
  })
}

// /contractTemp/getById 详情
export const getContractTempGetById = params => {
  return request({
    url: '/contractTemp/getById',
    method: 'get',
    params
  })
}

// ​/contractTemp/getFieldList 按合同类型获取字段列表
export const getContractTempGetFieldList = params => {
  return request({
    url: '/contractTemp/getFieldList',
    method: 'get',
    params
  })
}
// /contractTemp/list 列表查询
export const getContractTempList = params => {
  return request({
    url: '/contractTemp/list',
    method: 'get',
    params
  })
}
// /contractTemp/listMyAudit 由我审批
export const getContractTempListMyAudit = params => {
  return request({
    url: '/contractTemp/listMyAudit',
    method: 'get',
    params
  })
}
// /contractTemp/listMyCreate 我提交的
export const getContractTempListMyCreate = params => {
  return request({
    url: '/contractTemp/listMyCreate',
    method: 'get',
    params
  })
}
// /contractTemp/saveOrUpdate 保存数据
export const postContractTempSaveOrUpdate = query => {
  return request({
    url: '/contractTemp/saveOrUpdate',
    method: 'post',
    data: query
  })
}
//模板审核通过还是驳回
export const reviewTemplateAudit = data => {
  return request({
    url: '/bizNodeHistory/tempReviewAudit',
    method: 'post',
    data
  })
}
