<!--
 * @Description: 跟进记录
 * @Author: thb
 * @Date: 2023-05-30 11:12:10
 * @LastEditTime: 2023-11-09 16:55:42
 * @LastEditors: thb
-->
<template>
  <div class="list-wrap">
    <el-input v-model="searchText" placeholder="搜索跟进记录">
      <!-- <template #append>
        <el-button :icon="Search" @click="getList" />
      </template> -->
      <template #suffix>
        <el-icon style="cursor: pointer">
          <Search @click="getList" />
        </el-icon>
      </template>
    </el-input>
    <div class="list">
      <div class="list-item" v-for="(record, index) in list" :key="index">
        <div class="item-title">
          <span class="icon arrow-left-sl"></span>
          <span class="name">{{ record?.createBy || '--' }}</span>
          <span class="time">{{ record?.createTime }}</span>
        </div>
        <p>
          <span class="label">联系人：</span>
          <span class="label-text">{{ record?.contact || '--' }}</span>
        </p>
        <p>
          <span class="label">跟进方式：</span>
          <span class="label-text">{{ record?.type }}</span>
        </p>
        <p>
          <span class="label">跟进内容：</span>
          <ClickMore class="label-text" :des="record?.content" />
        </p>
      </div>
    </div>
  </div>
</template>
<script setup>
import { Search } from '@element-plus/icons-vue'
import { getClueRecordList } from '@/api/material-manage/clue'
import ClickMore from '@/components/ClickMore'
const searchText = ref('')

const list = ref([])
const getList = async () => {
  if (props.requestApi) {
    const { data } = await props.requestApi({
      text: searchText.value,
      ccId: props.detail.id,
      pageSize: 999
    })
    list.value = data || []
  } else {
    const { data } = await getClueRecordList({
      text: searchText.value,
      ccId: props.detail.id,
      pageSize: 999
    })
    list.value = data || []
  }
}

const props = defineProps({
  detail: {
    type: Object,
    default: () => {
      return {}
    }
  },
  requestApi: Function
})

onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.list-wrap {
  :deep(.el-input-group__append) {
    background-color: transparent;
    border-top-right-radius: 25px;
    border-bottom-right-radius: 25px;
  }
}
.list {
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #6a7697;
  padding: 20px;
  .item-title {
    display: flex;
    // justify-content: space-between;
    .icon {
      margin-right: 10px;
    }
    .name {
      font-family: SourceHanSansCN-Medium, SourceHanSansCN;
      font-weight: 500;
      color: #333333;
      flex: 1;
    }
  }
  .list-item {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.name {
  font-size: 14px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  color: #333333;
}
.label-text {
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #333333;
}
</style>
