<template>
  <el-dialog
    v-model="visible"
    title="人员回款详情"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1350px"
    top="5vh"
    append-to-body
  >
    <div class="top">
      <div class="title">
        {{ detail?.customerSuccess }}
      </div>
      <div class="tag-name">
        <span>{{ detail?.deptName }}</span>
      </div>
    </div>
    <el-card>
      <div class="wrap">
        <div class="left">
          <div class="list-item">
            <span class="color-blue">本月营业额：</span>
            <span>{{ detail.monthTurnover || 0 }}元</span>
          </div>
          <div class="list-item">
            <span class="color-blue">本月记账营业额：</span>
            <span>{{ detail.bookkeepingMonthTurnover || 0 }}元</span>
          </div>
          <div class="list-item">
            <span class="color-blue">本月地址营业额：</span>
            <span>{{ detail.addressMonthTurnover || 0 }}元</span>
          </div>
          <div class="list-item">
            <span class="color-blue">本月应收：</span>
            <span>{{ detail.allPaymentAmount || 0 }}元</span>
          </div>
          <div class="list-item">
            <span class="color-blue">本月实收：</span>
            <span>{{ detail.monthPaymentClearCustomer || 0 }}家</span>
            /
            <span>{{ detail.allReceiptAmount || 0 }}元</span>
          </div>
          <div class="list-item">
            <span class="color-blue">本月欠费：</span>
            <span>{{ detail.monthPaymentDebtCustomer || 0 }}家</span>
            /
            <span>{{ detail.monthDebt || 0 }}元</span>
          </div>
          <div class="list-item">
            <span class="color-red">欠费总金额：</span>
            <span>{{ detail.arrearageAmount || 0 }}元</span>
          </div>
          <div class="list-item">
            <span class="color-red">欠费实收：</span>
            <span>{{ detail.receiveArrearageAmount || 0 }}元</span>
          </div>
          <div class="list-item-b">
            <el-progress
              :percentage="
                detail.arrearageAmount && multiply(divide(detail.receiveArrearageAmount, detail.arrearageAmount), 100).toFixed(2)
              "
              :format="format"
            />
          </div>
        </div>
      </div>
    </el-card>
    <div class="chart-con">
      <el-date-picker v-model="queryParamYear" type="year" @change="handleChangeYear" />
      <div ref="monthlyTurnoverRef" style="height: 250px" />
    </div>
    <el-radio-group v-model="initParam.type">
      <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
    </el-radio-group>
    <!-- 此处列表不变动，使用v-show切换 -->
    <div class="my-table" v-show="initParam.type === 0">
      <ProTable
        ref="proTable1"
        title="关联企业"
        :isShowSearch="false"
        :init-param="initParam"
        :columns="columnsCustomer"
        :toolButton="false"
        rowKey="customerId"
        :request-api="financeCustomerAnalyseList"
      >
        <template #customerName="{ row }">
          <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
        </template>
      </ProTable>
    </div>
    <div class="my-table" v-show="initParam.type === 1">
      <ProTable
        ref="proTable2"
        title="企业账单"
        :isShowSearch="false"
        :init-param="initParam"
        :columns="columnsAccount"
        :toolButton="false"
        rowKey="id"
        :request-api="financePaymentList"
      >
        <template #customerName="{ row }">
          <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
        </template>
        <template #paymentNo="{ row }">
          <span class="blue-text" @click="handleShowAccountsDetail(row, row.id)">{{ row.paymentNo }}</span>
        </template>
        <template #contractNo="{ row }">
          <span class="blue-text" @click="handlShowContractDetail(row.contractId)">{{ row.contractNo }}</span>
        </template>
      </ProTable>
    </div>
    <div class="my-table" v-show="initParam.type === 2">
      <ProTable
        ref="proTable3"
        title="月营业额明细"
        :isShowSearch="false"
        :init-param="initParamStatement"
        :columns="columnsStatement"
        :requestAuto="false"
        :toolButton="false"
        rowKey="id"
        :request-api="turnoverStatementGetMonthlyTurnoverDetailTab"
        :hideBtn="true"
        :row-class-name="tableRowClassName"
      >
        <template #customerName="{ row }">
          <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
        </template>
        <!-- 账单 -->
        <template #paymentNo="{ row }">
          <span class="blue-text" @click="handleShowAccountsDetail(row, row.paymentId)">{{ row.paymentNo }}</span>
        </template>
      </ProTable>
    </div>
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
    </template>
  </el-dialog>
  <customerDetail v-if="customerDetailShow" :id="rowId" :hideActionBtn="true" @on-close="handleCloseCustomerDetail" />
  <contractDetail v-if="contractDetailShow" :id="contractId" :hideActionBtn="true" @on-close="contractDetailShow = false" />
  <div v-show="false"><feeTypeTree></feeTypeTree></div>
</template>

<script setup lang="jsx">
import {
  getTurnoverStatementCustomerSuccessDetail,
  getTurnoverStatementMonthlyCustomerSuccessTurnover,
  turnoverStatementGetMonthlyTurnoverDetailTab
} from '@/api/finance/account-statement'
import { financeCustomerAnalyseList } from '@/api/finance/collection-analysis'
import { financePaymentList, financePaymentGetById } from '@/api/finance/accounts-receivable'
import { getFinanceReceiptList, getFinanceReceiptGetById } from '@/api/finance/collection-ledger'
import { postFinanceReceiptSaveOrUpdate } from '@/api/finance/collection-ledger'
import accountsForm from '@/views/finance/accounts-receivable/components/form.vue'
import collectionForm from '@/views/finance/collection-ledger/components/form.vue'
import * as echarts from 'echarts'
import { useDialog } from '@/hooks/useDialogFinance'
import { multiply, divide } from '@/utils/math'
import bus from 'vue3-eventbus'
import contractDetail from '@/views/contract-manage/contract-list/components/contract-detail.vue'
import dayjs from 'dayjs'
import { nextTick } from 'vue'
import { customerStatus, customerProperty, customerIndustry, receiveStatusArr } from '@/utils/constants'
import { useDic } from '@/hooks/useDic'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import feeTypeTree from '@/views/finance/accounts-receivable/components/fee-type-tree'

const { getDic } = useDic()
const { proxy } = getCurrentInstance()

const tabs = [
  { dicValue: 0, dictLabel: '关联企业' },
  { dicValue: 1, dictLabel: '企业账单' },
  { dicValue: 2, dictLabel: '月营业额明细' }
]

const emit = defineEmits()
const handleClose = () => {
  emit('on-close')
}

const props = defineProps({
  customerSuccessUserId: String,
  addressMonthTurnover: String,
  bookkeepingMonthTurnover: String
})

const format = percentage => `总回款率 ${percentage}%`

const visible = ref(true)
const detail = ref({})
const monthlyTurnoverRef = ref(null)
const queryParamYear = ref(dayjs().format('YYYY'))

const handleChangeYear = value => {
  queryParamYear.value = dayjs(value).format('YYYY')
  getChart()
}

const getDetail = () => {
  getTurnoverStatementCustomerSuccessDetail({ customerSuccessUserId: props.customerSuccessUserId }).then(res => {
    detail.value = res.data
  })
}
const getChart = () => {
  getTurnoverStatementMonthlyCustomerSuccessTurnover({
    customerSuccessUserId: props.customerSuccessUserId,
    year: queryParamYear.value
  }).then(res => {
    drawChart(res.data)
  })
}

//计算最大值
function calMax(arr) {
  let max = 0
  arr.forEach(el => {
    el.forEach(el1 => {
      if (!(el1 === undefined || el1 === '')) {
        if (max < el1) {
          max = el1
        }
      }
    })
  })
  let maxint = Math.ceil(max / 9.5) //不让最高的值超过最上面的刻度
  let maxval = maxint * 10 //让显示的刻度是整数
  return maxval
}

//计算最小值
function calMin(arr) {
  let min = 0
  arr.forEach(el => {
    el.forEach(el1 => {
      if (!(el1 === undefined || el1 === '')) {
        if (min > el1) {
          min = el1
        }
      }
    })
  })
  let minint = Math.floor(min / 10)
  let minval = minint * 10 //让显示的刻度是整数
  return minval
}

const drawChart = data => {
  const Min1 = calMin([data.map(item => item.value.allReceiptAmount), data.map(item => item.value.monthDebt)])
  const Min2 = calMin([
    data.map(item => item.value.monthPaymentDebtCustomer),
    data.map(item => item.value.monthPaymentClearCustomer)
  ])
  const Max1 = calMax([data.map(item => item.value.allReceiptAmount), data.map(item => item.value.monthDebt)])
  const Max2 = calMax([
    data.map(item => item.value.monthPaymentDebtCustomer),
    data.map(item => item.value.monthPaymentClearCustomer)
  ])
  const monthlyTurnoverIntance = echarts.init(monthlyTurnoverRef.value)
  // console.log(
  //   'data',
  //   data.map(item => item.value.monthPaymentDebtCustomer)
  // )
  monthlyTurnoverIntance.setOption({
    xAxis: {
      type: 'category',
      data: data.map(item => item.name),
      axisLabel: {
        formatter: '{value} 月',
        margin: 20
      }
    },
    yAxis: [
      {
        type: 'value',
        interval: (Max1 - Min1) / 5,
        min: Min1,
        max: Max1,
        axisLabel: {
          formatter: '{value} 元'
        }
      },
      {
        type: 'value',
        interval: (Max2 - Min2) / 5,
        min: Min2,
        max: Max2,
        axisLabel: {
          formatter: '{value} 家'
        }
      }
    ],
    legend: {
      show: true
    },
    series: [
      {
        name: '本月实收',
        data: data.map(item => item.value.allReceiptAmount),
        type: 'bar',
        showBackground: true,
        label: {
          show: true,
          position: 'top'
        },
        itemStyle: {
          color: 'rgba(91, 144, 249, 1)'
        },
        backgroundStyle: {
          color: 'rgba(91, 144, 249, 0.2)'
        },
        yAxisIndex: 0
      },
      {
        name: '本月欠费',
        data: data.map(item => item.value.monthDebt),
        type: 'bar',
        showBackground: true,
        label: {
          show: true,
          position: 'top'
        },
        itemStyle: {
          color: 'rgba(93, 112, 147, 1)'
        },
        backgroundStyle: {
          color: 'rgba(93, 112, 147, 0.2)'
        },
        yAxisIndex: 0
      },
      {
        name: '应收企业数',
        data: data.map(item => item.value.monthPaymentDebtCustomer),
        // data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
        type: 'line',
        label: {
          show: true,
          position: 'top',
          textStyle: {
            color: 'rgba(246, 189, 22, 1)'
          }
        },
        itemStyle: {
          color: 'rgba(246, 189, 22, 1)'
        },
        lineStyle: {
          color: 'rgba(246, 189, 22, 1)'
        },
        yAxisIndex: 1
      },
      {
        name: '实收企业数',
        data: data.map(item => item.value.monthPaymentClearCustomer),
        type: 'line',
        label: {
          show: true,
          position: 'bottom',
          textStyle: {
            color: 'rgba(90, 216, 166, 1)'
          }
        },
        itemStyle: {
          color: 'rgba(90, 216, 166, 1)'
        },
        lineStyle: {
          color: 'rgba(90, 216, 166, 1)'
        },
        yAxisIndex: 1
      }
    ]
  })
}

const proTable1 = ref(null)
const proTable2 = ref(null)
const proTable3 = ref(null)
const initParam = reactive({ customerSuccessUserId: props.customerSuccessUserId, expireType: 0, type: 0 })
const initParamStatement = reactive({ customerSuccessUserId: props.customerSuccessUserId })

const columnsCustomer = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'customerName',
    label: '企业名称',
    width: '300',
    isColShow: false
  },
  {
    prop: 'recentContractExpirationDate',
    width: '200',
    label: '最近合同到期日期'
  },
  {
    prop: 'paymentAmount',
    label: '账单总金额',
    width: '200',
    render: scope => {
      return <span>{scope.row.paymentAmount || 0}元</span>
    }
  },
  {
    prop: 'receiptAmount',
    label: '已收款',
    width: '150',
    render: scope => {
      return <span>{scope.row.receiptAmount >= 0 ? `${scope.row.receiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'receivableAmount',
    label: '应待收款',
    width: '200',
    render: scope => {
      return <span>{scope.row.receivableAmount >= 0 ? `${scope.row.receivableAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'customerStatus',
    label: '客户状态',
    // enum: remoteEnumList,
    enum: getDic('customer_status', [
      {
        label: '废弃客户',
        value: '废弃客户'
      }
    ]),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty.concat([
    //   {
    //     label: '废弃客户',
    //     value: '废弃客户'
    //   }
    // ]),
    enum: getDic('customer_property', [
      {
        label: '废弃客户',
        value: '废弃客户'
      }
    ]),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'mangerName',
    width: '150',
    label: '财税顾问'
  },
  {
    prop: 'customerSuccessName',
    width: '150',
    label: '客户成功'
  },
  {
    prop: 'counselorName',
    width: '150',
    label: '开票员'
  },
  {
    prop: 'sponsorAccountingName',
    width: '150',
    label: '主办会计'
  }
]
const columnsAccount = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'customerName',
    label: '企业名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'paymentNo',
    label: '账单编号',
    width: '200',
    search: { el: 'input' }
  },
  {
    prop: 'feeType',
    label: '费用类别',
    width: '150',
    search: {
      render: ({ searchParam }) => {
        return <feeTypeTree vModel={searchParam.feeType} />
      }
    },
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    }
  },
  {
    prop: 'receiveStatus',
    width: '100',
    label: '收款状态',
    isShow: false,
    enum: receiveStatusArr,
    search: { el: 'select' }
  },
  {
    prop: 'paymentAmount',
    label: '账款金额', // 账款金额->账款金额总计
    width: '120',
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'discount',
    label: '优惠',
    width: '200',
    isColShow: false,
    render: scope => {
      return (
        <span>
          {scope.row.discount === '金额优惠'
            ? `金额优惠：${scope.row.discountAmount}元`
            : scope.row.discount === '时长优惠'
            ? `时长优惠：${scope.row.discountTime}月`
            : scope.row.discount === '无优惠'
            ? '无优惠'
            : scope.row.discount === '活动优惠'
            ? `活动优惠：${scope.row.activityTxt}`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'allReceiptAmount',
    label: '已收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.allReceiptAmount >= 0 ? `${scope.row.allReceiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'allReceivableAmount',
    label: '应待收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.allReceivableAmount >= 0 ? `${scope.row.allReceivableAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'paymentDate',
    label: '账期',
    width: '100',
    render: scope => {
      return <span>{scope.row.paymentDate ? `${scope.row.paymentDate}月` : '--'}</span>
    }
  },
  {
    prop: 'paymentStartTime',
    label: '账期开始时间',
    width: '120'
  },
  {
    prop: 'paymentEndTime',
    width: '120',
    label: '账期结束时间',
    search: {
      el: 'date-picker',
      props: { type: 'monthrange', valueFormat: 'YYYY-MM' }
    }
  },
  {
    prop: 'contractNo',
    label: '关联合同',
    width: '150'
  }
]
const columnsStatement = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'customerName',
    label: '企业名称',
    width: '300',
    isColShow: false
  },
  {
    prop: 'analysisMonth',
    label: '所属月份',
    width: '120',
    render: scope => {
      return <span>{proTable3.value?.searchParam.analysisMonth}</span>
    },
    search: {
      render: ({ searchParam }) => {
        return (
          <elDatePicker
            modelValue={searchParam.analysisMonth}
            onChange={value => {
              handleMonthChange(value)
            }}
            type="month"
            valueFormat="YYYY-MM"
            clearable={false}
            editable={false}
          />
        )
      }
    }
  },
  {
    prop: 'monthAmount',
    label: '月营业额',
    width: '120',
    render: scope => {
      return <span>{scope.row.monthAmount >= 0 ? `${scope.row.monthAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'paymentNo',
    label: '来源账单',
    width: '200'
  },
  {
    prop: 'typeName',
    label: '费用类别',
    width: '150',
    render: scope => {
      return <span>{scope.row.typeName || '--'}</span>
    }
  },
  {
    prop: 'paymentAmount',
    label: '账款金额', // 账款金额->账款金额总计
    width: '120',
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'discount',
    label: '优惠',
    width: '200',
    isColShow: false,
    render: scope => {
      return (
        <span>
          {scope.row.discount === '金额优惠'
            ? `金额优惠：${scope.row.discountAmount}元`
            : scope.row.discount === '时长优惠'
            ? `时长优惠：${scope.row.discountTime}月`
            : scope.row.discount === '无优惠'
            ? '无优惠'
            : scope.row.discount === '活动优惠'
            ? `活动优惠：${scope.row.activityTxt}`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'sumReceiptAmount',
    label: '已收金额',
    width: '100',
    render: scope => {
      return <span>{scope.row.sumReceiptAmount >= 0 ? `${scope.row.sumReceiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'paymentDate',
    label: '账期',
    width: '100',
    render: scope => {
      return <span>{scope.row.paymentDate ? `${scope.row.paymentDate}月` : '--'}</span>
    }
  },
  {
    prop: 'paymentStartTime',
    label: '账期开始时间',
    width: '120'
  },
  {
    prop: 'paymentEndTime',
    width: '120',
    label: '账期结束时间'
  },
  {
    prop: 'lastReceiptDate',
    label: '更新时间',
    width: '120'
  }
]

const { showDialog } = useDialog()

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = id => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
}
/* 客户详情弹窗 ---end--- */

/* 账单详情弹窗 ---start--- */
const handleShowAccountsDetail = (row, id) => {
  console.log('id', id)
  showDialog({
    title: '账单详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: accountsForm,
    getApi: financePaymentGetById,
    requestParams: { id }
  })
  nextTick(() => {
    bus.emit('paymentNo', row.paymentNo)
    bus.emit('paymentId', row.paymentId || row.id)
    bus.emit('feeType', row.feeType || row.typeName)
  })
}
/* 账单弹窗 ---end--- */
/* 合同详情弹窗 ---start--- */
const contractDetailShow = ref(false)
const contractId = ref()
const handlShowContractDetail = id => {
  // proxy.$modal.msgWarning(`合同详情模块建设中!`)
  contractDetailShow.value = true
  contractId.value = id
}
/* 合同弹窗 ---end--- */
/* 收款详情弹窗 ---start--- */
const handleShowCollectionDetail = id => {
  console.log('id', id)
  showDialog({
    title: '收款详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: collectionForm,
    getApi: getFinanceReceiptGetById,
    requestParams: { id }
  })
}
/* 收款弹窗 ---end--- */
/* 操作列 ---start--- */
const handleRelateCollection = row => {
  console.log(row)
  showDialog({
    title: '关联收款',
    component: collectionForm,
    customClass: 'customer-dialog',
    rowFormData: {
      customerName: row.customerName,
      customerId: row.customerId,
      customerNo: row.customerNo,
      paymentId: row.id,
      paymentNo: row.paymentNo
    },
    submitApi: postFinanceReceiptSaveOrUpdate,
    submitCallback,
    handleRevertParams: handleRevertParamsCollection
  })
}
// 处理表单提交参数
const handleRevertParamsCollection = data => {
  if (Array.isArray(data.receiptVoucherFile) && data.receiptVoucherFile[0]) {
    const file = data.receiptVoucherFile[0]
    data.receiptVoucherFile = {
      fileSize: file.uploadSize,
      fileNames: file.newFileName,
      // bizType: 'dddd', // 假数据
      uploadBy: file.uploadBy,
      uploadTime: file.uploadTime,
      urls: file.url
    }
  } else {
    delete data.receiptVoucherFile
  }
}
const submitCallback = () => {
  setTimeout(() => {
    proxy.$modal.msgSuccess(`新增收款审核通过后将完成关联，待审核收款可在收款审批中查看`)
  }, 1000)
}
/* 操作列 ---end--- */

const handleMonthChange = value => {
  proTable3.value.searchParam.analysisMonth = value
  proTable3.value?.search()
  console.log('handleMonthChange', value, proTable3.value.searchParam)
}

watch(
  () => props.customerSuccessUserId,
  async () => {
    nextTick(() => {
      getDetail()
      getChart()
      // 给“月营业额明细”列表赋参数，并search触发查询
      // 触发时机上，handleMonthChange可能在proTable3渲染前触发了，设置proTable3不要自动请求
      handleMonthChange(dayjs().format('YYYY-MM'))
    })
  },
  {
    immediate: true
  }
)
const tableRowClassName = ({ row, rowIndex }) => {
  if (
    dayjs(dayjs(row.lastReceiptDate).format('YYYY-MM')).valueOf() > dayjs(proTable3.value?.searchParam.analysisMonth).valueOf()
  ) {
    return 'warning-row'
  }
  return ''
}
</script>

<style lang="scss">
.my-table {
  min-height: 300px;
  display: flex;
  .el-table__append-wrapper {
  }
  .card {
    box-sizing: border-box;
    padding: 0;
    overflow-x: hidden;
    background-color: none;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }
}
</style>
<style lang="scss" scoped>
:deep(.el-table) {
  .warning-row {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
  }
  .success-row {
    --el-table-tr-bg-color: var(--el-color-success-light-9);
  }
}
:deep(.table-search) {
  margin-top: 15px;
  margin-bottom: 0px;
}
.top {
  display: flex;
  align-items: flex-end;
  .title {
    font-weight: 700;
    font-size: 18px;
  }
  .tag-name {
    margin-left: 20px;
  }
}
.el-card {
  margin: 15px 0;
  .wrap {
    display: flex;
    width: 100%;
    padding: 25px 25px 10px;
    .left,
    .right {
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      .list-item-b {
        width: 20%;
        margin-bottom: 15px;
      }
      .list-item {
        width: 16%;
        margin-bottom: 15px;
      }
    }
    .color-blue {
      color: #409eff;
    }
    .color-red {
      color: #f56c6c;
    }
    .color-green {
      color: #67c23a;
    }
    .color-gray {
      // color: #999;
    }
  }
}

.el-progress {
  margin-left: 30px;
  min-width: 100%;
}
</style>
