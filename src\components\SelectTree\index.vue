<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-07-05 16:51:46
 * @LastEditTime: 2024-03-19 09:34:50
 * @LastEditors: thb
-->
<template>
  <el-tree-select
    v-model="treeSelect"
    :props="defaultProps"
    v-bind="$attrs"
    :data="treeData"
    :render-after-expand="false"
    :filter-node-method="filterNodeMethod"
    filterable
    :default-expanded-keys="expandedKeys"
    value-key="id"
    @node-click="handleNodeClick"
  />
</template>
<script setup>
import { getReviewerTreeData } from '@/api/process/process'
// const defaultProps = {
//   value: 'id',
//   label: 'label',
//   children: 'children',
//   disabled: (data, node) => {
//     return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
//   }
// }
const props = defineProps({
  modelValue: Object,
  defaultProps: {
    type: Object,
    default: () => {
      return {
        value: 'id',
        label: 'label',
        children: 'children',
        disabled: (data, node) => {
          return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
        }
      }
    }
  }
})

const emits = defineEmits(['update:modelValue', 'on-node-click'])
const treeSelect = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})

const handleNodeClick = (node, node1) => {
  console.log('node', node, node1)
  if (node.type === '1') {
    // viewProcess.value[reviewerIndex].reviewer = node.label
    // viewProcess.value[reviewerIndex].reviewerId = node.id
    emits('on-node-click', node, node1)
  }
}

const treeData = ref()
const expandedKeys = ref([]) // 与产品沟通过，优化为默认展开到第二级部门，便于选择
const getTreeData = async () => {
  const { data } = await getReviewerTreeData()
  expandedKeys.value = data[0].children.map(item => item.id)
  // console.log('expandedKeys', expandedKeys.value)
  treeData.value = data || []
}
// 目前先用特定的接口调用吧,后期有时间改用父子组件传参api

const filterNodeMethod = (value, data) => data.label.includes(value)
onMounted(() => {
  getTreeData()
})
</script>
<style lang="scss" scoped></style>
