T.D3Overlay=T.Overlay.extend({initialize:function(t,o,i){this.init=t;this.redraw=o;if(i)this.options=i;d3.select("head").append("style").attr("type","text/css")},_zoomChange:function(){if(!this.redraw)this.init(this.selection,this.transform);else this.redraw(this.selection,this.transform)},onAdd:function(t){this.map=t;var o=this;this._svg=new T.SVG;t.addLayer(this._svg);this._rootGroup=d3.select(this._svg._rootGroup).classed("d3-overlay",true);this.selection=this._rootGroup;this.transform={LngLatToD3Point:function(t,i){var e=t instanceof T.LngLat?t:new T.LngLat(t,i);var r=o.map.lngLatToLayerPoint(e);this.stream.point(r.x,r.y)},unitsPerMeter:function(){return 256*Math.pow(2,t.getZoom())/40075017},map:o.map,layer:o};this.transform.pathFromGeojson=d3.geo.path().projection(d3.geo.transform({point:this.transform.LngLatToD3Point}));this.init(this.selection,this.transform);if(this.redraw)this.redraw(this.selection,this.transform);t.addEventListener("zoomend",this._zoomChange,this)},onRemove:function(t){t.removeEventListener("zoomend",this._zoomChange,this);this._rootGroup.remove();t.removeOverLay(this._svg)},bringToFront:function(){if(this._svg&&this._svg._rootGroup){var t=this._svg._rootGroup.parentNode;t.parentNode.appendChild(t)}return this},bringToBack:function(){if(this._svg&&this._svg._rootGroup){var t=this._svg._rootGroup.parentNode;var o=t.parentNode;o.insertBefore(t,o.firstChild)}return this}});