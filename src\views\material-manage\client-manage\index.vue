<!--
 * @Description: 客户管理
 * @Author: thb
 * @Date: 2023-08-16 13:06:05
 * @LastEditTime: 2023-12-14 16:23:23
 * @LastEditors: thb
-->
<template>
  <ProTable
    v-if="show"
    ref="proTable"
    title="客户管理"
    :columns="columns"
    :request-api="getTabList"
    :init-param="initParam"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <!-- 表格tabs -->
    <template #tabs>
      <el-radio-group :model-value="initParam.tabType" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #companyName="{ row }">
      <span class="blue-text" @click="showDetail(row)">{{ row.companyName }}</span>
    </template>
    <template #winNum="{ row }">
      <span>赢单：{{ row.winNum }} 输单: {{ row.loseNum }} 其他: {{ row.otherNum }} </span>
    </template>
    <template #tableHeader>
      <el-button :icon="Download" @click="handleExport" v-hasPermi="['material-manage:client-manage:export']">导出</el-button>
    </template>
  </ProTable>

  <clientDetail
    v-if="detailShow"
    :tabType="tabType"
    :id="clientId"
    @on-close="handleClose"
    @on-edit="handleEdit"
    @on-success="getList"
  />
</template>
<script setup lang="tsx">
import {
  getClientList,
  getFollowedClientList,
  cusCustomerOrClueCustomerListExport,
  cusCustomerOrClueAllCustomerListExport
} from '@/api/material-manage/client'
import { ColumnProps } from '@/components/ProTable/interface'
import { useDic } from '@/hooks/useDic'
import clientDetail from './components/client-detail.vue'
import { ref } from 'vue'
import { useDialog } from '@/hooks/useDialog'
import clientForm from './components/form'
import { getClueDetail, saveClue } from '@/api/material-manage/clue'
import { cusSourceTree } from '@/api/material-manage/source'
import { Download } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()
const { showDialog } = useDialog()
const { getDic } = useDic()
import useCommonStore from '@/store/modules/common'
const useCommon = useCommonStore()
// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ tabType: '1' })
const tabs = [
  {
    dicValue: '0',
    dictLabel: '跟进客户'
  },
  {
    dicValue: '1',
    dictLabel: '我的客户'
  }
]

const getTree = async () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await cusSourceTree({ enable: 1 })
    // 将后端传回的数据结构进行转换
    const revertTreeData = (data = []) => {
      data.forEach(item => {
        item.label = item.name
        item.value = item.id
        item.children = item.child
        if (Array.isArray(item.children) && item.children.length) {
          revertTreeData(item.children)
        }
      })
    }
    revertTreeData(data)
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}
// 表格配置项
const columns: ColumnProps<any>[] = ref([
  {
    prop: 'index',
    label: '序号',
    fixed: 'left',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'companyName',
    width: 300,
    fixed: 'left',
    label: '客户名称',
    search: {
      el: 'input'
    },
    sortable: 'custom',
    sortName: 'ccoc.company_name'
  },
  {
    prop: 'contactPhone',
    width: 150,
    label: '手机号',
    search: {
      el: 'input'
    },
    sortable: 'custom',
    sortName: 'ccc.contact_phone'
  },
  {
    prop: 'sourceId',
    width: 200,
    label: '客户来源',
    enum: getTree,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'name',
          children: 'child',
          disabled: data => {
            return data.child
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.sourceName}</span>
    },
    sortable: 'custom',
    sortName: 'source.name'
  },
  {
    prop: 'level',
    width: 150,
    label: '客户等级',
    enum: getDic('customer_level'),
    search: { el: 'select' },
    sortable: 'custom',
    sortName: 'ccoc.level'
  },
  {
    prop: 'followStatus',
    width: 150,
    // enum: getDic('follow_status'),
    enum: [
      {
        label: '未跟进',
        value: '0'
      },
      {
        label: '跟进中',
        value: '1'
      },
      {
        label: '已转企业',
        value: '2'
      }
    ],
    search: {
      el: 'select'
    },
    label: '跟进状态',
    sortable: 'custom',
    sortName: 'ccoc.follow_status'
  },
  {
    prop: 'becomeTime',
    width: 200,
    label: '成为客户时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'ccoc.become_time'
  },

  {
    prop: 'lastFollowTime',
    width: 200,
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    label: '最近跟进时间',
    sortable: 'custom',
    sortName: 'ccoc.last_follow_time'
  },
  {
    prop: 'tagsName',
    width: 150,
    label: '标签'
  },
  {
    prop: 'opportunityNum',
    width: 150,
    label: '商机数量'
  },
  {
    prop: 'winNum',
    width: 200,
    label: '商机分布'
  },
  {
    prop: 'remark',
    width: 300,
    label: '备注'
  },
  {
    prop: 'taxNature', // customer_property
    width: 150,
    label: '税务性质',
    enum: getDic('customer_property'),
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.taxNature || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'ccoc.tax_nature'
  },
  {
    prop: 'lastModifiedTime',
    width: 200,
    label: '最近修改时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'ccoc.last_modified_time'
  },
  {
    prop: 'createBy',
    width: 150,
    search: {
      el: 'input'
    },
    label: '创建人',
    sortable: 'custom',
    sortName: 'ccoc.create_by'
  },
  {
    prop: 'createTime',
    // fixed: 'right',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    width: 200,
    label: '创建时间',
    sortable: 'custom',
    sortName: 'ccoc.create_time'
  }
])

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// 自定义
const transformRequestParams = (data: any) => {
  // 创建时间
  if (data.createTime) {
    data.startCreateTime = data.createTime[0]
    data.endCreateTime = data.createTime[1]
  }
  if (data.lastFollowTime) {
    data.startLastFollowTime = data.lastFollowTime[0]
    data.endLastFollowTime = data.lastFollowTime[1]
  }
  if (data.becomeTime) {
    data.becomeTimeStart = data.becomeTime[0]
    data.becomeTimeEnd = data.becomeTime[1]
  }
  if (data.lastModifiedTime) {
    data.lastModifiedTimeStart = data.lastModifiedTime[0]
    data.lastModifiedTimeEnd = data.lastModifiedTime[1]
  }
}

// 显示客户详情弹窗
const detailShow = ref(false)
const clientId = ref()
const tabType = ref('1')
provide('tabType', tabType)
const showDetail = (row: any) => {
  clientId.value = row.id
  detailShow.value = true
  tabType.value = initParam.tabType
}

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}

// 编辑资料
const handleEdit = (id: string) => {
  showDialog({
    title: '编辑',
    customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: clientForm, // 表单组件
    getApi: getClueDetail,
    requestParams: id,
    handleConvertParams: data => {
      data.tags = data.tags.map(item => item.tagId)
    },
    handleRevertParams: data => {
      if (data.tags.length) {
        data.tags = data.tags.map(item => {
          return {
            tagId: item
          }
        })
      }
      data.entryType = '0' // 手动录入
      data.type = '1' // 代表客户,
    },
    submitApi: saveClue, // 提交api
    submitCallback: getList // 提交成功之后的回调函数
  })
}
const handleClose = () => {
  detailShow.value = false
  getList()
}

watch(
  () => useCommon.id,
  () => {
    if (useCommon.id && useCommon.bizType === 'cus') {
      showDetail({
        id: useCommon.id
      })
      useCommon.clearId()
      useCommon.clearBizType()
    }
  },
  {
    immediate: true
  }
)

const getTabList = async (data: any) => {
  if (data.tabType === '0') {
    const result1 = await getFollowedClientList(data)
    return result1
  } else {
    const result2 = await getClientList(data)
    return result2
  }
}

const show = ref(true)
const handleRadioChange = value => {
  proTable.value.pageable.pageNum = 1
  initParam.tabType = value
  if (value === '0') {
    const index = columns.value.findIndex(item => item.label === '客户等级')
    if (index > 0) {
      columns.value.splice(index + 1, 0, {
        prop: 'currentUserName',
        width: 150,
        isColShow: true,
        isShow: true,
        label: '跟进人',
        search: {
          el: 'input'
        },
        sortable: 'custom',
        sortName: 'su.nick_name'
      })
    }
  } else {
    const index = columns.value.findIndex(item => item.label === '跟进人')
    if (index > 0) {
      columns.value.splice(index, 1)
    }
  }
  show.value = false
  nextTick(() => {
    show.value = true
  })
}

// 导出列表功能
const handleExport = async () => {
  // console.log('proTable.value.searchParam', proTable.value.searchParam)
  const params = Object.assign({}, proTable.value.searchParam)
  if (params.lastFollowTime) {
    params.startLastFollowTime = params.lastFollowTime[0]
    params.endLastFollowTime = params.lastFollowTime[1]
    delete params.lastFollowTime
  }
  if (params.createTime) {
    params.startCreateTime = params.createTime[0]
    params.endCreateTime = params.createTime[1]
    delete params.createTime
  }
  if (params.becomeTime) {
    params.becomeTimeStart = params.becomeTime[0]
    params.becomeTimeEnd = params.becomeTime[1]
    delete params.becomeTime
  }
  if (params.lastModifiedTime) {
    params.lastModifiedTimeStart = params.lastModifiedTime[0]
    params.lastModifiedTimeEnd = params.lastModifiedTime[1]
    delete params.lastModifiedTime
  }
  let result
  if (initParam.tabType === '0') {
    result = await cusCustomerOrClueAllCustomerListExport(params)
  } else {
    result = await cusCustomerOrClueCustomerListExport(params)
  }
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}
</script>
<style lang="scss" scoped></style>
