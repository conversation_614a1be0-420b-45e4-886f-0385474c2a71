<template>
  <el-dialog
    align-center
    title="转为正式合同"
    :width="isPermission ? 1200 : 800"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <!-- 头部信息 -->
    <template v-if="isPermission">
      <div class="container">
        <div class="left">
          <p class="p-t">
            意向合同文件: <span class="p-t-small">{{ detail.contractNo }}</span>
          </p>
          <iframe :src="urlPreview" frameborder="no" style="width: 100%; height: 100%" scrolling="auto" />
        </div>
        <div class="right">
          <p class="p-t">正式合同信息</p>
          <!-- 只会是记账合同 -->
          <component
            :isChange="false"
            :changeDisabled="false"
            :translateDisabled="true"
            :rowChange="true"
            :productTreeData="productTreeData"
            :ref="el => setCompRef(el, detail.contractId)"
            :is="formMap[detail.contractTypeName]"
            v-model="detail"
          />
        </div>
      </div>
    </template>
    <template v-else>
      <div class="wrap">
        <el-icon :size="40"><Lock /></el-icon>
        <p>您暂无权限对本合同进行操作</p>
      </div>
      <!-- <checkPermission @on-success="handleClose" />  -->
    </template>
    <template #footer>
      <template v-if="isPermission">
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </template>

      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { Lock } from '@element-plus/icons-vue'
import { getBusinessList } from '@/api/business/business'
import { usePreview } from '@/hooks/usePreview'
import accountingForm from './accounting-form.vue'
import oneOff from './one-off.vue'
import addressService from './address-service.vue'
import checkPermission from './check-permission.vue'
import { getContractDetailById, checkContractDetailIsPermission, checkContractChangeIsPermission } from '@/api/contract/contract'
import { cloneDeep } from 'lodash'
import { customerContractChangeToFormal } from '@/api/contract/contract'
import { changeFileData } from '@/utils/common'
import { ElMessage, ElMessageBox } from 'element-plus'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}
const props = defineProps({
  id: Number
})
const formMap = {
  记账合同: accountingForm,
  一次性合同: oneOff,
  地址服务协议合同: addressService
}
const detail = ref({})
const urlPreview = ref('')
const isPermission = ref(false)
const getContractDetail = async id => {
  const { data } = await checkContractChangeIsPermission(id)
  if (data) {
    isPermission.value = true
    const { isInContract, feeType, salesRevenue } = data
    const salesSplit = salesRevenue?.split('/') || ''
    console.log('salesSplit', salesRevenue, salesSplit)
    // const file = data.file
    // if (file) {
    //   delete file.id
    // }

    detail.value =
      Object.assign({}, data, {
        file: null, // 对上传的文件进行处理file
        salesRevenue: salesSplit.length === 2 && salesSplit[0] === '其他' ? '其他' : salesRevenue,
        otherSalesText: salesSplit.length === 2 && salesSplit[0] === '其他' ? salesSplit[1] : '',
        rowData: {
          isInContract,
          feeType
        },
        changeReason: '' // 变更的时候默认为空
      }) || {}
    // 后续恢复以下注释
    if (data?.file?.urls) {
      const previewUrl = await usePreview(data?.file?.urls)
      urlPreview.value = previewUrl
      // console.log('urlPreview.value', urlPreview.value)
    }
  } else {
    isPermission.value = false
  }
}
getContractDetail(props.id)

const compRefs = {}
const setCompRef = (el, id) => {
  if (el && id) {
    compRefs[id] = el
  }
}
const { proxy } = getCurrentInstance()
const handleSubmit = async () => {
  console.log('compRef', compRefs)
  // 遍历循环compRefs 进行校验
  for (let [contractId, comRef] of Object.entries(compRefs)) {
    console.log('key', contractId)
    console.log('value', comRef)
    const result = await comRef.validateForm()
    console.log('result', result)
    if (!result) {
      // 如果检验没成功，需要提示
      return
    }
  }
  const queryData = cloneDeep(detail.value)
  if (queryData.contractTypeName === '记账合同') {
    queryData.startTime = queryData.startTime + '-01'
    queryData.endTime = queryData.endTime + '-01'
  }
  const data = await customerContractChangeToFormal({
    ...queryData,
    file: Array.isArray(queryData.file) && queryData.file[0] ? changeFileData(queryData.file) : queryData.file,
    // bizType: '1', // '1'为变更合同
    salesRevenue: queryData.salesRevenue === '其他' ? '其他/' + queryData.otherSalesText : queryData.salesRevenue
  })
  if (data.code === 200) {
    // 说明变更成功
    proxy.$modal.msgSuccess(`提交成功!`)
    handleClose()
    emits('on-success')
  } else {
    proxy.$modal.msgError(`提交失败!`)
  }
}

// 获取所有的产品名称
const productTreeData = ref()
const getAllProducts = async () => {
  const { data } = await getBusinessList({
    pageNum: 1,
    pageSize: 10000
  })
  // 将后端传回的数据结构进行转换
  const revertData = []
  data.forEach(item => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      children: []
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach(child => {
        obj.children.push({
          name: child.productName,
          types:
            item.contractType?.split(',')?.map(item => {
              return {
                label: item,
                value: item
              }
            }) || [],
          type: '产品类型',
          quotation: child.quotation, // 合同报价
          isInContract: child.isInContract, // 是否在合同中定义
          feeType: child.feeType, //收费类型
          id: child.id // 产品类型id
        })
      })
    }
  })

  console.log('revertData', revertData)
  productTreeData.value = revertData || []
}

getAllProducts()
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  .left {
    flex: 1;
    margin-right: 24px;
  }
  .right {
    flex: 1;
  }
}
.p-t {
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
}
.p-t-small {
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #2383e7;
}

.wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
p {
  font-size: 18px;
}
</style>
