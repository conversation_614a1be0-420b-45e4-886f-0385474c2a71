<template>
  <el-dialog
    v-model="visible"
    title="导出结果"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    top="5vh"
    destroy-on-close
    append-to-body
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :disabled="disabled">
      <el-form-item label="结果来源日期" prop="analyseDate">
        <el-date-picker
          style="width: 100%"
          v-model="formData.analyseDate"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled="isDisabled"
          type="date"
          placeholder="请选择"
          @change="handleDateChange"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit"> 确认 </el-button>
      <el-button @click="handleClose"> 取消 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
const formData = reactive({
  id: undefined,
  analyseDate: undefined
})

const rules = {
  analyseDate: [{ required: true, message: '请选择日期', trigger: ['blur', 'change'] }]
}

const visible = ref(false)
const onShow = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}
const handleSubmit = () => {
  handleClose()
}

defineExpose({
  onShow
})
</script>

<style lang="scss" scoped></style>
