<!--
 * @Description: list-item
 * @Author: thb
 * @Date: 2023-09-05 13:23:23
 * @LastEditTime: 2024-03-13 13:19:38
 * @LastEditors: thb
-->
<template>
  <div class="list" v-for="(item, index) in listConvert" :key="index">
    <div class="list-item" v-for="(iItem, iIndex) in item.list" :key="iIndex">
      <div class="number">
        {{ index === 0 ? iItem.value + '%' : numberWithCommas(iItem.value) + '元' }}
      </div>
      <div class="name">{{ iItem.name || '--' }}</div>
    </div>
  </div>
</template>
<script setup>
import { numberWithCommas } from '@/utils/index'
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        sourceList: []
      }
    }
  }
})

const listConvert = computed(() => {
  const list = []
  const list1 = props.data?.sourceList?.map(item => {
    return {
      name: item.name,
      value: item.value.changeRate
    }
  })
  list.push({
    list: list1
      ? [
          {
            name: '线索转化率',
            value: props.data.clueChangeRate
          }
        ].concat(list1)
      : list1
  })

  const list2 = props.data?.sourceList?.map(item => {
    return {
      name: item.name,
      value: item.value.amount
    }
  })

  list.push({
    list: list2
      ? [
          {
            name: '线索转化金额',
            value: props.data.amount
          }
        ].concat(list2)
      : list2
  })
  console.log('list', list)
  return list
})
</script>
<style lang="scss" scoped>
.list {
  display: flex;
  // flex: 1 0 auto;
  background: #fafafa;
  border-radius: 4px 0px 0px 4px;
  flex: 1;
  .list-item {
    align-items: center;
    display: flex;
    justify-content: center;
    flex-direction: column;
    flex: 1 0 auto;
    position: relative;
    padding: 10px;
    background: #fafafa;
    &:first-child::after {
      content: '';
      display: none;
    }
    &:last-child::after {
      content: '';
      display: none;
    }
    &::after {
      content: '';
      width: 1px;
      height: 33px;
      right: 0;
      top: calc(30%);
      position: absolute;
      background: #d8d8d8;
    }
    &:first-child {
      .number {
        color: #2383e7;
      }
      .name {
        color: #2383e7;
      }
    }
    .number {
      font-size: 18px;
      font-family: SourceHanSansCN-Bold, SourceHanSansCN;
      font-weight: bold;
      color: #333333;
      text-align: center;
    }
    .name {
      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #a7a7a7;
      text-align: center;
    }
  }
}
</style>
