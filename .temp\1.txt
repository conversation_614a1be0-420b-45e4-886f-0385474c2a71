reason
: 
"未知异常请联系管理员:\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and DATE_FORMAT(first_sponsor_accounting_relate_record.operation_time,'%Y-%m-%d'' at line 132\n### The error may exist in URL [jar:file:/home/<USER>/jri-admin.jar!/BOOT-INF/lib/jri-biz-3.8.5.jar!/mapper/biz/CustomerInformationMapper.xml]\n### The error may involve com.jri.biz.mapper.CustomerInformationMapper.listPage-Inline\n### The error occurred while setting parameters\n### SQL: select             info.customer_id,             info.customer_no,             info.customer_name,             info.contract_main_id,             info.manger,             info.branch_office,             info.address,             info.information_mark,             info.customer_status,             info.customer_property,             info.industry,             info.nofee_reason_mark,             info.discard,             info.discard_reason,             info.nofee_reason,             info.counselor,             info.customer_success,             info.sponsor_accounting,             info.completeness,             info.company_identification,             info.lng,             info.manger_user_id,             info.sponsor_accounting_user_id,             info.counselor_user_id,             info.customer_success_user_id,             info.lat,             info.create_time,             bank.bank_id,             bank.bank_base_name,             bank.bank_base_account,             bank.receipt_card_flag,             bank.receipt_card_account,             bank.receipt_card_password,             bank.receipt_card_type,             biz_info.credi_code,             biz_info.type,             biz_info.legal_person,             biz_info.registered_address,             biz_info.contract,             biz_info.scope,             biz_info.website,             biz_info.bussiness_status,             biz_info.registration_authority,             biz_info.establish_date,             biz_info.registered_capital,             biz_info.industry business_industry,             biz_info.registration_number,             biz_info.open_date,             biz_info.open_end,             biz_info.organization_code,             biz_info.approval_date,             social_fund.social_account_flag,             social_fund.social_account,             social_fund.social_password,             social_fund.fund_account_flag,             social_fund.fund_account,             social_fund.fund_password,             tax.tax_registration_organ,             tax.tax_organ_address,             tax.rate_registration,             tax.tax_real_name_flag,             tax.individual_check_flag,             tax.reserved_phone_number,             tax.natural_person_password,             tax.identification_method_flag,             tax.certificate_account,             tax.certificate_password,             tax.drawing_sheet_flag,             tax.invoice_flag,             tax.invoice_seal_flag,             tax.drawing_sheet_dept,             tax.invoice_dept,             tax.invoice_seal_dept,             tax.invoice_type,             tax.drawing_sheet_type,             tax.invoice_limit,             tax_rebate_identified.watch_time,             tax_rebate_identified.approval_time,             tax_rebate_identified.watch_instructions,             tax_rebate_identified.dept,             cc.name contactPerson,             cc.phone contactPhone,                       manager_user.nick_name manager_user_name,             sponsor_accounting_user.nick_name sponsor_accounting_user_name,             counselor_user.nick_name counselor_user_name,             customer_success_user.nick_name customer_success_user_name,             a.date latestFollowUpDate         from customer_information info         left join customer_bank bank on info.customer_id = bank.ci_id and bank.is_deleted = 0         left join customer_business_information biz_info on info.customer_id = biz_info.ci_id and biz_info.is_deleted = 0         left join customer_social_fund social_fund on info.customer_id = social_fund.ci_id and social_fund.is_deleted = 0         left join customer_tax_information tax on info.customer_id = tax.ci_id and tax.is_deleted = 0         left join customer_tax_rebate_identified tax_rebate_identified on tax.id = tax_rebate_identified.main_id and tax_rebate_identified.is_deleted = 0         left join customer_contact cc on info.contract_main_id = cc.id         left join (             select distinct ci_id ,max(date) as date from customer_follow             group by ci_id         ) a on a.ci_id = info.customer_id         left join sys_user as manager_user on info.manger_user_id = manager_user.user_id         left join sys_user as sponsor_accounting_user on info.sponsor_accounting_user_id = sponsor_accounting_user.user_id         left join sys_user as counselor_user on info.counselor_user_id = counselor_user.user_id         left join sys_user as customer_success_user on info.customer_success_user_id = customer_success_user.user_id         left join (select min(operation_time) operation_time,                           customer_id                    from customer_user_relate_record                    where role = 'sponsor_accounting' and pre_user_id is null                    group by customer_id) as first_sponsor_accounting_relate_record on first_sponsor_accounting_relate_record.customer_id = info.customer_id         left join (select min(operation_time) operation_time,                             customer_id                     from customer_user_relate_record                     where role = 'customer_success' and pre_user_id is null                     group by customer_id) as first_customer_success_relate_record on first_customer_success_relate_record.customer_id = info.customer_id          WHERE info.is_deleted = 0                                                                                                                                                                 and                                                            and DATE_FORMAT(first_sponsor_accounting_relate_record.operation_time,'%Y-%m-%d') between ? and ?                                                      order by info.customer_no desc\n### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and DATE_FORMAT(first_sponsor_accounting_relate_record.operation_time,'%Y-%m-%d'' at line 132\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'and DATE_FORMAT(first_sponsor_accounting_relate_record.operation_time,'%Y-%m-%d'' at line 132"
