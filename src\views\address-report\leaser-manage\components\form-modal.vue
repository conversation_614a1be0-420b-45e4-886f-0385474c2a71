<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="600px"
    top="5vh"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-position="top"
      :rules="rules"
      :disabled="['详情'].includes(mode)"
      :hide-required-asterisk="['详情'].includes(mode)"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="出租人名称" prop="lessorName">
            <el-input v-model="formData.lessorName" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="银行" prop="bankName">
            <el-input v-model="formData.bankName" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="银行账户" prop="bankAccount">
            <el-input v-model="formData.bankAccount" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
      <el-button v-if="!['详情'].includes(mode)" type="primary" @click="handleSubmit" :loading="loading"> 保存 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { getAddressLessorGetById, postAddressLessorSave, postAddressLessorUpdate } from '@/api/address-report/index.js'
import { getBankTreeList } from '@/api/basicData/basicData'

const { proxy } = getCurrentInstance()
const emit = defineEmits()

const mode = ref('')
const formRef = ref()
const formData = reactive({})
const visible = ref(true)

const rules = {
  lessorName: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  phone: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ]
}

function getDetail(row) {
  getAddressLessorGetById({ id: row.id }).then(res => {
    Object.assign(formData, res.data)
    // formData.bankName = formData.bankName?.split(',') || []
  })
}

const onAdd = () => {
  mode.value = '新增'
}
const onDetail = row => {
  getDetail(row)
  mode.value = '详情'
}
const onEdit = row => {
  getDetail(row)
  mode.value = '编辑'
}
const handleClose = () => {
  emit('close')
}

const loading = ref(false)
const handleSubmit = async () => {
  console.log('formData', formData)
  if (loading.value) return
  await formRef.value.validate()
  loading.value = true
  // formData.bankName = formData.bankName?.join(',') || ''
  if (formData.id) {
    postAddressLessorUpdate(formData)
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
      .finally(() => {
        loading.value = false
      })
  } else {
    postAddressLessorSave(formData)
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}

const bank_list = ref([])
function onGetBasicData() {
  getBankTreeList({ enable: 1 }).then(res => {
    bank_list.value = res.data // 已验证，新增下拉有对应数据
  })
}
onGetBasicData()

defineExpose({
  onAdd,
  onEdit,
  onDetail
})
</script>

<style lang="scss" scoped>
.tips {
  color: #aaa;
}
</style>
