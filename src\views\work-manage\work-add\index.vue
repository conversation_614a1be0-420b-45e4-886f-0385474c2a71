<template>
  <div class="work-wrap">
    <div class="work-body">
      <div class="title">新增工单</div>
      <el-row justify="center" style="margin-top: 12px">
        <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="关联客户" prop="customerName">
                <div @click="listSelectShow = true" style="width: 100%">
                  <el-input v-model="formData.customerName" readonly maxlength="20" placeholder="请输入" />
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="紧急状态" prop="isUrgent">
                <el-radio-group v-model="formData.isUrgent">
                  <el-radio label="0">一般</el-radio>
                  <el-radio label="1">紧急</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="期望完成时间" prop="expectTime">
                <el-date-picker
                  v-model="formData.expectTime"
                  type="date"
                  format="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择"
              /></el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="工单类型" prop="orderTypeId">
                <!-- <el-select v-model="formData.orderTypeId" placeholder="请选择" clearable @change="handleChange">
                  <el-option v-for="(item, index) in orderList" :key="index" :label="item.typeName" :value="item.id"> </el-option>
                </el-select> -->
                <el-tree-select
                  v-model="formData.orderTypeId"
                  :data="orderList"
                  :placeholder="isDisabled ? ' ' : '请选择'"
                  :disabled="isDisabled"
                  clearable
                  :props="{ value: 'id', label: 'typeName', children: 'child' }"
                  @change="handleChange"
                  @node-click="handleNodeClick"
                >
                </el-tree-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="工单标题" prop="orderTitle">
                <el-input v-model="formData.orderTitle" maxlength="20" placeholder="请输入" />
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="地区" prop="address">
                <!-- <el-input v-model="formData.address" maxlength="100" placeholder="请输入" /> -->
                <el-cascader
                  v-model="formData.address"
                  filterable
                  :props="{ label: 'name', value: 'name', children: 'child' }"
                  :options="cascaderOptions"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="工单内容" prop="content">
                <el-input
                  v-model="formData.content"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  maxlength="1000"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  maxlength="1000"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24" v-if="formData.supplementExplain">
            <el-col :span="24">
              <el-form-item label="补充说明">
                <el-input
                  disabled
                  v-model="formData.supplementExplain"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  maxlength="1000"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="指派给" prop="executor">
                <SelectTree v-model="formData.executor" clearable placeholder="请选择" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
    </div>

    <el-row justify="end" class="work-footer">
      <el-button type="primary" @click="handleSubmit(formRef)">提交</el-button>
      <el-button @click="handleReset(formRef)">清空</el-button>
    </el-row>
  </div>
  <tableModal
    v-if="listSelectShow"
    rowKey="customerId"
    title="关联客户"
    :init-param="{ discard: 0 }"
    :columns="columns"
    multiple
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-multiple-select="handleSelect"
  />
</template>
<script setup lang="tsx">
import SelectTree from '@/components/SelectTree'
import { getCustomers } from '@/api/customer/file'
import tableModal from '@/components/tableModal'
import { orderSaveBatch, getOrderDropdownList, getOrderList } from '@/api/work/work'
import { ColumnProps } from '@/components/ProTable/interface'
import { customerProperty, customerIndustry } from '@/utils/constants'
import { useDic } from '@/hooks/useDic'
import useCommonStore from '@/store/modules/common'
import { getAreaTreeList } from '@/api/basicData/basicData'
const useCommon = useCommonStore()
const { getDic } = useDic()
const formData = ref({
  customerName: '',
  isUrgent: '0'
})

const rules = {
  customerName: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  isUrgent: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  orderTypeId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  orderTitle: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  content: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  executor: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

// 日期选择器禁用
const disabledDate = time => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用今天以前的时间
}
const formRef = ref()
const { proxy } = getCurrentInstance()
const listSelectShow = ref(false)
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_Property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]
const router = useRouter()
// handleSubmit
const handleSubmit = formEl => {
  if (!formEl) return
  formEl.validate(async (valid: any) => {
    if (valid) {
      // 校验成功
      const result: any = await orderSaveBatch({
        ...formData.value,
        address: formData.value.address?.join('/')
      })
      if (result.code === 200) {
        proxy.$modal.msgSuccess(`提交信息成功!`)
        formEl.resetFields()
        // 成功跳到我发起页面
        router.push({
          name: 'My-create'
          // params: {
          //   orderNo: result.data // orderNo 工单编号
          // }
        })
        // useCommon.setOrderNo(result.data)
      } else {
        proxy.$modal.msgError(`提交信息失败!`)
      }
    }
  })
}

// handleReset
const handleReset = formEl => {
  if (!formEl) return
  formEl.resetFields()
}
// 选择单个客户后
const handleSelect = (selectedList: any) => {
  // const { customerId, customerName } = data
  // // 填充记账合同表单
  // formData.value = Object.assign(formData.value, {
  //   ciId: customerId,
  //   customerName
  // })
  console.log('selectedList', selectedList)
  const nameStr = `${selectedList[0].customerName}${selectedList.length === 1 ? '' : `等${selectedList.length}家企业`}`
  formData.value = Object.assign(formData.value, {
    // ciId: customerId,
    customerName: nameStr,
    // selectedList,
    ciIdList: selectedList.map(item => item.customerId)
  })
}

const orderList = ref([])
const getOrderTypeList = async () => {
  const { data } = await getOrderList({
    status: '1'
  })
  orderList.value = data || []
}
getOrderTypeList()

const handleChange = value => {
  if (!value) {
    formData.value.supplementExplain = ''
  }
}
const handleNodeClick = (node: any) => {
  if (!node.child) {
    formData.value.supplementExplain = node.supplementExplain
  }
}

const cascaderOptions = ref([])
const getAddressCascader = async () => {
  const { data } = await getAreaTreeList({
    enable: '1'
  })
  cascaderOptions.value = data || []
}
getAddressCascader()
</script>
<style lang="scss" scoped>
.work-wrap {
  width: 100%;
  height: 100%;
  background: white;
  .work-body {
    height: calc(100% - 56px);
    padding: 20px 12px 0 16px;
    .title {
      font-size: 18px;
      font-family: AlibabaPuHuiTi_2_65_Medium;
      color: #333333;
      padding-bottom: 18px;
      border-bottom: 1px solid #e8e8e8ff;
    }
  }
}
.work-footer {
  height: 56px;
  align-items: center;
  padding-right: 16px;
  border-top: 1px solid #e8e8e8ff;
}
.el-form {
  display: flex;
  flex-direction: column;
  width: 1240px;
}
.el-select {
  width: 100%;
}
.el-form-item {
  :deep(.el-date-editor.el-input) {
    width: 100%;
  }
}
</style>
