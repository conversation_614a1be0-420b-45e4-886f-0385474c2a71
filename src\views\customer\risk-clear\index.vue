<template>
  <ProTable :init-param="initParam" ref="proTable" row-key="id" :columns="columns" :request-api="riskCustomerLoseHandleList">
    <!-- 表格 header 按钮 -->
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handleClear(scope.row)">办理</el-button>
    </template>
  </ProTable>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    :hideActionBtnForRiskCustomer="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { riskCustomerLoseHandleList } from '@/api/customer/risk.js'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from '@/views/customer/risk-audit/components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
import { useDic } from '@/hooks/useDic'
import useCommonStore from '@/store/modules/common'
import { CirclePlus } from '@element-plus/icons-vue'
import { reasonDict, stageDict } from '@/utils/constants.js'

const userStore = useUserStore()
const useCommon = useCommonStore()
const { getDic } = useDic()
const { proxy } = getCurrentInstance()

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}
const initParam = reactive({})
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'customerName',
    label: '客户名称',
    search: { el: 'input' },
    minWidth: 300
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'recentContractExpirationDate',
    label: '最近合同到期日期',
    width: 150
  },
  {
    prop: 'reason',
    label: '风险原因',
    width: 180,
    enum: reasonDict,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true }
    },
    render: scope => {
      return <span>{scope.row.reason || '--'}</span>
    }
  },
  {
    prop: 'remark',
    label: '说明',
    minWidth: 200
  },
  {
    prop: 'createBy',
    label: '创建人',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180
  },
  {
    prop: 'stage',
    label: '当前阶段',
    width: 200,
    enum: stageDict[1].children.slice(0, -1),
    search: {
      el: 'select'
    },
    render: scope => {
      return <span>{scope.row.stage || '--'}</span>
    }
  },
  {
    prop: 'handleUserName',
    label: '办理人',
    width: 150
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 100
  }
]

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handleClear = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onClear(row)
  })
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

// onMounted(() => {
// })
</script>
<style lang="scss" scoped></style>
