<div class="vue-office-docx-main">
  <!--docxjs library predefined styles--><style>
    .docx-wrapper {
      background: gray;
      padding: 30px;
      padding-bottom: 0px;
      display: flex;
      flex-flow: column;
      align-items: center;
    }
    .docx-wrapper > section.docx {
      background: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
      margin-bottom: 30px;
    }
    .docx {
      color: black;
    }
    section.docx {
      box-sizing: border-box;
      display: flex;
      flex-flow: column nowrap;
      position: relative;
      overflow: hidden;
    }
    section.docx > article {
      margin-bottom: auto;
    }
    .docx table {
      border-collapse: collapse;
    }
    .docx table td,
    .docx table th {
      vertical-align: top;
    }
    .docx p {
      margin: 0pt;
      min-height: 1em;
    }
    .docx span {
      white-space: pre-wrap;
      overflow-wrap: break-word;
    }
    .docx a {
      color: inherit;
      text-decoration: inherit;
    }</style
  ><!--docxjs document theme values--><style>
    .docx {
      --docx-majorHAnsi-font: Cambria;
      --docx-minorHAnsi-font: Calibri;
      --docx-dk1-color: #000000;
      --docx-lt1-color: #ffffff;
      --docx-dk2-color: #1f497d;
      --docx-lt2-color: #eeece1;
      --docx-accent1-color: #4f81bd;
      --docx-accent2-color: #c0504d;
      --docx-accent3-color: #9bbb59;
      --docx-accent4-color: #8064a2;
      --docx-accent5-color: #4bacc6;
      --docx-accent6-color: #f79646;
      --docx-hlink-color: #0000ff;
      --docx-folHlink-color: #800080;
    }</style
  ><!--docxjs document styles--><style>
    .docx span {
      font-family: Times New Roman;
    }
    .docx p,
    p.docx_1 {
      text-align: justify;
    }
    .docx p,
    p.docx_1 span {
      min-height: 10.5pt;
      font-size: 10.5pt;
    }
    .docx table,
    table.docx_6 td {
      padding-top: 0pt;
      padding-left: 5.4pt;
      padding-bottom: 0pt;
      padding-right: 5.4pt;
    }
    p.docx_2 {
      background-color: #000080;
      text-align: justify;
    }
    p.docx_2 span {
      min-height: 10.5pt;
      font-size: 10.5pt;
    }
    p.docx_3 span {
      min-height: 9pt;
      font-size: 9pt;
    }
    p.docx_3 {
      text-align: justify;
    }
    p.docx_4 {
      text-align: left;
    }
    p.docx_4 span {
      min-height: 9pt;
      font-size: 9pt;
    }
    p.docx_5 {
      border-bottom: 0.75pt solid black;
      text-align: center;
    }
    p.docx_5 span {
      min-height: 9pt;
      font-size: 9pt;
    }
    table.docx_7 p {
      text-align: justify;
    }
    table.docx_7 td {
      border-top: 0.5pt solid black;
      border-left: 0.5pt solid black;
      border-bottom: 0.5pt solid black;
      border-right: 0.5pt solid black;
      padding-top: 0pt;
      padding-left: 5.4pt;
      padding-bottom: 0pt;
      padding-right: 5.4pt;
    }
  </style>
  <div class="docx-wrapper">
    <section
      class="docx"
      style="padding: 70.9pt 89.85pt 56.7pt; width: 595.3pt; min-height: 841.9pt; column-count: 1; column-gap: 36pt"
    >
      <header><p class="docx_5" style="border-bottom: 0pt solid black"></p></header>
      <article>
        <p style="line-height: 1.5; margin-left: -44.95pt">
          <span id="_GoBack"></span
          ><span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 14pt; font-size: 14pt">“</span
          ><span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 14pt; font-size: 14pt">三</span
          ><span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 14pt; font-size: 14pt"
            >实”信息采集表</span
          ><span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 14pt; font-size: 14pt">一</span>
        </p>
        <p style="text-align: center">
          <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 16pt; font-size: 16pt">□</span
          ><span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 18pt; font-size: 18pt"
            >常住人口、</span
          ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 16pt; font-size: 16pt">□</span
          ><span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 18pt; font-size: 18pt"
            >寄住人口、</span
          ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 16pt; font-size: 16pt">□</span
          ><span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 18pt; font-size: 18pt"
            >未落户人口</span
          >
        </p>
        <p style="text-align: center"></p>
        <p style="text-align: center">
          <span style="font-family: 宋体; font-weight: bold">临汾市</span
          ><span style="font-family: 宋体; font-weight: bold; text-decoration: underline"> （县、市、</span
          ><span style="font-family: 宋体; font-weight: bold">区</span><span style="font-family: 宋体; font-weight: bold">）</span
          ><span style="font-family: 宋体; font-weight: bold">_________街办(乡、镇) _______社区居委会(村)______小区（组）</span>
        </p>
        <table
          class="docx_7"
          style="width: auto; background-color: rgb(255, 255, 255); table-layout: auto; margin-left: auto; margin-right: auto"
        >
          <colgroup>
            <col style="width: 29.45pt" />
            <col style="width: 44.6pt" />
            <col style="width: 8.3pt" />
            <col style="width: 0.9pt" />
            <col style="width: 14.85pt" />
            <col style="width: 11.9pt" />
            <col style="width: 13.75pt" />
            <col style="width: 12.9pt" />
            <col style="width: 27.8pt" />
            <col style="width: 2.3pt" />
            <col style="width: 2.5pt" />
            <col style="width: 20.95pt" />
            <col style="width: 9.3pt" />
            <col style="width: 4.45pt" />
            <col style="width: 2.8pt" />
            <col style="width: 3.4pt" />
            <col style="width: 7.55pt" />
            <col style="width: 4.25pt" />
            <col style="width: 9.5pt" />
            <col style="width: 13.75pt" />
            <col style="width: 13.75pt" />
            <col style="width: 13.75pt" />
            <col style="width: 6.65pt" />
            <col style="width: 0.35pt" />
            <col style="width: 0.6pt" />
            <col style="width: 3.6pt" />
            <col style="width: 2.55pt" />
            <col style="width: 5.85pt" />
            <col style="width: 7.15pt" />
            <col style="width: 0.75pt" />
            <col style="width: 4.9pt" />
            <col style="width: 8.9pt" />
            <col style="width: 0.95pt" />
            <col style="width: 3.1pt" />
            <col style="width: 9.7pt" />
            <col style="width: 3.1pt" />
            <col style="width: 10.85pt" />
            <col style="width: 1.95pt" />
            <col style="width: 11.8pt" />
            <col style="width: 1.05pt" />
            <col style="width: 8.3pt" />
            <col style="width: 4.5pt" />
            <col style="width: 12.8pt" />
            <col style="width: 0.95pt" />
            <col style="width: 1.5pt" />
            <col style="width: 10.35pt" />
            <col style="width: 1.9pt" />
            <col style="width: 10.9pt" />
            <col style="width: 0.75pt" />
            <col style="width: 2.1pt" />
            <col style="width: 9.95pt" />
            <col style="width: 3.8pt" />
            <col style="width: 9pt" />
            <col style="width: 4.9pt" />
            <col style="width: 7.9pt" />
            <col style="width: 12.8pt" />
            <col style="width: 12.8pt" />
            <col style="width: 12.8pt" />
            <col style="width: 12.8pt" />
            <col style="width: 13.2pt" />
          </colgroup>
          <tr style="height: 16.35pt; text-align: center">
            <td
              rowspan="16"
              style="
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">基</span>
              </p>
              <p style="text-align: center"></p>
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">本</span>
              </p>
              <p style="text-align: center"></p>
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">信</span>
              </p>
              <p style="text-align: center"></p>
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">息</span>
              </p>
            </td>
            <td
              colspan="2"
              style="
                width: 52.9pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">姓 名*</span>
              </p>
            </td>
            <td
              colspan="4"
              style="
                width: 41.4pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="5"
              style="
                width: 66.45pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">公民身份号码*</span>
              </p>
            </td>
            <td
              colspan="2"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="5"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 13.8pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 13.95pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 13.85pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 13.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 13.9pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              rowspan="4"
              colspan="6"
              style="
                width: 72.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >照片</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 19.15pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="2"
              style="
                width: 52.9pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">别 名</span>
              </p>
            </td>
            <td
              colspan="4"
              style="
                width: 41.4pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="5"
              style="
                width: 66.45pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">出生日期</span>
              </p>
            </td>
            <td
              colspan="11"
              style="
                width: 89.15pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline">
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"> 年</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">月</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">日</span>
              </p>
            </td>
            <td
              colspan="10"
              style="
                width: 35.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">性别</span>
              </p>
            </td>
            <td
              colspan="8"
              style="
                width: 49.85pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"><span>R</span></span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">男□女</span>
              </p>
            </td>
            <td
              colspan="6"
              style="
                width: 32pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">民族*</span>
              </p>
            </td>
            <td
              colspan="7"
              style="
                width: 41.4pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="
                display: none;
                width: 72.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 18.7pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="2"
              style="
                width: 52.9pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">籍 贯</span>
              </p>
            </td>
            <td
              colspan="25"
              style="
                width: 209.95pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 9pt"></p>
            </td>
            <td
              colspan="9"
              style="
                width: 49.4pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">户口类型</span></p>
            </td>
            <td
              colspan="17"
              style="
                width: 96.5pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 4.5pt">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□城镇 　□农村</span>
              </p>
            </td>
            <td
              colspan="6"
              style="
                display: none;
                width: 72.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 8.6pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="2"
              style="
                width: 52.9pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">身 高</span>
              </p>
            </td>
            <td
              colspan="7"
              style="
                width: 84.4pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="8"
              style="
                width: 55.2pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">血型</span></p>
            </td>
            <td
              colspan="36"
              style="
                width: 216.25pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□A型 □B型 □AB型 □O型 □未知 □其他</span
                >
              </p>
            </td>
            <td
              colspan="6"
              style="
                display: none;
                width: 72.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 8.6pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">与户主关系</span>
              </p>
            </td>
            <td
              colspan="23"
              style="
                width: 194.2pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"><span>£</span></span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >户主 □配偶 □子 □女 □亲属 □其他</span
                >
              </p>
            </td>
            <td
              colspan="13"
              style="
                width: 72.5pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">二代证</span>
              </p>
            </td>
            <td
              colspan="13"
              style="
                width: 73.4pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□有□无</span>
              </p>
            </td>
            <td
              colspan="6"
              style="
                width: 72.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">照片编号</span>
              </p>
            </td>
          </tr>
          <tr style="height: 14.8pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">政治面貌</span>
              </p>
            </td>
            <td
              colspan="4"
              style="
                width: 66.35pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="7"
              style="
                width: 45.7pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">固定电话</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">*</span>
              </p>
            </td>
            <td
              colspan="8"
              style="
                width: 69.55pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center"></p>
            </td>
            <td
              colspan="12"
              style="
                width: 51.15pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">移动电话</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">*</span>
              </p>
            </td>
            <td
              colspan="24"
              style="
                width: 179.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 13.25pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">文化程度</span>
              </p>
            </td>
            <td
              colspan="4"
              style="
                width: 66.35pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="7"
              style="
                width: 45.7pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">兵役状况</span>
              </p>
            </td>
            <td
              colspan="44"
              style="
                width: 300.35pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□未服兵役□退出现役□士兵预备役□军官预备役□服现役</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 14.8pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">宗教信仰</span>
              </p>
            </td>
            <td
              colspan="37"
              style="
                width: 271.2pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 7.5pt; font-size: 7.5pt"
                  >□无 □佛教 □喇嘛教□道教 □天主教 □基督教□东正教□伊斯兰教□其他</span
                >
              </p>
            </td>
            <td
              colspan="7"
              style="
                width: 39.15pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">专 长</span>
              </p>
            </td>
            <td
              colspan="11"
              style="
                width: 102.05pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 19.05pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">QQ号</span>
              </p>
            </td>
            <td
              colspan="11"
              style="
                width: 112.05pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="12"
              style="
                width: 82.15pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">服务处所及职务</span>
              </p>
            </td>
            <td
              colspan="32"
              style="
                width: 218.2pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 18.75pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">车辆类别</span>
              </p>
            </td>
            <td
              colspan="13"
              style="
                width: 123.85pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□汽车□摩托车□其他</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"><span>R</span></span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">无</span>
              </p>
            </td>
            <td
              colspan="6"
              style="
                width: 57.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">车辆型号</span>
              </p>
            </td>
            <td
              colspan="16"
              style="
                width: 76.8pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="9"
              style="
                width: 51.95pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">车牌号码</span>
              </p>
            </td>
            <td
              colspan="11"
              style="
                width: 102.05pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 18.75pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">婚姻状况</span>
              </p>
            </td>
            <td
              colspan="55"
              style="
                width: 412.4pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□未婚□已婚□离婚□丧偶</span>
              </p>
            </td>
          </tr>
          <tr style="height: 18.75pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">人员类别*</span>
              </p>
            </td>
            <td
              colspan="55"
              style="
                width: 412.4pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□普通 □空巢老人 □留守儿童 □有条件接触爆炸物品 □有爆破技能的 □重点人员 □其他</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 18.75pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">户口本地址</span>
              </p>
            </td>
            <td
              colspan="55"
              style="
                width: 412.4pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 14.25pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              rowspan="3"
              colspan="4"
              style="
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">实际居住详址</span>
              </p>
            </td>
            <td
              colspan="55"
              style="
                width: 412.4pt;
                border-top: 0.75pt solid black;
                border-bottom: none;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-left: 2.25pt solid black;
                border-right: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">临汾市</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"> </span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">县（</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">区</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">、市）</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">街办（乡、镇）</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">街路巷</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">门牌号</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">小区</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">楼号</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">单元</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">室</span>
              </p>
            </td>
          </tr>
          <tr style="height: 14.25pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                display: none;
                width: 68.65pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              rowspan="2"
              colspan="24"
              style="
                width: 201.35pt;
                border-top: none;
                border-bottom: none;
                border-right: none;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-left: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">临汾市</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"> </span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"> 县（</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">区</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">、市）</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">街办（乡、镇）</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">村</span>
              </p>
            </td>
            <td
              colspan="31"
              style="
                width: 211.05pt;
                border-top: none;
                border-left: none;
                border-bottom: none;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-right: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline">
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">街路巷</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">门牌号</span>
              </p>
            </td>
          </tr>
          <tr style="height: 14.25pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                display: none;
                width: 68.65pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="24"
              style="
                display: none;
                width: 201.35pt;
                border-top: none;
                border-bottom: 0.75pt solid black;
                border-right: none;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-left: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="31"
              style="
                width: 211.05pt;
                border-top: none;
                border-left: none;
                border-bottom: 0.75pt solid black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-right: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline">
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">组</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">号</span>
              </p>
            </td>
          </tr>
          <tr style="height: 23.45pt; text-align: center">
            <td
              rowspan="4"
              style="
                width: 29.45pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">家</span>
              </p>
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">庭</span>
              </p>
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">成</span>
              </p>
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">员</span>
              </p>
            </td>
            <td
              style="
                width: 44.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">姓名</span>
              </p>
            </td>
            <td
              colspan="6"
              style="
                width: 62.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 30.1pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">关系</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 40pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="8"
              style="
                width: 72.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; min-height: 9pt; font-size: 9pt">公民身份号码</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 12.95pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.95pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.85pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 13.2pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 23.5pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              style="
                width: 44.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">姓名</span>
              </p>
            </td>
            <td
              colspan="6"
              style="
                width: 62.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 30.1pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">关系</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 40pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="8"
              style="
                width: 72.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; min-height: 9pt; font-size: 9pt">公民身份号码</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 12.95pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.95pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.85pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 13.2pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 23.5pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              style="
                width: 44.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"><span style="font-family: 黑体">姓名</span></p>
            </td>
            <td
              colspan="6"
              style="
                width: 62.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 30.1pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">关系</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 40pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="8"
              style="
                width: 72.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; min-height: 9pt; font-size: 9pt">公民身份号码</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 12.95pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.95pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.85pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 13.2pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 22.6pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              style="
                width: 44.6pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"><span style="font-family: 黑体">姓名</span></p>
            </td>
            <td
              colspan="6"
              style="
                width: 62.6pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 30.1pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">关系</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 40pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="8"
              style="
                width: 72.6pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; min-height: 9pt; font-size: 9pt">公民身份号码</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 12.95pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.95pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.85pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 12.8pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 13.2pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 21.1pt; text-align: center">
            <td
              rowspan="3"
              style="
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >寄住信息</span
                >
              </p>
            </td>
            <td
              style="
                width: 44.6pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">寄住原因*</span>
              </p>
            </td>
            <td
              colspan="58"
              style="
                width: 436.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: -7.2pt; margin-left: 7.2pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□房屋拆迁 □入托上学 □务工经商 □投靠亲友 □分配调动工作 □分换住房 □两处以上住房 □其他</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 19.5pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              style="
                width: 44.6pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">寄住时间</span>
              </p>
            </td>
            <td
              colspan="9"
              style="
                width: 95.2pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline">
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">年</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">月</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">日</span>
              </p>
            </td>
            <td
              colspan="12"
              style="
                width: 110.1pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">与房主（户主）关系*</span>
              </p>
            </td>
            <td
              colspan="37"
              style="
                width: 231.15pt;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□本人□配偶□子女□父母□亲戚□租赁□其他</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 7.5pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="
                width: 94.3pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">房主/代理人</span>
              </p>
            </td>
            <td
              colspan="9"
              style="
                width: 86.4pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="9"
              style="
                width: 70.15pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">联系电话</span>
              </p>
            </td>
            <td
              colspan="35"
              style="
                width: 230.2pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 20.85pt; text-align: center">
            <td
              rowspan="3"
              style="
                width: 29.45pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >未落</span
                >
              </p>
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >户信</span
                >
              </p>
              <p style="margin-left: -5.25pt; margin-right: -5.25pt; text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">息</span>
              </p>
            </td>
            <td
              colspan="6"
              style="
                width: 94.3pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">未落户时间</span>
              </p>
            </td>
            <td
              colspan="11"
              style="
                width: 98.2pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline">
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">年</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">月</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">日</span>
              </p>
            </td>
            <td
              colspan="8"
              style="
                width: 61.95pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">未落户原因*</span>
              </p>
            </td>
            <td
              colspan="34"
              style="
                width: 226.6pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□出生未落□证件遗失□外流注销□持证未落□其他</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 20pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="3"
              style="
                width: 53.8pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">持证种类</span>
              </p>
            </td>
            <td
              colspan="32"
              style="
                width: 247.6pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□持迁移证□持出生证□持释放证□持退伍证□持护照</span
                >
              </p>
            </td>
            <td
              colspan="9"
              style="
                width: 53.7pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">证件编号</span>
              </p>
            </td>
            <td
              colspan="15"
              style="
                width: 125.95pt;
                border-width: 0.75pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 20pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="
                width: 94.3pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">原户籍所在地</span>
              </p>
            </td>
            <td
              colspan="53"
              style="
                width: 386.75pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt"></p>
            </td>
          </tr>
          <tr style="height: 22.8pt; text-align: center">
            <td
              rowspan="2"
              style="
                width: 29.45pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >有户无人信息</span
                >
              </p>
            </td>
            <td
              colspan="2"
              style="
                width: 52.9pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">起始时间</span>
              </p>
            </td>
            <td
              colspan="7"
              style="
                width: 84.4pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline">
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">年</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">月</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">日</span>
              </p>
            </td>
            <td
              colspan="3"
              style="
                width: 32.75pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">原因</span>
              </p>
            </td>
            <td
              colspan="47"
              style="
                width: 311pt;
                border-width: 2.25pt 2.25pt 0.75pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□求学入托 □出国 □务工经商 □房屋拆迁 □另有住房 □在刑 □其他</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 22.9pt; text-align: center">
            <td
              style="
                display: none;
                width: 29.45pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="5"
              style="
                width: 80.55pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">去往地址（详址）</span>
              </p>
            </td>
            <td
              colspan="54"
              style="
                width: 400.5pt;
                border-width: 0.75pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
          </tr>
          <tr style="height: 22.9pt; text-align: center">
            <td
              style="
                width: 29.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; font-weight: bold; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >备注</span
                >
              </p>
            </td>
            <td
              colspan="59"
              style="
                width: 481.05pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
          </tr>
        </table>
        <p style="min-height: 18pt; line-height: 18pt; text-align: left">
          <span style="font-weight: bold; color: rgb(0, 0, 0); min-height: 12pt; font-size: 12pt">采集人：</span
          ><span style="color: rgb(0, 0, 0); min-height: 12pt; font-size: 12pt"> </span
          ><span style="font-weight: bold; color: rgb(0, 0, 0); min-height: 12pt; font-size: 12pt">采集时间：</span
          ><span style="color: rgb(0, 0, 0); min-height: 12pt; font-size: 12pt"> 年 月 日 </span
          ><span style="font-weight: bold; color: rgb(0, 0, 0); min-height: 12pt; font-size: 12pt">审核人：</span>
        </p>
        <p style="text-align: center">
          <span style="font-family: 宋体; font-weight: bold; min-height: 18pt; font-size: 18pt"
            >《实有人口信息采集表四》填表说明</span
          >
        </p>
        <p style="min-height: 18pt; line-height: 18pt; text-indent: 24pt"></p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">一、</span
          ><span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 14pt; font-size: 14pt">填表范围</span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >本辖区所有常住户口人口（包括人户分离和空挂户人员）、在本辖区居住的寄住人口和未落常住人口填写此表。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28.1pt">
          <span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 14pt; font-size: 14pt">二、填写要求</span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt; text-align: left">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >1、照片： 16周岁以上尚未办理二代证的人员必采。人员照片为白底，大小（分辨率）为390*260，不超过20KB。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt; text-align: left">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">2、统一使用黑色中性笔填写。</span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt; text-align: left">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >3、表中所有*号为必填项，若无符合对应信息，则填“无”。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt; text-align: left">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">4、凡有“</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">口”的</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">项目，可以多选，在对应项目内打“</span
          ><span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 14pt; font-size: 14pt">√</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">”。</span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >5、本表填写后由采集人签字，填写采集时间，交由带队民警审核签字。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28.1pt">
          <span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 14pt; font-size: 14pt">三、有关具体填表说明</span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >填写实有人口基本信息采集表时，应先选择表头中常住、寄住、未落户人口其中一项后，再填写表内信息项。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28.1pt">
          <span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 14pt; font-size: 14pt">（一）基本信息</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >：常住人口（包括人户分离和空挂户人员）、寄住人口、未落常住户口人口，均应首先填写基本信息。</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"><span> </span></span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt; text-align: left">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >1、姓名：按登记人身份证或户口簿上的姓名填写（未落户的新生儿按已取姓名填写）。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >2、别名：填写未正式登记使用过的小名、绰号、诨名。</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"><span> </span></span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >3、专长：擅长一定行业或领域的专业技能，如开锁、机修、刻章、木工、文艺等。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">4、服务处所：填写工作单位。</span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >5、户口本地址：表格填写人在户口本上的登记地址。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">6、实际居住详址：填写</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">第一阶段编划的地址信息。如与户籍</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">对应实际详址一致，可在口内划“</span
          ><span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 14pt; font-size: 14pt">√</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">”</span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >7、车辆类别：若填表人拥有一辆以上的车辆类别，应分别填写，并用逗号“，”隔开。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28.1pt">
          <span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 14pt; font-size: 14pt">（二）寄住信息</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >：同一县内，离开常住户口所在地，在本县其他派出所辖区内居住的常住人口，填写相关信息。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28.1pt">
          <span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 14pt; font-size: 14pt">（三）未落户信息</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">：未落常住户口人员，填写相关信息。</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"><span> </span></span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28.1pt">
          <span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 14pt; font-size: 14pt">（四）有户无人信息</span
          ><span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt"
            >：离开户口所在地6个月以上的人员，填写相关信息。</span
          >
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">1、起始时间：填写离开户口所在地的日期。</span>
        </p>
        <p style="min-height: 19.5pt; line-height: 19.5pt; text-indent: 28pt">
          <span style="font-family: 仿宋_GB2312; min-height: 14pt; font-size: 14pt">2、去住地址：填写现居住详细地址。</span>
        </p>
      </article>
      <footer><p class="docx_4"></p></footer>
    </section>
  </div>
</div>
