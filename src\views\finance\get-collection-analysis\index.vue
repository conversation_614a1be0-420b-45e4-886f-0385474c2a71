<template>
  <div class="main-wrap">
    <div class="card table-search" style="margin-bottom: 10px">
      <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
        <el-row :gutter="0">
          <el-col :span="8">
            <el-form-item label="人员名称" prop="customerSuccess" style="width: 100%; padding-right: 25px">
              <el-input
                v-model="queryParams.customerSuccess"
                placeholder="请输入"
                clearable
                style="width: 100%"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属部门" prop="deptId" style="width: 100%; padding-right: 25px">
              <el-tree-select
                style="width: 100%"
                v-model="queryParams.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择"
                check-strictly
                default-expand-all
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分析月份" prop="month" style="width: 100%; padding-right: 25px">
              <el-date-picker
                v-model="queryParams.month"
                :editable="false"
                :clearable="false"
                format="YYYY-MM"
                value-format="YYYY-MM"
                type="month"
                placeholder="请选择"
                style="width: 100%"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div style="text-align: left">
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="card table-main">
      <div class="action-btns">
        <el-radio-group v-model="queryParams.role" @change="handleQuery">
          <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{
            item.dictLabel
          }}</el-radio-button>
        </el-radio-group>
        <el-button @click="handleExport">导出</el-button>
      </div>
      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="index" width="50" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column width="100" label="人员名称" align="center">
          <template #default="{ row }">
            <span class="blue-text" @click="handlShowCustomerSuccessDetail(row)">{{ row.customerSuccess || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column width="200" label="所属部门" align="center">
          <template #default="{ row }"> {{ row.deptName }} </template>
        </el-table-column>
        <el-table-column width="100" label="分析月份" align="center">
          <template #default="{ row }">
            <span v-if="row.month">{{ row.month }} </span></template
          >
        </el-table-column>
        <el-table-column width="100" label="当月营业额" align="center">
          <template #default="{ row }">
            <span v-if="row.monthTurnover > 0">{{ Number(row.monthTurnover).toLocaleString() }}</span>
            <span v-else>0</span>
          </template>
        </el-table-column>
        <el-table-column label="当月记账营业额" width="150" align="center" prop="bookkeepingMonthTurnover">
          <template #default="{ row }">
            <span v-if="row.bookkeepingMonthTurnover > 0">{{ Number(row.bookkeepingMonthTurnover).toLocaleString() }}</span>
            <span v-else>0</span></template
          >
        </el-table-column>
        <el-table-column label="当月地址营业额" width="150" align="center" prop="addressMonthTurnover">
          <template #default="{ row }">
            <span v-if="row.addressMonthTurnover > 0">{{ Number(row.addressMonthTurnover).toLocaleString() }}</span>
            <span v-else>0</span>
          </template>
        </el-table-column>

        <el-table-column label="当月维护企业" width="150" align="center">
          <template #default="{ row }"> {{ row.monthMaintainCustomer }}家</template>
        </el-table-column>
        <el-table-column label="当月应收" width="100" align="center">
          <template #default="{ row }">
            <div v-if="row.allPaymentAmount > 0">{{ Number(row.allPaymentAmount).toLocaleString() }}</div>
            <div v-else>0</div></template
          >
        </el-table-column>
        <el-table-column label="当月实收企业" width="150" align="center">
          <template #default="{ row }"> {{ row.monthPaymentClearCustomer }}家</template>
        </el-table-column>
        <el-table-column label="当月实收" width="150" align="center">
          <template #default="{ row }">
            <span v-if="row.allReceiptAmount > 0">{{ Number(row.allReceiptAmount).toLocaleString() }} </span
            ><span v-else>0</span></template
          >
        </el-table-column>
        <el-table-column label="当月欠费企业" width="150" align="center">
          <template #default="{ row }"> {{ row.monthPaymentDebtCustomer }}家</template>
        </el-table-column>
        <el-table-column label="当月欠费" width="150" align="center">
          <template #default="{ row }">
            <div v-if="row.monthDebt > 0">{{ Number(row.monthDebt).toLocaleString() }}</div>
            <div v-else>0</div></template
          >
        </el-table-column>
        <el-table-column label="当月回款率" width="100" align="center">
          <template #default="{ row }">
            <span v-if="row.allPaymentAmount > 0"
              >{{ multiply(divide(Number(row.allReceiptAmount), Number(row.allPaymentAmount)), 100).toFixed(2) }}% </span
            ><span v-else>-</span></template
          >
        </el-table-column>
        <el-table-column label="欠费总金额" width="150" align="center">
          <template #default="{ row }">
            <span v-if="row.arrearageAmount > 0">{{ Number(row.arrearageAmount).toLocaleString() }} </span><span v-else>0</span>
          </template>
        </el-table-column>
        <el-table-column label="欠费实收" width="150" align="center">
          <template #default="{ row }">
            <span v-if="row.receiveArrearageAmount > 0">{{ Number(row.receiveArrearageAmount).toLocaleString() }} </span
            ><span v-else>0</span></template
          >
        </el-table-column>
        <el-table-column label="总回款率" width="100" align="center">
          <template #default="{ row }">
            <span v-if="row.arrearageAmount > 0"
              >{{ multiply(divide(Number(row.receiveArrearageAmount), Number(row.arrearageAmount)), 100).toFixed(2) }}% </span
            ><span v-else>-</span></template
          >
        </el-table-column>
        <template #append v-if="!dataList.length">
          <slot name="append"> </slot>
        </template>
        <template #empty v-if="!dataList.length">
          <div class="table-empty">
            <slot name="empty">
              <img src="@/assets/images/notData.png" alt="notData" />
              <div>暂无数据</div>
            </slot>
          </div>
        </template>
      </el-table>
    </div>
    <div class="my-pagination">
      <div class="tip-con">
        <div class="line1">
          <span class="t1-con"
            ><span class="t1">当月维护企业总计：</span
            ><span class="t2">{{ Number(infoGetCollectionAnalysis.maintainCustomer) }} 家</span></span
          >
          <span class="t1-con"
            ><span class="t1">当月实收总计：</span
            ><span class="t2">{{ Number(infoGetCollectionAnalysis.allReceiptAmount).toLocaleString() }} 元</span></span
          >
        </div>
        <div class="line1">
          <span class="t1-con"
            ><span class="t1">当月应收总计：</span
            ><span class="t2">{{ Number(infoGetCollectionAnalysis.allPaymentAmount).toLocaleString() }} 元</span></span
          >
          <span class="t1-con">
            <span class="t1">当月欠费总计：</span
            ><span class="t2">{{ Number(infoGetCollectionAnalysis.monthDebt).toLocaleString() }} 元</span></span
          >
        </div>
      </div>
      <Pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>
  </div>
  <customerSuccessDetail
    v-if="customerSuccessDetailShow"
    :customerSuccessUserId="customerSuccessUserId"
    :addressMonthTurnover="addressMonthTurnover"
    :bookkeepingMonthTurnover="bookkeepingMonthTurnover"
    @on-close="handlCloseCustomerSuccessDetail"
    @on-list="getList"
  />
</template>

<script setup lang="jsx">
import {
  turnoverStatementGetCollectionAnalysis,
  getTurnoverStatementGetCollectionAnalysisStatistic
} from '@/api/finance/account-statement'
import { deptTreeSelect } from '@/api/system/user'
import customerSuccessDetail from '@/views/finance/account-statement/components/customer-success-detail.vue'
import dayjs from 'dayjs'
import { onMounted } from 'vue'
import { multiply, divide } from '@/utils/math'

const tabs = [
  { dicValue: 'manager', dictLabel: '财税顾问' },
  { dicValue: 'customer_success', dictLabel: '客户成功' },
  { dicValue: 'sponsor_accounting', dictLabel: '主办会计' },
  { dicValue: 'counselor', dictLabel: '开票员' }
]

const { proxy } = getCurrentInstance()

const dataList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const deptOptions = ref(undefined)

const initType = { role: 'manager' }
const initQueryParams = {
  pageNum: 1,
  pageSize: 10,
  /* 回款分析 start */
  customerSuccess: undefined,
  deptId: undefined,
  month: dayjs().format('YYYY-MM')
  /* 回款分析 end */
}

const data = reactive({
  queryParams: Object.assign({}, initType, initQueryParams)
})

const { queryParams } = toRefs(data)

/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data
  })
}

const infoGetCollectionAnalysis = ref({})
const getList = async () => {
  console.log('getList', queryParams.value.role)
  loading.value = true
  const response = await turnoverStatementGetCollectionAnalysis(queryParams.value)
  dataList.value = response.data.records
  console.log('dataList', dataList.value)
  // console.log('dataList.value', dataList.value)
  total.value = parseInt(response.data.total)
  loading.value = false
  const responseGetCollectionAnalysis = await getTurnoverStatementGetCollectionAnalysisStatistic(queryParams.value)
  infoGetCollectionAnalysis.value = responseGetCollectionAnalysis.data
}

// 根据节点名称获取树结构的节点
const getTreeNodeByName = (treeData, name) => {
  for (let i = 0; i < treeData.length; i++) {
    if (treeData[i].label === name) {
      return treeData[i] //名称
    } else if (treeData[i].children) {
      const result = getTreeNodeByName(treeData[i].children, name)
      if (result) {
        return result
      }
    }
  }
  return null
}

/** 搜索按钮操作 */
async function handleQuery(value) {
  if (value === '回款分析') {
    if (deptOptions.value.length) {
      // 获取客户成功部的节点
      const searchNode = getTreeNodeByName(deptOptions.value, '客户成功部')
      console.log('searchNode', searchNode)
      queryParams.value.deptId = searchNode.id
    } else {
      await getDeptTree()
      // 获取客户成功部的节点
      const searchNode = getTreeNodeByName(deptOptions.value, '客户成功部')
      console.log('searchNode', searchNode)
      queryParams.value.deptId = searchNode.id
    }
  }
  getList()
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  queryParams.value = Object.assign(queryParams.value, initQueryParams)
  handleQuery()
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.postId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

const customerSuccessUserId = ref('')
/* 客户成功详情弹窗 ---start--- */
const customerSuccessDetailShow = ref(false)
let addressMonthTurnover = ''
let bookkeepingMonthTurnover = ''
const handlShowCustomerSuccessDetail = row => {
  customerSuccessUserId.value = row.customerSuccessUserId
  addressMonthTurnover = row.addressMonthTurnover
  bookkeepingMonthTurnover = row.bookkeepingMonthTurnover
  customerSuccessDetailShow.value = true
}
const handlCloseCustomerSuccessDetail = () => {
  customerSuccessDetailShow.value = false
}
/* 客户成功详情弹窗 ---end--- */

// exportTaxDetail
const handleExport = async () => {
  proxy.download('/turnoverStatement/getCollectionAnalysisExport', queryParams.value, '回款分析导出.xlsx')
}
getDeptTree()
getList()
onMounted(() => {})
</script>
<style lang="scss" scoped>
// .my-table {
//   min-height: 300px;
//   display: flex;
//   .el-table__append-wrapper {
//   }
// }
:deep(.el-table__append-wrapper) {
  // todo 无数据时候塌陷的问题
  min-height: 110px;
}
.table-main {
  border-radius: 0;
}
.my-pagination {
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .tip-con {
    padding-left: 35px;
    flex: 1 1 auto;
    .line1 {
      margin-bottom: 5px;
      display: flex;
      .t1-con {
        display: inline-block;
        width: 250px;
        margin-right: 10px;
      }
      .t1 {
        color: #409eff;
      }
    }
  }
  :deep(.pagination-container) {
    height: 50px;
    margin-top: 10px;
    width: 1000px;
    position: relative;
    .el-pagination {
      right: 40px;
    }
  }
}
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}

.action-btns {
  display: flex;
  justify-content: space-between;
}
</style>
