<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12" v-if="formData.parentId && formData.parentId !== '0'">
        <el-form-item label="上级菜单" prop="parentId">
          <el-select v-model="formData.parentId" placeholder="请选择" @change="handleChange" disabled>
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="来源类型" prop="name">
          <el-input
            :disabled="['1', '2', '3', '4'].includes(formData.id)"
            v-model="formData.name"
            maxlength="20"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 平台1 直投2 客户介绍3 渠道4 -->
    <el-row :gutter="24" v-if="formData.parentId === '1'">
      <el-col :span="12">
        <el-form-item label="线索单价" prop="price">
          <NumberInput v-model="formData.price" maxlength="10" placeholder="请输入">
            <template #suffix> 元 </template>
          </NumberInput>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24" v-if="formData.parentId === '2'">
      <el-col :span="24">
        <el-form-item>
          <template #label>
            <div class="my-form-item-label">
              <div class="label">充值记录</div>
              <div class="button">
                <el-button type="primary" plain @click="handleAdd" v-if="formData.id">新增</el-button>
              </div>
            </div>
          </template>
          <div class="my-table">
            <ProTable ref="proTable" row-key="id" :init-param="initParam" :columns="columns" :request-api="cusSourceDirectlist">
            </ProTable>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24" v-if="formData.parentId === '4'">
      <el-col :span="12">
        <el-form-item label="电话号码" prop="phone">
          <el-input v-model="formData.phone" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="银行账户" prop="bankAccount">
          <el-input v-model="formData.bankAccount" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="jsx">
import { reactive, watch } from 'vue'
import { cusSourceTree, cusSourceDirectlist, cusSourceSave } from '@/api/material-manage/source'
import { useDialog } from '@/hooks/useDialogFinance'
import formModalCharge from './form-modal-charge.vue'
import NumberInput from '@/components/NumberInput'

const options = ref([])
function getList() {
  cusSourceTree({ enable: 1 }).then(res => {
    console.log('res', res.data)
    options.value = res.data.map(item => {
      return {
        label: item.name,
        value: item.id
      }
    })
    if (formData.parentId) {
      formData.biz = options.value.find(item => item.value === formData.parentId)?.label
    }
  })
}
getList()

const formData = reactive({
  parentId: undefined,
  biz: '',
  name: '',
  // sort: '',
  // enable: '1', // 默认为正常
  remark: '',
  id: undefined
})

const rules = {
  name: [{ required: true, trigger: 'blur', message: '请输入' }]
}

// 表单检验方法
const formRef = ref()

/** 充值记录 */
const proTable = ref()
const initParam = reactive({
  mainId: formData.id
})
watch(
  () => formData.parentId,
  () => {
    initParam.mainId = formData.id
  }
)
const columns = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'amount',
    label: '金额',
    render: scope => {
      return <span>{scope.row.amount || '0'}元</span>
    }
  },
  {
    prop: 'chargeTime',
    label: '充值时间'
  }
]

// 新增数据
const { showDialog } = useDialog()
const handleAdd = row => {
  showDialog({
    title: '新增',
    customClass: 'medium-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: formModalCharge, // 表单组件
    rowFormData: { mainId: formData.id },
    submitApi: cusSourceSave, // 提交api
    submitCallback: submitCallback // 提交成功之后的回调函数
  })
}
const submitCallback = () => {
  proTable.value?.getTableList()
}

function handleChange(val) {
  // console.log('val', val)
  formData.biz = options.value.find(item => item.value === formData.parentId)?.label
  // console.log('formData.biz', formData.biz)
}

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  padding: 0;
}
.my-form-item-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
<style lang="scss" scoped>
:deep(.form-rap) {
  width: 100%;
}
:deep(.el-form-item--default) {
  .el-form-item__content {
    width: 100%;
  }
}
.el-select {
  width: 573px;
}

.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
.my-table {
  // min-height: 300px;
  // max-height: 500px;
  // overflow-y: scroll;
  display: flex;
  :deep(.table-box) {
    .card {
      padding: 0;
      border: none;
      box-shadow: none;
    }
    .table-search {
      margin-top: 15px;
      margin-bottom: 0px;
    }
    .table-main {
      min-height: 200px;
      max-height: 400px;
      .table-header {
        margin-bottom: 0px;
      }
    }
  }
}
</style>
