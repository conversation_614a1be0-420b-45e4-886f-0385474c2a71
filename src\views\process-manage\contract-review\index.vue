<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-06-16 14:27:19
 * @LastEditTime: 2023-06-19 16:41:38
 * @LastEditors: thb
-->
<!--合同评审流程控制 -->
<template>
  <ProTable
    ref="proTable"
    :init-param="initParam"
    title="合同评审审批流程"
    :columns="columns"
    :request-api="getContractReviewList"
  >
    <!-- 表格 header 按钮 -->
    <template #tableHeader="scope">
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button type="danger" :icon="Delete" @click="handleDelete(scope.selectedListIds)">删除</el-button>
    </template>
    <template #name="{ row }">
      <span class="blue-text" @click="showDetail(row.id)">{{ row.name }}</span>
    </template>
  </ProTable>
  <processAdd
    v-if="isShow"
    :type="type"
    :stepType="stepType"
    :id="stepId"
    @on-close="isShow = false"
    @on-list="getList"
    @on-edit="type = 'update'"
  />
</template>
<script setup lang="tsx">
import { ref, reactive } from 'vue'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { getContractReviewList, changeProcessStatus, deleteProcess } from '@/api/process/process'
import { useHandleData } from '@/hooks/useHandleData'
import processAdd from './components/process-add.vue'

const props = defineProps({
  stepType: {
    type: String,
    default: '2' // '2'合同审批 '1'合同借阅 '0'模板审批
  }
})
// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: props.stepType })
const columns: ColumnProps<any>[] = [
  {
    type: 'selection',
    fixed: 'left',
    width: 80,
    selectable: row => {
      return row.enable === '0'
    }
  },
  {
    prop: 'name',
    label: '流程名称',
    search: { el: 'input' }
  },
  {
    prop: 'contractTypeName',
    label: '合同类型'
  },
  {
    prop: 'deptNames',
    label: '应用部门'
  },
  {
    prop: 'enable',
    label: '使用状态',
    enum: [
      {
        label: '正常',
        value: '1'
      },
      {
        label: '停用',
        value: '0'
      }
    ],
    search: { el: 'select' },
    render: scope => {
      return (
        <>
          {
            <el-switch
              model-value={scope.row.enable}
              active-text={scope.row.enable === '1' ? '正常' : '停用'}
              active-value={'1'}
              inactive-value={'0'}
              onClick={() => changeStatus(scope.row)}
            />
          }
        </>
      )
    }
  },
  {
    prop: 'createBy',
    label: '创建人'
  },
  {
    prop: 'createTime',
    label: '创建时间'
  }
]
// 切换用户状态
const proTable = ref()
const changeStatus = async (row: any) => {
  await useHandleData(changeProcessStatus, row.id, `切换【${row.name}】流程状态`)
  proTable.value?.getTableList()
}

// 批量删除流程
const handleDelete = async (ids: string[]) => {
  if (!ids.length) return
  await useHandleData(deleteProcess, ids, '删除所选流程信息')
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
}
// 新增流程
const isShow = ref(false)
const type = ref('add') // 'add'表示新增 'detail'表示详情 'update'表示编辑
const handleAdd = () => {
  type.value = 'add'
  isShow.value = true
}
const getList = () => {
  proTable.value?.getTableList()
}
//stepId
const stepId = ref()
const showDetail = (id: number) => {
  type.value = 'detail'
  stepId.value = id
  isShow.value = true
}
</script>
<style lang="scss" scoped></style>
