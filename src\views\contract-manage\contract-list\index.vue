<template>
  <ProTable
    ref="proTable"
    title="合同台账"
    rowKey="contractId"
    :init-param="initParam"
    :columns="columns"
    :request-api="getContractList"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <!-- 表格tabs -->
    <template #tabs>
      <el-radio-group :model-value="initParam.contractStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >

    <!-- 操作 -->
    <template #operation="{ row }" v-if="initParam.contractStatus === '6'">
      <!-- 变更操作 -->
      <!-- <span v-if="initParam.contractStatus === '4'">
        <el-button link type="primary" @click="handleChange(row.contractId, row.type)">变更</el-button>
      </span> -->
      <span v-if="initParam.contractStatus === '6'">
        <el-button link type="primary" @click="handleTranslate(row.contractId, row.type)">转为正式合同</el-button>
      </span>
      <span v-else>--</span>
    </template>
    <template #contractNo="{ row }">
      <span class="blue-text" @click="handleCheck(row)">{{ row.contractNo }}</span>
    </template>
    <!-- customerName -->
    <template #customerName="{ row }">
      <span class="blue-text" @click="handleShowCustomer(row.ciId)">{{ row.customerName }}</span>
    </template>
    <!-- totalCost -->
    <template #totalCost="{ row }">
      <span>{{ row.totalCost }} 元</span>
    </template>

    <!-- 新增归档号 -->
    <template #documentNo="{ row }">
      <span :class="[row.documentNo ? '' : 'blue-text']" @click="editDocumentNo(row)">{{ row.documentNo || '归档' }} </span>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader="scope">
      <el-button type="primary" :icon="CirclePlus" @click="contractShow = true">新增</el-button>
      <el-button
        type="danger"
        :icon="Delete"
        @click="handleDelete(scope.selectedListIds)"
        :disabled="initParam.contractStatus !== '3'"
        >删除</el-button
      >
    </template>
  </ProTable>
  <contract v-if="contractShow" @on-close="contractShow = false" @on-next="handelNext" />

  <normalCreate v-if="normalShow" @on-close="normalShow = false" @on-success="getList" />
  <templateCreate
    v-if="templateShow"
    :id="contractId"
    :isRenewal="isRenewal"
    @on-close="templateShow = false"
    @on-success="getList"
  />

  <contractDetail
    v-if="detailShow"
    :isChange="isChange"
    :id="contractId"
    :contractStatus="initParam.contractStatus"
    @on-close="
      () => {
        detailShow = false
      }
    "
    @on-change="handleChange"
    @on-translate="handleTranslate"
    @on-terminate="handleTerminate"
  />

  <changeContract
    v-if="changeShow"
    :id="contractId"
    :isRenewal="isRenewal"
    @on-close="changeShow = false"
    @on-success="getList"
  />
  <translateContract v-if="translateShow" :id="contractId" @on-close="translateShow = false" @on-success="getList" />

  <customerDetail v-if="customerDetailShow" :id="rowId" :hideActionBtn="true" @on-close="handleClose" @on-list="getList" />
</template>
<script setup lang="tsx">
import { reactive, ref, onMounted, provide, nextTick } from 'vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
// import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { getContractList, deleteBatchContract, checkContractDetailIsPermission, updateDocumentNo } from '@/api/contract/contract'
import contract from '@/views/customer/customer-file/components/contract.vue'
import normalCreate from './components/normal-create.vue'
import templateCreate from './components/template-create.vue'
import { useHandleData } from '@/hooks/useHandleData'
import contractDetail from './components/contract-detail.vue'
import changeContract from './components/change-contract.vue'
import translateContract from './components/translate-contract.vue'
import { useRoute } from 'vue-router'
import useCommonStore from '@/store/modules/common'
import useUserStore from '@/store/modules/user'
import { useDialog } from '@/hooks/useDialog'
import documentNoForm from './components/documentNo-form'
import { getBusinessList } from '@/api/business/business'
import InputRange from '@/views/finance/day-book-statement/components/input-range'
import { getReviewerTreeData } from '@/api/process/process'

// editDocumentNo 编辑归档号
const { showDialog } = useDialog()
const editDocumentNo = row => {
  showDialog({
    title: '合同归档',
    component: documentNoForm,
    customClass: 'document-dialog',
    getApi: checkContractDetailIsPermission,
    requestParams: row.contractId,
    submitApi: updateDocumentNo,
    submitCallback: () => {
      getList()
    }
    // handleRevertParams
  })
}

const userStore = useUserStore()
const useCommon = useCommonStore()
const getTreeData = () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await getReviewerTreeData()
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ contractStatus: '4' })

const tabs = ref([
  {
    dictLabel: '执行中',
    dicValue: '4'
  },
  {
    dictLabel: '意向合同',
    dicValue: '6'
  },
  {
    dictLabel: '已到期',
    dicValue: '5'
  },
  {
    dictLabel: '已终止',
    dicValue: '3'
  }
])
// const defaultColumns = [
//   {
//     prop: 'index',
//     label: '序号',
//     render: (scope: any) => {
//       return <span>{scope.$index + 1}</span>
//     }
//   },

//   {
//     prop: 'operation',
//     label: '操作'
//   },
//   {
//     prop: 'customerName',
//     label: '客户名称',
//     search: { el: 'input' }
//   },
//   {
//     prop: 'customerNo',
//     label: '客户编号',
//     search: { el: 'input' }
//   },
//   {
//     prop: 'contractName',
//     label: '合同名称',
//     search: { el: 'input' }
//   },
//   {
//     prop: 'contractNo',
//     label: '合同编号',
//     search: { el: 'input' }
//   },
//   {
//     prop: 'productName',
//     label: '服务产品'
//   },
//   {
//     prop: 'contractType',
//     label: '合同类型',
//     enum: [
//       {
//         label: '记账合同',
//         value: '0'
//       },
//       {
//         label: '一次性合同',
//         value: '1'
//       },
//       {
//         label: '地址服务协议合同',
//         value: '2'
//       }
//     ],
//     search: { el: 'select' }
//   },
//   {
//     prop: 'totalCost',
//     label: '合同金额'
//   },
//   {
//     prop: 'startTime',
//     label: '起始时间'
//   },
//   {
//     prop: 'endTime',
//     label: '结束时间'
//   },
//   {
//     prop: 'manager',
//     label: '财税顾问'
//   }
// ]
// ColumnProps<any>[]

// 获取当前路由
const route = useRoute()
const columns: any = ref([
  {
    prop: 'index',
    label: '序号',
    fixed: 'left',
    render: (scope: any) => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'contractNo',
    label: '合同编号',
    fixed: 'left',
    width: 150,
    search: { el: 'input', order: 5 }
  },

  {
    prop: 'customerName',
    label: '客户名称',
    width: 300,
    search: { el: 'input', order: 1 }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: 150,
    search: { el: 'input', order: 4 }
  },
  {
    prop: 'productId',
    width: 200,
    enum: () => {
      return new Promise(async (resolve, reject) => {
        const { data } = await getBusinessList({
          pageNum: 1,
          pageSize: 10000
        })
        // 将后端传回的数据结构进行转换
        const revertData = []
        data.forEach(item => {
          const obj = {
            label: item.typeName,
            value: item.id,
            children: []
          }
          revertData.push(obj)
          if (Array.isArray(item.child) && item.child.length) {
            item.child.forEach(child => {
              obj.children.push({
                label: child.productName,
                value: child.id // 产品类型id
              })
            })
          }
        })
        if (data) {
          resolve({
            data: revertData
          })
        } else {
          reject({
            data: []
          })
        }
      })
    },
    label: '服务产品',
    search: {
      el: 'tree-select',
      order: 2
    },
    sortable: 'custom',
    sortName: 'bp.product_name'
  },
  // 关联人员
  {
    prop: 'personnel',
    width: 200,
    isShow: false,
    label: '关联人员'
    // search: {
    //   el: 'input',
    //   order: 3
    // }
  },
  {
    prop: 'contractType',
    label: '合同类型',
    width: 200,
    enum: [
      {
        label: '记账合同',
        value: '0'
      },
      {
        label: '一次性合同',
        value: '1'
      },
      {
        label: '地址服务协议合同',
        value: '2'
      }
    ],
    search: { el: 'select', order: 6 },
    sortable: 'custom',
    sortName: 'cc.contract_type'
  },
  {
    prop: 'totalCost',
    width: 100,
    label: '合同金额',
    search: {
      render: ({ searchParam }) => {
        return <InputRange vModel={searchParam.paymentAmount} />
      }
    },
    sortable: 'custom',
    sortName: 'cc.total_cost'
  },
  {
    prop: 'startTime',
    label: '起始时间',
    width: 150,
    search: {
      el: 'date-picker',
      props: { type: 'daterange', valueFormat: 'YYYY-MM-DD' }
    },
    sortable: 'custom',
    sortName: 'cc.start_time'
  },
  {
    prop: 'endTime',
    width: 150,
    label: '结束时间',
    search: {
      el: 'date-picker',
      props: { type: 'daterange', valueFormat: 'YYYY-MM-DD' }
    },
    sortable: 'custom',
    sortName: 'cc.end_time'
  },
  {
    prop: 'mangerUserId',
    width: 100,
    label: '财税顾问',
    enum: getTreeData,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    },
    render: (scope: any) => {
      return <span>{scope.row.manager || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'cc.manger_user_id'
  },
  {
    prop: 'customerSuccessUserId',
    width: 100,
    label: '客户成功',
    enum: getTreeData,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    },
    render: (scope: any) => {
      return <span>{scope.row.customerSuccess || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'cc.customer_success_user_id'
  },
  {
    prop: 'sponsorAccountingUserId',
    width: 100,
    label: '主办会计',
    enum: getTreeData,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    },
    render: (scope: any) => {
      return <span>{scope.row.sponsorAccounting || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'cc.sponsor_accounting_user_id'
  },
  {
    prop: 'counselorUserId',
    width: 100,
    label: '开票员',
    enum: getTreeData,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    },
    render: (scope: any) => {
      return <span>{scope.row.counselor || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'cc.counselor_user_id'
  },
  {
    prop: 'createTime',
    // fixed: 'right',
    width: 200,
    label: '创建时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'cc.create_time'
  }
])

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// 如果该用户的角色是归档员且具有查看归档的属性
// 自定义
const transformRequestParams = (data: any) => {
  if (data.totalCost) {
    data.totalCostMin = data.totalCost[0]
    data.totalCostMax = data.totalCost[1]
  }
  if (data.createTime) {
    data.createTimeStart = data.createTime[0]
    data.createTimeEnd = data.createTime[1]
  }
  if (data.startTime) {
    data.startTimeStart = data.startTime[0]
    data.startTimeEnd = data.startTime[1]
  }
  if (data.endTime) {
    data.endTimeStart = data.endTime[0]
    data.endTimeEnd = data.endTime[1]
  }
}

if (
  userStore.roles?.includes('admin') ||
  (userStore.roles?.includes('document') && userStore.permissions?.includes('contract:list:archiveCheck'))
) {
  columns.value.push({
    prop: 'documentNo',
    fixed: 'right',
    width: 200,
    label: '归档号'
  })
}
// 手动新建还是模板新建的弹窗显示标志
const contractShow = ref(false)
// 下一步
const handelNext = (createType: string) => {
  // '0'代表 普通新建
  // '1'代表 模板新建
  if (createType === '0') {
    normalShow.value = true
  }
  if (createType === '1') {
    contractId.value = undefined
    templateShow.value = true
  }
}

// 手动新建的弹窗显示标志
const normalShow = ref(false)
// getList
const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}
// 变更操作
const changeShow = ref(false)

const isRenewal = ref(false)
const handleChange = (id: string | number, type: string, renewal: Boolean) => {
  // 如果是续签
  if (renewal) {
    isRenewal.value = true
  } else {
    isRenewal.value = false
  }
  if (type && type === '1') {
    contractId.value = id
    templateShow.value = true
  } else {
    contractId.value = id
    changeShow.value = true
  }
}

// 转为正式合同
const translateShow = ref(false)
const handleTranslate = (id: string | number, type: string) => {
  if (type && type === '1') {
    contractId.value = id
    templateShow.value = true
  } else {
    contractId.value = id
    translateShow.value = true
  }
}
// 终止合同
const handleTerminate = () => {
  // 跳转到 终止合同 的tab下
  handleRadioChange('3')
}

// 批量删除合同

const handleDelete = async (id: string[]) => {
  if (!id.length) return
  await useHandleData(deleteBatchContract, id, '删除所选合同信息')
  proTable.value?.clearSelection()
  proTable.value?.getTableList()
}

const deleteColumn = (prop: string) => {
  const index = columns.value.findIndex((column: any) => column.prop === prop)
  if (index > 0) {
    columns.value.splice(index, 1)
  }
}

const addColumn = (option: any) => {
  if (option?.prop) {
    const index = columns.value.findIndex((column: any) => column.prop === option.prop)
    if (index < 0) {
      columns.value.push(option)
    }
  }
}

const handleRadioChange = (value: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.contractStatus = value
  // 只有在执行中才会有归档号
  if (value === '4') {
    // 增加归档号
    if (
      userStore.roles?.includes('admin') ||
      (userStore.roles?.includes('document') && userStore.permissions?.includes('contract:list:archiveCheck'))
    ) {
      addColumn({
        prop: 'documentNo',
        fixed: 'right',
        isColShow: true,
        isShow: true,
        width: 200,
        label: '归档号'
      })
    }
  } else {
    // 删除归号
    deleteColumn('documentNo')
  }

  if (value === '3') {
    columns.value.push({ prop: 'terminationTime', isColShow: true, isShow: true, width: 200, label: '终止时间' })
    deleteColumn('contractNo')

    columns.value.push({ type: 'selection', fixed: 'left', width: 80 })
    addColumn({
      prop: 'contractNo',
      label: '合同编号',
      isColShow: true,
      isShow: true,
      fixed: 'left',
      width: 150,
      search: { el: 'input' }
    })
  } else {
    const index = columns.value.findIndex((item: any) => item.type === 'selection')
    const timeIndex = columns.value.findIndex((item: any) => item.prop === 'terminationTime')
    if (index > 0) {
      columns.value.splice(index, 1)
    }
    if (timeIndex > 0) {
      columns.value.splice(timeIndex, 1)
    }
    // 只有在意向合同tab下展示操作列--转为意向合同
    if (value === '6') {
      columns.value.push({ prop: 'operation', isColShow: true, isShow: true, fixed: 'right', label: '操作', width: 120 })
    } else {
      // 除了意向合同外 不需要操作行
      // 删除归号
      deleteColumn('operation')
    }
  }
}
// 查看合同详情
const detailShow = ref(false)
const isChange = ref(false) // 默认不是变更合同
const contractId = ref()

//给子孙组件传递数据
const rowData = ref({})
provide('customerData', rowData)
const handleCheck = async (row: any) => {
  rowData.value = {
    ...row
  }
  detailShow.value = true
  contractId.value = row.contractId
  isChange.value = row.bizType === '1' // 为1 为变更合同
  // await getContractDetailById(row.contractId)
}

/* 模板合同逻辑 ---start--- */
const templateShow = ref(false)
/* 模板合同逻辑 ---end--- */
const customerDetailShow = ref(false)
const rowId = ref()
const isEdit = ref(false)
provide('isEdit', isEdit)
const handleShowCustomer = (id: number) => {
  if (id) {
    rowId.value = id
    customerDetailShow.value = true
  }
}

const handleClose = () => {
  customerDetailShow.value = false
  getList()
}
watch(
  () => useCommon.id,
  async () => {
    if (useCommon.id && useCommon.bizType === 'contract') {
      const { data } = await checkContractDetailIsPermission(useCommon.id)
      if (data) {
        handleCheck(data)
        useCommon.clearId()
        useCommon.clearBizType()
      }
    }
  },
  {
    immediate: true
  }
)
</script>
<style lang="scss" scoped></style>
