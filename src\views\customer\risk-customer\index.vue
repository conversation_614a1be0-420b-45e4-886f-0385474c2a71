<template>
  <ProTable
    v-if="show"
    :init-param="initParam"
    ref="proTable"
    row-key="id"
    :columns="columns"
    :request-api="riskCustomerList"
    :dataCallback="dataCallback"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button :icon="Download" @click="handleExport" v-hasPermi="['customer:risk-customer:export']">导出</el-button>
    </template>
    <template #tabs>
      <el-radio-group :model-value="initParam.stageTier" @change="handleRadioChange">
        <el-radio-button label="风险审核">风险审核</el-radio-button>
        <el-radio-button label="流失清理">流失清理</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
    </template>
  </ProTable>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    :hideActionBtnForRiskCustomer="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { riskCustomerList, riskCustomerListExport } from '@/api/customer/risk.js'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from '@/views/customer/risk-audit/components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import useUserStore from '@/store/modules/user'
import { useDic } from '@/hooks/useDic'
import useCommonStore from '@/store/modules/common'
import { reasonDict, stageDict } from '@/utils/constants.js'
import { CirclePlus, Upload, Download } from '@element-plus/icons-vue'
dayjs.extend(duration)

const userStore = useUserStore()
const useCommon = useCommonStore()
const { getDic } = useDic()
const { proxy } = getCurrentInstance()

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}
const initParam = reactive({
  stageTier: '风险审核'
})

// 表格配置项
const columns: ColumnProps<any>[] = ref([
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'customerName',
    label: '客户名称',
    search: { el: 'input' },
    minWidth: 300,
    sortable: 'custom',
    sortName: 'risk.customer_id'
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'recentContractExpirationDate',
    label: '最近合同到期日期',
    width: 150
  },
  {
    prop: 'reason',
    label: '风险原因',
    width: 180,
    enum: reasonDict,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true }
    },
    render: scope => {
      return <span>{scope.row.reason || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'risk.reason'
  },
  {
    prop: 'remark',
    label: '说明',
    minWidth: 200
  },
  {
    prop: 'createBy',
    label: '创建人',
    search: { el: 'input' },
    width: 150,
    sortable: 'custom',
    sortName: 'risk.create_by'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180,
    search: {
      el: 'date-picker',
      props: { type: 'daterange', valueFormat: 'YYYY-MM-DD' }
    },
    sortable: 'custom',
    sortName: 'risk.create_time'
  },
  {
    prop: 'stage',
    label: '当前阶段',
    width: 200,
    enum: stageDict,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true }
    },
    render: scope => {
      return <span>{scope.row.stage || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'risk.stage'
  },
  {
    prop: 'handleUserName',
    label: '当前办理人',
    width: 150
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 100
  }
])

//
const show = ref(false)
const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.stageTier = e
  if (e === '风险审核') {
    const index = columns.value.findIndex(item => item.label === '流失时间')
    console.log('index', index)
    if (index > 0) {
      columns.value.splice(index, 1, {
        prop: 'createTime',
        label: '创建时间',
        width: 180,
        isColShow: false,
        isShow: true,
        // search: {
        //   el: 'date-picker',
        //   props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
        // },
        search: {
          el: 'date-picker',
          props: { type: 'daterange', valueFormat: 'YYYY-MM-DD' }
        },
        sortable: 'custom',
        sortName: 'risk.create_time'
      })
      columns.value.splice(-7, 6)
    }
  } else if (e === '流失清理') {
    const index = columns.value.findIndex(item => item.label === '创建时间')
    console.log('index', index)
    if (index > 0) {
      columns.value.splice(index, 1, {
        prop: 'loseTime',
        label: '流失时间',
        width: 180,
        isColShow: true,
        isShow: true,
        search: {
          el: 'date-picker',
          props: { type: 'daterange', valueFormat: 'YYYY-MM-DD' }
        },
        sortable: 'custom',
        sortName: 'lose_node.handle_time'
      })
      columns.value.splice(-1, 0, ...columnsStage)
    }
  }
  show.value = false
  nextTick(() => {
    show.value = true
  })
}

// 前端：【显示】风险客户：风险审核阶段 显示各步骤名称及步骤用时
const dataCallback = (data: any) => {
  if (data) {
    // console.log('dataCallback-data', data)
    data.records.forEach(item => {
      // console.log('item', item, item.processRecordVO)
      for (let itemx in item.processRecordVO) {
        const _time = parseInt(item.processRecordVO[itemx])
        // console.log('item.processRecordVO[itemx]', item.processRecordVO[itemx])
        // console.log('_time', _time)
        if (_time) {
          const day = dayjs.duration(_time, 'seconds').asDays()
          const hour = dayjs.duration(_time, 'seconds').hours()
          item.processRecordVO[itemx] = `${Math.trunc(day)}天${hour}小时`
        }
      }
      Object.assign(item, item.processRecordVO)
    })
    return data
  }
}

// 前端：【显示】风险客户：风险审核阶段 显示各步骤名称及步骤用时
const stageNum = [
  '',
  '一',
  '二',
  '三',
  '四',
  '五',
  '六',
  '七',
  '八',
  '九',
  '十',
  '十一',
  '十二',
  '十三',
  '十四',
  '十五',
  '十六',
  '十七',
  '十八',
  '十九',
  '二十'
]
const columnsStage = [
  {
    prop: 'stage8',
    label: `步骤八`,
    width: 150
  },
  {
    prop: 'stage8Time',
    label: `步骤八用时`,
    width: 150
  },
  {
    prop: 'stage9',
    label: `步骤九`,
    width: 150
  },
  {
    prop: 'stage9Time',
    label: `步骤九用时`,
    width: 150
  },
  {
    prop: 'stage10',
    label: `步骤十`,
    width: 150
  },
  {
    prop: 'stage10Time',
    label: `步骤十用时`,
    width: 150
  }
]
riskCustomerList().then((res: any) => {
  show.value = true
  if (res.code === 200 && res.data?.records?.[0]?.processRecordVO) {
    let stageVO = {}
    let processRecordVO = res.data.records[0].processRecordVO
    let keys = Object.keys(processRecordVO).slice(0, 14)
    keys.forEach(key => {
      stageVO[key] = processRecordVO[key]
    })
    // console.log('stageVO', stageVO)
    if (stageVO) {
      for (const item in stageVO) {
        // console.log('item', item)
        columns.value.splice(-1, 0, {
          prop: item,
          label: `步骤${stageNum[item.replace('Time', '').replace('stage', '')]}${item.includes('Time') ? '用时' : ''}`,
          width: 150
        })
      }
      // console.log('columns', columns)
    }
  }
})

// 自定义
// 这里数组直接接收
const transformRequestParams = (data: any) => {
  // 创建时间
  // if (data.createTime) {
  //   data.createTimeStart = data.createTime[0]
  //   data.createTimeEnd = data.createTime[1]
  // }
  // if (data.loseTime) {
  //   data.loseTimeStart = data.loseTime[0]
  //   data.loseTimeEnd = data.loseTime[1]
  // }
}

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handleAdd = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onAdd()
  })
}

const handleDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

// 导出列表功能
const handleExport = async () => {
  const result = await riskCustomerListExport({
    ...proTable.value.searchParam,
    // tab的参数
    stageTier: initParam.stageTier
  })
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}

// onMounted(() => {
// })
</script>
<style lang="scss" scoped></style>
