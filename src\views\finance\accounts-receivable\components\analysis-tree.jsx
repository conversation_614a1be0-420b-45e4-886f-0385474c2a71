import { defineComponent, nextTick, ref, watch } from 'vue'
import { ElTreeSelect } from 'element-plus'
import { useBasicDictAnalyse } from '@/utils/dict'

const { receive_analyse } = useBasicDictAnalyse('receive_analyse')
console.log('receive_analyse', receive_analyse.value)

export default defineComponent({
  props: {
    modelValue: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }) {
    const txtArr = ref([])
    watch(
      () => props.modelValue,
      val => {
        txtArr.value = val
      },
      { deep: true, immediate: true }
    )

    const handleSelectChange = (node, row) => {
      nextTick(() => {
        console.log('handleSelectChange', node, txtArr.value)
        emit('update:modelValue', txtArr.value)
      })
    }

    return {
      txtArr,
      handleSelectChange,
      receive_analyse
    }
  },
  render() {
    return (
      <ElTreeSelect
        filterable
        vModel={this.txtArr}
        data={this.receive_analyse}
        onCurrentChange={this.handleSelectChange}
        onRemoveTag={this.handleSelectChange}
        props={{ value: 'totalName', label: 'name', children: 'children' }}
        renderAfterExpand={false}
        defaultExpandAll
      />
    )
  }
})
