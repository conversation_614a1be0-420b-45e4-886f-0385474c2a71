<template>
  <div class="upload-file">
    <el-upload
      multiple
      ref="fileUpload"
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
    >
      <!-- 上传按钮 -->
      <el-button type="primary">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->

    <div v-if="showTip" class="el-upload__tip">
      <template v-if="tipMessage"> {{ tipMessage }}</template>
      <template v-else>
        请上传
        <template v-if="fileSize">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
        </template>
        的文件</template
      >
    </div>
    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li v-for="(file, index) in fileList" :key="file.uid" class="el-upload-list__item ele-upload-list__item-content">
        <!-- <el-link :href="`${baseUrl}${file.url}`" :underline="false" target="_blank" class="flex-1">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link> -->
        <span class="el-icon-document" @click="previewFile(file.url || file.urls, file)">
          {{ file.name || file.fileNames }}
        </span>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" type="danger" @click="handleDelete(index)">删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>

<script setup>
import { getToken } from '@/utils/auth'
import { cloneDeep } from 'lodash'
import iFrame from '@/components/iFrame'
const props = defineProps({
  modelValue: [String, Object, Array],
  // 数量限制
  limit: {
    type: Number,
    default: 1
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 20
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ['doc', 'xls', 'txt', 'pdf', 'docx', 'xlsx', 'jpg', 'png', 'jpeg']
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  tipMessage: {
    type: String,
    default: ''
  }
})

const { proxy } = getCurrentInstance()
const emit = defineEmits()
const number = ref(0)
const uploadList = ref([])
const baseUrl = import.meta.env.VITE_APP_BASE_API
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + '/common/upload2Oss') // 上传文件服务器地址 // 走oss
const headers = ref({ Authorization: 'Bearer ' + getToken() })
const fileList = ref([])
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize))

watch(
  () => props.modelValue,
  val => {
    if (val) {
      let temp = 1
      // 首先将值转为数组

      if (Array.isArray(val) || typeof val === 'string') {
        const list = Array.isArray(val) ? cloneDeep(val) : props.modelValue.split(',')
        // 然后将数组转为对象数组
        fileList.value = list.map(item => {
          if (typeof item === 'string') {
            item = { name: item, url: item }
          }
          item.uid = item.uid || new Date().getTime() + temp++
          return item
        })
      }
      // 如果是对象，则需要将其转化成list
      if (!Array.isArray(val) && val instanceof Object) {
        const array = cloneDeep([val])
        fileList.value = array.map(item => {
          if (typeof item === 'string') {
            item = { name: item, url: item }
          }
          item.uid = item.uid || new Date().getTime() + temp++

          return {
            name: val.urls,
            url: val.urls
          }
        })
      }
    } else {
      fileList.value = []
      return []
    }
  },
  { deep: true, immediate: true }
)

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.')
    const fileExt = fileName[fileName.length - 1]
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0
    if (!isTypeOk) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join('/')}格式文件!`)
      return false
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`)
      return false
    }
  }
  proxy.$modal.loading('正在上传文件，请稍候...')
  number.value++
  return true
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError('上传文件失败')
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    uploadList.value.push({
      ...res,
      name: res.fileName,
      fileNames: res.fileName,
      urls: res.url,
      url: res.url,
      fileSize: res.uploadSize
    })
    uploadedSuccessfully()
  } else {
    number.value--
    proxy.$modal.closeLoading()
    proxy.$modal.msgError(res.msg)
    proxy.$refs.fileUpload.handleRemove(file)
    uploadedSuccessfully()
  }
}

// 删除文件
function handleDelete(index) {
  fileList.value.splice(index, 1)
  // emit('update:modelValue', listToString(fileList.value))
  emit('update:modelValue', fileList.value)
}

// 上传结束处理
function uploadedSuccessfully() {
  // console.log('fileList.value', fileList.value)
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined || f.urls !== undefined).concat(uploadList.value)
    uploadList.value = []
    number.value = 0
    // console.log('fileList.value', fileList.value)
    // emit('update:modelValue', listToString(fileList.value))
    emit('update:modelValue', fileList.value)
    emit('on-load-success', fileList.value)
    proxy.$modal.closeLoading()
  }
}

// 获取文件名称
// function getFileName(name) {
//   if (name.lastIndexOf('/') > -1) {
//     return name.slice(name.lastIndexOf('/') + 1)
//   } else {
//     return ''
//   }
// }

// 对象转成指定字符串分隔
// function listToString(list, separator) {
//   let strs = ''
//   separator = separator || ','
//   for (const i in list) {
//     if (list[i].url) {
//       strs += list[i].url + separator
//     }
//   }
//   return strs != '' ? strs.substr(0, strs.length - 1) : ''
// }
const previewShow = ref(false)
const previewUrl = ref('')

// 音频的格式mp3、m4a、wav
const mpList = ['mp3', 'wav', 'm4a']
const previewFile = (url, file) => {
  if (file && (file.name || file.fileNames)) {
    const name = file.name || file.fileNames
    // 判断 文件后缀 如果是音频 不需要预览
    const fileName = name.split('.')
    const fileExt = fileName[fileName.length - 1]
    if (mpList.includes(fileExt)) return
  }

  previewShow.value = true
  previewUrl.value = url
}
</script>

<style scoped lang="scss">
.upload-file {
  width: 100%;
}
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  position: relative;
  margin-bottom: 10px;
  line-height: 2;
  border: 1px solid #e4e7ed;
  padding: 0 10px;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: inherit;
  word-break: break-all;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
.ele-upload-list__item-content-action {
  margin-left: 10px;
  flex: 0 0 40px;
}
.flex-1 {
  flex: 1;
}
</style>
