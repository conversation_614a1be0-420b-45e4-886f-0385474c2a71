<!--
 * @Description: 借阅申请
 * @Author: thb
 * @Date: 2023-07-12 10:04:46
 * @LastEditTime: 2023-09-01 13:31:49
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center title="借阅申请" width="800" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="type === 'detail'">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="合同编号" prop="contractNo">
            <el-input v-model="formData.contractNo" maxlength="20" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="关联客户" prop="customerName">
            <el-tooltip effect="light" :content="formData.customerName" placement="top-start">
              <el-input v-model="formData.customerName" maxlength="20" disabled
            /></el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户编码" prop="customerNo">
            <el-input v-model="formData.customerNo" maxlength="20" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="借阅人" prop="userId">
            <SelectTree v-model="formData.userId" placeholder="请选择" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="借阅到期" prop="expirationTime">
            <el-date-picker
              v-model="formData.expirationTime"
              type="date"
              :disabled-date="disabledDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="type === 'detail'"
              :placeholder="type === 'detail' ? ' ' : '请选择'"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="借阅事由" prop="borrowReason">
            <el-input
              v-model="formData.borrowReason"
              maxlength="1000"
              :disabled="type === 'detail'"
              type="textarea"
              :placeholder="type === 'detail' ? ' ' : '请输入'"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 驳回原因 -->
      <el-row :gutter="24" v-if="formData.reason">
        <el-col :span="24">
          <el-form-item label="驳回原因">
            <el-input
              v-model="formData.reason"
              maxlength="1000"
              :disabled="type === 'detail'"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <Collapse title="审批记录" v-if="formData.reviewList && formData.reviewList.length">
      <!-- 审批流程 -->
      <processSet
        v-if="formData.reviewList && formData.reviewList.length"
        type="detail"
        :isReview="true"
        :isHorizontal="true"
        :createTime="formData.createTime"
        v-model="formData.reviewList"
      />
    </Collapse>

    <template #footer>
      <template v-if="type === 'add'">
        <el-button type="primary" @click="handleSubmit(formRef)">提交</el-button>
      </template>

      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import SelectTree from '@/components/SelectTree'
import { getCurrentInstance, inject } from 'vue'
import Collapse from '@/components/Collapse'
import useUserStore from '@/store/modules/user'
import { reportContractBorrow } from '@/api/contract/contract'
import processSet from '@/views/process-manage/contract-review/components/process-set.vue'
const userStore = useUserStore()
const customerData = inject('customerData')

const { customerName, customerNo, contractId, contractNo, borrowReason, expirationTime, reviewList, createTime, userId } =
  customerData.value
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  type: {
    type: String,
    default: 'add' // 默认为add 添加  'detail'为详情
  }
})
const formData = ref({
  customerName,
  customerNo,
  contractId,
  borrowReason,
  expirationTime,
  contractNo,
  reviewList,
  createTime,
  userId: userId || userStore.user.userId
})

// 查找驳回原因
if (reviewList) {
  formData.value.reason = reviewList.filter(item => item.reviewStatus === '2')[0]?.reason || ''
}

const rules = {
  expirationTime: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  borrowReason: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ]
}

const formRef = ref()
const { proxy } = getCurrentInstance()
const handleSubmit = formEl => {
  if (!formEl) return
  formEl.validate(async valid => {
    if (valid) {
      const result = await reportContractBorrow({
        ...formData.value,
        mainId: formData.value.contractId
      })
      if (result.code === 200) {
        proxy.$modal.msgSuccess(`提交成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`提交失败!`)
      }
    } else {
      return false
    }
  })
}
// 日期选择器禁用
const disabledDate = time => {
  return time.getTime() < Date.now() // 禁用今天以前的时间
}
</script>
<style lang="scss" scoped>
:deep(.el-collapse-item__content) {
  line-height: normal;
}
.el-select {
  width: 100%;
}
:deep(.el-date-editor.el-date-editor--date) {
  width: 100%;
}

.collapse {
  margin-bottom: 20px;
}

:deep(.el-collapse-item__wrap) {
  background: #f3f4f7;
  .el-collapse-item__content {
    height: 262px;
    display: flex;
    padding: 0 24px;
    align-items: center;
  }
}
</style>
