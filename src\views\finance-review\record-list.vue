<template>
  <ProTable
    :init-param="initParam"
    v-if="initParam.searchStatus === ''"
    :requestAuto="true"
    ref="proTable"
    title="审账记录"
    row-key="id"
    :columns="columnsExtra"
    :request-api="financeReviewAuditList"
    :transformRequestParams="transformRequestParams"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.searchStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #auditStatus="{ row }">
      <span v-if="row.auditStatus === 'pending'" style="color: #409eff">待审批</span>
      <span v-else-if="row.auditStatus === 'pass'" style="color: #67c23a">通过</span>
      <span v-else-if="row.auditStatus === 'not_pass'" style="color: #f56c6c">不通过</span>
      <span v-else>--</span>
    </template>
    <template #rectificationTime="{ row }">
      <span
        v-if="
          row.expectCompleteDate &&
          dayjs().add(-1, 'day').valueOf() >= dayjs(row.expectCompleteDate).valueOf() &&
          row.rectificationStatus === 'pending'
        "
        style="color: #f56c6c"
        >已超时</span
      >
      <span
        v-else-if="
          row.expectCompleteDate &&
          row.rectificationTime &&
          dayjs(row.rectificationTime).valueOf() > dayjs(row.expectCompleteDate).valueOf()
        "
        style="color: #f56c6c"
        >{{ row.rectificationTime }}</span
      >
      <span v-else>{{ row.rectificationTime }}</span>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button :icon="Upload" @click="handleExport">导出</el-button>
    </template>

    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button
        v-if="scope.row.rectificationStatus !== 'pending' && scope.row.auditStatus === 'pending'"
        type="primary"
        link
        @click="handlReview(scope.row)"
        >审批</el-button
      >
      <el-button
        v-if="scope.row.rectificationStatus === 'pending' || scope.row.auditStatus !== 'pending'"
        type="primary"
        link
        @click="handlDetail(scope.row)"
        >详情</el-button
      >
      <el-button v-if="scope.row.rectificationStatus === 'pending'" type="primary" link @click="handleEdit(scope.row)"
        >编辑</el-button
      >
      <el-button
        v-if="scope.row.rectificationStatus === 'pending'"
        type="danger"
        link
        @click="handleDelete(scope.row, scope.$index)"
        >删除</el-button
      >
    </template>
  </ProTable>
  <ProTable
    :init-param="initParam"
    v-if="initParam.searchStatus === 'rectification_pending'"
    :requestAuto="true"
    ref="proTable"
    title="审账记录"
    row-key="id"
    :columns="columns"
    :request-api="financeReviewAuditList"
    :transformRequestParams="transformRequestParams"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.searchStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #auditStatus="{ row }">
      <span v-if="row.auditStatus === 'pending'" style="color: #409eff">待审批</span>
      <span v-else-if="row.auditStatus === 'pass'" style="color: #67c23a">通过</span>
      <span v-else-if="row.auditStatus === 'not_pass'" style="color: #f56c6c">不通过</span>
      <span v-else>--</span>
    </template>
    <template #rectificationTime="{ row }">
      <span
        v-if="
          row.expectCompleteDate &&
          dayjs().add(-1, 'day').valueOf() >= dayjs(row.expectCompleteDate).valueOf() &&
          row.rectificationStatus === 'pending'
        "
        style="color: #f56c6c"
        >已超时</span
      >
      <span
        v-else-if="
          row.expectCompleteDate &&
          row.rectificationTime &&
          dayjs(row.rectificationTime).valueOf() > dayjs(row.expectCompleteDate).valueOf()
        "
        style="color: #f56c6c"
        >{{ row.rectificationTime }}</span
      >
      <span v-else>{{ row.rectificationTime }}</span>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button :icon="Upload" @click="handleExport">导出</el-button>
    </template>

    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button
        v-if="scope.row.rectificationStatus !== 'pending' && scope.row.auditStatus === 'pending'"
        type="primary"
        link
        @click="handlReview(scope.row)"
        >审批</el-button
      >
      <el-button
        v-if="scope.row.rectificationStatus === 'pending' || scope.row.auditStatus !== 'pending'"
        type="primary"
        link
        @click="handlDetail(scope.row)"
        >详情</el-button
      >
      <el-button v-if="scope.row.rectificationStatus === 'pending'" type="primary" link @click="handleEdit(scope.row)"
        >编辑</el-button
      >
      <el-button
        v-if="scope.row.rectificationStatus === 'pending'"
        type="danger"
        link
        @click="handleDelete(scope.row, scope.$index)"
        >删除</el-button
      >
    </template>
  </ProTable>
  <ProTable
    :init-param="initParam"
    v-if="initParam.searchStatus === 'audit_pending'"
    :requestAuto="true"
    ref="proTable"
    title="审账记录"
    row-key="id"
    :columns="columnsExtra"
    :request-api="financeReviewAuditList"
    :transformRequestParams="transformRequestParams"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.searchStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #auditStatus="{ row }">
      <span v-if="row.auditStatus === 'pending'" style="color: #409eff">待审批</span>
      <span v-else-if="row.auditStatus === 'pass'" style="color: #67c23a">通过</span>
      <span v-else-if="row.auditStatus === 'not_pass'" style="color: #f56c6c">不通过</span>
      <span v-else>--</span>
    </template>
    <template #rectificationTime="{ row }">
      <span
        v-if="
          row.expectCompleteDate &&
          dayjs().add(-1, 'day').valueOf() >= dayjs(row.expectCompleteDate).valueOf() &&
          row.rectificationStatus === 'pending'
        "
        style="color: #f56c6c"
        >已超时</span
      >
      <span
        v-else-if="
          row.expectCompleteDate &&
          row.rectificationTime &&
          dayjs(row.rectificationTime).valueOf() > dayjs(row.expectCompleteDate).valueOf()
        "
        style="color: #f56c6c"
        >{{ row.rectificationTime }}</span
      >
      <span v-else>{{ row.rectificationTime }}</span>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button :icon="Upload" @click="handleExport">导出</el-button>
    </template>

    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button
        v-if="scope.row.rectificationStatus !== 'pending' && scope.row.auditStatus === 'pending'"
        type="primary"
        link
        @click="handlReview(scope.row)"
        >审批</el-button
      >
      <el-button
        v-if="scope.row.rectificationStatus === 'pending' || scope.row.auditStatus !== 'pending'"
        type="primary"
        link
        @click="handlDetail(scope.row)"
        >详情</el-button
      >
      <el-button v-if="scope.row.rectificationStatus === 'pending'" type="primary" link @click="handleEdit(scope.row)"
        >编辑</el-button
      >
      <el-button
        v-if="scope.row.rectificationStatus === 'pending'"
        type="danger"
        link
        @click="handleDelete(scope.row, scope.$index)"
        >删除</el-button
      >
    </template>
  </ProTable>
  <ImportExcel ref="dialogRef" />
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import useDictStore from '@/store/modules/dict'
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { financeReviewAuditList, financeReviewDelete } from '@/api/finance-review/finance-review.js'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus, Upload } from '@element-plus/icons-vue'
import formModal from './components/form-modal.vue'
import { useHandleData } from '@/hooks/useHandleData'
import ImportExcel from '@/components/ImportExcel/index.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import dayjs from 'dayjs'
import { useDic } from '@/hooks/useDic'
import { useSetDic } from '@/hooks/useSetDic'
const { getDic } = useDic()
const { setDic } = useSetDic()

const { proxy } = getCurrentInstance()
const { finance_review_category } = proxy.useBasicDict('finance_review_category')

// 整改状态
// pending 待整改
// rectified 已整改
// not_rectified 不予整改
const rectificationStatusDict = [
  {
    label: '待整改',
    value: 'pending'
  },
  {
    label: '已整改',
    value: 'rectified'
  },
  {
    label: '不予整改',
    value: 'not_rectified'
  }
]
// 审核状态
// pending 待整改
// pass 通过
// not_pass 不通过
const auditStatusDict = [
  {
    label: '待审批',
    value: 'pending'
  },
  {
    label: '通过',
    value: 'pass'
  },
  {
    label: '不通过',
    value: 'not_pass'
  }
]

const proTable = ref()
// ProTable在1.2.0后可以动态改变columns，现在是固定的
// 通过v-if切换让ProTable重新渲染
// 通过setDic设置字典最新数据，并改变传入的参数 finance_review_category
const getList = async (newDic: any) => {
  const temp = initParam.searchStatus
  initParam.searchStatus = '-1'
  nextTick(() => {
    // 2024.4.15
    // 前端：【显示】审账记录：全部、待审批tab 中新增 完成时间 字段、查询
    // 从界面结果看，下方逻辑好像没有产生效果，但是也跟着修改一下吧，作冗余
    columns.value = columnsFun(newDic)
    columnsExtra.value = Object.assign([], columns.value)
    columnsExtra.value.splice(10, 0, {
      prop: 'auditStatus',
      enum: auditStatusDict,
      label: '审批状态',
      width: 150
    })
    columnsExtra.value.splice(13, 0, {
      prop: 'completeTime',
      label: '完成时间',
      search: {
        el: 'date-picker',
        props: { type: 'daterange', valueFormat: 'YYYY-MM-DD' }
      },
      width: 150
    })
    initParam.searchStatus = temp
    // proTable.value?.getTableList()
  })
}
const initParam = reactive({
  searchStatus: 'rectification_pending'
})
const tabs = ref([
  {
    dictLabel: '全部',
    dicValue: ''
  },
  {
    dictLabel: '待整改',
    dicValue: 'rectification_pending'
  },
  {
    dictLabel: '待审批',
    dicValue: 'audit_pending'
  }
])
const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.searchStatus = e
}
function columnsFun(newDic: any) {
  return [
    { type: 'index', fixed: 'left', width: 50 }, // id
    {
      prop: 'customerName',
      label: '关联企业',
      search: { el: 'input' },
      fixed: 'left',
      width: 300
    },
    {
      prop: 'category',
      label: '分类',
      enum: newDic || getDic('finance_review_category'),
      search: { el: 'select' },
      render: scope => {
        return <span>{scope.row.category || '--'}</span>
      },
      width: 150
    },
    {
      prop: 'issue',
      label: '问题',
      width: 200
    },
    {
      prop: 'reason',
      label: '原因',
      width: 200
    },
    {
      prop: 'voucherCode',
      label: '凭证号',
      width: 150
    },
    {
      prop: 'sponsorAccountingUserName',
      label: '做账会计',
      search: { el: 'input' },
      width: 150
    },
    {
      prop: 'createBy',
      width: 150,
      search: { el: 'input' },
      label: '审账人'
    },
    {
      prop: 'voucherPeriod',
      label: '凭证所属期',
      search: {
        el: 'date-picker',
        props: { type: 'month', valueFormat: 'YYYY-MM' }
      },
      width: 150
    },
    {
      prop: 'reviewDate',
      label: '查账日期',
      search: {
        el: 'date-picker',
        props: { type: 'date', valueFormat: 'YYYY-MM-DD' }
      },
      width: 150
    },
    {
      prop: 'rectificationStatus',
      enum: rectificationStatusDict,
      search: { el: 'select' },
      label: '整改状态',
      width: 150
    },
    {
      prop: 'rectificationTime',
      label: '改账日期',
      width: 150
    },
    {
      prop: 'operation',
      label: '操作',
      fixed: 'right',
      width: 200
    }
  ]
}
// 表格配置项
const columns = ref(columnsFun())
const columnsExtra = ref(Object.assign([], columns.value))
columnsExtra.value.splice(10, 0, {
  prop: 'auditStatus',
  enum: auditStatusDict,
  label: '审批状态',
  width: 150
})
columnsExtra.value.splice(13, 0, {
  prop: 'completeTime',
  label: '完成时间',
  search: {
    el: 'date-picker',
    props: { type: 'daterange', valueFormat: 'YYYY-MM-DD' }
  },
  width: 150
})
const transformRequestParams = (data: any) => {
  // 完成时间
  if (data.completeTime) {
    data.rectificationCompleteStartDate = data.completeTime[0]
    data.rectificationCompleteEndDate = data.completeTime[1]
  }
}

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handleAdd = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onAdd()
  })
}

const handlReview = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onReview(row)
  })
}

const handlDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}

const handleEdit = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onEdit(row)
  })
}

const handleDelete = async (row: any, index: number) => {
  await useHandleData(financeReviewDelete, row.id, `确认删除${row.customerName}的审账记录`)
  proTable.value?.getTableList()
}

const dialogRef = ref<InstanceType<typeof ImportExcel> | null>(null)
const handleExport = async () => {
  proxy.download(
    '/financePaymentReviewRecord/export',
    Object.assign({}, proTable.value.searchParam, initParam),
    '审账记录导出.xlsx'
  )
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

onMounted(() => {
  // initParam.auditStatus = ''
  // initParam.rectificationStatus = 'pending'
  // proTable.value?.search()
})
</script>
<style lang="scss" scoped></style>
