<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    top="5vh"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      :hide-required-asterisk="['交接详情'].includes(mode)"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="关联企业" prop="customerName">
            <!-- 点击弹窗出现客户列表  -->
            <div @click="handleListSelectShow" style="width: 100%">
              <el-input
                :disabled="['交接详情'].includes(mode)"
                v-model="formData.customerName"
                readonly
                maxlength="20"
                :placeholder="['交接详情'].includes(mode) ? '' : '请输入'"
                style="width: 100%"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业编号" prop="customerNo">
            <el-input disabled v-model="formData.customerNo" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="接收人" prop="recipientUserId">
            <SelectTree
              :disabled="['交接详情'].includes(mode)"
              style="width: 100%"
              v-model="formData.recipientUserId"
              placeholder="请选择"
              clearable
              filterable
              :filter-node-method="filterNodeMethod"
              @on-node-click="handleSelectRecipient"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="接收部门" prop="recipientDeptName">
            <el-input disabled v-model="formData.recipientDeptName" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="['交接详情'].includes(mode)">
          <el-form-item label="创建时间" prop="operateTime">
            <el-input disabled v-model="formData.operateTime" placeholder="" />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="tit-line">
        <div class="tit">交接明细</div>
        <div v-if="!['交接详情'].includes(mode)" style="float: right">
          <el-button plain type="primary" @click="handleAdd">新增</el-button>
        </div>
      </div>
      <FormTable ref="formTableRef" :formData="formData" :option="!['交接详情'].includes(mode) ? option : option_detail">
        <!-- @blur="handleBlurInput"  -->
        <template #category="{ row }">
          <el-select
            v-model="row.category"
            filterable
            :disabled="!formData.customerId || ['交接详情'].includes(mode)"
            @change="e => handleChange(e, row)"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in materialCategoryArr"
              :key="index"
            /> </el-select
        ></template>
        <template #availableNum="{ row }"
          ><el-input-number v-model="row.availableNum" placeholder="请输入" disabled></el-input-number
        ></template>
        <template #handoverNum="{ row }"
          ><el-input-number
            :min="1"
            v-model="row.handoverNum"
            placeholder="请输入"
            :disabled="['交接详情'].includes(mode)"
          ></el-input-number
        ></template>
        <template #remark="{ row }"
          ><el-input
            style="width: 100%"
            v-model="row.remark"
            :placeholder="['交接详情'].includes(mode) ? '' : '请输入'"
            :disabled="['交接详情'].includes(mode)"
          ></el-input
        ></template>
        <template #action="{ row, $index }">
          <el-button
            :disabled="['交接详情'].includes(mode) || formData.tableData?.length === 1"
            type="danger"
            text
            @click="handleDelete(row, $index)"
            >删除</el-button
          >
        </template>
      </FormTable>
    </el-form>

    <el-form ref="formRef2" :model="formData" :rules="rules2" label-position="top">
      <template v-if="handoverStatus === 'pending' && confirmFlag">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-radio-group v-model="formData.reviewStatus">
              <el-radio label="1">确认接受</el-radio>
              <el-radio label="2">拒绝接受</el-radio>
            </el-radio-group></el-col
          >
        </el-row>
        <el-row :gutter="24" v-if="formData.reviewStatus === '2'">
          <el-col :span="12">
            <el-form-item label="拒绝理由" prop="handoverRemark">
              <el-input v-model="formData.handoverRemark" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <template v-if="handoverStatus === 'refuse' && !confirmFlag">
        <el-row
          :gutter="24"
          :style="{
            'margin-top': '24px'
          }"
        >
          <el-col :span="12">
            <el-form-item label="拒绝理由">
              <el-input v-model="formData.handoverRemark" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" disabled />
            </el-form-item>
          </el-col> </el-row
      ></template>
    </el-form>
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
      <el-button v-if="!['交接详情'].includes(mode)" type="primary" @click="handleSubmit" :loading="loading"> 保存 </el-button>
      <el-button v-if="confirmFlag" type="primary" @click="confirmSubmit"> 提交 </el-button>
    </template>
  </el-dialog>
  <tableModal
    :init-param="{ discard: 0 }"
    v-if="listSelectShow"
    rowKey="customerId"
    title="关联企业"
    :columns="columns"
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
</template>

<script setup lang="jsx">
import { getCustomers } from '@/api/customer/file'
import tableModal from '@/components/tableModal'
import FormTable from '@/components/FormTable'
import { materialCategoryArr } from '@/utils/constants.js'
import {
  materialHandoverRecordSave,
  materialHandoverRecordGetById,
  materialStockRecordGetAvailableStockCount,
  acceptHandoverRecord,
  refuseHandoverRecord
} from '@/api/customer/material.js'
import SelectTree from '@/components/SelectTree'

import { useDic } from '@/hooks/useDic'
const { getDic } = useDic()

const props = defineProps({
  confirmFlag: {
    type: Boolean,
    default: false
  },
  handoverStatus: {
    type: String,
    default: ''
  }
})

const { proxy } = getCurrentInstance()
const emit = defineEmits()

const visible = ref(true)
const disabled = ref(false)
const mode = ref('')

const initItem = {
  category: '',
  availableNum: '',
  handoverNum: '',
  remark: ''
}
const formRef = ref()
const formData = reactive({
  id: undefined,
  reviewStatus: '1', // 默认为通过
  tableData: [Object.assign({}, initItem)],
  rules: {
    category: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    num: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
    handoverNum: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
  }
})
const rules = {
  customerName: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  recipientUserId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
}

const formRef2 = ref()
const rules2 = {
  handoverRemark: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
}
const confirmSubmit = async () => {
  const result = await formRef2.value.validate()
  if (result) {
    // 校验成功后
    // 确认交接通过
    if (formData.reviewStatus === '1') {
      const response = await acceptHandoverRecord({
        id: Number(formData.id)
      })
      if (response.code) {
        // 通过成功
        proxy.$modal.msgSuccess('提交成功')
        handleClose()
        emit('ok')
      } else {
        proxy.$modal.msgError('提交失败')
      }
    } else {
      // 拒绝交接
      const response = await refuseHandoverRecord({
        id: Number(formData.id),
        handoverRemark: formData.handoverRemark
      })
      if (response.code) {
        // 拒绝交接成功
        proxy.$modal.msgSuccess('提交成功')
        handleClose()
        emit('ok')
      } else {
        proxy.$modal.msgError('提交失败')
      }
    }
  }
}

const getDetail = async row => {
  // console.log('getDetail', row)
  await materialHandoverRecordGetById({ id: row.id }).then(res => {
    Object.assign(formData, res.data, { tableData: res.data.detailList })
  })
}
const onAdd = row => {
  mode.value = '新建交接'
}
const onDetail = row => {
  getDetail(row)
  mode.value = '交接详情'
}
const handleClose = () => {
  emit('close')
}
const loading = ref(false)
const handleSubmit = async () => {
  console.log('formData', formData)
  if (loading.value) return
  formData.handoverRecordDetailList = formData.tableData
  await formRef.value.validate()
  if (await formTableRef.value.handleValidate()) {
    loading.value = true
    materialHandoverRecordSave(formData)
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}

/** 入库明细表格 */
const formTableRef = ref(null)
const option = [
  {
    prop: 'category',
    label: '资料类型',
    width: '150px'
  },
  {
    prop: 'availableNum',
    label: '可交接数量',
    width: '180px'
  },
  {
    prop: 'handoverNum',
    label: '本次交接数量',
    width: '180px'
  },
  {
    prop: 'remark',
    label: '备注',
    minWidth: '150px'
  },
  {
    prop: 'action',
    label: '操作',
    width: '100px'
  }
]
const option_detail = [
  {
    prop: 'category',
    label: '资料类型',
    width: '150px'
  },
  {
    prop: 'handoverNum',
    label: '交接数量',
    width: '180px'
  },
  {
    prop: 'remark',
    label: '备注',
    minWidth: '150px'
  }
]
// 新增行
const handleAdd = () => {
  if (formData?.tableData?.length) {
    formData.tableData.push(Object.assign({}, initItem))
  } else {
    formData.tableData = [Object.assign({}, initItem)]
  }
}
// 删除行
const handleDelete = (row, index) => {
  formData.tableData.splice(index, 1)
}

/** 选择资料类型并获取可交接数量 */
const handleChange = (e, row) => {
  console.log('handleChange', e, row)
  materialStockRecordGetAvailableStockCount({ category: e, customerId: formData.customerId }).then(res => {
    row.availableNum = res.data
  })
}

/** 客户列表弹窗显示 */
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  if (['交接详情'].includes(mode.value)) return
  listSelectShow.value = true
}
const handleSelect = data => {
  formData.customerName = data.customerName
  formData.customerNo = data.customerNo
  formData.customerId = data.customerId
  formData.tableData.forEach(item => {
    if (item && item.category) {
      handleChange(item.category, item)
    }
  })
}
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },
  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

/** 接收人 */
const handleSelectRecipient = (node, node1) => {
  // console.log('node1', node1)
  formData.recipientDeptName = node1.parent?.data?.label
}

const filterNodeMethod = (value, data) => data.label.includes(value)

defineExpose({
  onAdd,
  onDetail
})
</script>

<style lang="scss" scoped>
.tit-line {
  color: #333;
  margin-bottom: 5px;
  font-weight: bold;
  &:before,
  &:after {
    content: '';
    display: table;
  }
  &:after {
    clear: both;
  }
  .tit {
    float: left;
    font-size: 16px;
  }
}
:deep(.el-table .cell) {
  padding: 0 12px 0 0;
}
:deep(.el-form-item--default) {
  .el-form-item__content {
    width: 100%;
  }
}
.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
</style>
