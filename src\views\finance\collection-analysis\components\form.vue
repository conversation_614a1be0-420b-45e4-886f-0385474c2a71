<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1350px"
    top="5vh"
    destroy-on-close
    append-to-body
  >
    <!-- customer_status-{{ customer_status }} -->
    <!-- columnsAccountInit-{{ columnsAccountInit[7] }} -->
    <div class="my-table" v-if="step === 1">
      <ProTable
        ref="proTable"
        title="收款分析"
        :isShowSearch="false"
        :init-param="initParam"
        :columns="columnsAccountInit"
        :toolButton="false"
        rowKey="customerId"
        :request-api="financeCustomerAnalyseList"
      >
        <template #customerName="{ row }">
          <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
        </template>
      </ProTable>
    </div>
    <div class="my-table" v-else-if="step === 2">
      <ProTable
        ref="proTable"
        title="收款分析"
        :isShowSearch="false"
        :init-param="initParam"
        :columns="columnsAccount"
        :toolButton="false"
        rowKey="id"
        :request-api="financeCustomerAnalyseGetByAnalyseId"
      >
        <template #customerName="{ row }">
          <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
        </template>
        <template #receiveAnalyse="{ row }">
          <el-button type="primary" text @click="handleAnalyzeCollection(row)">{{ row.receiveAnalyse || '未分析' }}</el-button>
        </template>
      </ProTable>
    </div>
    <!-- step-{{ step }} -->
    <!-- initParam.analyseId-{{ initParam.analyseId }} -->
    <template #footer>
      <el-button v-if="step === 1" type="primary" @click="handleStepAfter"> 下一步 </el-button>
      <el-button @click="handleClose"> 关闭 </el-button>
    </template>
  </el-dialog>
  <customerDetail v-if="customerDetailShow" :id="rowId" :hideActionBtn="true" @on-close="handleCloseCustomerDetail" />
  <div v-show="false"><analysisTree></analysisTree></div>
</template>

<script setup lang="jsx">
import {
  financeCustomerAnalyseList,
  financeCustomerAnalyseGetByAnalyseId,
  financeCustomerAnalyseSave,
  financeCustomerAnalyseUpdate
} from '@/api/finance/collection-analysis'
import analysisForm from '@/views/finance/accounts-receivable/components/analysis-form.vue'
import { useDialog } from '@/hooks/useDialogFinance'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import { receiveStatusArr } from '@/utils/constants'
import { nextTick, watch } from 'vue'
import { customerProperty } from '@/utils/constants'
import analysisTree from '@/views/finance/accounts-receivable/components/analysis-tree'
import { useDict } from '@/utils/dict'
import { useDic } from '@/hooks/useDic'
const { getDic } = useDic()
const { proxy } = getCurrentInstance()

const { customer_status } = proxy.useDict('customer_status')

const emit = defineEmits('ok')

const props = defineProps({})

const visible = ref(false)
// let step = 1 // 诡异的问题，ref的话不会变，let就符合预期了...所以现在用initParam.analyseId辅助判断
const step = ref(1)
let analyseName = ''
const proTable = ref(null)
const initParam = reactive({})
const title = ref('新建分析')
watch(step, () => {
  title.value = step.value === 1 ? '新建分析' : '收款分析'
})

const columnsAccountInit = [
  { type: 'index', fixed: 'left', width: 50 },
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '200',
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '200',
    search: { el: 'input' }
  },
  {
    prop: 'recentContractExpirationDate',
    width: '180',
    label: '最近合同到期日期',
    search: {
      el: 'date-picker',
      props: { type: 'monthrange', valueFormat: 'YYYY-MM-DD' }
    }
  },
  {
    prop: 'paymentAmount',
    label: '账单总金额',
    width: '120',
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'receiptAmount',
    label: '已收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.receiptAmount >= 0 ? `${scope.row.receiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'receivableAmount',
    label: '应待收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.receivableAmount >= 0 ? `${scope.row.receivableAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: [], // 诡异的问题，在别的页面直接此处concat进去即可，但是此处失败了，所以特地手写一个watch来处理
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    width: '150',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    search: { el: 'select' }
  },
  {
    prop: 'mangerName',
    label: '财税顾问',
    width: '100'
  },
  {
    prop: 'customerSuccessName',
    label: '客户成功',
    width: '100'
  },
  {
    prop: 'counselorName',
    label: '开票员',
    width: '100'
  },
  {
    prop: 'sponsorAccountingName',
    label: '主办会计',
    width: '100'
  },
  {
    prop: 'receiveStatus',
    width: '100',
    label: '收款情况',
    isColShow: false,
    isShow: false,
    enum: receiveStatusArr,
    search: { el: 'select' }
  }
]

const columnsAccount = [
  { type: 'index', fixed: 'left', width: 50 },
  // { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '200',
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '200',
    search: { el: 'input' }
  },
  {
    prop: 'paymentAmount',
    label: '账单总金额',
    width: '120',
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'receiptAmount',
    label: '已收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.receiptAmount >= 0 ? `${scope.row.receiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'receivableAmount',
    label: '应待收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.receivableAmount >= 0 ? `${scope.row.receivableAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: [], // 诡异的问题，在别的页面直接此处concat进去即可，但是此处失败了，所以特地手写一个watch来处理
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    width: '150',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    search: { el: 'select' }
  },
  {
    prop: 'mangerName',
    label: '财税顾问',
    width: '100'
  },
  {
    prop: 'customerSuccessName',
    label: '客户成功',
    width: '100'
  },
  {
    prop: 'counselorName',
    label: '开票员',
    width: '100'
  },
  {
    prop: 'sponsorAccountingName',
    label: '主办会计',
    width: '100'
  },
  {
    prop: 'receiveAnalyse',
    label: '收款分析',
    width: 280,
    isColShow: false,
    fixed: 'right',
    search: {
      render: ({ searchParam }) => {
        return <analysisTree vModel={searchParam.receiveAnalyse} />
      }
    }
  }
]

const { showDialog } = useDialog()
watch(
  customer_status,
  () => {
    columnsAccountInit.find(item => item.prop === 'customerStatus').enum = customer_status.value
    columnsAccount.find(item => item.prop === 'customerStatus').enum = customer_status.value
  },
  { deep: true, immediate: true }
)
const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = id => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
}
/* 客户详情弹窗 ---end--- */

/* 操作列 ---start--- */
const handleAnalyzeCollection = row => {
  // console.log(id)
  showDialog({
    title: '收款分析',
    showConfirmButton: 'hide',
    cancelButtonText: '关闭',
    component: analysisForm,
    submitApi: financeCustomerAnalyseUpdate,
    customClass: 'mini-dialog',
    rowFormData: row,
    submitCallback
  })
}

// const selectedListTemp = []
// const selectedList = ref([])

const submitCallback = (data, id) => {
  // console.log('submitCallback', data, id)
  if (id === true) {
    proTable.value?.getTableList()
    return
  }
  initParam.analyseId === id ? proTable.value?.getTableList() : (initParam.analyseId = id)
  // if (initParam.analyseId) {
  //   const index = selectedListTemp.findIndex(item => {
  //     return item.customerId === data.customerId
  //   })
  //   data.analyseId = initParam.analyseId
  //   if (index === -1) {
  //     selectedListTemp.push(data)
  //   } else {
  //     selectedListTemp.find(item => {
  //       if (item.customerId === data.customerId) {
  //         item.receiveAnalyse = data.receiveAnalyse
  //       }
  //     })
  //   }
  // }
  // selectedList.value.find(item => {
  //   if (item.customerId === data.customerId) {
  //     item.receiveAnalyse = data.receiveAnalyse
  //   }
  // })
}

const handleStepAfter = () => {
  // console.log('handleStepAfter', proTable.value.selectedList)
  if (proTable.value.selectedList.length) {
    const formData = {
      analyseName: analyseName,
      financeCustomerAnalyseList: proTable.value.selectedList
    }
    if (buttonLoading.value) return
    buttonLoading.value = true
    financeCustomerAnalyseSave(formData)
      .then(res => {
        buttonLoading.value = false
        if (res.code === 200) {
          proxy.$modal.msgSuccess(res.msg)
          emit('ok')
          initParam.analyseId = res.data
          step.value++
        }
      })
      .catch(() => {
        buttonLoading.value = false // 注意是否触发
      })
  } else {
    proxy.$modal.msgWarning(`请选择客户再进行下一步操作`)
  }
}

const buttonLoading = ref(false)

const onAdd = data => {
  initParam.analyseId = undefined
  visible.value = true
  analyseName = data.analyseName
  step.value = 1
}
const onEdit = async data => {
  initParam.analyseId = data.id
  // console.log('initParam.analyseId', initParam.analyseId)
  visible.value = true
  step.value = 2
}
const handleClose = async () => {
  visible.value = false
  emit('ok')
}

/* 操作列 ---end--- */
defineExpose({
  onAdd,
  onEdit
})
</script>

<style lang="scss">
.my-table {
  min-height: 300px;
  display: flex;
  .el-table__append-wrapper {
  }
  .card {
    box-sizing: border-box;
    padding: 0;
    overflow-x: hidden;
    background-color: none;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
