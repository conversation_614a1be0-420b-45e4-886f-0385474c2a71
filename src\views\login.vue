<template>
  <div class="login">
    <img src="@/assets/images/login-bg1.svg" class="img-bg" />
    <div class="right">
      <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
        <h3 class="title">三优CRM</h3>
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="密码"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="captchaEnabled" prop="code">
          <el-input
            v-model="loginForm.code"
            size="large"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" class="login-code-img" @click="getCode" />
          </div>
        </el-form-item>
        <!-- <el-checkbox v-model="loginForm.rememberMe" style="margin: 0 0 25px"> 记住密码 </el-checkbox> -->
        <el-form-item style="width: 100%">
          <el-button :loading="loading" size="large" type="primary" style="width: 100%" @click.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div v-if="register" style="float: right">
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2023 ruoyi.vip All Rights Reserved.</span>
    </div>

    <!-- logo -->
    <img src="@/assets/logo/new-logo.svg" class="logo-fixed" />
  </div>
</template>

<script setup>
import { getCodeImg, getNoticesNotRead } from '@/api/login'
import Cookies from 'js-cookie'
import { encrypt } from '@/utils/jsencrypt'
import useUserStore from '@/store/modules/user'
import { ElNotification } from 'element-plus'
import { h } from 'vue'
const userStore = useUserStore()
const router = useRouter()
const { proxy } = getCurrentInstance()

const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: ''
})

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
}

const codeUrl = ref('')
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(true)
// 注册开关
const register = ref(false)
const redirect = ref(undefined)

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set('username', loginForm.value.username, { expires: 30 })
        Cookies.set('password', encrypt(loginForm.value.password), {
          expires: 30
        })
        Cookies.set('rememberMe', loginForm.value.rememberMe, { expires: 30 })
      } else {
        // 否则移除
        Cookies.remove('username')
        Cookies.remove('password')
        Cookies.remove('rememberMe')
      }
      // 调用action的登录方法
      userStore
        .login(loginForm.value)
        .then(async () => {
          router.push({ path: redirect.value || '/' })
          // 同时获取未读接口消息
          const { data } = await getNoticesNotRead()
          if (data) {
            // 提示用户
            ElNotification({
              title: '提示',
              // dangerouslyUseHTMLString: true,
              // duration: 100000, // 假数据
              message: h('p', null, [
                h('span', null, data),
                h(
                  'span',
                  {
                    class: 'message-text',
                    // vNode绑定点击事件
                    onclick: () => {
                      console.log('h函数点击事件触发')
                      routerToMessageList()
                    }
                  },
                  '请查看'
                )
              ]),
              type: 'warning'
            })
          }
        })
        .catch(() => {
          loading.value = false
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode()
          }
        })
    }
  })
}
// 跳转至消息列表

const routerToMessageList = () => {
  router.push('/message/my-message')
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = 'data:image/gif;base64,' + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

// function getCookie() {
//   const username = Cookies.get('username')
//   const password = Cookies.get('password')
//   const rememberMe = Cookies.get('rememberMe')
//   loginForm.value = {
//     username: username === undefined ? loginForm.value.username : username,
//     password: password === undefined ? loginForm.value.password : decrypt(password),
//     rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
//   }
// }

getCode()
// getCookie()
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  // align-items: center;
  // justify-content: center;
  width: 100%;
  height: 100%;
  // background-image: url('../assets/images/login-bg.svg');

  // .left {
  //   background: url('../assets/images/login-bg1.svg') no-repeat;
  //   background-size: 100% 100%;
  //   // background-position: center;
  //   width: 700px;
  //   overflow: hidden;
  //   flex: 1;
  // }d

  .right {
    flex: 7;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.title {
  margin: 0 auto 30px;
  font-size: 32px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
  text-align: center;
}
.login-form {
  width: 400px;
  padding: 25px 25px 5px;
  background: #ffffff;
  border-radius: 6px;
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
  .input-icon {
    width: 14px;
    height: 39px;
    margin-left: 0;
  }
}
.login-tip {
  font-size: 13px;
  color: #bfbfbf;
  text-align: center;
}
.login-code {
  float: right;
  width: 33%;
  height: 40px;
  img {
    vertical-align: middle;
    cursor: pointer;
  }
}
.el-login-footer {
  position: fixed;
  bottom: 30px;
  width: 100%;
  height: 25px;
  line-height: 25px;
  text-align: center;
  letter-spacing: 1px;
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #646a73;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}

.logo-fixed {
  position: fixed;
  left: 56px;
  top: 56px;
}

.img-bg {
  flex: 3;
}
</style>

<style>
.message-text {
  color: #409eff;
  cursor: pointer;
  margin-left: 10px;
}
</style>
