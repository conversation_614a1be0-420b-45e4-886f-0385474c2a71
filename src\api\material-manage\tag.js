/*
 * @Description: 标签管理
 * @Author: thb
 * @Date: 2023-08-09 13:55:42
 * @LastEditTime: 2023-08-09 14:30:20
 * @LastEditors: thb
 */
import request from '@/utils/request'

// 查询线索标签列表
export const getClueTagList = params => {
  return request({
    url: '/cusTag/list',
    method: 'get',
    params
  })
}

// 获取线索标签详情
export const getClueTagDetail = id => {
  return request({
    url: '/cusTag/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 删除线索标签
export const deleteClueTag = id => {
  return request({
    url: '/cusTag/delete',
    method: 'delete',
    params: {
      id
    }
  })
}

// 新增或者编辑线索标签
export const saveClueTag = data => {
  return request({
    url: '/cusTag/saveOrUpdate',
    method: 'post',
    data
  })
}
// 状态设置
export const setClueTagStatus = id => {
  return request({
    url: '/cusTag/setStatus',
    method: 'post',
    params: {
      id
    }
  })
}
