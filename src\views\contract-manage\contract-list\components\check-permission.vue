<!--
 * @Description: 借阅申请权限
 * @Author: thb
 * @Date: 2023-07-12 09:49:14
 * @LastEditTime: 2023-07-12 10:13:33
 * @LastEditors: thb
-->
<template>
  <div class="wrap">
    <el-icon :size="40"><Lock /></el-icon>
    <p>您暂无权限对本合同进行查看,请联系财税顾问添加权限,或点击下方按钮申请借阅合同</p>
    <el-button @click="showReport">借阅申请</el-button>
  </div>
  <checkReport v-if="reportShow" @on-close="reportShow = false" @on-success="handleSuccess" />
</template>
<script setup>
import { Lock } from '@element-plus/icons-vue'
import checkReport from './check-report.vue'

// 借阅申请弹窗标志
const reportShow = ref(false)
const showReport = () => {
  reportShow.value = true
}

const emits = defineEmits(['on-success'])
const handleSuccess = () => {
  reportShow.value = false
  emits('on-success')
}
</script>
<style lang="scss" scoped>
.wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
p {
  font-size: 18px;
}
</style>
