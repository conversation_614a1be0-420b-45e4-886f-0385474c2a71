import { reactive, toRefs, ref, provide } from 'vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
export const useCustomer = () => {
  const state = reactive({
    rowId: '',
    customerDetailShow: false
  })
  const isEdit = ref(false)
  provide('isEdit', isEdit)
  const handleShowCustomerDetail = (id: string) => {
    state.rowId = id
    state.customerDetailShow = true
  }
  return {
    ...toRefs(state),
    handleShowCustomerDetail,
    customerDetail
  }
}
