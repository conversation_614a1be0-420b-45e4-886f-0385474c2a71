<template>
  <div class="main-wrap">
    <!-- <dataList ref="staticsRef" :selectReceivableAmount="selectReceivableAmount" /> -->
    <!-- <el-date-picker v-model="initParam.month" type="month" /> -->
    <!-- initParam.month - {{ initParam.month }} -->
    <dataList ref="staticsRef" :selectedList="proTable && proTable.selectedList" />
    <ProTable
      ref="proTable"
      title="应收台账"
      :init-param="initParam"
      :columns="columns"
      :toolButton="true"
      rowKey="id"
      :transformRequestParams="transformRequestParams"
      @sort-change="sortChange"
      :request-api="financePaymentList"
    >
      <template #tabs>
        <el-radio-group :model-value="initParam.expireType" @change="handleRadioChange">
          <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{
            item.dictLabel
          }}</el-radio-button>
        </el-radio-group></template
      >
      <template #customerName="{ row }">
        <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
      </template>
      <template #paymentNo="{ row }">
        <span class="blue-text" @click="handleShowAccountsDetail(row)">{{ row.paymentNo }}</span>
      </template>
      <template #contractNo="{ row }">
        <span class="blue-text" @click="handlShowContractDetail(row.contractId)">{{ row.contractNo }}</span>
      </template>
      <template #action="{ row }">
        <el-button v-if="row.paymentStatus !== 'close'" type="primary" text @click="handleRelateCollection(row)"
          >关联收款</el-button
        >
        <el-button
          v-if="row.paymentStatus !== 'close'"
          v-hasPermi="['finance:payment:delete']"
          type="danger"
          text
          @click="handleDelete(row)"
          >删除</el-button
        >
        <!-- maybe_todo 还有别的判断条件吗-->
        <!-- todo 还有台账列表也涉及吗-->
        <!-- <el-button type="primary" text @click="handleAnalyzeCollection(row.id)">收款分析</el-button> -->
      </template>
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="handleAdd" v-hasPermi="['finance:payment:add']">新增</el-button>
        <el-button :icon="Upload" @click="handleImport">导入</el-button>
        <!-- 增加列表导出功能 -->
        <el-button :icon="Download" @click="handleExport" v-hasPermi="['finance:accounts-receivable:export']">导出</el-button>
      </template>
    </ProTable>
  </div>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <contractDetail v-if="contractDetailShow" :id="contractId" :hideActionBtn="true" @on-close="contractDetailShow = false" />
  <!-- todo 问题：不在dom里先引入，在protable里再获取就获取不到了 -->
  <div v-show="false"><feeTypeTree></feeTypeTree></div>
  <div v-show="false"><deptTree></deptTree></div>
  <ImportExcel ref="dialogRef" />
</template>

<script setup lang="tsx">
import {
  financePaymentList,
  financePaymentSaveOrUpdate,
  financePaymentGetById,
  financePaymentCollectionAnalysisSaveOrUpdate,
  uploadPayment,
  financePaymentDelete,
  downloadFinancePaymentExport
} from '@/api/finance/accounts-receivable'
import { postFinanceReceiptSaveOrUpdate } from '@/api/finance/collection-ledger'
import { ref, reactive, nextTick } from 'vue'
import dataList from './components/data-list.vue'
import accountsForm from '@/views/finance/accounts-receivable/components/form.vue'
import collectionForm from '@/views/finance/collection-ledger/components/form.vue'
import analysisForm from '@/views/finance/accounts-receivable/components/analysis-form.vue'
import { useDialog } from '@/hooks/useDialogFinance'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus, Upload, Download } from '@element-plus/icons-vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import feeTypeTree from '@/views/finance/accounts-receivable/components/fee-type-tree'
import deptTree from '@/views/finance/accounts-receivable/components/dept-tree'
import { receiveStatusArr } from '@/utils/constants'
import dayjs from 'dayjs'
import bus from 'vue3-eventbus'
import { multiply } from '@/utils/math'
import contractDetail from '@/views/contract-manage/contract-list/components/contract-detail.vue'
import ImportExcel from '@/components/ImportExcel/index.vue'
import { ElMessageBox } from 'element-plus'
import { checkPermi } from '@/utils/permission'
import { getReviewerTreeData } from '@/api/process/process'
import InputRange from '@/views/finance/day-book-statement/components/input-range'

const { proxy } = getCurrentInstance()

const staticsRef = ref()
const proTable = ref()

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({
  // receiveAnalyse: ['确定付款 > 确定付款时间', '有异议 > 风险客户 > 同行挖走'],
  // paymentEndTime: '',
  expireType: 1
})
const tabs = ref([
  {
    dictLabel: '全部',
    dicValue: 0
  },
  {
    dictLabel: '本月到期',
    dicValue: 1
  },
  {
    dictLabel: '未到期',
    dicValue: 2
  },
  {
    dictLabel: '逾期未回',
    dicValue: 3
  },
  {
    dictLabel: '已关闭',
    dicValue: 4
  }
])

const getTreeData = () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await getReviewerTreeData()
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}

const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'paymentNo',
    label: '账单编号',
    sortable: 'custom',
    width: '200',
    fixed: 'left',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'feeType',
    label: '费用类别',
    sortable: 'custom',
    width: '150',
    isColShow: false,
    fixed: 'left',
    search: {
      render: ({ searchParam }) => {
        return <feeTypeTree vModel={searchParam.feeType} />
      }
    },
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    }
  },
  {
    prop: 'customerName',
    label: '客户名称',
    sortable: 'custom',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerStatus',
    label: '企业状态',
    width: '150'
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    sortable: 'custom',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'mangerAndDept',
    width: '240',
    sortable: 'custom',
    label: '财税顾问'
  },
  {
    prop: 'counselorAndDept',
    width: '240',
    sortable: 'custom',
    label: '开票员'
  },
  {
    prop: 'customerSuccessAndDept',
    width: '240',
    sortable: 'custom',
    label: '客户成功'
  },
  {
    prop: 'sponsorAccountingAndDept',
    width: '240',
    sortable: 'custom',
    label: '主办会计'
  },
  {
    prop: 'manger',
    width: '240',
    sortable: 'custom',
    label: '财税顾问',
    enum: getTreeData,
    isShow: false,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    }
  },
  {
    prop: 'counselor',
    width: '240',
    sortable: 'custom',
    label: '开票员',
    enum: getTreeData,
    isShow: false,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    }
  },
  {
    prop: 'customerSuccess',
    width: '240',
    sortable: 'custom',
    label: '客户成功',
    enum: getTreeData,
    isShow: false,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    }
  },
  {
    prop: 'sponsorAccounting',
    width: '240',
    sortable: 'custom',
    label: '主办会计',
    enum: getTreeData,
    isShow: false,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    }
  },
  {
    prop: 'receiveStatus',
    width: '120',
    sortable: 'custom',
    label: '收款情况',
    enum: receiveStatusArr,
    search: { el: 'select' }
  },
  // {
  //   prop: 'deptId',
  //   label: '所属部门',
  //   isColShow: false,
  //   isShow: false, // 需要该筛选，但不需要该列
  //   search: {
  //     render: ({ searchParam }) => {
  //       return <deptTree vModel={searchParam.deptId} />
  //     }
  //   }
  // },
  {
    prop: 'paymentAmount',
    sortable: 'custom',
    label: '账款金额', // 账款金额->账款金额总计
    width: '120',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'allReceiptAmount',
    label: '已付款金额', // 已收款->已付款金额
    width: '150',
    sortable: 'custom',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.allReceiptAmount >= 0 ? `${scope.row.allReceiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'allReceivableAmount',
    label: '应待收款',
    sortable: 'custom',
    width: '120',
    isColShow: false,
    render: scope => {
      // 思考：hooks下如何篡改列表项数据
      return <span>{scope.row.allReceivableAmount >= 0 ? `${scope.row.allReceivableAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'monthTurnover',
    label: '月营业额',
    width: '120',
    sortable: 'custom',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.monthTurnover >= 0 ? `${scope.row.monthTurnover}元` : '--'}</span>
    },
    search: {
      render: ({ searchParam }) => {
        return <InputRange vModel={searchParam.monthTurnover} />
      }
    }
  },
  {
    prop: 'priceFluctuation',
    label: '价格变动',
    width: '120',
    sortable: 'custom',
    isColShow: false,
    enum: [
      {
        label: '上涨',
        value: '上涨'
      },
      {
        label: '下跌',
        value: '下跌'
      },
      {
        label: '未变动',
        value: '未变动'
      }
    ],
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.priceFluctuation || '--'}</span>
    }
  },
  {
    prop: 'priceFluctuationAmount',
    label: '变动金额',
    width: '120',
    sortable: 'custom',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.priceFluctuationAmount >= 0 ? `${scope.row.priceFluctuationAmount}元` : '--'}</span>
    },
    search: {
      render: ({ searchParam }) => {
        return <InputRange vModel={searchParam.priceFluctuationAmount} />
      }
    }
  },
  {
    prop: 'discount',
    label: '优惠',
    width: '200',
    isColShow: false,
    render: scope => {
      return (
        <span>
          {scope.row.discount === '金额优惠'
            ? `金额优惠：${scope.row.discountAmount}元`
            : scope.row.discount === '时长优惠'
            ? `时长优惠：${scope.row.discountTime}月`
            : scope.row.discount === '无优惠'
            ? '无优惠'
            : scope.row.discount === '活动优惠'
            ? `活动优惠：${scope.row.activityTxt}`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'discountRate',
    label: '折扣率',
    sortable: 'custom',
    width: '100',
    sortable: 'custom',
    render: scope => {
      return (
        <span>
          {scope.row.discount === '金额优惠'
            ? `${multiply(scope.row.discountRate, 100)}%`
            : scope.row.discount === '时长优惠'
            ? `${multiply(scope.row.discountTimeRate, 100)}%`
            : scope.row.discount === '无优惠'
            ? '--'
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'returnRate',
    label: '回款率',
    width: '100',
    render: scope => {
      return <span>{`${multiply(scope.row.returnRate, 100)}%`}</span>
    }
  },
  {
    prop: 'paymentDate',
    label: '账期',
    sortable: 'custom',
    width: '100',
    render: scope => {
      return <span>{scope.row.paymentDate ? `${scope.row.paymentDate}月` : '--'}</span>
    }
  },
  {
    prop: 'paymentStartTime',
    label: '账期开始时间',
    sortable: 'custom',
    width: '150',
    search: {
      el: 'date-picker',
      props: { type: 'monthrange', valueFormat: 'YYYY-MM' }
    }
  },
  {
    prop: 'paymentEndTime',
    label: '账期结束时间',
    sortable: 'custom',
    width: '150',
    // search: { el: 'input', props: { type: 'month', placeholder: '请点击右侧日历图标选择' } }
    // todo 为什么paymentEndTime没有代入 date-picker
    // search: {
    //   render: ({ searchParam }) => {
    // 怎么把值反馈到searchParam对象里去
    // 不要通过initParam传递，不然一改变就触发搜索，且没法通过reset去清空
    // 【1】
    // return <elDatePicker vModel={searchParam.paymentEndTime} type="month" placeholder="请选择" />
    // 【2】
    // onChange={(date: any) => {
    // searchParam.paymentEndTime = days(date).format('YYYY-MM')
    // }}
    // 【3】
    //     return (
    //       <elDatePicker
    //         modelValue={searchParam.paymentEndTime}
    //         onChange={value => {
    //           handleMonthChange(value)
    //         }}
    //         type="month"
    //       />
    //     )
    //   }
    // },
    // 【4】
    search: {
      el: 'date-picker',
      props: { type: 'monthrange', valueFormat: 'YYYY-MM' }
    }
  },
  {
    prop: 'receiveTime',
    width: '160',
    sortable: 'custom',
    label: '收款时间'
  },
  {
    prop: 'contractNo',
    label: '关联合同',
    width: '150'
  },
  {
    prop: 'action',
    label: '操作',
    width: 190,
    isColShow: false,
    fixed: 'right'
  }
]

const transformRequestParams = data => {
  console.log('currentColumn', currentColumn)
  if (currentColumn) {
    const label = currentColumn
    // sortByColumn(currentColumn, labelMap[currentColumn].order)
    if (labelMap[label]['key']) {
      const order = labelMap[label]['order']
      if (order === 'descending') {
        data.isAsc = 'desc'
        data.orderByColumn = labelMap[label]['key']
      } else if (order === 'ascending') {
        data.isAsc = 'asc'
        data.orderByColumn = labelMap[label]['key']
      } else {
        data.isAsc = ''
        data.orderByColumn = ''
      }
    }
  }
  if (data.monthTurnover) {
    data.monthTurnoverMin = data.monthTurnover[0]
    data.monthTurnoverMax = data.monthTurnover[1]
  }
  if (data.priceFluctuationAmount) {
    data.priceFluctuationAmountMin = data.priceFluctuationAmount[0]
    data.priceFluctuationAmountMax = data.priceFluctuationAmount[1]
  }
}

const handleMonthChange = (e: any) => {
  console.log('handleMonthChange', e)
  initParam.paymentEndTime = dayjs(e).format('YYYY-MM') || ''
}

const { showDialog } = useDialog()

const handleAdd = () => {
  showDialog({
    title: '新增账单',
    component: accountsForm,
    customClass: 'customer-dialog',
    submitApi: financePaymentSaveOrUpdate,
    submitCallback
  })
}

const handleShowAccountsDetail = (row: any) => {
  console.log('row', row, checkPermi(['finance:payment:update']))
  showDialog({
    title: '账单详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    confirmButtonText: '编辑',
    showConfirmButton: row.paymentStatus !== 'close' && checkPermi(['finance:payment:update']) ? '' : 'hide',
    // 如何把paymentNo传递到详情form中，并且在详情form的table加载时带上参数
    // rowFormData传值
    // provide
    // evenntbus并且在详情form渲染之后再触发传值
    // rowFormData传值并在useDialogFinance中额外处理一次请求
    // rowFormData: { paymentId: row.id, paymentNo: row.paymentNo },
    component: accountsForm,
    submitApi: financePaymentSaveOrUpdate,
    getApi: financePaymentGetById,
    requestParams: { id: row.id },
    submitCallback,
    cancelCallback,
    handleRevertParams
  })
  nextTick(() => {
    // provide('paymentNo', row.paymentNo)
    bus.emit('paymentNo', row.paymentNo)
    bus.emit('paymentId', row.paymentId || row.id)
    // bus.emit('feeType', row.feeType) // 其实这里语义有问题，账单里feeType是费用类别，业务产品里feeType是收费类型
  })
}

const handleRevertParams = (data: any) => {
  if (data.discount !== '金额优惠') {
    data.discountAmount = null
  }
  if (data.discount !== '时长优惠') {
    data.discountTime = null
  }
}

const submitCallback = () => {
  proTable.value?.getTableList()
  staticsRef.value?.getList()
}

const cancelCallback = () => {
  proTable.value?.getTableList()
  staticsRef.value?.getList()
}

const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.expireType = e
}

const getList = () => {
  proTable.value?.getTableList()
  staticsRef.value?.getList()
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

/* 合同详情弹窗 ---start--- */
const contractDetailShow = ref(false)
const contractId = ref()
const handlShowContractDetail = (id: number) => {
  // proxy.$modal.msgWarning(`合同详情模块建设中!`)
  contractDetailShow.value = true
  contractId.value = id
}
/* 合同弹窗 ---end--- */

/* 操作列 ---start--- */
const handleRelateCollection = (row: any) => {
  console.log(row)
  showDialog({
    title: '关联收款',
    component: collectionForm,
    customClass: 'customer-dialog',
    rowFormData: {
      customerName: row.customerName,
      customerId: row.customerId,
      customerNo: row.customerNo,
      paymentId: row.id,
      paymentNo: row.paymentNo
    },
    submitApi: postFinanceReceiptSaveOrUpdate,
    submitCallback,
    handleRevertParams: handleRevertParamsCollection
  })
}
// 处理表单提交参数
const handleRevertParamsCollection = (data: any) => {
  if (Array.isArray(data.receiptVoucherFile) && data.receiptVoucherFile[0]) {
    const file = data.receiptVoucherFile[0]
    data.receiptVoucherFile = {
      fileSize: file.uploadSize,
      fileNames: file.newFileName,
      // bizType: 'dddd', // 假数据
      uploadBy: file.uploadBy,
      uploadTime: file.uploadTime,
      urls: file.url
    }
  } else {
    delete data.receiptVoucherFile
  }
}
const handleAnalyzeCollection = (id: any) => {
  // console.log(id)
  showDialog({
    title: '收款分析',
    component: analysisForm,
    customClass: 'mini-dialog',
    rowFormData: { id },
    submitApi: financePaymentCollectionAnalysisSaveOrUpdate,
    submitCallback
    // handleRevertParams: handleRevertParamsAnalyzeCollection
  })
}
// const handleRevertParamsAnalyzeCollection = (formData: any) => {
//   const row = formData.receiveAnalyseTempRow
//   const arr = []
//   arr.push(row.data.name)
//   if (row.parent.data.name) {
//     arr.push(row.parent.data.name)
//     if (row.parent.parent.data.name) {
//       arr.push(row.parent.parent.data.name)
//     }
//   }
//   arr.reverse()
//   console.log('arr', arr)
//   formData.receiveAnalyse = arr.join(' > ')
//   formData.receiveAnalyseTempRow = undefined
// }

const handleDelete = async (row: any) => {
  ElMessageBox.confirm('删除后账单关联的收款单将同步删除，由系统从余额结算的金额将返回企业余额', '是否确认删除该账单？', {
    confirmButtonText: '确认',
    cancelButtonText: '关闭',
    distinguishCancelAndClose: true,
    type: 'warning'
  })
    .then(async () => {
      financePaymentDelete({ id: row.id }).then((res: any) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('删除成功!')
          proTable.value?.getTableList()
        }
      })
    })
    .catch(action => {})
}
/* 操作列 ---end--- */
const dialogRef = ref<InstanceType<typeof ImportExcel> | null>(null)
const handleImport = () => {
  const params = {
    title: '财务',
    tempApiUrl: '/账务导入下载模板.xls',
    // tempApi: '', // 下载模板接口
    importApi: uploadPayment, // 导入接口
    getTableList: proTable.value?.getTableList
  }
  console.log('dialogRef', dialogRef)
  dialogRef.value?.acceptParams(params)
}

// 导出列表功能
const handleExport = async () => {
  const result = await downloadFinancePaymentExport({
    ...proTable.value.searchParam,
    // tab的参数
    expireType: initParam.expireType
  })
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}

// 表头字段排序

const labelMap = {
  账单编号: {
    key: 'paymentVo.payment_no',
    order: ''
  },
  费用类别: {
    key: 'paymentVo.fee_type',
    order: ''
  },
  客户名称: {
    key: 'paymentVo.customer_name',
    order: ''
  },
  客户编号: {
    key: 'paymentVo.customer_no',
    order: ''
  },
  财税顾问: {
    key: 'paymentVo.manger_user_id',
    order: ''
  },
  开票员: {
    key: 'paymentVo.counselor_user_id',
    order: ''
  },
  客户成功: {
    key: 'paymentVo.customer_success_user_id',
    order: ''
  },
  主办会计: {
    key: 'paymentVo.sponsor_accounting_user_id',
    order: ''
  },
  收款情况: {
    key: 'paymentVo.receive_status',
    order: ''
  },
  账款金额: {
    key: 'paymentVo.payment_amount',
    order: ''
  },
  已付款金额: {
    key: 'paymentVo.all_receipt_amount',
    order: ''
  },
  应待收款: {
    key: 'paymentVo.all_receivable_amount',
    order: ''
  },
  折扣率: {
    key: 'paymentVo.discount_rate',
    order: ''
  },
  账期: {
    key: 'paymentVo.payment_date',
    order: ''
  },
  账期开始时间: {
    key: 'paymentVo.payment_start_time',
    order: ''
  },
  账期结束时间: {
    key: 'paymentVo.payment_end_time',
    order: ''
  },
  收款时间: {
    key: 'paymentVo.receive_time',
    order: ''
  },
  月营业额: {
    key: 'paymentVo.month_turnover',
    order: ''
  },
  价格变动: {
    key: 'paymentVo.price_fluctuation',
    order: ''
  },
  变动金额: {
    key: 'paymentVo.price_fluctuation_amount',
    order: ''
  }
}

let currentColumn = ''
const sortByColumn = (label, order) => {
  // 如果存在可排序的列名
  if (labelMap[label]['key']) {
    currentColumn = label
    labelMap[label]['order'] = order
    if (order === 'descending') {
      proTable.value.searchParam.isAsc = 'desc'
      proTable.value.searchParam.orderByColumn = labelMap[label]['key']
      proTable.value.search()
    } else if (order === 'ascending') {
      proTable.value.searchParam.isAsc = 'asc'
      proTable.value.searchParam.orderByColumn = labelMap[label]['key']
      proTable.value.search()
    } else {
      proTable.value.searchParam.isAsc = ''
      proTable.value.searchParam.orderByColumn = ''
      proTable.value.search()
    }
  }
}
const sortChange = ({ column, prop, order }) => {
  sortByColumn(column.label, order)
}
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
