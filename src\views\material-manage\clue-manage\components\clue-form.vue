<!--
 * @Description: 线索配置表单
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-11-24 09:49:57
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <Collapse title="基础信息">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="姓名" prop="contactName">
            <el-input v-model="formData.contactName" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="手机号" prop="contactPhone">
            <el-input v-model="formData.contactPhone" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="线索来源" prop="sourceId">
            <el-tree-select
              filterable
              v-model="formData.sourceId"
              :data="clue_source"
              check-strictly
              :render-after-expand="false"
              default-expand-all
              placeholder="请选择"
              :props="{
                value: 'id',
                label: 'name',
                children: 'child',
                disabled: (data, node) => {
                  return data.child
                }
              }"
              clearable
              @current-change="(node, nodeData) => handleSelectChange(node, nodeData)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="formData.sourceId === '3'">
          <el-form-item label="介绍来源" prop="introductionCustomerName">
            <el-input
              @click="handleListSelectShow"
              readonly
              v-model="formData.introductionCustomerName"
              maxlength="50"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="公司名称" prop="companyName">
            <el-input v-model="formData.companyName" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              maxlength="1000"
              type="textarea"
              placeholder="请输入"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="标签" prop="tags">
            <el-select v-model="formData.tags" multiple placeholder="请选择">
              <el-option
                v-for="(item, index) in tagsList"
                :key="index"
                :disabled="item.status === '0'"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 新增业务类型 -->
        <el-col :span="12">
          <el-form-item label="业务类型" prop="productId">
            <el-tree-select
              v-model="formData.productId"
              filterable
              :data="productTreeData"
              check-strictly
              :props="{ value: 'id', label: 'name' }"
              node-key="id"
              :render-after-expand="false"
              @node-click="nodeClick"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>

    <Collapse title="更多信息">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="职位" prop="post">
            <el-input v-model="formData.post" maxlength="10" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="行业" prop="industry">
            <el-select
              v-model="formData.industry"
              placeholder="请选择"
              clearable
              filterable
              allow-create
              default-first-option
              @change="handleChange"
            >
              <el-option v-for="(item, index) in industry" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="formData.phone" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="微信" prop="wx">
            <el-input v-model="formData.wx" maxlength="100" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="QQ" prop="qq">
            <el-input v-model="formData.qq" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="formData.sex">
              <el-radio label="0">未知</el-radio>
              <el-radio label="1">男</el-radio>
              <el-radio label="2">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="生日" prop="birthday">
            <el-date-picker
              v-model="formData.birthday"
              type="date"
              placeholder="请选择"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="地区" prop="area">
            <el-input v-model="formData.area" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="formData.address" maxlength="100" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="税务性质" prop="taxNature">
            <el-select v-model="formData.taxNature" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in customer_property" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="首次跟进时间" prop="firstFollowTime">
            <el-date-picker
              v-model="formData.firstFollowTime"
              type="date"
              placeholder="请选择"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
    <!-- 
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="公海管理员" prop="manageId">
          <SelectTree v-model="formData.manageId" placeholder="请选择" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="掉保时长" prop="duration">
          <NumberInput v-model="formData.duration" placeholder="请输入">
            <template #suffix> 小时 </template>
          </NumberInput>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="资源分配规则" prop="rule">
          <el-radio-group v-model="formData.rule">
            <el-radio label="0">员工领取</el-radio>
            <el-radio label="1">仅管理员分配</el-radio>
            <el-radio label="2">员工领取+管理员分配</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row> -->
    <tableModal
      v-if="listSelectShow"
      rowKey="customerId"
      title="介绍来源"
      :columns="columns"
      :request-api="getCustomers"
      @on-close="listSelectShow = false"
      @on-select="handleSelect"
    />
  </el-form>
</template>

<script setup lang="jsx">
import { reactive } from 'vue'
import SelectTree from '@/components/SelectTree'
import Collapse from '@/components/Collapse'
import { getClueTagList } from '@/api/material-manage/tag'
import { FormValidators } from '@/utils/validate'
import { getBusinessList } from '@/api/business/business'
import { cusSourceTree } from '@/api/material-manage/source'
import tableModal from '@/components/tableModal'
import { getCustomers } from '@/api/customer/file'
import { useDic } from '@/hooks/useDic'
import { useSetDic } from '@/hooks/useSetDic'
const { proxy } = getCurrentInstance()
const { setDic } = useSetDic()
const { getDic } = useDic()
const handleChange = value => {
  setDic('industry', value, value, industry)
}

const clue_source = ref([])
cusSourceTree({ enable: 1 }).then(res => {
  clue_source.value = res.data
})
// const { clue_source } = proxy.useDict('clue_source')
const { industry } = proxy.useDict('industry')
const { customer_property } = proxy.useDict('customer_property')
const formData = reactive({
  contactName: '',
  mainContactId: '',
  isSea: '',
  contactPhone: '',
  sourceId: '',
  sourceName: '',
  introductionCustomerId: '',
  introductionCustomerName: '',
  companyName: '',
  remark: '',
  sex: '0',
  post: '',
  industry: '',
  phone: '',
  email: '',
  wx: '',
  qq: '',
  birthday: '',
  area: '',
  address: '',
  firstFollowTime: '',
  taxNature: '',
  productId: '',
  id: undefined
})

const rules = {
  contactName: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  contactPhone: [
    {
      required: false,

      validator: FormValidators.mobilePhone,
      trigger: ['blur']
    }
  ],
  phone: [
    {
      required: false,
      message: '请输入正确的电话格式',
      validator: FormValidators.allPhone,
      trigger: ['blur']
    }
  ],
  email: [
    {
      required: false,
      message: '请输入正确的邮箱格式',
      validator: FormValidators.email,
      trigger: ['blur']
    }
  ],
  qq: [
    {
      required: false,
      message: '请输入正确格式的qq账号',
      validator: FormValidators.qq,
      trigger: ['blur']
    }
  ],
  sourceId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  introductionCustomerName: [
    {
      required: true,
      message: '请选择',
      trigger: ['change', 'blur']
    }
  ],
  companyName: [
    {
      required: false,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  remark: [
    {
      required: false,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  productId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
const tagsList = ref([])
const getTagsList = async () => {
  const { data } = await getClueTagList({
    pageSize: 10000,

    pageNum: 1
  })
  tagsList.value = data.records || []
}
getTagsList()

const productTreeData = ref([])
const getAllProducts = async () => {
  const { data } = await getBusinessList({
    pageNum: 1,
    pageSize: 10000
  })
  // 将后端传回的数据结构进行转换
  const revertData = []
  data.forEach(item => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      children: []
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach(child => {
        obj.children.push({
          name: child.productName,
          types:
            item.contractType?.split(',')?.map(item => {
              return {
                label: item,
                value: item
              }
            }) || [],
          type: '产品类型',
          quotation: child.quotation, // 合同报价
          isInContract: child.isInContract, // 是否在合同中定义
          feeType: child.feeType, //收费类型
          id: child.id // 产品类型id
        })
      })
    }
  })

  console.log('revertData', revertData)
  productTreeData.value = revertData || []
}

const nodeClick = node => {
  formData.productName = node.productName
}
getAllProducts()
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

/** 选择线索来源 */
function handleSelectChange(node, nodeData) {
  // console.log('handleSelectChange', node)
  formData.sourceName = node.name
}

/** 客户列表弹窗显示 */
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  listSelectShow.value = true
}
const handleSelect = data => {
  // console.log('data', data)
  formData.introductionCustomerName = data.customerName
  formData.introductionCustomerId = data.customerId
}
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
:deep(.el-date-editor) {
  width: 100%;
}
</style>
