<!--
 * @Description: 线索共享
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-08-16 09:00:49
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <p>请选择共享对象：</p>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="共享线索" prop="userIds">
          <SelectTree
            ref="selectRef"
            v-model="formData.userIds"
            placeholder="请选择"
            value-key="id"
            :defaultProps="defaultProps"
            :render-after-expand="false"
            multiple
            show-checkbox
          />
        </el-form-item>
      </el-col>
    </el-row>
    <p>当前已选择共享人数： {{ formData.userIds.length }}人</p>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import SelectTree from '@/components/SelectTree'
import useUserStore from '@/store/modules/user'
const formData = reactive({
  userIds: [],
  id: undefined
})
const defaultProps = {
  value: 'id',
  label: 'label',
  children: 'children',
  disabled: (data, node) => {
    return (
      (data.type === '0' && Array.isArray(data.children) && data.children.length === 0) || data.id === useUserStore().user.userId
    )
  }
}
const selectRef = ref()
const rules = {
  userIds: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
