/*
 * @Description: hooks 弹窗
 * @Author: thb
 * @Date: 2023-05-25 13:51:37
 * @LastEditTime: 2023-08-22 14:11:26
 * @LastEditors: thb
 */
import { ElMessageBox, ElMessage } from 'element-plus'
// getCurrentInstance
import { h, nextTick, ref } from 'vue'
/**
 * @description dialog 弹窗  使用函数调用的方式显示和隐藏弹窗并且自定义内部组件
 * */
export const useDialog = () => {
  // 显示弹窗
  const showDialog = (params: {
    title?: string
    showConfirmButton?: boolean
    cancelButtonText?: string
    confirmButtonText?: string
    component?: any
    showClose?: boolean
    customClass?: string
    handelCallBack?: (params: any) => void
    submitApi?: (param: any) => Promise<any> // 保存接口,注意提供自定义修改接口参数的功能
    submitCallback?: (param: any) => void
    requestParams?: any // getApi查询参数
    handleRevertParams?: (param1: any, param2: any) => void
    getApi?: (param: any) => Promise<any>
    handleConvertParams?: (param1: any) => void // 获取详情时自定义详情数据
  }) => {
    const {
      title,
      showConfirmButton = true,
      component,
      customClass,
      submitApi,
      getApi,
      cancelButtonText,
      confirmButtonText,
      showClose,
      requestParams,
      handleRevertParams,
      handleConvertParams,
      submitCallback
    } = params
    const compRef = ref()
    ElMessageBox({
      title,
      customClass,
      autofocus: true,
      // Should pass a function if VNode contains dynamic props
      message: () => {
        if (getApi) {
          getApi(requestParams)
            .then(response => {
              // 如果成功
              console.log('data', response.data)
              // 获取formData的key值
              const keys = Object.keys(compRef.value.formData)
              // 循环给formData赋值
              keys.forEach(key => {
                console.log('key', key, response.data[key])
                compRef.value.formData[key] = response.data[key]
              })
              //
              if (Array.isArray(response.data) && response.data) {
                compRef.value.formData.userIds = response.data
              }

              handleConvertParams && handleConvertParams(compRef.value.formData)
            })
            .catch(error => {
              console.log('error', error)

              handleConvertParams && handleConvertParams(compRef.value.formData)
            })
        } else {
          nextTick(() => {
            handleConvertParams && handleConvertParams(compRef.value.formData)
          })
        }
        return h(component || 'div', {
          ref: compRef
        })
      },
      showClose: !(showClose + '' === 'false'),
      showCancelButton: true,
      // show-confirm-button
      showConfirmButton: showConfirmButton,
      closeOnClickModal: false,
      cancelButtonText: cancelButtonText || '取消',
      confirmButtonText: confirmButtonText || '保存',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          // 如果表单对象存在，则执行表单校验规则
          if (compRef.value.getFormRef) {
            compRef.value.getFormRef().validate(async (valid: any) => {
              if (valid) {
                // 校验通过且如果存在api接口则调用接口
                console.log('submitApi', submitApi)
                if (submitApi) {
                  const returnBool = handleRevertParams && handleRevertParams(compRef.value.formData, requestParams)
                  if (returnBool) return
                  // // 获取详情时需要将id 传入
                  // if (requestParams) {
                  //   compRef.value.formData.id = requestParams
                  // }
                  const result = await submitApi(compRef.value.formData)
                  console.log('result', result)
                  // const { proxy } = getCurrentInstance() as any
                  if (result.data > 0 || result.code === 200) {
                    // 说明保存成功

                    done()
                    submitCallback &&
                      submitCallback({
                        ...compRef.value.formData,
                        result_data_id: result.data,
                        ciId: result.data,
                        customerId: result.data
                      })
                    nextTick(() => {
                      ElMessage({
                        message: '保存成功!',
                        type: 'success'
                      })
                    })
                  } else {
                    ElMessage({
                      message: '保存失败!',
                      type: 'error'
                    })
                  }
                }
                // 测试伪代码
                // done()
                // submitCallback && submitCallback(compRef.value.formData)
              } else {
              }
            })
          }
        } else {
          // cancel情况下
          done()
        }
      }
    }).then(() => {
      //
    })
  }

  return {
    showDialog
  }
}
