<!--
 * @Author: qiuaiping <EMAIL>
 * @Date: 2022-10-19 16:21:14
 * @LastEditors: zzzlucas <EMAIL>
 * @LastEditTime: 2023-10-27 09:05:54
 * @FilePath: \yuxin-admin\src\components\iFrame\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <teleport to="body" :disabled="false">
    <div
      v-loading="loading"
      v-if="show"
      class="iframe-overlay"
      :class="{ bg: fileType === 'other' }"
      :style="fileType === 'video' ? 'width:1024px;height:576.5px;' : 'height:' + height"
    >
      <template v-if="fileType === 'video' || fileType === 'other'">
        <iframe :src="urlPreview" frameborder="no" style="width: 100%; height: 100%" scrolling="auto" />
        <el-icon class="close-icon" :size="24" @click="handleClose"><CircleCloseFilled /></el-icon>
      </template>
      <el-image-viewer
        v-if="fileType === 'object' || fileType === 'image'"
        :url-list="srcList"
        :initial-index="initialIndex"
        infinite
        hide-on-click-modal
        @close="show = false"
      />
    </div>
  </teleport>
</template>

<script setup>
import { usePreview } from '@/hooks/usePreview'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { getFileUrlByOss } from '@/api/file/file.js'
const props = defineProps({
  src: {
    type: String || Array,
    required: true
  },
  modelValue: {
    type: Boolean,
    default: true
  },
  initialIndex: {
    type: Number,
    default: 0
  }
})

const emits = defineEmits(['update:modelValue'])
const show = computed({
  get: () => {
    return props.modelValue
  },
  set: value => {
    emits('update:modelValue', value)
  }
})
const height = ref(document.documentElement.clientHeight - 94.5 + 'px;')
const loading = ref(true)
// const urlPreview = computed(async () => {
//   const previewUrl = await usePreview(props.src)
//   console.log('previewUrl', previewUrl)
//   return previewUrl
// })

const urlPreview = ref('')
// 设置预览地址
const setUrlPreview = async () => {
  // 如果文件不是图片则需要kkfile预览
  if (fileType.value === 'other' || fileType.value === 'video') {
    const previewUrl = await usePreview(props.src)
    urlPreview.value = previewUrl
  }
}
onMounted(() => {
  setTimeout(() => {
    loading.value = false
  }, 300)
  window.onresize = function temp() {
    height.value = document.documentElement.clientHeight - 94.5 + 'px;'
  }
})
// 判断是否图片类型
const srcList = ref([])
const fileType = ref()
const typeImageList = ['png', 'jpg', 'jpeg', 'svg']
const typeVideoList = ['mp4', 'mov']
const fileRemoteUrl = import.meta.env.VITE_APP_FILE_API || window.location.protocol + '//' + window.location.host + '/prod-api'

// 图片处理走oss
const injuredImg = async url => {
  switch (typeof url) {
    case 'object':
      srcList.value = url.map(u => {
        return fileRemoteUrl + u
      })
      fileType.value = 'object'
      break
    case 'string':
      {
        const fileSuffix = props.src.split('.').pop()?.toLowerCase()
        if (typeImageList.includes(fileSuffix)) {
          let { data } = await getFileUrlByOss(props.src)
          // 后端表示阿里云oss不支持url包含+的情况，但因为浏览器自动转义（解码），%2B浏览器里丢进去会重新解码成+
          console.log('---data-iFrame', data)
          data = data.replaceAll('+', '%2B')
          console.log('---data-iFrame', data)
          srcList.value = [data]
          fileType.value = 'image'
        } else if (typeVideoList.includes(fileSuffix)) {
          fileType.value = 'video'
          setUrlPreview()
        } else {
          fileType.value = 'other'
          setUrlPreview()
        }
      }
      break
    default:
      break
  }
  console.log(fileType.value)
}
watch(
  () => props.src,
  val => {
    if (val) {
      injuredImg(val)
    }
  },
  {
    immediate: true
  }
)

const handleClose = () => {
  show.value = false
}
</script>

<style lang="scss" scoped>
.iframe-overlay {
  position: fixed;
  z-index: 9999;
  top: 50%;
  left: 0;
  width: calc(50%);
  left: 50%;
  transform: translate(-50%, -50%);
  &.bg {
    background-color: #fff;
  }
}
.close-icon {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 40px;
  height: 40px;
  cursor: pointer;

  /* 添加阴影效果 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

  /* 使按钮在背景为纯黑或纯白时都能明显区分 */
  background-color: rgba(255, 255, 255, 0.5); /* 白色背景 */
  border: 1px solid rgba(0, 0, 0, 0.2); /* 边框略微加深以突出 */
  border-radius: 50%; /* 圆角按钮，具体形状可以根据需求调整 */
  transition: box-shadow 0.3s ease; /* 添加过渡效果，使阴影变化更自然 */
}

/* 鼠标悬停时的效果 */
.close-icon:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}
</style>
