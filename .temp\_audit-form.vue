<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :disabled="disabled">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="通过" prop="isChecked">
          <el-radio-group v-model="formData.isChecked">
            <el-radio v-for="(item, index) in collectionIsCheckedArr" :key="index" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24" v-if="formData.isChecked === 2">
        <el-form-item label="驳回原因" prop="rejectReason">
          <el-input
            v-model="formData.rejectReason"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入"
            type="textarea"
            maxlength="1000"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
const collectionIsCheckedArr = [
  {
    label: '通过',
    value: 1
  },
  {
    label: '驳回',
    value: 2
  }
]
const formData = reactive({
  ciId: undefined,
  contractId: undefined,
  createBy: undefined,
  createTime: undefined,
  customerName: undefined,
  customerNo: undefined,
  feeType: undefined,
  fundAmount: undefined,
  id: undefined,
  isDeleted: undefined,
  isChecked: undefined,
  mark: undefined,
  payee: undefined,
  paymentId: undefined,
  receiptAmount: undefined,
  receiptMethod: undefined,
  receiptVoucherFile: undefined,
  receivableAmount: undefined,
  rejectReason: undefined,
  updateBy: undefined,
  updateTime: undefined
})

const rules = {
  isChecked: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  rejectReason: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped></style>
