<!--
 * @Description: 合同预警表单
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-08-02 15:48:18
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="isDisabled">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="formData.name" maxlength="20" :disabled="isDisabled" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="合同类型" prop="contractType">
          <el-select v-model="formData.contractType" placeholder="请选择" clearable :disabled="isDisabled">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="预警时间" prop="alertDate">
          到期前&nbsp;&nbsp;
          <NumberInput v-model="formData.alertDate" placeholder="请输入" class="number" :disabled="isDisabled">
            <template #suffix>
              <div>天</div>
            </template></NumberInput
          >
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="通知方式" prop="notificationMethod">
          <el-checkbox-group v-model="formData.notificationMethod" :disabled="isDisabled">
            <el-checkbox :label="checkbox.value" v-for="(checkbox, index) in notification_method" :key="index">{{
              checkbox.label
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="通知发送时间" prop="sendTime">
          <el-time-picker
            v-model="formData.sendTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="请选择"
            :disabled="isDisabled"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 消息模板 -->
    <el-row>
      <el-col :span="24">
        <el-form-item label="消息模板" prop="messageTemplate">
          <el-input
            ref="inputRef"
            v-model="formData.messageTemplate"
            style="margin-bottom: 20px"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            :disabled="isDisabled"
            @blur="blurInput"
            placeholder="请输入"
          >
          </el-input>
          <template v-if="!isDisabled">
            <el-button v-for="(value, key) in templateMap" :key="value" @click="setPlaceholder(value)">{{ key }}</el-button>
          </template>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            placeholder="请输入"
            :disabled="isDisabled"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import NumberInput from '@/components/NumberInput'
import { useDict } from '@/utils/dict'
const { proxy } = getCurrentInstance()
const { notification_method } = proxy.useDict('notification_method')
const templateMap = {
  // 合同名称: 'contractName',
  合同编号: 'contractNo',
  客户名称: 'customerName',
  客户编号: 'customerNo',
  合同结束时间: 'endTime',
  合同类型: 'contractTypeName'
}
// input blur事件触发
let rangeIndex
let inputEl = null
const blurInput = el => {
  console.log('el', el)
  inputEl = el.target
  rangeIndex = el.target.selectionStart
}
// input 光标处插入占位符

const inputRef = ref()
const setPlaceholder = text => {
  console.log('rangeIndex', rangeIndex)
  if (rangeIndex) {
    let oldValue = formData.messageTemplate
    formData.messageTemplate = oldValue.slice(0, rangeIndex) + '${' + text + '}' + oldValue.slice(rangeIndex)
    rangeIndex = rangeIndex + text.toString().length
    inputEl?.focus()
    inputEl?.setSelectionRange(rangeIndex, rangeIndex) //重新定位光标
  } else {
    formData.messageTemplate += '${' + text + '}'
    rangeIndex = formData.messageTemplate.length
    console.log('rangeIndex', rangeIndex)
  }
  inputRef.value.focus()
  // 手动校验
  formRef.value.validateField('messageTemplate')
}
const options = [
  {
    label: '记账合同',
    value: '0'
  },
  {
    label: '一次性合同',
    value: '1'
  },
  {
    label: '地址服务协议合同',
    value: '2'
  }
]

const formData = reactive({
  name: '', // 配置名称
  contractType: '', // 合同类型
  alertDate: undefined, // 预警时间
  // alertStatus: '1', // 默认为正常
  notificationMethod: ['0', '1'],
  sendTime: '',
  messageTemplate: '',
  remark: '',
  contractAlertId: undefined
})

const isDisabled = ref(false)
watch(
  formData,
  () => {
    console.log('formData', formData)
    if (formData.type === 'detail') {
      isDisabled.value = true
    }
  },
  {
    immediate: true
  }
)
const rules = {
  contractType: [{ required: true, message: '请选择', trigger: 'change' }],
  name: [{ required: true, trigger: 'blur', message: '请输入' }],
  alertDate: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入'
    }
  ],
  sort: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入'
    }
  ],
  sendTime: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  messageTemplate: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  notificationMethod: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}

.number {
  flex: 1;
}
</style>
