<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-09-04 09:49:31
 * @LastEditTime: 2023-09-15 09:44:01
 * @LastEditors: thb
-->
<template>
  <div class="middle-left">
    <dataWrap
      ref="wrapRef"
      title="服务客户记账单价"
      :selectOptions="selectOptions"
      :requestApi="getServiceClientMoney"
      @on-select="drawChart"
      :charts="charts"
    >
      <template #default>
        <div ref="lineRef" class="line-chart"></div>
      </template>
    </dataWrap>
  </div>
</template>
<script setup>
import * as echarts from 'echarts'
import { getServiceClientMoney } from '@/api/panel-data/collecting'
import dataWrap from './data-wrap'

const selectOptions = [
  {
    prop: 'year',
    type: 'date-picker',
    defaultValue: new Date().getFullYear() + '',
    props: { type: 'year', value: 'YYYY', valueFormat: 'YYYY' }
  }
]

const wrapRef = ref()
const lineRef = ref()
const charts = ref([])
const drawChart = () => {
  console.log('wrapRef', wrapRef.value.panelData)
  let myChart = echarts.init(lineRef.value)
  charts.value[0] = myChart
  const panelData = wrapRef.value.panelData || []
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['本年度', '上一年度']
    },
    grid: {
      left: '0%',
      top: '12%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },

    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },

    series: [
      {
        type: 'line',

        name: '本年度',
        data: wrapRef.value.panelData.map(item => item.value.count)
      },
      {
        type: 'line',

        name: '上一年度',
        data: wrapRef.value.panelData.map(item => item.value.yoYCount)
      }
    ]
  }
  myChart.setOption(option)
}

onMounted(async () => {
  await wrapRef.value.requestResult
  drawChart()
})
</script>
<style lang="scss" scoped>
.middle-left {
  flex: 1;
  margin-right: 16px;
}
.line-chart {
  flex: 1;
}
</style>
