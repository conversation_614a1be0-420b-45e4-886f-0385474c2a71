<template>
  <div class="data-wrap">
    <div class="wrap-top">
      <top />
    </div>
    <div class="wrap-mid">
      <midLeft />
      <midRight />
    </div>
    <div class="wrap-bottom">
      <bottom />
    </div>
  </div>
</template>
<script setup>
import top from './components/top'
import midLeft from './components/mid-left'
import midRight from './components/mid-right'
import bottom from './components/bottom.vue'
</script>
<style lang="scss" scoped>
.data-wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 16px;
  .wrap-top {
    flex: 1;
    display: flex;
    gap: 16px;
  }
  .wrap-mid {
    flex: 0.5;
    display: flex;
    gap: 16px;
  }
  .wrap-bottom {
    flex: 2;
    display: flex;
  }
}
</style>
