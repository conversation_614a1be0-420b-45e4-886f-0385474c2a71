<!--
 * @Description: 编辑联系人
 * @Author: thb
 * @Date: 2023-08-17 13:20:18
 * @LastEditTime: 2023-08-17 16:19:49
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center width="1200" title="编辑联系人" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <el-button type="primary" @click="handleAddContact">新增</el-button>
    <el-form ref="formRef" :model="formData" label-position="top">
      <Collapse title="联系人" v-for="(item, index) in formData.list" :key="item">
        <template #button>
          <el-button v-if="index !== 0" type="danger" @click.stop="handleDelete(index)">删除</el-button>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item
              label="姓名"
              :prop="`list.` + index + '.contactName'"
              :rules="{
                required: true,
                message: '请输入',
                trigger: 'blur'
              }"
            >
              <el-input v-model="item.contactName" length="20" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="手机号"
              :prop="`list.` + index + '.contactPhone'"
              :rules="{
                required: true,
                trigger: 'blur',
                validator: phoneValidate
              }"
            >
              <el-input v-model="item.contactPhone" length="20" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="关键决策人">
              <el-radio-group v-model="item.isLeader">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item></el-col
          >
          <el-col :span="6">
            <el-form-item label="常用联系人">
              <el-radio-group v-model="item.isOften">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="职位">
              <el-input v-model="item.post" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="微信">
              <el-input v-model="item.wx" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="QQ"
              :prop="`list.` + index + '.qq'"
              :rules="{
                required: false,
                trigger: 'blur',
                validator: FormValidators.qq
              }"
            >
              <el-input v-model="item.qq" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="邮箱"
              :prop="`list.` + index + '.email'"
              :rules="{
                required: false,
                trigger: 'blur',
                validator: FormValidators.email
              }"
            >
              <el-input v-model="item.email" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="性别">
              <el-radio-group v-model="item.sex">
                <el-radio label="0">未知</el-radio>
                <el-radio label="1">男</el-radio>
                <el-radio label="2">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生日">
              <el-date-picker
                v-model="item.birthday"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                type="date"
                placeholder="请选择"
            /></el-form-item>
          </el-col>
        </el-row>
      </Collapse>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave(formRef)">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { getClientContactList, saveBatchClientContactList } from '@/api/material-manage/client'
import Collapse from '@/components/Collapse'
import { FormValidators } from '@/utils/validate'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

// 手机号检验
const phoneValidate = (rules, value, callback) => {
  if (value) {
    FormValidators.mobilePhone(rules, value, callback)
  } else {
    callback(new Error('请输入'))
  }
}

const props = defineProps({
  id: String
})

const formData = ref({
  list: []
})
const formRef = ref()

const getContactList = async () => {
  const { data } = await getClientContactList(props.id)
  formData.value.list = data || []
}

// 新增联系人
const handleAddContact = () => {
  formData.value.list.push({
    contactName: '',
    contactPhone: '',
    isLeader: 0,
    isOften: 0,
    post: '',
    wx: '',
    qq: '',
    email: '',
    sex: '0',
    birthday: ''
  })
}

// 删除联系人
const handleDelete = index => {
  formData.value.list.splice(index, 1)
}

// 批量保存联系人
const { proxy } = getCurrentInstance()
const handleSave = formEl => {
  if (!formEl) return
  formEl.validate(async valid => {
    if (valid) {
      const result = await saveBatchClientContactList({
        ccId: props.id,
        list: formData.value.list
      })
      if (result.code === 200) {
        handleClose()
        proxy.$modal.msgSuccess('保存成功!')
        emits('on-success')
      } else {
        proxy.$modal.msgError('保存失败!')
      }
    } else {
      return false
    }
  })
}
onMounted(() => {
  getContactList()
})
</script>
<style lang="scss" scoped></style>
