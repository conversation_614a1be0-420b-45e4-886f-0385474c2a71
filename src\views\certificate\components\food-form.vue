<template>
  <el-form ref="formRef" :model="data" :rules="rules" label-width="120px" label-position="top">
    <el-row :gutter="24">
      <rowCheck
        v-for="(value, key) in formItemMap"
        :data="data"
        :key="key"
        :label="value"
        :labelKey="key"
        :flagKey="flagMap[key]"
        @on-preview="previewFile"
        @on-load-success="validateFormFiled"
      ></rowCheck>
    </el-row>
  </el-form>
</template>
<script setup>
import { FormValidators } from '@/utils/validate'
import rowCheck from './row-check'

// @ApiModelProperty("平面图")
//     private List<CommonBizFile> planViewFileList;

//     @ApiModelProperty("健康证")
//     private List<CommonBizFile> healthCertificateFileList;

//     @ApiModelProperty("健康证人员身份证")
//     private List<CommonBizFile> healthCertificateIdentityFileList;
defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const formItemMap = {
  copyOfLicenseFileList: '执照副本',
  officialSealFileList: '公章',
  planViewFileList: '平面图',
  healthCertificateFileList: '健康证',
  healthCertificateIdentityFileList: '健康证人员身份证',
  legalIdentityFileList: '法人身份证',
  propertyOwnershipCertificateFileList: '房产证',
  leaseContractFileList: '租赁合同'
}

const flagMap = {
  propertyOwnershipCertificateFileList: 'propertyCertificateFileListFlag'
}
// 劳务派遣表单检验规则
const rules = {
  copyOfLicenseFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  officialSealFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  planViewFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  healthCertificateFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  healthCertificateIdentityFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  legalIdentityFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  propertyOwnershipCertificateFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  leaseContractFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
// 预览文件
const emits = defineEmits('on-preview')
const previewFile = file => {
  emits('on-preview', file)
}
const formRef = ref()

// 文件上传后检验
const validateFormFiled = field => {
  formRef.value.validateField(field)
}
defineExpose({
  formRef,
  rules,
  flagMap
})
</script>
<style lang="scss" scoped>
.el-checkbox {
  margin-right: 8px;
}
:deep(.el-col.el-col-12.is-guttered) {
  display: flex;
  align-items: baseline;
  .el-form-item {
    flex: 1;
  }
}
</style>
