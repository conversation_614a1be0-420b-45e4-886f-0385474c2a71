<template>
  <div class="m-b">
    <span class="font-bold"> 回收规则 </span>
    直接在私海中添加的{{ type === '0' ? '线索' : '客户' }},超过
    <NumberInput class="input-w" v-model="setData.duration">
      <template #suffix> 天 </template>
    </NumberInput>
    未产生跟进、转化，{{ type === '0' ? '线索' : '客户' }}回收至
    <el-select placeholder="请选择公海" clearable v-model="setData.seaId">
      <el-option v-for="item in seaOptions" :key="item.id" :label="item.name" :value="item.id" /> </el-select
    >中
  </div>
  <div>
    <span class="font-bold"> 回收提醒 </span>
    提前
    <NumberInput class="input-w" v-model="setData.recovery">
      <template #suffix> 天 </template>
    </NumberInput>
    提醒线索跟进人员{{ type === '0' ? '线索' : '客户' }}即将被回收
  </div>
  <el-table :data="setData.list">
    <el-table-column label="人员类型">
      <template #default="scope">{{ scope.row.name }} </template>
    </el-table-column>
    <el-table-column :label="`私海${type === '0' ? '线索' : '客户'}上线数`">
      <template #default="scope">
        <NumberInput v-model="scope.row.limit" />
      </template>
    </el-table-column>
    <el-table-column label="状态">
      <template #default="scope">
        <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" />
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup>
import NumberInput from '@/components/NumberInput'
import { getZoneSettingList } from '@/api/material-manage/zone-setting'

const props = defineProps({
  setData: {
    type: Object,
    default: () => {
      return {
        list: [
          {
            name: '员工',
            status: '0'
          },
          {
            name: '管理员',
            status: '0'
          }
        ]
      }
    }
  },
  type: {
    type: String,
    default: '0'
  }
})
const seaOptions = ref([])
const getSeaOptions = async () => {
  const { data } = await getZoneSettingList({
    pageSize: 1000,
    pageNum: 1,
    status: '1',
    type: props.type
  })
  seaOptions.value = data.records || []
}
getSeaOptions()
</script>
<style lang="scss" scoped>
.el-select.el-select--default {
  width: 168px;
}
.input-w {
  width: 150px;
}
.m-b {
  margin-bottom: 24px;
}
.font-bold {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
}
</style>
