import request from '@/utils/request'

// "name": "渠道来源分析",
// "method": "get",
// "path": "/clueAnalyse/channel",
export const clueAnalyseChannel = params => {
  return request({
    url: '/clueAnalyse/channel',
    method: 'get',
    params
  })
}

// "name": "客户介绍来源分析",
// "method": "get",
// "path": "/clueAnalyse/customerIntroduction",
export const clueAnalyseCustomerIntroduction = params => {
  return request({
    url: '/clueAnalyse/customerIntroduction',
    method: 'get',
    params
  })
}

// "name": "直投来源分析",
// "method": "get",
// "path": "/clueAnalyse/direct",
export const clueAnalyseDirect = params => {
  return request({
    url: '/clueAnalyse/direct',
    method: 'get',
    params
  })
}

// "name": "平台来源分析",
// "method": "get",
// "path": "/clueAnalyse/platform",
export const clueAnalysePlatform = params => {
  return request({
    url: '/clueAnalyse/platform',
    method: 'get',
    params
  })
}

// "name": "人员&全部来源分析",
// "method": "get",
// "path": "/clueAnalyse/staff/all",
export const clueAnalyseStaffAll = params => {
  return request({
    url: '/clueAnalyse/staff/all',
    method: 'get',
    params
  })
}

// "name": "人员&渠道来源分析",
// "method": "get",
// "path": "/clueAnalyse/staff/channel",
export const clueAnalyseStaffChannel = params => {
  return request({
    url: '/clueAnalyse/staff/channel',
    method: 'get',
    params
  })
}

// "name": "人员&客户介绍来源分析",
// "method": "get",
// "path": "/clueAnalyse/staff/customerIntroduction",
export const clueAnalyseStaffCustomerIntroduction = params => {
  return request({
    url: '/clueAnalyse/staff/customerIntroduction',
    method: 'get',
    params
  })
}

// "name": "人员&直投来源分析",
// "method": "get",
// "path": "/clueAnalyse/staff/direct",
export const clueAnalyseStaffDirect = params => {
  return request({
    url: '/clueAnalyse/staff/direct',
    method: 'get',
    params
  })
}

// "name": "人员&其他来源分析",
// "method": "get",
// "path": "/clueAnalyse/staff/other",
export const clueAnalyseStaffOther = params => {
  return request({
    url: '/clueAnalyse/staff/other',
    method: 'get',
    params
  })
}

// "name": "人员&平台来源分析",
// "method": "get",
// "path": "/clueAnalyse/staff/platform",
export const clueAnalyseStaffPlatform = params => {
  return request({
    url: '/clueAnalyse/staff/platform',
    method: 'get',
    params
  })
}
