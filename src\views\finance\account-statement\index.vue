<template>
  <div class="main-wrap">
    <div class="card table-search" style="margin-bottom: 10px">
      <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
        <el-row :gutter="0">
          <el-col :span="8">
            <el-form-item label="企业名称" prop="customerName" style="width: 100%; padding-right: 25px">
              <el-input
                v-model="queryParams.customerName"
                placeholder="请输入"
                clearable
                style="width: 100%"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业编码" prop="customerNo" style="width: 100%; padding-right: 25px">
              <el-input
                v-model="queryParams.customerNo"
                placeholder="请输入"
                clearable
                style="width: 100%"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分析月份" prop="month" style="width: 100%; padding-right: 25px">
              <el-date-picker
                v-model="queryParams.monthlyTurnoverStartAndEnd"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                :editable="false"
                :clearable="false"
                type="monthrange"
                placeholder="请选择"
                style="width: 100%"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="queryParams.type === '记账'">
            <el-form-item label="当月营业额" prop="monthlyTurnover" style="width: 100%; padding-right: 25px">
              <el-input
                style="width: 45%"
                v-model="queryParams.monthlyTurnoverMin"
                placeholder="请输入"
                clearable
                @keyup.enter="handleQuery"
              >
                <template #suffix>元</template>
              </el-input>
              <span style="width: 10%; text-align: center">~</span>
              <el-input
                style="width: 45%"
                v-model="queryParams.monthlyTurnoverMax"
                placeholder="请输入"
                clearable
                @keyup.enter="handleQuery"
              >
                <template #suffix>元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="财税顾问" prop="manger" style="width: 100%; padding-right: 25px">
              <el-input
                v-model="queryParams.manger"
                placeholder="请输入"
                clearable
                style="width: 100%"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户成功" prop="customerSuccess" style="width: 100%; padding-right: 25px">
              <el-input
                v-model="queryParams.customerSuccess"
                placeholder="请输入"
                clearable
                style="width: 100%"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="主办会计" prop="sponsorAccounting" style="width: 100%; padding-right: 25px">
              <el-input
                v-model="queryParams.sponsorAccounting"
                placeholder="请输入"
                clearable
                style="width: 100%"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票员" prop="counselor" style="width: 100%; padding-right: 25px">
              <el-input
                v-model="queryParams.counselor"
                placeholder="请输入"
                clearable
                style="width: 100%"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div style="text-align: left">
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="card table-main">
      <div class="action-btns">
        <el-radio-group v-model="queryParams.type" @change="handleChangeRadio">
          <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{
            item.dictLabel
          }}</el-radio-button>
        </el-radio-group>
        <el-button :icon="Download" @click="handleExport" v-hasPermi="['finance:account-statement:export']">导出</el-button>
      </div>
      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="index" width="50" align="center" fixed="left" />
        <el-table-column type="selection" width="55" align="center" fixed="left" />
        <el-table-column label="企业名称" min-width="300" align="center" fixed="left" prop="customerName">
          <template #default="{ row }">
            <span class="blue-text" @click="handlShowAccountStatementDetail(row)">{{ row.customerName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="企业编码" min-width="150" align="center" prop="customerNo" />
        <el-table-column v-if="queryParams.type === '记账'" label="当月营业额" width="100" align="center" prop="monthlyTurnover">
          <template #default="{ row }"> {{ row.monthlyTurnover }} </template>
        </el-table-column>
        <el-table-column
          v-if="queryParams.type === '记账'"
          label="记账营业额"
          width="150"
          align="center"
          prop="bookkeepingMonthlyTurnover"
        >
          <template #default="{ row }"> {{ row.bookkeepingMonthlyTurnover }} </template>
        </el-table-column>
        <el-table-column
          v-if="queryParams.type === '地址'"
          label="地址营业额"
          width="150"
          align="center"
          prop="addressMonthlyTurnover"
        >
          <template #default="{ row }"> {{ row.addressMonthlyTurnover }} </template>
        </el-table-column>
        <el-table-column label="财税顾问" width="100" align="center" prop="manger">
          <template #default="{ row }"> {{ row.manger || '-' }} </template>
        </el-table-column>
        <el-table-column label="客户成功" width="100" align="center" prop="customerSuccess">
          <template #default="{ row }"> {{ row.customerSuccess || '-' }} </template>
        </el-table-column>
        <el-table-column label="主办会计" width="100" align="center" prop="sponsorAccounting">
          <template #default="{ row }"> {{ row.sponsorAccounting || '-' }} </template>
        </el-table-column>
        <el-table-column label="开票员" width="100" align="center" prop="counselor">
          <template #default="{ row }"> {{ row.counselor || '-' }} </template>
        </el-table-column>
        <el-table-column
          width="100"
          :label="item"
          :render-header="renderHeader"
          align="center"
          v-for="(item, index) in monthlyTurnoverListArr"
          :key="index"
          :prop="`monthlyTurnoverList_${index}`"
        >
          <template #default="scope"> {{ scope.row[`monthlyTurnoverList_${index}`] || 0 }} </template>
        </el-table-column>
        <template #append v-if="!dataList.length">
          <slot name="append"> </slot>
        </template>
        <template #empty v-if="!dataList.length">
          <div class="table-empty">
            <slot name="empty">
              <img src="@/assets/images/notData.png" alt="notData" />
              <div>暂无数据</div>
            </slot>
          </div>
        </template>
      </el-table>
    </div>
    <div class="my-pagination">
      <div class="tip-con">
        <div class="line1">
          <span class="t1">已执行金额总计：</span><span class="t2">{{ Number(info.executed).toLocaleString() }} 元</span>
        </div>
        <div class="line1">
          <span class="t1">预收款总计：</span><span class="t2">{{ Number(info.total - info.executed).toLocaleString() }}元</span>
        </div>
      </div>
      <Pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>
  </div>
  <accountStatementDetail
    v-if="accountStatementDetailShow"
    :customerNo="customerNo"
    :monthlyTurnoverList="monthlyTurnoverList"
    :monthlyTurnover="monthlyTurnover"
    :bookkeepingMonthlyTurnover="bookkeepingMonthlyTurnover"
    :addressMonthlyTurnover="addressMonthlyTurnover"
    @on-close="handleCloseAccountStatementDetail"
    @on-list="getList"
  />
</template>

<script setup lang="jsx">
import {
  turnoverStatementGetMonthlyTurnoverDetail,
  turnoverStatementGetMonthlyTurnoverDetailExport,
  getMonthlySalesBreakdown
} from '@/api/finance/account-statement'
import { deptTreeSelect } from '@/api/system/user'
import accountStatementDetail from '@/views/finance/account-statement/components/detail.vue'
import dayjs from 'dayjs'
import { onMounted } from 'vue'
import { multiply, divide } from '@/utils/math'
import { CirclePlus, Upload, Download } from '@element-plus/icons-vue'

const tabs = [
  { dicValue: '记账', dictLabel: '记账营业额明细' },
  { dicValue: '地址', dictLabel: '地址营业额明细' }
]

const { proxy } = getCurrentInstance()

const dataList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const initType = { type: '记账' }
const initQueryParams = {
  pageNum: 1,
  pageSize: 10,
  /* 月营业额明细 start */
  customerName: undefined,
  customerNo: undefined,
  salesmanName: undefined,
  monthlyTurnoverStartAndEnd: undefined,
  monthlyTurnoverStart: undefined,
  startDate: dayjs().month(0).format('YYYY-MM'),
  monthlyTurnoverEnd: undefined,
  endDate: dayjs().month(11).format('YYYY-MM'),
  monthlyTurnoverMin: undefined,
  monthlyTurnoverMax: undefined
  /* 月营业额明细 end */
}

const data = reactive({
  queryParams: Object.assign({}, initType, initQueryParams)
})

const { queryParams } = toRefs(data)

const monthlyTurnoverListArr = ref([])
const numArr = ref([])
const info = ref({})
const getList = async () => {
  console.log('getList', queryParams.value.type)
  loading.value = true
  const response = await turnoverStatementGetMonthlyTurnoverDetail({ ...queryParams.value, bizType: queryParams.value.type })
  if (response.data.records.length) {
    monthlyTurnoverListArr.value = []
    response.data.records[0]?.monthlyTurnoverList?.map(item => monthlyTurnoverListArr.value.push(item.date))
    // console.log('monthlyTurnoverListArr.value', monthlyTurnoverListArr.value)
    response.data.records.map(item => {
      item.monthlyTurnoverList.map((item2, index2) => {
        item['monthlyTurnoverList_' + index2] = item2.amount
      })
    })
  }
  dataList.value = response.data.records
  console.log('dataList', dataList.value)
  // console.log('dataList.value', dataList.value)
  total.value = parseInt(response.data.total)
  loading.value = false
  const response_2 = await getMonthlySalesBreakdown(queryParams.value)
  numArr.value = response_2.data.monthly
  info.value = {
    executed: response_2.data.executed,
    total: response_2.data.total
  }
}

let column_no = 0 // 作为标记，用来从接口中得知这个表格的动态列从哪一列开始渲染
const renderHeader = ({ column, $index }) => {
  // console.log('renderHeader', column, $index)
  // console.log('column', column)
  // console.log('renderHeader', numArr.value, column.no)
  // console.log('column_no-1', column_no)
  if (!column_no) column_no = column.no
  // console.log('column_no-2', column_no)
  if (!numArr.value.length || column.no < column_no) return
  const num = numArr.value[column.no - column_no]?.value
  // console.log('num', num)
  return (
    <div style="font-size:14px">
      <div>{column.label}</div> <div>{(num?.addressMonthTotal + num?.bookkeepingMonthTotal).toLocaleString()}元</div>
    </div>
  )
}

// 根据节点名称获取树结构的节点
const getTreeNodeByName = (treeData, name) => {
  for (let i = 0; i < treeData.length; i++) {
    if (treeData[i].label === name) {
      return treeData[i] //名称
    } else if (treeData[i].children) {
      const result = getTreeNodeByName(treeData[i].children, name)
      if (result) {
        return result
      }
    }
  }
  return null
}

function handleChangeRadio(value) {
  queryParams.value.monthlyTurnoverMin = undefined
  queryParams.value.monthlyTurnoverMax = undefined
  queryParams.value.pageNum = 1
  handleQuery(value)
}

/** 搜索按钮操作 */
async function handleQuery(value) {
  // console.log('handleQuery')
  queryParams.value.pageNum = 1
  if (queryParams.value.monthlyTurnoverStartAndEnd && queryParams.value.monthlyTurnoverStartAndEnd[0]) {
    queryParams.value.monthlyTurnoverStart = dayjs(queryParams.value.monthlyTurnoverStartAndEnd[0]).format('YYYY-MM')
    queryParams.value.startDate = dayjs(queryParams.value.monthlyTurnoverStartAndEnd[0]).format('YYYY-MM')
    queryParams.value.monthlyTurnoverEnd = dayjs(queryParams.value.monthlyTurnoverStartAndEnd[1]).format('YYYY-MM')
    queryParams.value.endDate = dayjs(queryParams.value.monthlyTurnoverStartAndEnd[1]).format('YYYY-MM')
  } else {
    queryParams.value.monthlyTurnoverStart = undefined
    queryParams.value.startDate = dayjs().month(0).format('YYYY-MM')
    queryParams.value.monthlyTurnoverEnd = undefined
    queryParams.value.endDate = dayjs().month(11).format('YYYY-MM')
  }
  column_no = 0
  getList()
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  queryParams.value = Object.assign(queryParams.value, initQueryParams)
  handleQuery()
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.postId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

const customerNo = ref('')
let monthlyTurnoverList = []
let monthlyTurnover = ''
let bookkeepingMonthlyTurnover = ''
let addressMonthlyTurnover = ''
/* 客户详情弹窗 ---start--- */
const accountStatementDetailShow = ref(false)
const handlShowAccountStatementDetail = row => {
  customerNo.value = row.customerNo
  monthlyTurnoverList = row.monthlyTurnoverList.concat()
  // monthlyTurnoverList = row.monthlyTurnoverList.concat(row.monthlyTurnoverList)
  monthlyTurnover = row.monthlyTurnover
  bookkeepingMonthlyTurnover = row.bookkeepingMonthlyTurnover
  addressMonthlyTurnover = row.addressMonthlyTurnover
  accountStatementDetailShow.value = true
}
const handleCloseAccountStatementDetail = () => {
  accountStatementDetailShow.value = false
}
/* 客户详情弹窗 ---end--- */

// 导出列表功能
const handleExport = async () => {
  const result = await turnoverStatementGetMonthlyTurnoverDetailExport({
    ...queryParams.value
  })
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}

getList()
onMounted(() => {})
</script>
<style lang="scss" scoped>
// .my-table {
//   min-height: 300px;
//   display: flex;
//   .el-table__append-wrapper {
//   }
// }
:deep(.el-table__append-wrapper) {
  // todo 无数据时候塌陷的问题
  min-height: 110px;
}
.table-main {
  border-radius: 0;
}
.my-pagination {
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .tip-con {
    padding-left: 35px;
    flex: 1 1 auto;
    .line1 {
      margin-bottom: 5px;
      display: flex;
      .t1-con {
        display: inline-block;
        width: 250px;
        margin-right: 10px;
      }
      .t1 {
        color: #409eff;
      }
    }
  }
  :deep(.pagination-container) {
    height: 50px;
    margin-top: 10px;
    width: 1000px;
    position: relative;
    .el-pagination {
      right: 40px;
    }
  }
}
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}

.action-btns {
  display: flex;
  justify-content: space-between;
}
</style>
