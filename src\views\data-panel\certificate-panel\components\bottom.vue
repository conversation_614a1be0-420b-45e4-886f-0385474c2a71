<!--
 * @Description: 办证效率
 * @Author: thb
 * @Date: 2023-09-11 09:11:22
 * @LastEditTime: 2023-09-15 16:52:51
 * @LastEditors: thb
-->
<template>
  <dataWrap
    class="bottom"
    title="办证效率"
    ref="wrapRef"
    :selectOptions="selectOptions"
    :request-api="getCertificateHandlingEfficiency"
    @on-select="drawBarChart"
    :charts="charts"
  >
    <template #default>
      <div class="bar-chart" ref="barRef"></div>
    </template>
  </dataWrap>
</template>
<script setup>
import dataWrap from '../../collecting-panel/components/data-wrap'
import { getCertificateHandlingEfficiency } from '@/api/panel-data/certificate'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
const selectOptions = [
  {
    prop: 'yearMonth',
    type: 'date-picker',
    defaultValue: dayjs().format('YYYY-MM'),
    props: { type: 'month', value: 'YYYY-MM', valueFormat: 'YYYY-MM' }
  }
]
const wrapRef = ref()
const barRef = ref()
const charts = ref([])
const drawBarChart = () => {
  let myChart = echarts.init(barRef.value)
  charts.value[0] = myChart
  const data = wrapRef.value.panelData || []
  const newRegister = []
  const newData = data.filter(item => item.name === '新注册')[0] || {
    value: {
      count: 0,
      moMCount: 0,
      yoYCount: 0
    }
  }
  newRegister.push('新注册', newData.value?.count || 0, newData.value?.moMCount || 0, newData.value?.yoYCount || 0)
  const change = []
  const changeData = data.filter(item => item.name === '变更')[0] || {
    value: {
      count: 0,
      moMCount: 0,
      yoYCount: 0
    }
  }
  change.push('变更', changeData?.value?.count || 0, changeData?.value?.moMCount || 0, changeData?.value?.yoYCount || 0)

  const change1 = []
  const change1Data = data.filter(item => item.name === '跨区变更')[0] || {
    value: {
      count: 0,
      moMCount: 0,
      yoYCount: 0
    }
  }
  change1.push('跨区变更', change1Data.value?.count || 0, change1Data.value?.moMCount || 0, change1Data.value?.yoYCount || 0)
  const change2 = []
  const change2Data = data.filter(item => item.name === '工商变更')[0] || {
    value: {
      count: 0,
      moMCount: 0,
      yoYCount: 0
    }
  }
  change2.push('工商变更', change2Data.value?.count || 0, change2Data.value?.moMCount || 0, change2Data.value?.yoYCount || 0)
  const cancel = []
  const cancelData = data.filter(item => item.name === '注销')[0] || {
    value: {
      count: 0,
      moMCount: 0,
      yoYCount: 0
    }
  }
  cancel.push('注销', cancelData.value?.count || 0, cancelData.value?.moMCount || 0, cancelData.value?.yoYCount || 0)
  const option = {
    legend: {},
    tooltip: {},
    color: ['#5B8FF9FF', '#5AD8A6FF', '#5D7092FF'],
    grid: {
      left: '2%',
      right: '2%',
      bottom: '10%'
    },
    dataset: {
      source: [['product', '当前月', '环比', '同比'], newRegister, change, change1, change2, cancel]
    },
    xAxis: { type: 'category' },
    yAxis: {},
    series: [
      {
        type: 'bar'
      },
      { type: 'bar' },
      { type: 'bar' }
    ]
  }
  myChart.setOption(option)
}
onMounted(async () => {
  await wrapRef.value.requestResult
  drawBarChart()
})
</script>
<style lang="scss" scoped>
.bottom {
  flex: 1;
}

.bar-chart {
  flex: 1;
}
</style>
