<template>
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" @edit="handleEditSelf" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import formModal from '@/views/customer/risk-audit/components/form-modal.vue'

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)
const formModalClose = () => {
  formModalShow.value = false
}
const handleAdd = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onAdd()
  })
}
const handleDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}

// ------------main弹窗Self---------------
const formModalRefSelf = ref()
const formModalShowSelf = ref(false)
const formModalCloseSelf = () => {
  formModalShowSelf.value = false
}
const handleEditSelf = (data: any) => {
  console.log('data', data)
  formModalShowSelf.value = true
  nextTick(() => {
    formModalRefSelf.value.onEdit()
  })
}

</script>
<style lang="scss" scoped></style>
