<!--
 * @Description: 资料上传form 表单
 * @Author: thb
 * @Date: 2023-09-28 15:24:07
 * @LastEditTime: 2023-12-15 08:42:38
 * @LastEditors: thb
-->
<template>
  <el-form
    ref="formRef"
    :hide-required-asterisk="disabled"
    :disabled="disabled"
    :model="data"
    :rules="rules"
    label-width="120px"
    label-position="top"
  >
    <el-row :gutter="24">
      <rowCheck
        v-for="(value, key) in formItemMap"
        :data="data"
        :key="key"
        :label="value"
        :labelKey="key"
        :flagKey="flagMap[key]"
        @on-preview="previewFile"
        @on-load-success="validateFormFiled"
      ></rowCheck>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-checkbox v-model="data.allHandoverFlag" />
        <el-form-item label="请确认资料已全部交接至客户处，提交后将删除企业的所有资料库存" prop="allHandoverFlag"> </el-form-item>
      </el-col>
    </el-row>
    <Collapse title="银行注销">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="客户是否需要进行注销" prop="bankCancellationFlag">
            <el-radio-group v-model="data.bankCancellationFlag" @change="clearUserId">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="data.bankCancellationFlag">
          <el-form-item label="银行注销派工" prop="bankHandleUserId">
            <SelectTree v-model="data.bankHandleUserId" placeholder="请选择" clearable @on-node-click="handleNodeClick" />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
  </el-form>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import iFrame from '@/components/iFrame'
import rowCheck from './row-check'
import Collapse from '@/components/Collapse'
import SelectTree from '@/components/SelectTree'
const props = defineProps({
  data: Object
})

const disabled = inject('disabled')

const formItemMap = {
  cancellationCertificateFileList: '注销证明证',
  handoverDocumentFileList: '交接单身份证'
}

const flagMap = {
  cancellationCertificateFileList: 'cancellationCertificateFlag',
  handoverDocumentFileList: 'handoverDocumentFlag'
}

const handleNodeClick = node => {
  props.data.bankHandleUserId = node.value
  props.data.bankHandleUserName = node.label
}
const clearUserId = () => {
  props.data.bankHandleUserId = ''
  props.data.bankHandleUserName = ''
}
// 许可证资料上传校验规则
const rules = {
  cancellationCertificateFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  handoverDocumentFileList: [
    {
      required: false,
      message: '请选择',
      trigger: ['change']
    }
  ],
  allHandoverFlag: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  bankHandleUserId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

const previewShow = ref(false)
const previewUrl = ref('')
const previewFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

// 文件上传后检验
const validateFormFiled = field => {
  formRef.value.validateField(field)
}

const formRef = ref()
const { proxy } = getCurrentInstance()
const validateForm = async () => {
  // 提交检验之前需要清除表单的提交校验
  formRef.value.clearValidate()
  if (!props.data.cancellationCertificateFlag) {
    proxy.$message.warning('打*项请打上勾再进行提交!')
    return false
  }

  if (!props.data.allHandoverFlag) {
    proxy.$message.warning('打*项请打上勾再进行提交!')
    return false
  }

  if (props.data.bankCancellationFlag && !props.data.bankHandleUserId) {
    formRef.value.validateField('bankHandleUserId')
    return false
  }

  return true
}

const validateCheckedForm = async () => {
  return true
}

defineExpose({
  validateForm,
  validateCheckedForm
})
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  display: block;
}

:deep(.el-checkbox) {
  margin-right: 8px;
}
:deep(.el-col.el-col-12.is-guttered) {
  display: flex;
  align-items: baseline;
  .el-form-item {
    flex: 1;
  }
}
</style>
