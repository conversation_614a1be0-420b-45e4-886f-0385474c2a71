<template>
  <ProTable
    ref="proTable"
    title="工单类型设置"
    :columns="columns"
    row-key="id"
    :request-api="getOrderList"
    :dataCallback="dataCallback"
    :pagination="false"
  >
    <template #sort="{ row }">
      <span>
        {{ row.sort }}
      </span>
    </template>
    <template #status="{ row }">
      <span :class="[row.status === '1' ? 'success-status' : 'danger-status']">
        {{ row.status === '1' ? '正常' : '禁用' }}
      </span>
    </template>
    <!-- 操作 -->
    <template #action="{ row }">
      <el-button type="primary" link @click="handleAdd(row)">新增</el-button>
      <!-- <el-button link type="primary" @click="handelDetail(row)">详情</el-button> -->
      <el-button link type="primary" @click="handelEdit(row)">编辑</el-button>
      <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAddOrder">新增</el-button>
    </template>
  </ProTable>
</template>
<script setup lang="tsx">
import { ColumnProps } from '@/components/ProTable/interface'
import { getOrderList, deleteOrder, getOrderDetail, addOrder, changeOrderTypeStatus } from '@/api/work/work'
import { CirclePlus } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialog'
import orderForm from './components/order-form'
import { useHandleData } from '@/hooks/useHandleData'
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '序号',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'action',
    fixed: 'right',
    label: '操作'
  },
  {
    prop: 'typeName',
    label: '工单类型',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'sort',
    label: '排序'
  },
  {
    prop: 'remark',
    label: '备注'
  },
  {
    prop: 'status',
    enum: [
      {
        label: '正常',
        value: '1'
      },
      {
        label: '停用',
        value: '0'
      }
    ],
    search: {
      el: 'select'
    },
    render: scope => {
      return (
        <>
          {
            <el-switch
              model-value={scope.row.status}
              active-text={scope.row.status === '1' ? '正常' : '停用'}
              active-value={'1'}
              inactive-value={'0'}
              onClick={() => changeStatus(scope.row)}
            />
          }
        </>
      )
    },
    label: '状态'
  },

  {
    prop: 'updateTime',
    label: '修改时间'
  }
]

const proTable = ref()
const { showDialog } = useDialog()
const handleSubmitCallback = () => {
  proTable.value?.getTableList()
}
function renameChildToChildren(items: any) {
  return items.map((item: any) => {
    if (item.child) {
      return {
        ...item,
        children: renameChildToChildren(item.child),
        child: undefined
      }
    }
    return item
  })
}
const dataCallback = (data: any) => {
  if (data) {
    data = renameChildToChildren(data)
    console.log('debuggerdata', data)
    return data
  }
}

// 改变状态
const changeStatus = async (row: any) => {
  await useHandleData(changeOrderTypeStatus, row.id, `切换【${row.typeName}】工单类型状态`)
  proTable.value?.getTableList()
}
const handleAdd = row => {
  showDialog({
    title: '新增',
    customClass: 'order-modal',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: orderForm, // 表单组件
    handleConvertParams: data => {
      data.parentId = row.id
    },
    submitApi: addOrder, // 提交api
    submitCallback: handleSubmitCallback // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 新增工单
const handleAddOrder = () => {
  showDialog({
    title: '新增',
    customClass: 'order-modal',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: orderForm, // 表单组件
    submitApi: addOrder, // 提交api
    submitCallback: handleSubmitCallback // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 编辑工单
const handelEdit = row => {
  showDialog({
    title: '编辑',
    customClass: 'order-modal',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: orderForm,
    submitApi: addOrder,
    getApi: getOrderDetail,
    handleConvertParams: data => {
      data.parentId = row.parentId
    },
    requestParams: row.id,
    submitCallback: handleSubmitCallback // 提交成功之后的回调函数
    // handleRevertParams: handleRevertProductParams // 处理提交参数
  })
}
// 删除工单
const handleDelete = async (row: any) => {
  await useHandleData(deleteOrder, row.id, `删除所选工单类型 ${row.typeName} `)
  proTable.value?.getTableList()
}

const handleConvertParams = data => {
  data.type = 'detail'
}
// 查看详情
const handelDetail = row => {
  showDialog({
    title: '详情',
    customClass: 'order-modal',
    cancelButtonText: '关闭',
    showConfirmButton: false,
    // confirmButtonText: '保存',
    component: orderForm,
    submitApi: addOrder,
    getApi: getOrderDetail,
    requestParams: row.id,
    handleConvertParams
    // submitCallback: handleSubmitCallback // 提交成功之后的回调函数
    // handleRevertParams: handleRevertProductParams // 处理提交参数
  })
}
</script>
<style lang="scss" scoped>
.success-status {
  color: #409eff;
}
.danger-status {
  color: #f56c6c;
}
</style>
