<!--
 * @Description: 废弃客户弹窗
 * @Author: thb
 * @Date: 2023-05-30 09:56:01
 * @LastEditTime: 2023-11-07 15:18:12
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    width="600"
    :title="type === 'edit' ? '模板审批' : '审批详情'"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="模板名称" prop="tempName">
            <el-input v-model="formData.tempName" disabled />
            <!-- 预览模板功能 -->
            <span class="blue-text" v-if="type === 'edit'" @click="templateShow = true">预览模板</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同类型" prop="contractType">
            <el-select v-model="formData.contractType" disabled placeholder=" ">
              <el-option :label="item.label" :value="item.value" v-for="(item, index) in typeList" :key="index" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="创建人" prop="createBy">
            <el-input v-model="formData.createBy" disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24" v-if="type === 'detail' && formData.reason">
        <el-col :span="24">
          <el-form-item label="驳回原因" prop="reason">
            <el-input v-model="formData.reason" disabled type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="type === 'edit'">
        <el-form-item label="通过" prop="reviewStatus">
          <el-radio-group v-model="formData.reviewStatus">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">驳回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="驳回原因" prop="reason" v-if="formData.reviewStatus === '2'">
          <el-input v-model="formData.reason" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
        </el-form-item>
      </template>
    </el-form>

    <!-- 审批流程 -->
    <processSet
      v-if="formData.reviewList?.length"
      type="detail"
      :isReview="true"
      :isHorizontal="true"
      :createTime="formData.createTime"
      v-model="formData.reviewList"
    />
    <template #footer>
      <template v-if="type === 'edit'">
        <el-button type="primary" @click="handleSubmit(formRef)"> 保存</el-button>
      </template>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 模板预览 -->
  <el-dialog align-center title="模板预览" :close-on-click-modal="false" v-model="templateShow">
    <p v-html="formData.htmlStr"></p>
  </el-dialog>
</template>
<script setup>
import { getContractTempGetById, reviewTemplateAudit } from '@/api/contract-template/contract-template'
import processSet from '@/views/process-manage/contract-review/components/process-set.vue'
const visible = ref(true)
const templateShow = ref(false)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number,
  type: {
    type: String,
    default: 'detail'
  }
})

const typeList = [
  {
    label: '记账合同',
    value: '0'
  },
  {
    label: '一次性合同',
    value: '1'
  },
  {
    label: '地址服务协议合同',
    value: '2'
  }
]

const formData = ref({})

const rules = {
  reason: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ]
}
const formRef = ref()
const { proxy } = getCurrentInstance()
const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      const result = await reviewTemplateAudit({
        reviewStatus: formData.value.reviewStatus,
        reason: formData.value.reason,
        mainId: props.id, // 模板id
        type: '0'
      })
      if (result.code === 200) {
        proxy.$modal.msgSuccess(`保存成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`保存失败!`)
      }
    } else {
    }
  })
}
const getTempDetail = async id => {
  const { data } = await getContractTempGetById({
    id
  })
  formData.value = data || {}
  if (props.type === 'edit') {
    formData.value.reviewStatus = '1'
  }

  // 获取驳回原因
  if (Array.isArray(data.reviewList) && data.reviewList.length) {
    const filtered = data.reviewList.filter(item => item.reviewStatus === '2')
    formData.value.reason = filtered[0]?.reason
    console.log('reason', filtered, filtered[0]?.reason)
  }
}
onMounted(() => {
  if (props.id) {
    // 获取模板详情
    getTempDetail(props.id)
  }
})
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
