<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="金额" prop="amount">
          <!-- <el-input-number style="width: 100%" :min="0" v-model="formData.amount" placeholder="请输入"></el-input-number> -->
          <NumberInput v-model="formData.amount" maxlength="10" placeholder="请输入">
            <template #suffix> 元 </template>
          </NumberInput>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="充值时间" prop="chargeTime">
          <el-date-picker
            style="width: 100%"
            v-model="formData.chargeTime"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder="请选择"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import NumberInput from '@/components/NumberInput'

const formData = reactive({
  mainId: undefined,
  biz: '直投'
})

const rules = {
  amount: [{ required: true, message: '请输入', trigger: 'blur' }],
  chargeTime: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped></style>
