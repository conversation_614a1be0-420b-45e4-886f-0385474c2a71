<template>
  <!-- :request-api="bizList" -->
  <div class="main-wrap">
    <ProTable
      v-if="show"
      ref="proTable"
      title="列表"
      :init-param="initParam"
      :columns="columns"
      :toolButton="false"
      rowKey="id"
      :request-api="bizList"
      :dataCallback="dataCallback"
      @on-change="selectChange"
      @sort-change="sortChange"
      :transformRequestParams="transformRequestParams"
    >
      <template #tabs>
        <el-radio-group :model-value="initParam.bizStatus" @change="handleRadioChange">
          <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group></template
      >
      <template #customerName="{ row }">
        <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
      </template>
      <template #code="{ row }">
        <span class="blue-text" @click="handleDetail(row)">{{ row.code }}</span>
        <!-- <span>-{{ row.id }}</span> -->
      </template>
      <template #contractNo="{ row }">
        <span class="blue-text" @click="handlShowContractDetail(row.contractId)">{{ row.contractNo }}</span>
      </template>

      <template #tableHeader>
        <!-- 增加列表导出功能 -->
        <el-button :icon="Download" @click="handleExport">导出</el-button>
      </template>
    </ProTable>
  </div>
  <formModal ref="formModalRef" actionType="todo" :productName="productName" @ok="getList" />
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <contractDetail v-if="contractDetailShow" :id="contractId" :hideActionBtn="true" @on-close="contractDetailShow = false" />
  <fromModalNew v-if="formShow" actionType="todo" :productName="productName" :id="id" @on-close="handleClose" />
</template>

<script setup lang="tsx">
import { bizList, certificateExport } from '@/api/certificate/certificate'
import { ref, reactive, nextTick, onMounted } from 'vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { useRoute, useRouter } from 'vue-router'
import formModal from './components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import contractDetail from '@/views/contract-manage/contract-list/components/contract-detail.vue'
import { bizStageArr, bizTypeArr, bizStatusArr } from '@/utils/constants'
import { getReviewerTreeData } from '@/api/process/process'
import fromModalNew from './components/form-modal-new'
import { Download } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
dayjs.extend(duration)

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const initParam = reactive({
  bizStatus: 'processing'
})

const getTreeData = () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await getReviewerTreeData()
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}
const tabs = [
  {
    label: '全部',
    value: ''
  }
].concat(bizStatusArr)
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'code',
    label: '流程编号',
    fixed: 'left',
    minWidth: '200',
    search: { el: 'input' }
  },
  {
    prop: 'bizTypeList',
    label: '业务名称',
    minWidth: '200',
    enum: bizTypeArr,
    render: scope => {
      let label = bizTypeArr.filter(item => item.value === scope.row.bizType)[0]?.label
      if (scope.row.changeSubject) {
        label = label + `(${scope.row.changeSubject} )`
      }

      if (scope.row.productName?.includes('单办证')) {
        label = label + `(单办证)`
      }
      return <span>{label || '--'}</span>
    },
    search: {
      el: 'select',
      props: {
        multiple: true
      }
    },
    sortable: 'custom',
    sortName: 'task.biz_type'
  },
  {
    prop: 'customerName',
    label: '企业名称',
    minWidth: '200',
    search: { el: 'input', order: 1 },
    sortable: 'custom',
    sortName: 'customer.customer_name'
  },
  {
    prop: 'customerNo',
    label: '企业编号',
    minWidth: '200',
    search: { el: 'input' },
    sortable: 'custom',
    sortName: 'customer.customer_no'
  },
  {
    prop: 'noHandleUserFlag',
    width: 150,
    label: '当前指派给',
    isShow: false,
    enum: [
      {
        value: true,
        label: '待派工'
      },
      {
        value: false,
        label: '在人员中选择'
      }
    ],
    search: {
      el: 'select'
    }
  },
  {
    prop: 'handleUserId',
    width: 150,
    label: '人员选择',
    enum: getTreeData,
    isShow: false,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    }
  },
  {
    prop: 'handleUserName',
    label: '当前办理人',
    minWidth: '200',
    sortable: 'custom',
    sortName: 'handle_user.nick_name'
  },
  {
    prop: 'counselorName',
    label: '财税顾问',
    minWidth: '200',
    enum: getTreeData,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'label',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'label',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.counselorName || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'counselor_user.nick_name'
  },
  {
    prop: 'creatorName',
    label: '创建人',
    minWidth: '200',
    enum: getTreeData,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'label',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'label',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.creatorName || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'create_user.nick_name'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    minWidth: '200',
    sortable: 'custom',
    sortName: 'task.create_time'
  },
  {
    prop: 'updateTime',
    label: '最近更新时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    minWidth: '200',
    sortable: 'custom',
    sortName: 'task.update_time'
  },
  // 完成用时
  {
    prop: 'days',
    label: '完成用时',
    minWidth: '200',
    render: scope => {
      return <span>{scope.row.days ? `${scope.row.days}天` : '--'}</span>
    }
  },
  {
    prop: 'contractNo',
    label: '关联合同',
    minWidth: '200'
  },
  {
    prop: 'bizStage',
    label: '办理阶段',
    enum: bizStageArr,
    fixed: 'right',
    minWidth: '200'
  }
]

// 前端：【显示】风险客户：风险审核阶段 显示各步骤名称及步骤用时
const dataCallback = (data: any) => {
  if (data) {
    // console.log('dataCallback-data', data)
    data.records.forEach(item => {
      // console.log('item', item, item.processRecordVO)
      for (let itemx in item.processRecordVO) {
        const _time = parseInt(item.processRecordVO[itemx])
        // console.log('item.processRecordVO[itemx]', item.processRecordVO[itemx])
        // console.log('_time', _time)
        if (_time) {
          const __time = dayjs.duration(_time, 'seconds')
          item.processRecordVO[itemx] = `${__time.days()}天${__time.hours()}小时`
        }
      }
      Object.assign(item, item.processRecordVO)
    })
    return data
  }
}

// 前端：【功能】待办业务：办证流程显示办理步骤，每步办理人员及用时
const stageNum = [
  '',
  '一',
  '二',
  '三',
  '四',
  '五',
  '六',
  '七',
  '八',
  '九',
  '十',
  '十一',
  '十二',
  '十三',
  '十四',
  '十五',
  '十六',
  '十七',
  '十八',
  '十九',
  '二十'
]
const show = ref(false)
bizList().then((res: any) => {
  show.value = true
  if (res.code === 200 && res.data?.records?.[0]?.processRecordVO) {
    let stageVO = {}
    let processRecordVO = res.data.records[0].processRecordVO
    let keys = Object.keys(processRecordVO).slice(0, 10)
    keys.forEach(key => {
      stageVO[key] = processRecordVO[key]
    })
    // console.log('stageVO', stageVO)
    if (stageVO) {
      for (const item in stageVO) {
        // console.log('item', item)
        columns.splice(-1, 0, {
          prop: item,
          label: `步骤${stageNum[item.replace('Time', '').replace('stage', '')]}${item.includes('Time') ? '用时' : ''}`,
          width: 150
        })
      }
      // console.log('columns', columns)
    }
  }
})

// 自定义
const transformRequestParams = (data: any) => {
  // 创建时间
  if (data.createTime) {
    data.startCreateTime = data.createTime[0]
    data.endCreateTime = data.createTime[1]
  }

  if (data.updateTime) {
    data.startUpdateTime = data.updateTime[0]
    data.endUpdateTime = data.updateTime[1]
  }
}

const proTable = ref()
function getList() {
  proTable.value?.getTableList()
}

function handleRadioChange(e: any) {
  proTable.value.pageable.pageNum = 1
  initParam.bizStatus = e
}

const formModalRef = ref()
const formShow = ref(false)
const id = ref()
const productName = ref('')
function handleDetail(row: any) {
  productName.value = row.productName
  const types = ['domestic_business_registration', 'foreign_business_registration', 'bank_account_open']
  if (types.includes(row.bizType)) {
    formModalRef.value?.onShow(row)
  } else {
    id.value = row.id
    formShow.value = true
  }
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
}
/* 客户详情弹窗 ---end--- */

/* 合同详情弹窗 ---start--- */
const contractDetailShow = ref(false)
const contractId = ref()
const handlShowContractDetail = (id: number) => {
  contractDetailShow.value = true
  contractId.value = id
}
/* 合同弹窗 ---end--- */
const handleClose = () => {
  formShow.value = false
  getList()
}

// 搜索区域条件改变触发该事件
const selectChange = (value, key) => {
  if (key === 'noHandleUserFlag' && value) {
    proTable.value.searchParam.handleUserId = ''
  }
  if (key === 'handleUserId' && value) {
    proTable.value.searchParam.noHandleUserFlag = false
  }
}

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// 导出
const handleExport = async () => {
  const result = await certificateExport({
    ...proTable.value.searchParam,
    ...initParam
  })

  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}

.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
