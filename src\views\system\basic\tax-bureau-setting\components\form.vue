<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="上级菜单" prop="parentId">
          <el-tree-select
            v-model="formData.parentId"
            :default-expanded-keys="[0]"
            :data="menuOptions"
            :props="{
              value: 'id',
              label: 'name',
              children: 'child'
            }"
            value-key="id"
            placeholder="选择上级菜单"
            check-strictly
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="税务局名称" prop="name">
          <el-input v-model="formData.name" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="显示排序" prop="sort">
          <el-input-number style="width: 100%" v-model="formData.sort" class="mx-4" :min="1" controls-position="right" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="税务局地址" prop="address">
          <el-input style="width: 100%" v-model="formData.address" maxlength="100" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="位置选择" prop="lonlat">
          <el-input style="width: 100%" v-model="formData.lonlat" placeholder="请输入">
            <template #append>
              <div @click="handleMapSelectShow" style="cursor: pointer">
                <el-icon><MapLocation /></el-icon>
              </div>
            </template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <SelectMap
    v-model:visible="mapSelectShow"
    :address="{ name: formData.name, address: formData.address }"
    @close="mapSelectShow = false"
    @get-location="getLocation"
  />
</template>

<script setup>
import { getTaxTreeList } from '@/api/basicData/basicData'
import SelectMap from '@/components/Tools/select-map.vue'
import { reactive, watch } from 'vue'
const formData = reactive({
  parentId: undefined,
  name: '',
  sort: '',
  // enable: '1', // 默认为正常
  remark: '',
  id: undefined,
  address: undefined,
  lonlat: undefined,
  lng: undefined,
  lat: undefined
})

const rules = {
  parentId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  name: [{ required: true, trigger: 'blur', message: '请输入' }],
  sort: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入'
    }
  ]
}
const menuOptions = ref([])

/** 查询菜单下拉树结构 */
const getTreeselect = () => {
  getTaxTreeList().then(res => {
    const temp = [
      {
        id: '0',
        name: '全部',
        child: res.data
      }
    ]
    menuOptions.value = temp
  })
}

getTreeselect()

// 地址地图选择
const mapSelectShow = ref(false)
const handleMapSelectShow = () => {
  mapSelectShow.value = true
}
const getLocation = data => {
  // 设置位置以及经纬度
  const [lng, lat] = data.lonlat.split(',')
  formData.lonlat = data.lonlat
  formData.lng = Number(lng)
  formData.lat = Number(lat)
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})

watch(
  () => formData.id,
  val => {
    if (val) {
      if (formData.lng && formData.lat) {
        formData.lonlat = [formData.lng, formData.lat].join(',')
      }
    }
  }
)
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
