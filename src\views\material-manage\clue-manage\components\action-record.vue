<!--
 * @Description: 操作记录
 * @Author: thb
 * @Date: 2023-08-14 09:07:19
 * @LastEditTime: 2023-11-13 10:49:12
 * @LastEditors: thb
-->
<template>
  <div>
    <el-timeline v-if="recordList.length">
      <el-timeline-item v-for="(item, index) in recordList" :class="index === 0 ? 'highlight-circle' : ''" :key="index">
        <p class="line-title">
          {{ item.recordName }}
        </p>
        <div class="line-remark" v-if="item.recordName === '线索申述'">
          {{ `${item.createBy} 发起线索申述` }}
        </div>
        <div class="line-remark" v-if="item.recordName === '申述成功'">
          {{ `${item.createBy} 标记线索申述成功` }}
        </div>
        <div class="line-remark" v-if="item.recordName === '申述失败'">
          {{ `${item.createBy} 标记线索申述失败` }}
        </div>
        <div class="line-remark">
          <span v-if="item.recordName === '线索申述'">申述原因：</span> <span v-html="item.remark"></span>
        </div>
        <div class="line-time">
          <span>操作时间：</span>
          <span>{{ item.createTime }}</span>
        </div>
        <div class="line-person">
          <span>操作人：</span>
          <span>{{ item.createBy }}</span>
        </div>
      </el-timeline-item>
    </el-timeline>
    <p v-else>暂无记录</p>
  </div>
</template>
<script setup>
import { getActionRecordList } from '@/api/material-manage/clue'

const props = defineProps({
  id: String,
  requestApi: Function
})

const recordList = ref([])
const getList = async () => {
  if (props.requestApi) {
    const { data } = await props.requestApi(props.id)
    recordList.value = data || []
  } else {
    const { data } = await getActionRecordList(props.id)
    recordList.value = data || []
  }
}
onMounted(() => {
  getList()
})

defineExpose({
  getList
})
</script>
<style lang="scss" scoped>
.line-title {
  font-size: 16px;
  font-family: SourceHanSansCN-Medium, SourceHanSansCN;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16px;
}
.line-remark {
  font-size: 14px;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-weight: 400;
  color: #333333;
  margin-bottom: 16px;
}
.line-time,
.line-person {
  font-size: 14px;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-weight: 400;
  color: #6a7697;
}
.line-time {
  margin-bottom: 12px;
}
:deep(.el-timeline) {
  .el-timeline-item__node.el-timeline-item__node--normal {
    width: 16px;
    height: 16px;
    background: #ffffff;
    border: 4px solid #b2b5b9;
  }
}
:deep(.highlight-circle) {
  .el-timeline-item__node.el-timeline-item__node--normal {
    width: 16px;
    height: 16px;
    background: #ffffff;
    border: 4px solid #2383e7;
  }
}
</style>
