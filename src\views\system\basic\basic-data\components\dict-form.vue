<template>
  <el-dialog v-model="open" :title="getTitle()" width="1200px" :close-on-click-modal="false" append-to-body @close="handleClose">
    <el-form ref="dictRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="数据名称" prop="baseconfigName">
        <el-input v-model="form.baseconfigName" placeholder="请输入字典名称" />
      </el-form-item>
      <el-form-item label="数据类型" prop="type">
        <el-input v-model="form.type" placeholder="请输入字典类型" />
      </el-form-item>
      <!-- <el-form-item label="状态" prop="baseConfigStatus">
        <el-radio-group v-model="form.baseConfigStatus">
          <el-radio v-for="dict in statusList" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
      </el-form-item>
      <el-form-item label="数据设置">
        <multiLevel ref="levelRef" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { addOrUpdateDict } from '@/api/system/baseData'
import multiLevel from './multi-level.vue'
const open = ref(true)

// const statusList = [
//   {
//     label: '启用',
//     value: 1
//   },
//   {
//     label: '禁用',
//     value: 0
//   }
// ]
const props = defineProps({
  title: String,
  detail: Object
})

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dictName: undefined,
    dictType: undefined,
    status: undefined
  },
  rules: {
    baseconfigName: [{ required: true, message: '数据名称不能为空', trigger: 'blur' }],
    type: [{ required: true, message: '数据类型不能为空', trigger: 'blur' }]
  }
})

const { form, rules } = toRefs(data)

const levelRef = ref()
watch(
  () => props.title,
  () => {
    if (props.title === 'edit') {
      // 编辑的情况下数据回显
      const { baseconfigName, type, baseConfigStatus, childrenVO, remark } = props.detail
      form.value = {
        baseconfigName,
        type,
        baseConfigStatus,
        remark
      }
      console.log('form', form.value)
      nextTick(() => {
        levelRef.value.setDefaultList([
          {
            list: childrenVO
          }
        ])
      })
    }
  },
  {
    immediate: true
  }
)

const dictRef = ref()

const { proxy } = getCurrentInstance()
const submitForm = () => {
  dictRef.value.validate(async valid => {
    if (valid) {
      const data = await addOrUpdateDict({
        baseconfigId: props.title === 'edit' ? props.detail.baseconfigId : '',
        ...form.value,
        baseConfigContext: JSON.stringify(levelRef.value.list[0]?.list || [])
      })
      if (data.code === 200) {
        proxy.$modal.msgSuccess(`${titleMap[props.title]}成功!`)
        handleClose()
        emits('on-list')
      } else {
        proxy.$modal.msgError(`${titleMap[props.title]}失败!`)
      }
    }
  })
}

const titleMap = {
  add: '新增',
  edit: '编辑'
}
const getTitle = () => {
  return titleMap[props.title] + '数据'
}

const emits = defineEmits(['on-close', 'on-list'])
const handleClose = () => {
  emits('on-close')
}
</script>
