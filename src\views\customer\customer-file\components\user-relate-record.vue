<template>
  <el-radio-group style="margin-bottom: 10px" :model-value="tabType" @change="handleRadioChange">
    <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
  </el-radio-group>
  <el-table :data="tableData">
    <el-table-column type="index" label="序号" width="100" />
    <el-table-column prop="userName" label="人员名称" />
    <el-table-column prop="operationTime" label="开始时间" />
    <el-table-column prop="endTime" label="结束时间">
      <template #default="{ row }">
        <span>{{ row.endTime || '--' }}</span>
      </template>
    </el-table-column>
  </el-table>
  <Pagination v-if="total > 0" :total="total" v-model:page="pages" @pagination="handlePageChange" />
</template>
<script setup>
import Pagination from '@/components/Pagination'
import { getUserRelateRecord } from '@/api/customer/file'
import { inject } from 'vue'
const props = defineProps({
  ciId: Number
})

// 监听
const onRefresh = inject('onRefresh')
watch(onRefresh, async () => {
  console.log('onRefresh', onRefresh)
  getRecords()
})

const tabType = ref('manager')
const tabs = [
  {
    dicValue: 'manager',
    dictLabel: '财税顾问'
  },
  {
    dicValue: 'counselor',
    dictLabel: '开票员'
  },
  {
    dicValue: 'customer_success',
    dictLabel: '客户成功'
  },
  {
    dicValue: 'sponsor_accounting',
    dictLabel: '主办会计'
  }
  // {
  //   dicValue: 'xxx', // todo
  //   dictLabel: '流程会计'
  // }
]
function handleRadioChange(e) {
  console.log('handleRadioChange', e)
  tabType.value = e
  pages.value = 1
  getRecords()
}

// 获取所有的编辑记录
const tableData = ref([])
const total = ref(0)
const pages = ref(1)
const getRecords = async () => {
  const { data } = await getUserRelateRecord({
    role: tabType.value,
    customerId: props.ciId,
    pageNum: 1,
    pageSize: 10
  })
  tableData.value = data.records || []
  pages.value = data.current
  total.value = Number(data.total) || 0
}

// 切换页数或者页码
// <!-- emit('pagination', { page: val, limit: pageSize.value }) -->
const handlePageChange = async ({ page, limit }) => {
  const { data } = await getUserRelateRecord({
    role: tabType.value,
    customerId: props.ciId,
    pageNum: page,
    pageSize: limit
  })
  tableData.value = data.records || []
  total.value = Number(data.total) || 0
}
getRecords()
</script>
<style lang="scss" scoped>
.pagination-container {
  height: 35px;
}
</style>
