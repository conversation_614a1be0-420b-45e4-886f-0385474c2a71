<template>
  <el-tabs v-model="activeName" :class="[tabList.length === 1 ? 'single-tab' : '']">
    <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.label">
      <component
        v-if="activeName === item.label"
        :detail="detail"
        :request-api="requestApi"
        :submit-api="submitApi"
        :is="componentsMap[item.label]"
        @on-success="emits('on-success')"
      />
    </el-tab-pane>
  </el-tabs>
</template>
<script setup>
import recordList from './record-list'
import recordForm from './record-form'

const props = defineProps({
  detail: {
    type: Object,
    default: () => {
      return {}
    }
  },
  isSea: {
    type: Boolean,
    default: false
  },
  tabType: {
    type: String,
    default: '1'
  },
  requestApi: Function,
  submitApi: Function
})
// 跟进
const activeName = ref('跟进记录')
const tabList = ref([
  {
    label: '跟进记录',
    value: '跟进记录'
  },
  {
    label: '快速跟进',
    value: '快速跟进'
  }
])

watch(
  () => props.tabType,
  () => {
    console.log('props.tabType', props.tabType)
    if (props.tabType !== '1') {
      // 说明是在共享线索
      tabList.value = [
        {
          label: '跟进记录',
          value: '跟进记录'
        }
      ]
    }
  },
  {
    immediate: true
  }
)
watch(
  () => props.isSea,
  () => {
    if (props.isSea) {
      // 说明是在公海
      tabList.value = [
        {
          label: '跟进记录',
          value: '跟进记录'
        }
      ]
    }
  },
  {
    immediate: true
  }
)

const componentsMap = {
  跟进记录: recordList,
  快速跟进: recordForm
}

const emits = defineEmits(['on-success'])
</script>
<style lang="scss" scoped>
.el-tabs {
  :deep(.el-tabs__item.is-top) {
    width: 139px;
    text-align: center;
    padding: 0;
  }
  :deep(.el-tabs__active-bar) {
    left: 16%;
    height: 2px;
    width: 48px !important;
  }
}
.single-tab {
  :deep(.el-tabs__active-bar) {
    left: 31%;
    height: 2px;
    width: 48px !important;
  }
}
</style>
