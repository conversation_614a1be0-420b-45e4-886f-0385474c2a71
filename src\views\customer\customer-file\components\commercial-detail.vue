<template>
  <el-form :model="formData" ref="formRef" label-position="top" :rules="rules" :hide-required-asterisk="disabled">
    <el-row :gutter="24">
      <el-col :span="6">
        <el-form-item label="企业名称">
          <el-input v-model="formData.customerName" maxlength="100" disabled :placeholder="disabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="统一社会信用代码">
          <el-input v-model="formData.crediCode" maxlength="100" :disabled="disabled" :placeholder="disabled ? '' : '请输入'">
            <template #append v-if="!disabled">
              <el-button :icon="Search" v-hasPermi="['customer:commercial:check']" @click="getCommercialDetail" />
            </template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="公司类型">
          <el-input v-model="formData.companyType" maxlength="100" :disabled="disabled" :placeholder="disabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="法定代表人">
          <el-input v-model="formData.legalPerson" maxlength="20" :disabled="disabled" :placeholder="disabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="注册地址">
          <el-input
            v-model="formData.registeredAddress"
            maxlength="250"
            type="textarea"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="经营范围">
          <el-input
            v-model="formData.scope"
            maxlength="1000"
            type="textarea"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="联系方式" prop="contract">
          <el-input v-model="formData.contract" maxlength="20" :disabled="disabled" :placeholder="disabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>

      <el-col :span="16">
        <el-form-item label="网址">
          <el-input v-model="formData.website" maxlength="100" :disabled="disabled" :placeholder="disabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="经营状态">
          <el-input
            v-model="formData.bussinessStatus"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="登记机关">
          <el-tooltip
            :content="formData.registrationAuthority"
            v-if="disabled && formData.registrationAuthority"
            effect="light"
            placement="top-start"
          >
            <el-input
              v-model="formData.registrationAuthority"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            v-model="formData.registrationAuthority"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="成立日期">
          <el-date-picker
            v-model="formData.establishDate"
            :disabled="disabled"
            type="date"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            :placeholder="disabled ? ' ' : '请选择'"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="注册资金">
          <el-input
            v-model="formData.registeredCapital"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="行业">
          <el-input v-model="formData.Cindustry" maxlength="100" :disabled="disabled" :placeholder="disabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="注册号">
          <el-input
            v-model="formData.registrationNumber"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="营业开始时间">
          <el-date-picker
            v-model="formData.openDate"
            type="date"
            :disabled="disabled"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            :placeholder="disabled ? ' ' : '请选择'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="营业结束时间">
          <el-date-picker
            :disabled="disabled"
            v-model="formData.openEnd"
            type="date"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            :placeholder="disabled ? ' ' : '请选择'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="组织机关代码">
          <el-tooltip
            :content="formData.organizationCode"
            v-if="disabled && formData.organizationCode"
            effect="light"
            placement="top-start"
          >
            <el-input
              v-model="formData.organizationCode"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            v-model="formData.organizationCode"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="核准日期">
          <el-date-picker
            v-model="formData.approvalDate"
            type="date"
            :disabled="disabled"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            :placeholder="disabled ? ' ' : '请选择'"
          />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="8">
        <el-form-item label="国税账号">
          <el-tooltip
            :content="formData.nationalTaxAccount"
            v-if="disabled && formData.nationalTaxAccount"
            effect="light"
            placement="top-start"
          >
            <el-input
              v-model="formData.nationalTaxAccount"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            v-model="formData.nationalTaxAccount"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col> -->
      <!-- <el-col :span="8">
        <el-form-item label="国税密码">
          <el-input
            v-model="formData.nationalTaxPassward"
            maxlength="100"
            autocomplete="new-password"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col> -->
    </el-row>

    <!-- <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="个税账号">
          <el-tooltip
            :content="formData.individualTaxAccount"
            v-if="disabled && formData.individualTaxAccount"
            effect="light"
            placement="top-start"
          >
            <el-input
              v-model="formData.individualTaxAccount"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            v-model="formData.individualTaxAccount"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="个税密码">
          <el-input
            v-model="formData.individualTaxPassword"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="个体户核定">
          <el-radio-group :disabled="disabled" v-model="formData.isIndividual">
            <el-radio :label="false" size="large">否</el-radio>
            <el-radio :label="true" size="large">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row> -->

    <Collapse title="相关附件">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="营业执照" prop="">
            <FileUpload v-if="!disabled" v-model="formData.businessFileList" :isShowTip="false" :limit="100" />

            <FileList v-else :list="formData.businessFileList" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册资料">
            <FileUpload v-if="!disabled" v-model="formData.registrationInformationFileList" :limit="100" :isShowTip="false" />
            <!-- <span class="download-text" @click="downloadFile(formData.registrationInformationFile)" v-else>
              {{ (formData.registrationInformationFile && formData.registrationInformationFile?.fileNames) || '暂无文件' }}</span
            > -->
            <FileList v-else :list="formData.registrationInformationFileList" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司章程">
            <FileUpload v-if="!disabled" v-model="formData.businessConstitutionFileList" :limit="100" :isShowTip="false" />
            <!-- <span class="download-text" @click="downloadFile(formData.businessConstitutionFile)" v-else>
              {{ (formData.businessConstitutionFile && formData.businessConstitutionFile?.fileNames) || '暂无文件' }}</span
            > -->
            <FileList v-else :list="formData.businessConstitutionFileList" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="股东会决议">
            <FileUpload
              v-if="!disabled"
              v-model="formData.shareholderCommitteeRessolutionFileList"
              :isShowTip="false"
              :limit="100"
            />
            <!-- <span class="download-text" @click="downloadFile(formData.shareholderCommitteeRessolutionFile)" v-else>
              {{
                (formData.shareholderCommitteeRessolutionFile && formData.shareholderCommitteeRessolutionFile?.fileNames) ||
                '暂无文件'
              }}</span
            > -->
            <FileList v-else :list="formData.shareholderCommitteeRessolutionFileList" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="地址">
            <FileUpload v-if="!disabled" v-model="formData.adressFileList" :limit="100" :isShowTip="false" />
            <!-- <span class="download-text" @click="downloadFile(formData.adressFile)" v-else>
              {{ (formData.adressFile && formData.adressFile?.fileNames) || '暂无文件' }}</span
            > -->
            <FileList v-else :list="formData.adressFileList" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证件" class="file-flex">
            <FileUpload v-if="!disabled" v-model="formData.identityDocumentFileList" :limit="50" :isShowTip="false" />
            <template v-else>
              <FileList :list="formData.identityDocumentFileList" />
              <!-- <template v-if="formData.identityDocumentFileList">
                <div
                  class="download-text"
                  v-for="(file, index) in formData.identityDocumentFileList"
                  :key="index"
                  @click="downloadFile(file)"
                >
                  {{ (file && file?.fileNames) || '暂无文件' }}
                </div></template
              >
              <template v-else>
                <span class="download-text">暂无文件</span>
              </template> -->
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="变更信息">
            <FileUpload v-if="!disabled" v-model="formData.businessChangeInfoFileList" :limit="100" :isShowTip="false" />
            <!-- <span class="download-text" @click="downloadFile(formData.businessChangeInfoFile)" v-else>
              {{ (formData.businessChangeInfoFile && formData.businessChangeInfoFile?.fileNames) || '暂无文件' }}</span
            > -->
            <fileList :list="formData.businessChangeInfoFileList" v-else />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="其他">
            <FileUpload v-if="!disabled" v-model="formData.businessOtherFileList" :isShowTip="false" :limit="100" />
            <!-- <span class="download-text" @click="downloadFile(formData.businessOtherFile)" v-else>
              {{ (formData.businessOtherFile && formData.businessOtherFile?.fileNames) || '暂无文件' }}</span
            > -->
            <FileList v-else :list="formData.businessOtherFileList" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="交接单">
            <FileUpload v-if="!disabled" v-model="formData.handoverDocumentFileList" :isShowTip="false" :limit="100" />
            <FileList v-else :list="formData.handoverDocumentFileList" />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
  </el-form>

  <commercial v-if="commercialShow" :data="params" @on-close="commercialShow = false" @search-success="setCommercialData" />
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import Collapse from '@/components/Collapse'
import commercial from './commercial.vue'
import { saveCustomerBusiness, getCustomerBusinessByCiId } from '@/api/customer/file'
import { useRemote } from '@/hooks/useRemote'
import { Search } from '@element-plus/icons-vue'
// import { downloadFile } from '@/utils/common'
import iFrame from '@/components/iFrame'
import { FormValidators } from '@/utils/validate'
import { getCommercialDetail as getCommercial } from '@/api/customer/file'
import FileList from '@/components/FileList'
const disabled = inject('disabled')
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emits = defineEmits(['update:modelValue', 'on-change', 'on-edit'])
const formData = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})

const rules = {
  contract: [
    {
      message: '请输入正确的联系方式',
      trigger: 'blur',
      validator: FormValidators.allPhone
    }
  ]
}
const getDetail = async () => {
  const { data } = await getCustomerBusinessByCiId(props.modelValue.ciId)
  const industry = data?.industry
  delete data?.industry
  formData.value = Object.assign(formData.value, {
    ...data,
    Cindustry: industry || '',
    companyType: data?.type || ''
  })
}

const setData = data => {
  const {
    customerName,
    crediCode,
    type,
    legalPerson,
    registeredAddress,
    scope,
    contract,
    website,
    bussinessStatus,
    registrationAuthority,
    establishDate,
    registeredCapital,
    industry,
    registrationNumber,
    openDate,
    openEnd,
    organizationCode,
    approvalDate,
    nationalTaxAccount,
    nationalTaxPassward,
    individualTaxAccount,
    individualTaxPassword,
    isIndividual
  } = data
  formData.value = Object.assign(formData.value, {
    customerName,
    crediCode,
    companyType: type || '',
    legalPerson,
    registeredAddress,
    scope,
    contract,
    website,
    bussinessStatus,
    registrationAuthority,
    establishDate,
    registeredCapital,
    registrationNumber,
    openDate,
    openEnd,
    organizationCode,
    approvalDate,
    nationalTaxAccount,
    nationalTaxPassward,
    individualTaxAccount,
    individualTaxPassword,
    isIndividual,
    Cindustry: industry || ''
  })
}
// 查询成功后填充数据
const setCommercialData = data => {
  setData(data)
}
// watch disabled
watch(
  disabled,
  () => {
    if (disabled.value) {
      // 如果为详情触发详情接口
      getDetail()
    }
  },
  {
    immediate: true
  }
)
// 监听是否是编辑
const edit = inject('isEdit')
watch(
  edit,
  async () => {
    if (edit.value) {
      await getDetail()
      emits('on-edit')
    }
  },
  {
    immediate: true
  }
)
// const customerName = inject('customerName')
// formData.value.customerName = customerName
const commercialShow = ref(false)
const params = ref()
const getCommercialDetail = async () => {
  const { customerName, crediCode } = formData.value
  params.value = {
    customerName,
    crediCode
  }
  // commercialShow.value = true
  try {
    const result = await getCommercial({
      keyword: crediCode || customerName
    })
    // 说明存在数据,如果存在数据则需要将数据填充进formData中，如果没有则展示
    if (result.code === 200) {
      const data = result.data
      // data.Cindustry = data.industry
      // delete data.industry
      // formData.value = Object.assign(formData.value, data)

      setData(data)
    }
  } catch (error) {
    // 如果只有customerName存在
    if (!crediCode && customerName) {
      commercialShow.value = true
    }
  }
}

const formRef = ref()

const saveRemote = async () => {
  // 检验
  if (!formRef.value) return
  const result = await formRef.value.validate(valid => {
    if (valid) {
    } else {
    }
  })
  if (result) {
    const id = await useRemote(
      saveCustomerBusiness,
      {
        ...formData.value,
        industry: formData.value.Cindustry,
        type: formData.value.companyType
      },
      ['businessChangeInfoFile'],
      '工商信息',
      [
        'businessFileList',
        'registrationInformationFileList',
        'businessConstitutionFileList',
        'shareholderCommitteeRessolutionFileList',
        'adressFileList',
        'identityDocumentFileList',
        'businessOtherFileList',
        'businessChangeInfoFileList',
        'handoverDocumentFileList'
      ]
    )
    formData.value.businessInformationId = id
    return id
  }
}
// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}
defineExpose({
  saveRemote
})
</script>
<style lang="scss" scoped>
.file-flex {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: start;
  }
}
</style>
