/*
 * @Description:
 * @Author: thb
 * @Date: 2023-05-25 11:31:06
 * @LastEditTime: 2023-07-07 13:51:38
 * @LastEditors: thb
 */
// import request from '@/utils/request'

import request from '@/utils/request'

// 导出
// 附件通用下载请求
// export function handleDownloadFile(fileName) {
//   return request({
//     url: '/common/download',
//     method: 'get',
//     params: {
//       fileName,
//       delete: false
//     }
//   })
// }
// oss预览

export function getFileUrlByOss(url) {
  return request({
    url: '/common/getUrlOss',
    method: 'get',
    params: {
      objectName: url
    }
  })
}
