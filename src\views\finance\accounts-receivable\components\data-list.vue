<template>
  <el-card style="margin-bottom: 20px; flex-shrink: 0">
    <div class="wrap">
      <div v-for="(value, key) in staticData" :key="key" class="list-item">
        <span class="color-blue">{{ labelMap[key] }}</span>
        <div class="num-text">
          {{ numberWithCommas(value || 0) || 0 }} <span class="unit">{{ unitMap[`${key}Unit`] }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { financePaymentGetStaticData } from '@/api/finance/accounts-receivable'
import { numberWithCommas } from '@/utils/index'
const props = defineProps({
  // selectReceivableAmount: Number
  selectedList: Array
})

const staticData = ref({})
const getList = async () => {
  const res = await financePaymentGetStaticData()
  res.data.selectReceivableAmount = 0
  delete res.data.receivableOfThisMonth
  delete res.data.receiptOfThisMonth
  staticData.value = res.data
}
getList()

const labelMap = {
  // receivableOfThisMonth: '本月应收',
  // receiptOfThisMonth: '本月实收',
  allReceivableAmountOfThisMonth: '本月应待收款',
  actualReceivableOfThisMonth: '本月实际收款',
  allReceivableAmountOfNotExpire: '未到期应待收款总计',
  selectReceivableAmount: '已选择应待收款',
  priceRiseAmount: '价格上涨总计金额',
  priceRiseAmountOfThisMonth: '本月价格上涨金额',
  priceFallAmount: '价格下跌金额总计',
  priceFallAmountOfThisMonth: '本月价格下跌金额'
}
const unitMap = {
  // receivableOfThisMonthUnit: '家',
  // receiptOfThisMonthUnit: '家',
  allReceivableAmountOfThisMonthUnit: '元',
  actualReceivableOfThisMonthUnit: '元',
  allReceivableAmountOfNotExpireUnit: '元',
  selectReceivableAmountUnit: '元',
  priceRiseAmountUnit: '元',
  priceRiseAmountOfThisMonthUnit: '元',
  priceFallAmountUnit: '元',
  priceFallAmountOfThisMonthUnit: '元'
}

// watch(
//   () => props.selectReceivableAmount,
//   () => {
//     staticData.value.selectReceivableAmount = props.selectReceivableAmount
//   },
//   {
//     immediate: true
//   }
// )
watch(
  () => props.selectedList,
  () => {
    let num = 0
    props.selectedList.map(item => {
      num += item.allReceivableAmount
    })
    staticData.value.selectReceivableAmount = num.toFixed(2)
  },
  { deep: true }
)

defineExpose({ getList })
</script>

<style lang="scss" scoped>
.el-card {
}

.wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  .list-item {
    box-sizing: border-box;
    width: calc((100% - 8px * 3) / 4);
    // flex: 1;
    height: 72px;
    padding: 7px 12px;
    background: #eff7ff;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

.color-blue {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #2383e7;
  margin-bottom: 8px;
}
.num-text {
  font-size: 20px;
  font-family: AlibabaPuHuiTi_2_85_Bold;
  color: #333333;
}

.unit {
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #7d8592;
}
.color-yellow {
  color: #e6a23c;
}
.el-card.is-always-shadow {
  box-shadow: none;
  border: none;
  :deep(.el-card__body) {
    padding: 8px !important;
  }
}
</style>
