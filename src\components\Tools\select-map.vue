<!-- 地图点位选择 -->
<template>
  <DialogModal v-model="currentVisible" width="1200px" title="点位" @close="closeDialog">
    <MapTian ref="maps" @on-init="onMapLoad" height="600px"></MapTian>
    <div class="map-search">
      <div>
        <el-input v-model="form.keyWord" placeholder="请输入">
          <template #append>
            <div class="select-tools">
              <img @click="form.keyWord = undefined" src="@/assets/icons/clear-icon.png" alt="" />
              <el-button :loading="selectLoading" class="select-search" @click="getAddress">
                <img v-show="!selectLoading" src="@/assets/icons/map-search.png" alt="" />
              </el-button>
            </div>
          </template>
        </el-input>
      </div>
      <div class="search-data" :style="{ maxHeight: height }">
        <div class="search-data-item" v-for="item in markerData" :key="item.location" @click="setCenter(item)">
          <img src="@/assets/icons/map-icon.png" alt="" />
          {{ item.beforeText }}
          <span style="color: #3183e2">{{ item.keyWord }}</span>
          {{ item.afterText }}
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="closeDialog"> 取消 </el-button>
      <el-button :disabled="!checkAddress || saveDisabledFlag" type="primary" @click="submit"> 保存 </el-button>
    </template>
    <div style="display: none">
      <div class="marker-info" ref="markerInfo">
        <div class="marker-info-title">
          {{ checkAddress && checkAddress.name }}
        </div>
        <div class="marker-info-address">
          <img src="@/assets/icons/map-icon.png" alt="" />{{ checkAddress && checkAddress.address }}
        </div>
      </div>
    </div>
  </DialogModal>
</template>
<script setup>
import { thirdSearch, geoCode } from '@/api/third'
import MapTian from '@/components/MapTianditu/index.vue'
import MapDefault from '@/assets/icons/map-default.png'
import MapActive from '@/assets/icons/map-active.png'
import { useThrottleFn } from '@vueuse/core'
import { nextTick } from 'vue'
const { proxy } = getCurrentInstance()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  position: {
    type: Array,
    default: () => {
      return []
    }
  },
  address: {
    type: Object,
    default: () => {
      return { name: '', address: '' }
    }
  }
})
watch(
  () => props.visible,
  val => {
    if (val && myMap) {
      setPoint()
    } else if (val) {
      onGetAddress()
    }
  }
)
const onGetAddress = () => {
  const timer = setInterval(() => {
    if (myMap) {
      clearTimeout(timer)
      form.keyWord = props.address?.address
      form.keyWord && getAddress()
    }
  }, 10)
}
const currentVisible = computed({
  get() {
    return props.visible
  },
  set(newValue) {
    proxy.$emit('update:visible', newValue)
  }
})

const openMarker = ref(false)
function closeDialog() {
  form.keyWord = undefined
  checkAddress.value = undefined
  height.value = '0px'
  markerData.value = []
  myMap.clearOverLays()
  openMarker.value = false
  proxy.$emit('close')
}
let myMap = null
let customMarker = null
const saveDisabledFlag = ref(true)
const onMapLoad = map => {
  myMap = map
  myMap.addEventListener(
    'click',
    useThrottleFn(async e => {
      markerList.forEach(marker => {
        marker?.setIcon(iconDefault)
      })
      if (customMarker) {
        myMap.removeOverLay(customMarker)
      }
      myMap.closeInfoWindow()
      const { data } = await geoCode({ lat: e.lnglat.lat, lon: e.lnglat.lng })

      checkAddress.value = {
        name: data.regeocode?.formatted_address,
        address: data.regeocode?.formatted_address,
        lonlat: e.lnglat.lng + ',' + e.lnglat.lat,
        type: 'customMarker'
      }
      customMarker = setMarker(e.lnglat, checkAddress.value)
      customMarker.setIcon(iconActive)
      saveDisabledFlag.value = false
    }, 1000)
  )
  setPoint()
}
// 点位编辑回显
const setPoint = () => {
  if (props.position.length > 0 && props.position[0] && props.position[1]) {
    const position = new T.LngLat(props.position[0], props.position[1])
    setMarker(position, { ...props.address, lonlat: props.position.join(',') })
    myMap.panTo(position)
  } else {
    onGetAddress()
  }
}
const form = reactive({
  keyWord: undefined
})
const selectLoading = ref(false)
const checkAddress = ref()
const markerData = ref([])
const height = ref('0px')
const markerList = []
// 搜索
const getAddress = async () => {
  // return
  myMap.clearOverLays()
  selectLoading.value = true
  const { data } = await thirdSearch(form).finally(() => {
    selectLoading.value = false
  })
  checkAddress.value = ''
  const pointData = []
  markerData.value = data.pois?.map(item => {
    const res = item.name.split(form.keyWord)
    item.beforeText = res[0]
    item.afterText = res[1]
    item.keyWord = form.keyWord
    item.lonlat = item.location
    return item
  })
  if (!markerData.value || markerData.value.length === 0) {
    proxy.$modal.msgWarning('暂无信息')
    height.value = '0px'
    return
  }
  height.value = '500px'
  markerData.value.forEach(p => {
    const lonLat = p.lonlat?.split(',')
    const position = new T.LngLat(lonLat[0], lonLat[1])
    pointData.push(position)
    markerList.push(setMarker(position, p))
  })
  myMap.setViewport(pointData)
}
//
const iconDefault = new T.Icon({
  iconUrl: MapDefault,
  iconSize: new T.Point(25, 25),
  iconAnchor: new T.Point(12.5, 25)
})
const iconActive = new T.Icon({
  iconUrl: MapActive,
  iconSize: new T.Point(42, 42),
  iconAnchor: new T.Point(21, 42)
})
// 当前点击的marker
let clickMarker = null
const setMarker = (position, p) => {
  const marker = new T.Marker(position, { icon: iconDefault, data: p })
  marker.addEventListener('click', e => {
    if (customMarker && e.target.options.data?.type !== 'customMarker') {
      // 清除已存在的customMarker
      myMap.removeOverLay(customMarker)
    }
    clickMarker?.setIcon(iconDefault)
    marker.setIcon(iconActive)
    clickMarker = marker
    if (p) {
      checkAddress.value = p
    }
    setInfoWindow(position)
  })
  myMap.addOverLay(marker)
  return marker
}
const markerInfo = ref()
// 点击下拉框聚焦点位
const setCenter = val => {
  // getMarker(val)
  for (let i of markerList) {
    // 存在name名称可以重复的情况，采用hotPointID进行判断
    if (i.options.data.hotPointID === val.hotPointID) {
      clickMarker?.setIcon(iconDefault)
      i.setIcon(iconActive)
      clickMarker = i
      checkAddress.value = val
      const position = val.lonlat.split(',')
      const tPosition = new T.LngLat(position[0], position[1])
      setInfoWindow(tPosition)
      myMap.panTo(tPosition)
      saveDisabledFlag.value = false
      return
    }
  }
}
// 设置marker窗体
const setInfoWindow = position => {
  const _infoWindow = new T.InfoWindow(markerInfo.value)
  _infoWindow.setOffset([200, 25])
  myMap.openInfoWindow(_infoWindow, position)
}
const submit = () => {
  proxy.$emit('get-location', checkAddress.value)
  closeDialog()
}
</script>
<style lang="scss" scoped>
@import '@/assets/styles/mixin.scss';
.map-search {
  position: absolute;
  top: 80px;
  left: 32px;
  // display: flex;
  z-index: 999;
  :deep(.el-input) {
    width: 328px;
    .el-input-group__append {
      padding: 0;
      background: #fff;
      box-shadow: none;
    }
  }
  :deep(.el-input__wrapper) {
    box-shadow: none;
  }
  :deep(.el-input-group__append button.el-button:hover) {
    background: #3183e2;
  }
  .select-tools {
    display: flex;
    align-items: center;
    padding-left: 6px;
    position: relative;
    :deep(.el-icon) {
      color: #fff;
      margin-left: 5px;
    }
    &::before {
      position: absolute;
      top: 6px;
      left: 0;
      content: '';
      background: #dddddd;
      width: 1px;
      height: 20px;
    }
    img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
  .select-search {
    margin-left: 6px;
    width: 36px;
    height: 32px;
    background: #3183e2;
    border-radius: 2px 2px 2px 0px;
    display: flex;
    img {
      margin: auto;
    }
  }
}
.search-data {
  @include scrollBar;
  margin-top: 4px;
  width: 328px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  font-size: 14px;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-weight: 400;
  color: #333333;
  height: unset;
  max-height: 500px;
  overflow: auto;
  transition: all 0.2s linear;
  &-item {
    height: 48px;
    display: flex;
    align-items: center;
    &:hover {
      background: #daebff;
      cursor: pointer;
    }
  }
}
.marker-info {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
  font-size: 14px;
  width: 337px;
  color: #333333;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 30px;
    border: 10px solid transparent;
    border-right: 20px solid #fff;
  }
  &-title {
    font-family: AlibabaPuHuiTi_2_85_Bold;
    padding: 20px 40px 20px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.5);
  }
  &-address {
    font-family: AlibabaPuHuiTi_2_55_Regular;
    display: flex;
    align-items: center;
    padding-right: 10px;
  }
}
</style>
