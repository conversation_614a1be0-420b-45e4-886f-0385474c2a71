server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/dist;
    autoindex off;
    autoindex_exact_size off;
    autoindex_localtime on;
    location / {
        try_files $uri $uri/ =404;
        index index.html index.htm;
        proxy_set_header Host $host;
        client_max_body_size 100m;
        gzip_static on;
        expires max;
        add_header Cache-Control public;
        if ($request_filename ~* ^.*?\.(eot)|(ttf)|(woff)|(svg)|(otf)$) {
            add_header Access-Control-Allow-Origin *;
        }
    }
}