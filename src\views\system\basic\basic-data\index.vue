<template>
  <ProTable ref="proTable" title="基础数据" :columns="columns" :request-api="getDictList">
    <!-- 表格 header 按钮 -->
    <template #tableHeader="">
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增数据</el-button>
    </template>

    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link :icon="EditPen" @click="handleEdit(scope.row)">编辑</el-button>
      <el-button type="primary" link :icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
    </template>
  </ProTable>

  <DictForm v-if="formShow" :title="formType" :detail="fromDetail" @on-close="formShow = false" @on-list="getList" />
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import ProTable from '@/components/ProTable/index.vue'

import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus, Delete, EditPen } from '@element-plus/icons-vue'
import { getDictList, deleteDict } from '@/api/system/baseData'
import DictForm from './components/dict-form.vue'
import { cloneDeep } from 'lodash'

// 自定义渲染表头（使用tsx语法）
// const headerRender = (scope: HeaderRenderScope<any>) => {
//   return (
//     <el-button
//       type="primary"
//       onClick={() => ElMessage.success("我是通过 tsx 语法渲染的表头")}
//     >
//       {scope.column.label}
//     </el-button>
//   );
// };

// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'baseconfigName',
    label: '数据名称',
    search: { el: 'input' }
  },
  {
    prop: 'type',
    label: '数据标签',
    search: { el: 'input' }
  },
  // {
  //   prop: 'status',
  //   label: '数据状态',
  //   // enum: getUserStatus,
  //   search: { el: 'tree-select', props: { filterable: true } },
  //   fieldNames: { label: 'userLabel', value: 'userStatus' },
  //   render: (scope: any) => {
  //     return <>{<el-tag type={scope.row.status ? 'success' : 'danger'}>{scope.row.status ? '启用' : '禁用'}</el-tag>}</>
  //   }
  // },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180
  },
  { prop: 'operation', label: '操作', fixed: 'right', width: 330 }
]

// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
// const getTableList = (params: any) => {
//   let newParams = JSON.parse(JSON.stringify(params));
//   return getUserList(newParams);
// };

const formShow = ref(false)
const formType = ref('add')
// 新增基础数据
const handleAdd = () => {
  formType.value = 'add'
  formShow.value = true
}
// 编辑基础数据
const fromDetail = ref()
const handleEdit = (row: any) => {
  formType.value = 'edit'
  fromDetail.value = cloneDeep(row)
  formShow.value = true
}
// 删除基础数据
const { proxy } = getCurrentInstance()

const handleDelete = async (row: any) => {
  proxy.$modal
    .confirm('是否确认删除数据编号为"' + row.baseconfigName + '"的数据项？')
    .then(function () {
      return deleteDict(row.baseconfigId)
    })
    .then(() => {
      proTable.value.getTableList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}

const proTable = ref()
// 编辑后重新更新列表
const getList = () => {
  proTable.value.getTableList()
}
</script>
