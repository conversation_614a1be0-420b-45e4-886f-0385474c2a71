<template>
  <el-dialog
    v-model="visible"
    title="分析结果"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1350px"
    top="5vh"
    destroy-on-close
    append-to-body
  >
    <el-form>
      <el-form-item label="结果来源日期" prop="timexx">
        <el-date-picker
          v-model="queryParams.analyseDate"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          type="date"
          :clearable="false"
          @change="handleDateChange"
        />
      </el-form-item>
    </el-form>
    <div class="my-box" v-if="detailSan?.financeSanLinksVO?.some(item => item.value && parseInt(item.value))">
      <div class="left">
        <div ref="chartRef" style="height: 350px" />
      </div>
      <div class="right">
        <div class="tit-line">
          <div class="tit">分析总数</div>
        </div>
        <div class="item-box">
          <div class="item">
            <div class="t1">分析企业数</div>
            <div class="t2">{{ detailSan?.analyseCustomerNumber }} 家</div>
          </div>
          <div class="item">
            <div class="t1">应收金额</div>
            <div class="t2">{{ detailSan?.receiveableAmount }} 元</div>
          </div>
          <div class="item">
            <div class="t1">已收金额</div>
            <div class="t2">{{ detailSan?.receiptAmount }} 元</div>
          </div>
        </div>
        <div class="tit-line">
          <div class="tit">分析结果</div>
          <el-tree-select
            check-strictly
            :props="{ value: 'totalName', label: 'name', children: 'children' }"
            filterable
            :data="receiveAnalyseArr"
            :render-after-expand="false"
            default-expand-all
            :model-value="receiveAnalyse"
            @node-click="handleNodeClick"
            @current-change="node => handleSelectChange(node, row)"
          />
          <!-- v-model="receiveAnalyse" -->
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
        <!-- detail.financeAnalyseTabCharsVO-{{ detail.financeAnalyseTabCharsVO }} -->
        <template v-if="detail?.financeAnalyseTabCharsVO">
          <div class="item-box">
            <div class="item">
              <div class="t1">企业数</div>
              <div class="t2">{{ detail?.financeAnalyseTabCharsVO?.analyseCustomerNumber }} 家</div>
            </div>
            <div class="item">
              <div class="t1">应收金额</div>
              <div class="t2">{{ detail?.financeAnalyseTabCharsVO?.receiveableAmount }} 元</div>
            </div>
            <div class="item">
              <div class="t1">已收金额</div>
              <div class="t2">{{ detail?.financeAnalyseTabCharsVO?.receiptAmount }} 元</div>
            </div>
          </div>
          <div class="item-box">
            <div class="item-chart">
              <miniChart :dataSource="detail?.financeAnalyseTabCharsVO?.listCharsNumber" ref="miniChartRef1" />
            </div>
            <div class="item-chart">
              <miniChart :dataSource="detail?.financeAnalyseTabCharsVO?.listCharsPaymentAmount" ref="miniChartRef2" />
            </div>
            <div class="item-chart">
              <miniChart :dataSource="detail?.financeAnalyseTabCharsVO?.listCharsReceiptAmount" ref="miniChartRef3" />
            </div>
          </div>
        </template>
      </div>
    </div>
    <div v-else class="table-empty" style="height: 350px; text-align: center">
      <el-empty description="该日期分析结果暂无数据" />
    </div>
    <div class="my-table">
      <ProTable
        ref="proTable"
        title="收款分析"
        :isShowSearch="false"
        :init-param="queryParams"
        :columns="columnsAccount"
        :toolButton="false"
        :dataCallback="dataCallback"
        rowKey="id"
        :request-api="financeReceiptAnalyseAnalyseTabList"
      >
        <template #customerName="{ row }">
          <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
        </template>
      </ProTable>
    </div>
    <template #footer>
      <!-- <el-button type="primary" @click="handleExportAll">导出全部 </el-button> -->
      <el-button @click="handleClose"> 关闭 </el-button>
    </template>
  </el-dialog>
  <customerDetail v-if="customerDetailShow" :id="rowId" :hideActionBtn="true" @on-close="handleCloseCustomerDetail" />
  <exportForm ref="exportFormRef" />
</template>

<script setup lang="jsx">
import { financeReceiptAnalyseAnalyseTabList, financeReceiptAnalyseGetSan } from '@/api/finance/collection-analysis'
import { useBasicDictAnalyse } from '@/utils/dict'
import * as echarts from 'echarts'
import { computed, nextTick, onMounted, watch } from 'vue'
import miniChart from '@/views/finance/collection-analysis/components/mini-chart.vue'
import { useDialog } from '@/hooks/useDialogFinance'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import exportForm from '@/views/finance/collection-analysis/components/export-form.vue'
import dayjs from 'dayjs'

const { receive_analyse } = useBasicDictAnalyse('receive_analyse')
const receiveAnalyseArr = computed(() => {
  if (receive_analyse.value) {
    const temp = [].concat(receive_analyse.value)
    temp.unshift({ totalName: '未分析', name: '' })
    return temp
  } else {
    return [{ totalName: '未分析', name: '' }]
  }
})

const { proxy } = getCurrentInstance()

const emit = defineEmits()

const props = defineProps({})

const visible = ref(false)
const onShow = data => {
  queryParams.value.analyseDate = dayjs().format('YYYY-MM-DD')
  queryParams.value.analyseId = data.id
  queryParams.value.analyseCustomerNumber = data.analyseCustomerNumber
  queryParams.value.batchId = data.batchId
  queryParams.value.receiptAmount = data.receiptAmount
  queryParams.value.receiveableAmount = data.receiveableAmount
  receiveAnalyse.value = undefined
  queryParams.value.receiveAnalyse = undefined
  // queryParams.value = Object.assign({}, queryParams.value, data)
  getDetail()
}
const detail = ref({})
const detailSan = ref({})
const getDetail = () => {
  visible.value = true
  financeReceiptAnalyseGetSan(queryParams.value).then(res => {
    detailSan.value = res.data
    if (detailSan.value.financeSanDataVO && detailSan.value.financeSanLinksVO) {
      nextTick(() => {
        const bool = detailSan.value?.financeSanLinksVO?.some(item => item.value && parseInt(item.value))
        if (bool) {
          drawChart()
        }
      })
    }
  })
}

const dataCallback = data => {
  // console.log('dataCallback', data)
  detail.value = data
  if (detail.value?.financeAnalyseTabCharsVO) {
    nextTick(() => {
      // console.log('dataCallback', detail.value.financeAnalyseTabCharsVO.listCharsNumber)
      miniChartRef1.value.drawChart()
      miniChartRef2.value.drawChart()
      miniChartRef3.value.drawChart()
    })
  }
  // console.log('dataCallback', detail.value.financeAnalyseTabCharsVO, detail.value.financeCustomerAnalyseVOPage)
  // console.log('dataCallback', data.financeCustomerAnalyseVOPage.records)
  if (data) {
    return {
      records: data.financeCustomerAnalyseVOPage.records,
      total: data.financeCustomerAnalyseVOPage.total,
      pageNum: data.financeCustomerAnalyseVOPage.current,
      pageSize: data.financeCustomerAnalyseVOPage.size
    }
  } else {
    // console.log('else')
    return {
      records: null,
      total: 0,
      pageNum: 1,
      pageSize: 10
    }
  }
}
const handleDateChange = () => {
  getDetail()
}

const handleClose = () => {
  visible.value = false
}

const miniChartRef1 = ref(null)
const miniChartRef2 = ref(null)
const miniChartRef3 = ref(null)

const chartRef = ref(null)
const drawChart = () => {
  const intance = echarts.init(chartRef.value)
  intance.setOption({
    series: {
      type: 'sankey',
      layout: 'none',
      nodeGap: 14,
      emphasis: {
        focus: 'adjacency'
      },
      data: detailSan.value.financeSanDataVO,
      links: detailSan.value.financeSanLinksVO // TODO 增加数据项
      // map(item => (item.value = parseInt(item.value)))
    }
  })
}

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    receiveAnalyse: undefined,
    analyseId: undefined,
    batchId: undefined,
    analyseDate: dayjs().format('YYYY-MM-DD')
  }
})
const { queryParams } = toRefs(data)
const handleSelectChange = (node, row) => {
  // console.log('handleSelectChange', node, row)
}
const handleNodeClick = node => {
  // console.log('handleNodeClick', node)
  receiveAnalyse.value = node.totalName + '   ' // 特地加个符号，让它与value不能对应，让select展示这个值
}
const receiveAnalyse = ref(null)
const handleQuery = () => {
  queryParams.value.receiveAnalyse = receiveAnalyse.value && receiveAnalyse.value.replace('   ', '')
  queryParams.value.pageNum = 1
  // getList()
}
const resetQuery = () => {
  receiveAnalyse.value = undefined
  handleQuery()
  // getList()
}

const proTable = ref(null)
const columnsAccount = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'customerName',
    label: '客户名称'
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '200'
  },
  {
    prop: 'paymentAmount',
    label: '账单总金额',
    width: '120',
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'receiptAmount',
    label: '已收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.receiptAmount >= 0 ? `${scope.row.receiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'receivableAmount',
    label: '应待收款',
    width: '100',
    render: scope => {
      return <span>{scope.row.receivableAmount >= 0 ? `${scope.row.receivableAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'receiveAnalyse',
    label: '收款分析',
    isColShow: false,
    fixed: 'right'
  }
]

const exportFormRef = ref(null)
const handleExportAll = () => {
  exportFormRef.value.onShow()
}

const { showDialog } = useDialog()

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = id => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
}
/* 客户详情弹窗 ---end--- */

defineExpose({
  onShow
})
</script>

<style lang="scss" scoped>
.my-box {
  display: flex;
  .left {
    width: 50%;
  }
  .right {
    width: 50%;
    .tit-line {
      display: flex;
      align-items: center;
      .tit {
        font-weight: bold;
      }
      .el-select--default {
        margin-left: 15px;
        margin-right: 15px;
        flex: 1 1 auto;
      }
    }
    .item-box {
      margin: 15px 0;
      display: flex;
      justify-content: space-between;
      .item {
        border: 1px solid #ccc;
        padding: 10px;
        text-align: center;
        width: 30%;
      }
      .item-chart {
        width: 32%;
      }
    }
  }
}
.my-table {
  margin-top: 15px;
  min-height: 300px;
  display: flex;
  .el-table__append-wrapper {
  }
  .card {
    box-sizing: border-box;
    padding: 0;
    overflow-x: hidden;
    background-color: none;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }
}
</style>
