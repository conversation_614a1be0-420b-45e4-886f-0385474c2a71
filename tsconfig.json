{
  "include": ["env.d.ts", "src/**/*.ts", "src/**/*.tsx","src/**/*.vue",],
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "allowJs": true,
    "noEmit": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "types": ["element-plus/global", 
   ],
    "baseUrl": "./",
    "paths": {
      "@/*": ["./src/*"],
      "components":["src/components/*"],
    },
  },
}

