<!--
 * @Description: 行政区划设置
 * @Author: thb
 * @Date: 2023-07-20 09:58:06
 * @LastEditTime: 2023-11-07 15:52:57
 * @LastEditors: thb
-->
<template>
  <ProTable ref="proTable" title="合同预警设置" :columns="columns" :request-api="getContractWarningList">
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
      <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
      <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
    </template>
  </ProTable>
</template>
<script setup lang="tsx">
import { ref } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  getContractWarningList,
  deleteContractWarning,
  getContractWarningDetail,
  addContractWarning
} from '@/api/basicData/basicData'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialog'
import warningForm from './components/warning-form.vue'
// import { addArea } from '@/api/basicData/basicData'
import { useHandleData } from '@/hooks/useHandleData'
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'name',
    label: '配置名称',
    width: 400,
    search: { el: 'input' }
  },
  {
    prop: 'contractType',
    width: 150,
    enum: [
      {
        label: '记账合同',
        value: '0'
      },
      {
        label: '一次性合同',
        value: '1'
      },
      {
        label: '地址服务协议合同',
        value: '2'
      }
    ],
    search: { el: 'select' },
    label: '合同类型'
  },
  {
    prop: 'notificationMethod',
    width: 250,
    // enum: [
    //   {
    //     label: '站内信通知',
    //     value: '0'
    //   },
    //   {
    //     label: '短信通知',
    //     value: '1'
    //   }
    // ],// 可以多选
    label: '通知方式'
  },
  {
    prop: 'remark',
    width: 400,
    label: '备注'
  },
  {
    prop: 'alertStatus',
    enum: [
      {
        label: '正常',
        value: '1'
      },
      {
        label: '停用',
        value: '0'
      }
    ],
    search: { el: 'select' },
    label: '状态',

    render: scope => {
      return (
        <>
          {
            <el-switch
              model-value={scope.row.alertStatus}
              active-text={scope.row.alertStatus === '1' ? '正常' : '停用'}
              active-value={'1'}
              inactive-value={'0'}
              onClick={() => changeStatus(scope.row)}
            />
          }
        </>
      )
    }
  },
  {
    prop: 'updateTime',
    width: 200,
    fixed: 'right',
    label: '修改时间'
  },
  {
    prop: 'operation',
    width: 200,
    fixed: 'right',
    label: '操作'
  }
]

// 修改状态
const changeStatus = async (row: any) => {
  await useHandleData(
    addContractWarning,
    {
      contractAlertId: row.contractAlertId,
      alertStatus: row.alertStatus === '1' ? '0' : '1'
    },
    `切换【${row.name}】状态`
  )
  proTable.value?.getTableList()
}

// 新增合同预警配置
const handleAdd = () => {
  showDialog({
    title: '新增',
    customClass: 'warning-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: warningForm, // 表单组件
    submitApi: addContractWarning, // 提交api
    submitCallback: submitCallback, // 提交成功之后的回调函数
    handleRevertParams: handleRevertParams // 处理提交参数
  })
}

// 创建业务
const { showDialog } = useDialog()
// 处理创建传递参数
const handleRevertParams = (data: any) => {
  data.alertDate = Number(data.alertDate)
}

const handleConvertParams = (data: any) => {
  // 表示详情
  data.type = 'detail'
}

const proTable = ref()
const submitCallback = () => {
  // 刷新列表
  proTable.value?.getTableList()
}

// 获取详情
const handleDetail = (row: any) => {
  showDialog({
    title: '详情',
    customClass: 'warning-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: false,
    // confirmButtonText: '提交',
    component: warningForm, // 表单组件
    requestParams: row.contractAlertId,
    getApi: getContractWarningDetail,
    // submitApi: addContractWarning, // 提交api
    // submitCallback: submitCallback // 提交成功之后的回调函数
    handleConvertParams
    // handleRevertParams: handleRevertParams2 // 处理提交参数
  })
}

// 编辑行政区划数据
const handleEdit = (row: any) => {
  showDialog({
    title: '编辑',
    customClass: 'warning-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: warningForm, // 表单组件
    requestParams: row.contractAlertId,
    getApi: getContractWarningDetail,
    submitApi: addContractWarning, // 提交api
    submitCallback: submitCallback // 提交成功之后的回调函数
    // handleRevertParams: handleRevertParams // 处理提交参数
  })
}

// 删除行政区划
const handleDelete = async (row: any) => {
  await useHandleData(deleteContractWarning, row.contractAlertId, `删除所选配置名称 ${row.name} 信息`)
  proTable.value?.getTableList()
}
</script>
<style lang="scss" scoped></style>
