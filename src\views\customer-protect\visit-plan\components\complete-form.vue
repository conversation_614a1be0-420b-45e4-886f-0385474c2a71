<!--
 * @Description: 完成计划
 * @Author: thb
 * @Date: 2023-07-28 10:53:47
 * @LastEditTime: 2023-11-16 13:15:04
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="实际拜访日期" prop="actualPlanDate">
          <el-date-picker
            v-model="formData.actualPlanDate"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择"
            clearable
          />
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="实际拜访方式" prop="actualVisitMethod">
          <el-select v-model="formData.actualVisitMethod" clearable placeholder="请选择">
            <el-option v-for="(option, index) in visit_method" :key="index" :label="option.label" :value="option.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="拜访反馈" prop="visitFeedback">
          <el-input
            v-model="formData.visitFeedback"
            maxlength="1000"
            type="textarea"
            placeholder="请输入"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="附件" prop="fileList">
          <FileUpload
            v-model="formData.fileList"
            @on-load-success="validateFormField('fileList')"
            :limit="99"
            :fileSize="20"
            :fileType="['jpg', 'jpeg', 'png', 'mp3', 'm4a', 'amr']"
            tipMessage="支持扩展名：jpg、jpeg、png、mp3、m4a、amr ，单个文件不能超过20MB"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import FileUpload from '@/components/FileUpload'

const formData = ref({
  actualPlanDate: '',
  actualVisitMethod: '',
  visitFeedback: ''
})
const { proxy } = getCurrentInstance()
const { visit_method } = proxy.useDict('visit_method')
const rules = {
  actualPlanDate: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  actualVisitMethod: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  visitFeedback: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  fileList: [
    {
      required: true,
      message: '请上传',
      trigger: 'change'
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

const validateFormField = field => {
  formRef.value.validateField(field)
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped>
.el-date-editor.el-input {
  width: 100%;
}

.el-select {
  width: 100%;
}

:deep(.el-form-item) {
  .el-date-editor {
    width: 100%;
  }
}
</style>
