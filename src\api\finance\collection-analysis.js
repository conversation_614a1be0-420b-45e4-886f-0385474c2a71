import request from '@/utils/request'

// 收款分析主列表查询
export const financeReceiptAnalyseList = params => {
  return request({
    url: '/financeReceiptAnalyse/list',
    method: 'get',
    params
  })
}

// 编辑时候列表查询
export const financeCustomerAnalyseGetByAnalyseId = params => {
  return request({
    url: '/financeCustomerAnalyse/getByAnalyseId',
    method: 'get',
    params
  })
}

// 新增时候列表查询
export const financeCustomerAnalyseList = params => {
  return request({
    url: '/financeCustomerAnalyse/list',
    method: 'get',
    params
  })
}

// 新增
export const financeCustomerAnalyseSave = query => {
  return request({
    url: '/financeCustomerAnalyse/save',
    method: 'post',
    data: query
  })
}

// 修改数据
export const financeCustomerAnalyseUpdate = query => {
  return request({
    url: '/financeCustomerAnalyse/update',
    method: 'post',
    data: query
  })
}

// 收款分析chart页tab查询
export const financeReceiptAnalyseAnalyseTabList = params => {
  return request({
    url: '/financeCustomerAnalyse/analyseTabList',
    method: 'get',
    params
  })
}

// 桑吉图查询
export const financeReceiptAnalyseGetSan = params => {
  return request({
    url: '/financeCustomerAnalyse/getSan',
    method: 'get',
    params
  })
}

// 关闭数据传batchId
export const financeReceiptAnalyseUpdateStatus = params => {
  return request({
    url: '/financeCustomerAnalyse/updateStatus',
    method: 'get',
    params
  })
}
