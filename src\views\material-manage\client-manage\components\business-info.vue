<template>
  <FormTable :formData="formData" :option="option">
    <template #index="{ $index }">
      {{ $index + 1 }}
    </template>
    <template #name="{ row }">
      <span class="blue-text" @click="checkBusinessDetail(row)">
        {{ row.name }}
      </span>
    </template>
  </FormTable>
  <businessDetail v-if="detailShow" :id="businessId" @on-close="handleClose" @on-success="getList" />
</template>
<script setup>
import FormTable from '@/components/FormTable/index.vue'
import { getClientBusinessListById } from '@/api/material-manage/client'
import businessDetail from '../../business/components/business-detail'
const props = defineProps({
  id: String,
  timestamp: String
})

watch(
  () => props.timestamp,
  () => {
    console.log('timeStamp', props.timestamp)
    getList()
  }
)
const formData = ref({
  tableData: []
})

const option = ref([
  {
    prop: 'index',
    label: '序号'
  },
  {
    prop: 'name',
    width: 150,
    label: '商机名称'
  },
  {
    prop: 'stage',
    width: 200,
    label: '销售阶段'
  },
  {
    prop: 'createTime',
    width: 200,
    label: '创建时间'
  },
  {
    prop: 'expectTime',
    width: 200,
    label: '预计成交时间'
  },
  {
    prop: 'expectAmount',
    width: 200,
    label: '预计成交金额(元)'
  },
  {
    prop: 'actualAmount',
    width: 200,
    label: '实际成交金额(元)'
  },
  {
    prop: 'winTime',
    fixed: 'right',
    width: 200,
    label: '实际成交时间'
  }
])

const emits = defineEmits(['on-success'])
const getList = async () => {
  const { data } = await getClientBusinessListById(props.id)
  formData.value.tableData = data || []
  emits('on-success')
}

// 查看商机详情
const businessId = ref()
const detailShow = ref(false)
const checkBusinessDetail = row => {
  businessId.value = row.id
  detailShow.value = true
}

const handleClose = () => {
  detailShow.value = false
  getList()
}
onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.form-rap {
  width: 800px;
}

:deep(.el-form-item.el-form-item--default) {
  line-height: normal;
  margin-bottom: 0;
}
</style>
