/*
 * @Description:
 * @Author: thb
 * @Date: 2023-06-16 14:40:03
 * @LastEditTime: 2024-02-22 10:30:55
 * @LastEditors: thb
 */
// 流程控制
// 业务管理api
import request from '@/utils/request'

export function getContractReviewList(query) {
  return request({
    url: '/bizFlow/list',
    method: 'get',
    params: query
  })
}

// 切换流程状态
export function changeProcessStatus(id) {
  return request({
    url: '/bizFlow/setStatus',
    method: 'post',
    params: {
      id
    }
  })
}

// 删除流程
export function deleteProcess(ids) {
  return request({
    url: '/bizFlow/deleteByIds?ids=' + ids.join(','),
    method: 'delete'
  })
}

// 获取审批人树结构
export function getReviewerTreeData() {
  return request({
    url: '/system/user/treeList',
    method: 'get',
    params: {
      enterpriseFlag: false
    }
  })
}

// 新增流程
export function addStep(data) {
  return request({
    url: '/bizFlow/saveOrUpdate',
    method: 'post',
    data
  })
}
// 获取流程详情接口
export function getStepDetail(id) {
  return request({
    url: '/bizFlow/getById',
    method: 'get',
    params: {
      id
    }
  })
}
