<template>
  <div class="reset-box">
    <!-- <div class="left"></div> -->
    <img src="@/assets/images/login-bg1.svg" />
    <div class="right">
      <el-form ref="resetRef" :model="resetForm" :rules="resetRules" class="reset-form" label-position="top">
        <h3 class="title">修改密码</h3>
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input v-model.trim="resetForm.oldPassword" placeholder="请输入旧密码" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model.trim="resetForm.newPassword" placeholder="请输入新密码" type="password" show-password />
        </el-form-item>
        <div class="info-text">
          <span class="icon warning-icon"></span>
          密码长度至少8位,请包括字母大小写、符号及数字
        </div>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model.trim="resetForm.confirmPassword" placeholder="请确认新密码" type="password" show-password />
        </el-form-item>
        <el-button
          type="primary"
          class="btn"
          :disabled="!(resetForm.oldPassword && resetForm.newPassword && resetForm.confirmPassword)"
          @click="resetPw(resetRef)"
          >保存</el-button
        >

        <el-button class="btn-back" @click="backToLogin">返回登录页</el-button>
      </el-form>
      <!-- <p class="footer">2023 “BoFeng” All Rights Reserved</p> -->
    </div>
    <!-- logo -->
    <img src="@/assets/logo/new-logo.svg" class="logo-fixed" />
  </div>
</template>
<script setup>
import { updateUserPwd } from '@/api/system/user'
import useUserStore from '@/store/modules/user'
import { removeToken } from '@/utils/auth'
const resetForm = ref({})
const router = useRouter()
const resetRules = {
  oldPassword: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  newPassword: [
    {
      required: true,
      // message: '请输入',
      validator: (rules, value, callback) => {
        //密码为八位及以上并且字母数字特殊字符三项都包括
        // let strongRegex = new RegExp('^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$', 'g')
        let strongRegex = /^(?![0-9]+$)(?![^0-9]+$)(?![a-zA-Z]+$)(?![^a-zA-Z]+$)(?![a-zA-Z0-9]+$)(?=.*[A-Z])[a-zA-Z0-9\S]{8,}$/
        if (resetForm.value.newPassword?.match(strongRegex)) {
          if (resetForm.value.newPassword === resetForm.value.oldPassword) {
            callback(new Error('新密码不能与旧密码相同'))
          } else {
            callback()
          }
        } else {
          callback(new Error('密码长度至少8位,请包括字母大小写、符号及数字'))
        }
      },
      trigger: ['blur']
    }
  ],
  confirmPassword: [
    {
      required: true,
      // message: '请输入',
      validator: (rules, value, callback) => {
        if (!resetForm.value.confirmPassword) {
          callback(new Error('请输入'))
        } else if (resetForm.value.confirmPassword && resetForm.value.confirmPassword !== resetForm.value.newPassword) {
          callback(new Error('与新密码不一致!'))
        } else {
          callback()
        }
      },
      trigger: ['blur']
    }
  ]
}

// 修改密码
const resetRef = ref()
const { proxy } = getCurrentInstance()
const resetPw = async formEl => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const result = await updateUserPwd(resetForm.value.oldPassword, resetForm.value.newPassword)
      if (result.code === 200) {
        proxy.$modal.msgSuccess('修改成功!')
        // 将原有的token信息和user信息删除 相当于退出登录
        removeToken()
        useUserStore().token = ''
        useUserStore().roles = []
        useUserStore().permissions = []

        // 修改成功之后跳到登录页
        router.push({
          path: '/login'
        })
      } else {
        proxy.$modal.msgError('修改失败!')
      }
    } else {
      console.log('检验失败', fields)
    }
  })
}

const backToLogin = () => {
  // 将原有的token信息和user信息删除 相当于退出登录
  removeToken()
  useUserStore().token = ''
  useUserStore().roles = []
  useUserStore().permissions = []
  router.push({
    path: '/login'
  })
}
</script>
<style lang="scss" scoped>
.reset-box {
  display: flex;
  width: 100%;
  height: 100%;
  // .left {
  //   background: url('../assets/images/login-bg1.svg') no-repeat;
  //   width: 700px;
  //   flex: 1;
  // }
  .right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
}

.reset-form {
  width: 400px;
  .title {
    height: 45px;
    line-height: 45px;
    font-size: 32px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    color: #333333;
    text-align: center;
  }
}
:deep(.el-form-item__label) {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #333333ff;
  font-weight: normal;
}
:deep(.el-input.el-input--default) {
  height: 48px;
}
.btn {
  width: 100%;
  height: 48px;
  margin-bottom: 16px;
  background: #4686ef;
  border-radius: 4px;
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #ffffff;
}
.btn-back {
  width: 100%;
  margin-left: 0;
  height: 48px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #4686ef;
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #4686ef;
}
.info-text {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #969ba4;
  display: flex;
  align-items: center;
}
.icon {
  margin-right: 8px;
}
.footer {
  position: absolute;
  bottom: 0px;
  width: 100%;
  height: 25px;
  line-height: 25px;
  text-align: center;
  letter-spacing: 1px;
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #646a73;
  margin: 0;
  margin-bottom: 40px;
  left: 50%;
  transform: translate(-50%);
}

.logo-fixed {
  position: fixed;
  left: 56px;
  top: 56px;
}
</style>
