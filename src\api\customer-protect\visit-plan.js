import request from '@/utils/request'
// 拜访计划列表查询
export const getVisitList = params => {
  params.planVisitDate = undefined
  params.actualPlanDate = undefined
  return request({
    url: '/customerVisit/list',
    method: 'get',
    params
  })
}

// 新增(编辑)拜访计划
export const saveVisit = data => {
  return request({
    url: '/customerVisit/save',
    method: 'post',
    data
  })
}

// 新增(编辑)拜访计划 批量
export const saveBatchVisit = data => {
  return request({
    url: '/customerVisit/batchSave',
    method: 'post',
    data
  })
}

// 获取拜访计划详情
export const getVisitDetail = id => {
  return request({
    url: '/customerVisit/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// // 完成计划
// export const completeVisit = data => {
//   return request({
//     url: '',
//     method: 'post',
//     data
//   })
// }

// // 取消计划
// export const cancelVisit = data => {
//   return request({
//     url: '',
//     method: 'post',
//     data
//   })
// }
// 删除计划
export const deleteVisit = id => {
  return request({
    url: '/customerVisit/delete',
    method: 'delete',
    params: {
      id
    }
  })
}

// 列表导出文件
export const customerVisitExport = query => {
  return request({
    url: '/customerVisit/export',
    method: 'post',
    data: query
  })
}
