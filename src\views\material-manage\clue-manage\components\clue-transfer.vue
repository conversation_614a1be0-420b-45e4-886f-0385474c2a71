<!--
 * @Description: 转让线索
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-08-14 13:21:54
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-alert
      v-if="formData.type === '0'"
      title="转出线索后,该线索将和您不再存在任何关系,且操作后无法恢复"
      type="warning"
      effect="dark"
      :closable="false"
    />
    <el-alert
      v-if="formData.type === '1'"
      title="转出客户后,该客户将和您不再存在任何关系,且操作后无法恢复"
      type="warning"
      effect="dark"
      :closable="false"
    />
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="转让接收人" prop="currentUserId">
          <SelectTree
            ref="selectRef"
            v-model="formData.currentUserId"
            placeholder="请选择"
            value-key="id"
            :render-after-expand="false"
            :defaultProps="defaultProps"
            show-checkbox
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import SelectTree from '@/components/SelectTree'
import useUserStore from '@/store/modules/user'
const formData = reactive({
  currentUserId: '',
  type: '0', //'0'代表线索转让
  id: undefined
})

const defaultProps = {
  value: 'id',
  label: 'label',
  children: 'children',
  disabled: (data, node) => {
    return (
      (data.type === '0' && Array.isArray(data.children) && data.children.length === 0) || data.id === useUserStore().user.userId
    )
  }
}
const selectRef = ref()
const rules = {
  currentUserId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
