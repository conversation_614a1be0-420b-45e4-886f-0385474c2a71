<template>
  <!-- <div>变更进度</div> -->
  <el-form ref="formRef" :model="data" label-width="120px" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="注册地址">
          <el-input v-model="data.registeredAddress" maxlength="250" disabled type="textarea" placeholder=" " />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="经营范围">
          <el-input v-model="data.scope" maxlength="1000" disabled type="textarea" placeholder=" " />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="注册资金">
          <el-input v-model="data.registeredCapital" disabled maxlength="100" placeholder=" " />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="股份比例">
          <el-input v-model="data.share" maxlength="20" disabled placeholder=" " />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="营业执照">
          <FileList :list="data.businessLicenseFileList" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="地址">
          <FileList :list="data.addressFileList" />
        </el-form-item>
      </el-col>
    </el-row>
    <Collapse title="办理进度汇报">
      <el-checkbox-group v-model="data.stageNameList">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-checkbox
              label="提交"
              @change="changeCheckboxSubmit"
              :disabled="
                data.businessChangeProcessRecordList?.filter(item => item.stageName === '提交')[0]?.completeTime || isDisabled
              "
            />
            <el-form-item label=" " prop="processName1">
              {{
                data.businessChangeProcessRecordList
                  ?.filter(item => item.stageName === '提交')[0]
                  ?.completeTime?.replace('T', ' ')
              }}</el-form-item
            ></el-col
          >
          <el-col :span="8">
            <el-checkbox
              label="审核"
              @change="changeCheckboxReview"
              :disabled="
                data.businessChangeProcessRecordList?.filter(item => item.stageName === '审核')[0]?.completeTime || isDisabled
              "
            />
            <el-form-item label=" " prop="processName2">
              {{
                data.businessChangeProcessRecordList
                  ?.filter(item => item.stageName === '审核')[0]
                  ?.completeTime?.replace('T', ' ')
              }}</el-form-item
            ></el-col
          >
          <el-col :span="8">
            <el-checkbox
              label="完成"
              @change="changeCheckboxComplete"
              :disabled="
                data.businessChangeProcessRecordList?.filter(item => item.stageName === '完成')[0]?.completeTime || isDisabled
              "
            />
            <el-form-item label=" " prop="processName3">
              {{
                data.businessChangeProcessRecordList
                  ?.filter(item => item.stageName === '完成')[0]
                  ?.completeTime?.replace('T', ' ')
              }}</el-form-item
            ></el-col
          >
        </el-row>
      </el-checkbox-group>
    </Collapse>
  </el-form>
</template>
<script setup>
import FileList from './file-list'
import Collapse from '@/components/Collapse'
const isDisabled = inject('disabled')
const props = defineProps({
  data: Object
})

const rules = {
  processName1: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  processName2: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  processName3: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

const changeCheckboxSubmit = value => {
  props.data.processName1 = value ? '提交' : ''
}

const changeCheckboxReview = value => {
  props.data.processName2 = value ? '审核' : ''
}

const changeCheckboxComplete = value => {
  props.data.processName3 = value ? '完成' : ''
}

const formRef = ref()

const validateForm = async () => {
  const result = await formRef.value.validate(valid => {
    if (valid) {
      console.log('检验成功')
    } else {
      console.log('校验失败')
      return false
    }
  })
  return result
}

const validateCheckedForm = async () => {
  //
  formRef.value.clearValidate()
  return true
}
defineExpose({
  formRef,
  rules,
  validateForm,
  validateCheckedForm
})
</script>
<style lang="scss" scoped>
:deep(.el-checkbox) {
  margin-right: 8px;
}
:deep(.el-col.el-col-8.is-guttered) {
  display: flex;
  align-items: baseline;
  .el-form-item {
    flex: 1;
  }
}
</style>
