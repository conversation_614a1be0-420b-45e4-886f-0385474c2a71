import request from '@/utils/request'
// 获取公海配置列表
export const getZoneSettingList = params => {
  return request({
    url: '/cusSea/list',
    method: 'get',
    params
  })
}

// 获取公海配置详情
export const getZoneSettingDetail = id => {
  return request({
    url: '/cusSea/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 新增或者编辑公海配置列表
export const saveZoneSetting = data => {
  return request({
    url: '/cusSea/saveOrUpdate',
    method: 'post',
    data
  })
}

// 删除公海配置
export const deleteZoneSetting = id => {
  return request({
    url: '/cusSea/delete',
    method: 'delete',
    params: {
      id
    }
  })
}
// 修改公海配置状态是否禁用
export const changeZoneSettingStatus = id => {
  return request({
    url: '/cusSea/setStatus',
    method: 'post',
    params: {
      id
    }
  })
}
