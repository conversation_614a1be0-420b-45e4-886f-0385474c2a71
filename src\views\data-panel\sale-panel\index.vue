<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-09-11 14:35:14
 * @LastEditTime: 2023-09-14 13:38:10
 * @LastEditors: thb
-->
<template>
  <div class="data-wrap">
    <div class="wrap-top">
      <topLeft />
      <topMid />
      <topRightNew />
    </div>
    <div class="wrap-mid">
      <midLeft />
      <midRight />
    </div>
    <div class="wrap-bottom">
      <bottom />
    </div>
  </div>
</template>
<script setup>
import topLeft from './components/top-left'
import topMid from './components/top-mid'
import topRight from './components/top-right'
import midLeft from './components/mid-left.vue'
import midRight from './components/mid-right.vue'
import bottom from './components/bottom'
import topRightNew from './components/top-right-new'
</script>
<style lang="scss" scoped>
.data-wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 16px;
  .wrap-top {
    flex: 1;
    display: flex;
    gap: 16px;
    // min-height: 319px;
  }
  .wrap-mid {
    flex: 1;
    display: flex;
    gap: 16px;
    // min-height: 319px;
  }
  .wrap-bottom {
    flex: 1;
    display: flex;
    min-height: 278px;
    overflow: hidden;
  }
}
</style>
