/*
 * @Description:
 * @Author: thb
 * @Date: 2023-05-19 15:10:43
 * @LastEditTime: 2023-07-27 15:55:46
 * @LastEditors: thb
 */
import useDictStore from '@/store/modules/dict'
import { getDicts } from '@/api/system/dict/data'
import { getDictList } from '@/api/system/baseData'
/**
 * 获取字典数据
 */
export function useBasicDict(...args) {
  const res = ref({})
  return (() => {
    args.forEach(dictType => {
      res.value[dictType] = []
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res.value[dictType] = dicts
      } else {
        getDictList({
          type: dictType
        }).then(resp => {
          // 修改 遍历树形结构
          // res.value[dictType] =
          //   resp.data.rows[0]?.childrenVO.map(p => ({
          //     label: p.name,
          //     value: p.name,
          //     elTagType: p.listClass,
          //     elTagClass: p.cssClass
          //   })) || []
          res.value[dictType] = resp.data.rows[0]?.childrenVO
          useDictStore().setDict(dictType, res.value[dictType])
        })
      }
    })
    return toRefs(res.value)
  })()
}
/**
 * 获取字典数据
 */
export function useDict(...args) {
  const res = ref({})
  return (() => {
    args.forEach(dictType => {
      res.value[dictType] = []
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res.value[dictType] = dicts
      } else {
        getDicts(dictType).then(resp => {
          res.value[dictType] = resp.data.map(p => ({
            label: p.dictLabel,
            value: p.dictValue,
            elTagType: p.listClass,
            elTagClass: p.cssClass
          }))
          useDictStore().setDict(dictType, res.value[dictType])
        })
      }
    })
    return toRefs(res.value)
  })()
}
/**
 * 获取字典数据(收款分析)
 */
export function useBasicDictAnalyse(...args) {
  const res = ref({})
  return (() => {
    args.forEach(dictType => {
      res.value[dictType] = []
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res.value[dictType] = dicts
      } else {
        getDictList({
          type: dictType
        }).then(resp => {
          resp.data.rows[0]?.childrenVO.map(item => {
            item.totalName = `${item.name}`
            if (item.children && item.children.length) {
              item.children.map(item1 => {
                item1.totalName = `${item.name} > ${item1.name}`
                if (item1.children && item1.children.length) {
                  item1.children.map(item2 => {
                    item2.totalName = `${item.name} > ${item1.name} > ${item2.name}`
                  })
                }
              })
            }
          })
          res.value[dictType] =
            resp.data.rows[0]?.childrenVO.map(p => ({
              name: p.name,
              totalName: p.totalName,
              label: p.name,
              value: p.name,
              children: p.children,
              elTagType: p.listClass,
              elTagClass: p.cssClass
            })) || []
          useDictStore().setDict(dictType, res.value[dictType])
        })
      }
    })
    return toRefs(res.value)
  })()
}
