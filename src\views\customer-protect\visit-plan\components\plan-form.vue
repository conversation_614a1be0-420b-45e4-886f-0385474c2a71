<!--
 * @Description: 计划表单--用于详情、新增
 * @Author: thb
 * @Date: 2023-07-28 09:02:15
 * @LastEditTime: 2023-11-27 14:09:07
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    :title="type === 'detail' ? '计划详情' : type === 'edit' ? '编辑计划' : '新增计划'"
    width="800"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="isDisabled">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="计划标题" prop="planName">
            <!-- <el-input
              v-model="formData.planName"
              maxlength="20"
              :disabled="isDisabled"
              :placeholder="isDisabled ? '' : '请输入'"
            /> -->
            <el-select
              v-model="formData.planName"
              :placeholder="isDisabled ? ' ' : '请选择'"
              clearable
              :disabled="isDisabled"
              allow-create
              filterable
            >
              <el-option v-for="(option, index) in visit_plan_name" :key="index" :label="option.label" :value="option.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="关联客户" prop="customerName">
            <!-- 点击弹窗出现客户列表  -->
            <div @click="handleShow" style="width: 100%">
              <el-input
                v-model="formData.customerName"
                readonly
                maxlength="20"
                :disabled="isDisabled"
                :placeholder="isDisabled ? '' : '请输入'"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划拜访日期" prop="planVisitDate">
            <el-date-picker
              v-model="formData.planVisitDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              type="date"
              :disabled="isDisabled"
              :placeholder="isDisabled ? ' ' : '请选择'"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划拜访方式" prop="planVisitMethod">
            <el-select
              v-model="formData.planVisitMethod"
              :placeholder="isDisabled ? ' ' : '请选择'"
              clearable
              :disabled="isDisabled"
              allow-create
              filterable
            >
              <el-option v-for="(option, index) in visit_method" :key="index" :label="option.label" :value="option.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="拜访人" prop="visitor"> <el-input v-model="formData.visitor" disabled /> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="拜访目的" prop="visitPurpose">
            <el-select
              multiple
              v-model="formData.visitPurpose"
              :placeholder="isDisabled ? ' ' : '请选择'"
              clearable
              :disabled="isDisabled"
            >
              <el-option v-for="(option, index) in visit_purpose" :key="index" :label="option.label" :value="option.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              maxlength="1000"
              type="textarea"
              :disabled="isDisabled"
              :placeholder="isDisabled ? ' ' : '请输入'"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 已完成的状态 -->
      <template v-if="status === '1' || formData.status === '1'">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="实际拜访日期" prop="actualPlanDate">
              <el-date-picker
                v-model="formData.actualPlanDate"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                disabled
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="实际拜访方式" prop="actualVisitMethod">
              <el-select v-model="formData.actualVisitMethod" disabled clearable>
                <el-option v-for="(option, index) in visit_method" :key="index" :label="option.label" :value="option.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="拜访打卡" prop="locationName">
              <el-input v-model="formData.locationName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="拜访反馈" prop="visitFeedback">
              <el-input
                v-model="formData.visitFeedback"
                type="textarea"
                maxlength="1000"
                disabled
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件" prop="fileList">
              <FileList :list="formData.fileList" />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 已取消的状态 -->
      <el-row :gutter="24" v-if="status === '2'">
        <el-col :span="24">
          <el-form-item label="取消原因" prop="cancelReason">
            <el-input
              v-model="formData.cancelReason"
              type="textarea"
              maxlength="1000"
              disabled
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <!-- 待完成 -->
      <template v-if="type === 'detail' && (userStore.user.admin || userStore.user.userId === formData.visitorId)">
        <template v-if="status === '0'">
          <el-button type="primary" @click="handleCancel" pain>取消计划</el-button>
          <el-button type="primary" @click="handleComplete">完成计划</el-button>
          <el-button type="primary" @click="handleEdit">编辑</el-button>
        </template>
        <!-- 已取消 删除功能 -->
        <template v-if="status === '2' && (userStore.user.admin || userStore.user.userId === formData.visitorId)">
          <el-button type="danger" @click="handleDelete">删除</el-button>
        </template>
      </template>
      <el-button type="primary" v-if="type !== 'detail'" @click="handleSubmit(formRef)">保存</el-button>
      <el-button @click="handleClose">{{ type === 'detail' ? '关闭' : '取消' }}</el-button>
    </template>
  </el-dialog>

  <tableModal
    v-if="listSelectShow"
    :init-param="{ discard: 0 }"
    rowKey="customerId"
    title="关联客户"
    :columns="columns"
    multiple
    :request-api="getCustomers"
    @on-close="listSelectShow = false"
    @on-multiple-select="handleSelect"
  />
</template>
<script setup lang="tsx">
import tableModal from '@/components/tableModal'
import { getCustomers } from '@/api/customer/file'
import { ColumnProps } from '@/components/ProTable/interface'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import { useDic } from '@/hooks/useDic'
import useUserStore from '@/store/modules/user'
import { useDialog } from '@/hooks/useDialog'
import completeForm from './complete-form.vue'
import cancelForm from './cancel-form.vue'
import { completeVisit, saveVisit, saveBatchVisit, getVisitDetail, deleteVisit } from '@/api/customer-protect/visit-plan'
import { useHandleData } from '@/hooks/useHandleData'
import FileList from '@/views/certificate/components/file-list'
import { useSetDic } from '@/hooks/useSetDic'
const { setDic } = useSetDic()
const { showDialog } = useDialog()
const userStore = useUserStore()
const { getDic } = useDic()
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success', 'on-edit'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number,
  status: String,
  type: {
    type: String,
    default: 'add' //默认为add ,detail
  }
})
const isDisabled = computed(() => {
  return props.type === 'detail'
})
//  拜访方式
const { proxy } = getCurrentInstance()
const { visit_plan_name } = proxy.useDict('visit_plan_name')
const { visit_method } = proxy.useDict('visit_method')
const { visit_purpose } = proxy.useDict('visit_purpose')

const formData = ref({})
console.log('user', userStore.user)
if (props.type === 'add') {
  formData.value.visitor = userStore.user.nickName
  formData.value.visitorId = userStore.user.userId
}

const rules = {
  planName: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  customerName: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ],
  planVisitDate: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  planVisitMethod: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  visitPurpose: [
    {
      required: true,
      message: '请输入',
      trigger: 'blur'
    }
  ]
}

const formRef = ref()

const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      let result
      if (formData.value.id) {
        result = await saveVisit({
          ...formData.value,
          visitPurpose: formData.value.visitPurpose.join('/')
        })
      } else {
        result = await saveBatchVisit({
          ...formData.value,
          visitPurpose: formData.value.visitPurpose.join('/')
        })
      }
      if (result.code === 200) {
        if (visit_plan_name.value.findIndex(item => item.value === formData.value.planName) === -1) {
          setDic('visit_plan_name', formData.value.planName, formData.value.planName, visit_plan_name)
        }
        proxy.$modal.msgSuccess(`保存成功!`)
        handleClose()

        emits('on-success')
      } else {
        proxy.$modal.msgError(`保存失败!`)
      }
    } else {
    }
  })
}
// 关联客户弹窗显示
const listSelectShow = ref(false)
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

const handleSelect = (selectedList: any) => {
  console.log('selectedList', selectedList)
  const nameStr = `${selectedList[0].customerName}${selectedList.length === 1 ? '' : `等${selectedList.length}家企业`}`
  formData.value = Object.assign(formData.value, {
    // ciId: customerId,
    customerName: nameStr,
    // selectedList,
    customerIdList: selectedList.map(item => item.customerId)
  })
  // 自动校验
  formRef.value.validateField('customerName')
}

// 关联客户弹窗显示
const handleShow = () => {
  // 如果是详情状态 不需要显示弹窗
  if (isDisabled.value) return
  listSelectShow.value = true
}

// 需要将计划拜访日期和计划拜访方式填充到实际拜访日期和实际拜访方式下
const handleConvertParams = data => {
  const keys = Object.keys(formData.value)
  keys.forEach(key => {
    data[key] = formData.value[key]
  })
  data.visitPurpose = formData.value.visitPurpose.join('/')
  data.actualPlanDate = formData.value.planVisitDate
  data.actualVisitMethod = formData.value.planVisitMethod
}

// 提交成功之后的回调函数
const submitCallback = () => {
  console.log('submitCallbackddddddddddddd')
  emits('on-success')
  handleClose()
}

// 提交参数自定义
const handleRevertParams = data => {
  data.status = '1'
}
// 完成计划
const handleComplete = () => {
  showDialog({
    title: '完成计划',
    customClass: 'complete-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: completeForm, // 表单组件
    submitApi: saveVisit, // 提交api
    submitCallback, // 提交成功之后的回调函数
    handleConvertParams,
    handleRevertParams
    // handleRevertParams: handleRevertParams // 处理提交参数
  })
}

const handleRevertParams1 = data => {
  const keys = Object.keys(formData.value)
  keys.forEach(key => {
    if (key !== 'cancelReason') {
      data[key] = formData.value[key]
    }
  })
  data.visitPurpose = formData.value.visitPurpose.join('/')
  data.status = '2'
}
// 取消计划
const handleCancel = () => {
  showDialog({
    title: '取消计划',
    customClass: 'complete-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: cancelForm, // 表单组件
    submitApi: saveVisit, // 提交api
    submitCallback, // 提交成功之后的回调函数
    handleRevertParams: handleRevertParams1
    // handleRevertParams: handleRevertParams // 处理提交参数
  })
}

// 编辑
const handleEdit = () => {
  emits('on-edit', props.id)
}

// handleDelete
const handleDelete = async () => {
  await useHandleData(deleteVisit, props.id, '删除该计划')
  handleClose()
  emits('on-success')
}
// 获取计划详情
const getPlanDetail = async id => {
  const { data } = await getVisitDetail(id)
  formData.value = Object.assign(formData.value, data)
  formData.value.visitPurpose = data.visitPurpose.split('/')
}
onMounted(() => {
  if (props.id && props.type !== 'add') {
    getPlanDetail(props.id)
  }
})
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
:deep(.el-form-item) {
  .el-date-editor {
    width: 100%;
  }
}
</style>
