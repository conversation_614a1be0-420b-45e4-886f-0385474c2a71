<template>
  <ProTable :init-param="initParam" ref="proTable" row-key="id" :columns="columns" :request-api="getAddressPropertyOwnershipList">
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button v-hasPermi="['address-report:premises-permit-manage:add']" type="primary" :icon="CirclePlus" @click="handleAdd"
        >新增</el-button
      >
    </template>
    <!-- 表格 header 按钮 -->
    <template #name="{ row }">
      <span class="blue-text" @click="handlDetail(row)">{{ row.name }}</span>
    </template>
    <template #leaseStartDate="{ row }">
      <span>{{ row.leaseStartDate }}</span>
      <!-- :class="{ 'danger-status': dayjs().valueOf() > dayjs(row.leaseEndDate).valueOf() }" -->
    </template>
    <template #leaseEndDate="{ row }">
      <span :class="{ 'danger-status': dayjs().valueOf() > dayjs(row.leaseEndDate).add(1, 'day').valueOf() }">{{
        row.leaseEndDate
      }}</span>
    </template>
    <template #enable="{ row }">
      <el-switch active-value="1" inactive-value="0" v-model="row.enable" @click="handleChangeStatus(row)" />
    </template>
    <template #useStatus="{ row }">
      <span v-if="row.useStatus" :style="`color:${useStatusColorMap[row.useStatus]}`">
        {{ useStatusMap[row.useStatus] }}
      </span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button
        v-if="scope.row.useStatus !== 'in_use'"
        v-hasPermi="['address-report:premises-permit-manage:edit']"
        type="primary"
        link
        @click="handlEdit(scope.row)"
        >编辑</el-button
      >
      <el-button
        v-if="scope.row.useStatus !== 'in_use'"
        v-hasPermi="['address-report:premises-permit-manage:delete']"
        type="danger"
        link
        @click="handleDelete(scope.row)"
        >删除</el-button
      >
    </template>
  </ProTable>
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  getAddressPropertyOwnershipList,
  deleteAddressPropertyOwnershipDelete,
  postAddressPropertyOwnershipEnable
} from '@/api/address-report/index.js'
import { removeAddressProvider } from '@/api/address-provider'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from './components/form-modal.vue'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { useHandleData } from '@/hooks/useHandleData'
import dayjs from 'dayjs'

const useStatusMap = {
  idle: '闲置',
  expired: '已到期',
  in_use: '使用中'
}
// 根据useStatusMap创建字典数组
const useStatusOptions = Object.keys(useStatusMap).map(key => ({ label: useStatusMap[key], value: key }))
const useStatusColorMap = {
  idle: '#409EFF',
  expired: '#F56C6C',
  in_use: '#409EFF'
}
const { proxy } = getCurrentInstance()

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}

const initParam = reactive({})
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id 1
  {
    prop: 'name',
    label: '房本名称',
    search: { el: 'input' },
    minWidth: 300
  },
  {
    prop: 'leasePrice',
    label: '租赁价格',
    minWidth: 150,
    render: ({ row }) => {
      return <span>{row.leasePrice || '0'}元</span>
    }
  },
  {
    prop: 'lessorName',
    label: '出租人名称',
    search: { el: 'input' },
    minWidth: 150
  },
  {
    prop: 'phone',
    label: '电话号码',
    minWidth: 150
  },
  {
    prop: 'leaseStartDate',
    label: '租赁期起',
    minWidth: 150
  },
  {
    prop: 'leaseEndDate',
    label: '租赁期止',
    minWidth: 150
  },
  {
    prop: 'remark',
    label: '备注',
    minWidth: 150
  },
  {
    prop: 'useStatus',
    label: '领用状态',
    enum: useStatusOptions,
    search: { el: 'select' },
    minWidth: 150
  },
  {
    prop: 'enable',
    label: '状态',
    minWidth: 150
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 150
  }
]

function handleChangeStatus(row: any) {
  // console.log('row.enable',row.enable);
  const formData = { ...row, enable: row.enable }
  postAddressPropertyOwnershipEnable(formData).then(res => {
    if (res.code === 200) {
      proxy.$modal.msgSuccess('保存成功')
      getList()
    }
  })
}

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handlDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}
const handleAdd = () => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onAdd()
  })
}
const handlEdit = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onEdit(row)
  })
}
const handleDelete = async (row: any) => {
  await useHandleData(deleteAddressPropertyOwnershipDelete, row, `删除所选房本 ${row.name} 信息`)
  proTable.value?.getTableList()
}
// onMounted(() => {
// })
</script>
<style lang="scss" scoped>
.success-status {
  color: #409eff;
}

.danger-status {
  color: #f56c6c;
}
</style>
