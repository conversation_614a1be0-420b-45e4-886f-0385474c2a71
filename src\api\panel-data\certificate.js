/*
 * @Description:
 * @Author: thb
 * @Date: 2023-09-06 08:35:27
 * @LastEditTime: 2023-09-07 16:06:50
 * @LastEditors: thb
 */
import request from '@/utils/request'

// 获取办证数量
export const getCertificateNums = () => {
  return request({
    url: '/kanban/license/licenseAnalysis',
    method: 'get'
  })
}

// // 获取许可证业务数量
export const getLicenceNums = () => {
  return request({
    url: '',
    method: 'get'
  })
}

// 获取办证效率数据
export const getCertificateHandlingEfficiency = params => {
  return request({
    url: '/kanban/license/licenseEfficiency',
    method: 'get',
    params
  })
}
