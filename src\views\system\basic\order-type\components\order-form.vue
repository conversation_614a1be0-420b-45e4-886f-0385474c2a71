<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-07-18 13:21:06
 * @LastEditTime: 2023-11-29 16:04:50
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="isDisabled">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="上级菜单" prop="parentId">
          <el-tree-select
            v-model="formData.parentId"
            :default-expanded-keys="[0]"
            :data="menuOptions"
            :props="{
              value: 'id',
              label: 'typeName',
              children: 'child'
            }"
            value-key="id"
            placeholder="选择上级菜单"
            check-strictly
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="formData.typeName" maxlength="10" :disabled="isDisabled" :placeholder="isDisabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="显示排序" prop="sort">
          <NumberInput v-model="formData.sort" :disabled="isDisabled" :placeholder="isDisabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status" :disabled="isDisabled">
            <el-radio label="1">正常</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row> -->
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="补充说明" prop="supplementExplain">
          <el-input
            v-model="formData.supplementExplain"
            maxlength="1000"
            :disabled="isDisabled"
            type="textarea"
            :placeholder="isDisabled ? '' : '请输入'"
            :autosize="{ minRows: 2, maxRows: 6 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            maxlength="1000"
            :disabled="isDisabled"
            type="textarea"
            :placeholder="isDisabled ? '' : '请输入'"
            :autosize="{ minRows: 2, maxRows: 6 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import { reactive } from 'vue'
import NumberInput from '@/components/NumberInput'
import { getOrderList } from '@/api/work/work'
const menuOptions = ref([])

/** 查询菜单下拉树结构 */
const getTreeselect = () => {
  getOrderList().then(res => {
    const temp = [
      {
        id: '0',
        typeName: '全部',
        child: res.data
      }
    ]
    menuOptions.value = temp
  })
}

getTreeselect()
const rules = {
  typeName: [{ required: true, message: '请输入', trigger: 'blur' }],

  sort: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入'
    }
  ]
}
const formData = reactive({
  typeName: '',
  status: '1',
  sort: '',
  supplementExplain: '',
  ancestors: '',
  remark: '',
  id: undefined
})
const isDisabled = computed(() => {
  return formData.type === 'detail'
})
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped></style>
