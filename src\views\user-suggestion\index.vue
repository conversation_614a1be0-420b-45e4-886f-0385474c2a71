<template>
  <ProTable
    v-if="initParam.handleStatus === 'pending'"
    :init-param="initParam"
    :requestAuto="true"
    ref="proTable"
    row-key="id"
    :columns="columns"
    :request-api="getUserSuggestionList"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.handleStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dictValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #handleStatus="{ row }">
      <!-- :class="row.handleStatus === 'pending' ? 'blue-text' : ''" -->
      <span>{{ row.handleStatus === 'pending' ? '待处理' : '已处理' }}</span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button v-if="scope.row.handleStatus === 'pending'" type="primary" link @click="handleDeal(scope.row)">回复</el-button>
      <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
    </template>
  </ProTable>
  <ProTable
    v-if="initParam.handleStatus === 'complete'"
    :init-param="initParam"
    :requestAuto="true"
    ref="proTable"
    row-key="id"
    :columns="columnsExtra"
    :request-api="getUserSuggestionList"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.handleStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dictValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #handleStatus="{ row }">
      <!-- :class="row.handleStatus === 'pending' ? 'blue-text' : ''" -->
      <span>{{ row.handleStatus === 'pending' ? '待处理' : '已处理' }}</span>
    </template>
    <template #star="{ row }">
      <el-rate v-model="row.star" disabled />
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button v-if="scope.row.handleStatus === 'pending'" type="primary" link @click="handleDeal(scope.row)">回复</el-button>
      <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
    </template>
  </ProTable>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { postUserCustomerBindRecordUnbind } from '@/api/customer-user/customer-list.js'
import { getUserSuggestionList } from '@/api/user-suggestion/user-suggestion.js'
import { CirclePlus } from '@element-plus/icons-vue'
import formModal from './components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import { ElMessageBox, ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()

const proTable = ref()

const getList = async (newDic: any) => {
  proTable.value?.getTableList()
}
const initParam = reactive({
  handleStatus: 'pending'
})
const tabs = ref([
  {
    dictLabel: '待处理',
    dictValue: 'pending'
  },
  {
    dictLabel: '已处理',
    dictValue: 'complete'
  }
])
const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.handleStatus = e
}
function columnsFun(newDic?: any) {
  return [
    { type: 'index', fixed: 'left', width: 50 },
    {
      prop: 'userName',
      label: '用户姓名',
      search: { el: 'input' },
      minWidth: 150
    },
    {
      prop: 'customerName',
      label: '企业名称',
      search: { el: 'input' },
      minWidth: 300
    },
    {
      prop: 'suggestionData',
      label: '建议内容',
      minWidth: 200
    },
    {
      prop: 'publishTime',
      label: '发布时间',
      minWidth: 200
    },
    {
      prop: 'handleStatus',
      label: '状态',
      minWidth: 100
    },
    {
      prop: 'operation',
      label: '操作',
      fixed: 'right',
      width: 200
    }
  ]
}
// 表格配置项
const columns = ref(columnsFun())
const columnsExtra = ref(columnsFun())
columnsExtra.value.splice(10, 0, {
  prop: 'responseUserName',
  label: '回复人',
  width: 150
})
columnsExtra.value.splice(10, 0, {
  prop: 'responseTime',
  label: '回复时间',
  width: 180
})
columnsExtra.value.splice(10, 0, {
  prop: 'star',
  label: '评价',
  width: 150
})

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handleDeal = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDeal({ id: row.id })
  })
}

const handleDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail({ id: row.id })
  })
}

const handleUnBind = async (row: any, index: number) => {
  ElMessageBox.confirm(`是否确认将 ${row.userName} 与 ${row.customerName} 解除授权绑定？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      postUserCustomerBindRecordUnbind([row.id]).then(res => {
        if (res.code === 200) {
          ElMessage.success('解绑成功')
          proTable.value?.getTableList()
        }
      })
    })
    .catch(() => {})
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

onMounted(() => {})
</script>
<style lang="scss" scoped></style>
