import request from '@/utils/request'

/** 材料交接记录 */
// "name": "详情"
// "method": "get"
// "path": "/materialHandoverRecord/getById"
export const materialHandoverRecordGetById = params => {
  return request({
    url: '/materialHandoverRecord/getById',
    method: 'get',
    params
  })
}

// "name": "列表查询"
// "method": "get"
// "path": "/materialHandoverRecord/list"
export const materialHandoverRecordList = params => {
  return request({
    url: '/materialHandoverRecord/list',
    method: 'get',
    params
  })
}

// "name": "保存数据"
// "method": "post"
// "path": "/materialHandoverRecord/save"
export const materialHandoverRecordSave = params => {
  return request({
    url: '/materialHandoverRecord/save',
    method: 'post',
    data: params
  })
}

/** 材料入库记录 */
// "name": "详情"
// "method": "get"
// "path": "/materialInboundRecord/getById"
export const materialInboundRecordGetById = params => {
  return request({
    url: '/materialInboundRecord/getById',
    method: 'get',
    params
  })
}

// "name": "列表查询"
// "method": "get"
// "path": "/materialInboundRecord/list"
export const materialInboundRecordList = params => {
  return request({
    url: '/materialInboundRecord/list',
    method: 'get',
    params
  })
}

// "name": "保存数据"
// "method": "post"
// "path": "/materialInboundRecord/save"
export const materialInboundRecordSave = params => {
  return request({
    url: '/materialInboundRecord/save',
    method: 'post',
    data: params
  })
}

/** 材料入库明细 */
// "name": "列表查询"
// "method": "get"
// "path": "/materialInboundRecordDetail/list"
export const materialInboundRecordDetailList = params => {
  return request({
    url: '/materialInboundRecordDetail/list',
    method: 'get',
    params
  })
}

/** 材料库存记录 */
// "name": "删除记录"
// "method": "get"
// "path": "/materialStockRecord/(/deleteRecordList"
export const materialStockRecordDeleteRecordList = params => {
  return request({
    url: '/materialStockRecord/deleteRecordList',
    method: 'get',
    params
  })
}

// "name": "根据id删除"
// "method": "delete"
// "path": "/materialStockRecord/delete"
export const materialStockRecordDelete = params => {
  return request({
    url: '/materialStockRecord/delete',
    method: 'delete',
    params
  })
}

// "name": "获取可交接库存"
// "method": "get"
// "path": "/materialStockRecord/getAvailableStockCount"
export const materialStockRecordGetAvailableStockCount = params => {
  return request({
    url: '/materialStockRecord/getAvailableStockCount',
    method: 'get',
    params
  })
}

// "name": "详情"
// "method": "get"
// "path": "/materialStockRecord/getById"
export const materialStockRecordGetById = params => {
  return request({
    url: '/materialStockRecord/getById',
    method: 'get',
    params
  })
}

// "name": "列表查询"
// "method": "get"
// "path": "/materialStockRecord/list"
export const materialStockRecordList = params => {
  return request({
    url: '/materialStockRecord/list',
    method: 'get',
    params
  })
}

// 导入材料
export const materialInboundRecordImportMaterial = query => {
  return request({
    url: '/materialInboundRecord/importMaterial',
    method: 'post',
    data: query
  })
}

// 接受交接单
export const acceptHandoverRecord = params => {
  return request({
    url: '/materialHandoverRecord/accept',
    method: 'post',
    params
  })
}

// 拒绝交接单
export const refuseHandoverRecord = data => {
  return request({
    url: '/materialHandoverRecord/refuse',
    method: 'post',
    data
  })
}
