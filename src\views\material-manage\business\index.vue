<template>
  <ProTable ref="proTable" title="商机管理" :columns="columns" :request-api="getClientBusinessList">
    <template #name="{ row }">
      <span class="blue-text" @click="showDetail(row)">
        {{ row.name }}
      </span>
    </template>
    <template #expectAmount="{ row }">
      <span v-if="row.expectAmount"> {{ row.expectAmount }}元</span>
      <span v-else>--</span>
    </template>
    <!-- actualAmount -->
    <template #actualAmount="{ row }">
      <span v-if="row.actualAmount"> {{ row.actualAmount }}元</span>
      <span v-else>--</span>
    </template>
  </ProTable>
  <businessDetail v-if="detailShow" :id="businessId" @on-close="handleClose" @on-success="getList" />
</template>
<script setup lang="tsx">
import { getClientBusinessList } from '@/api/material-manage/client'
import { ColumnProps } from '@/components/ProTable/interface'
import { useDic } from '@/hooks/useDic'
import businessDetail from './components/business-detail'
const { getDic } = useDic()
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '序号',
    fixed: 'left',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    width: 300,
    label: '商机名称',
    fixed: 'left',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'companyName',
    width: 300,
    search: {
      el: 'input'
    },
    label: '客户名称'
  },

  {
    prop: 'stagePercentage',
    width: 100,
    label: '阶段百分比'
  },
  {
    prop: 'followStatus',
    width: 150,
    enum: [
      {
        label: '未跟进',
        value: '0'
      },
      {
        label: '跟进中',
        value: '1'
      },
      {
        label: '已成单',
        value: '2'
      },
      {
        label: '已输单',
        value: '3'
      }
    ],
    search: {
      el: 'select'
    },
    label: '跟进状态'
  },
  {
    prop: 'expectAmount',
    width: 150,
    label: '预计成交金额'
  },
  {
    prop: 'expectTime',
    width: 200,
    label: '预计成交时间'
  },
  {
    prop: 'actualAmount',
    width: 150,
    label: '实际成交金额'
  },
  {
    prop: 'winTime',
    width: 200,
    label: '实际成交时间'
  },
  {
    prop: '',
    width: 200,
    label: '跟进人'
  },
  {
    prop: 'createTime',

    width: 200,
    label: '创建时间'
  },
  {
    prop: 'stage',
    width: 200,
    enum: getDic('sale_stage'),
    search: {
      el: 'select'
    },
    label: '销售阶段',
    fixed: 'right'
  }
]

// 显示商机详情
const businessId = ref()
const detailShow = ref(false)
const showDetail = (row: any) => {
  businessId.value = row.id
  detailShow.value = true
}
const proTable = ref('')
const getList = () => {
  proTable.value?.getTableList()
}
const handleClose = () => {
  detailShow.value = false
  getList()
}
</script>
<style lang="scss" scoped></style>
