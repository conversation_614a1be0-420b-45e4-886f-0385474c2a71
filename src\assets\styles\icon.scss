.icon {
  display: inline-block;
  width: 28px;
  height: 28px;
  cursor: pointer;
}

.avatar-icon{
  width: 24px;
  height: 24px;
  background: url('@/assets/images/avatar.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.circle-icon {
  width: 32px;
  height: 32px; 
  font-size: 20px;
  font-weight: bold;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-icon-default{
  background: #2383E7;
  border: 1px solid #fff;
  color: #FFFFFF;
}

.arrow-icon-right{
  width: 16px;
  height: 16px;
  background: url('@/assets/icons/arrow-icon-right.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.close-icon-small {
  width: 24px;
  height: 24px;
  background: url('@/assets/icons/close-icon-small.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.clue-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/clue-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.client-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/client-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}
.business-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/business-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.num-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/certificate-num-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.sign-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/sign-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}


.sign-icon-1 {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/sign-icon-1.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.export-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/export-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.labor-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/labor-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.verification-icon{
  width: 20px;
  height:20px;
  background: url('@/assets/icons/verification-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.food-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/food-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.load-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/load-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.other-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/other-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.account-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/account-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.another-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/another-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.nofee-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/nofee-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}


.certify-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/certify-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.transfer-icon{
  width: 12px;
  height:12px;
  background: url('@/assets/icons/arrow.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.com-icon{
  width: 24px;
  height:24px;
  background: url('@/assets/icons/business-title-1.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.title-icon{
  width: 20px;
  height:20px;
  background: url('@/assets/icons/business-title-2.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.warning-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/warning-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.shrink-icon {
  width: 16px;
  height:16px;
  background: url('@/assets/icons/shrink-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.arrow-left-sl {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/arrow-left-sl.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.book-icon{
  width: 24px;
  height:24px;
  background: url('@/assets/icons/book-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.review-icon {
  width: 24px;
  height:24px;
  background: url('@/assets/icons/review-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.upload-icon-sm{
  width: 20px;
  height:20px;
  background: url('@/assets/icons/upload-icon-sm.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.person-icon {
  width: 20px;
  height:20px;
  background: url('@/assets/icons/person-icon.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
}