import request from '@/utils/request'

export const getClueAppealRecordGetById = params => {
  return request({
    url: '/clueAppealRecord/getById',
    method: 'get',
    params
  })
}

export const getClueAppealRecordList = params => {
  params.createTime = undefined
  return request({
    url: '/clueAppealRecord/list',
    method: 'get',
    params
  })
}

export const postClueAppealRecordSave = params => {
  return request({
    url: '/clueAppealRecord/save',
    method: 'post',
    data: params
  })
}

export const postClueAppealRecordUpdate = params => {
  return request({
    url: '/clueAppealRecord/update',
    method: 'post',
    data: params
  })
}
