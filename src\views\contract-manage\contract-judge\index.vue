<template>
  <ProTable
    ref="proTable"
    title="合同评审"
    :init-param="initParam"
    :columns="columns"
    :request-api="getTabList"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <!-- 表格tabs -->
    <template #tabs>
      <el-radio-group :model-value="initParam.tabType" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #tableHeader>
      <el-button :icon="Download" @click="handleExport" v-hasPermi="['contract-manage:contract-judge:export']">导出</el-button>
    </template>
    <!-- 操作 -->
    <template #operation="{ row }">
      <!-- 变更操作 -->
      <el-button link type="primary" v-if="initParam.tabType !== '-1'" @click="handleDetail(row, 0)">详情</el-button>
      <!-- 审批 -->
      <el-button link type="primary" v-if="initParam.tabType === '-1' && row.reviewStatus === '0'" @click="handleDetail(row, 1)"
        >审批</el-button
      >
    </template>

    <template #customerName="{ row }">
      <span class="blue-text" @click="handleShowCustomerDetail(row.ciId)">{{ row.customerName }}</span>
    </template>
    <template #totalCost="{ row }">
      <span>{{ row.totalCost + '元' }}</span>
    </template>
    <!-- reviewStatus -->
    <template #reviewStatus="{ row }">
      <el-tag :type="statusMap[row.reviewStatus]">{{ typeMap[row.reviewStatus] }}</el-tag>
    </template>
  </ProTable>
  <reviewDetail
    :id="rowId"
    :reviewType="reviewType"
    :isChange="isChange"
    v-if="reviewShow"
    @on-close="reviewShow = false"
    @on-success="handleSuccess"
  />

  <customerDetail v-if="customerDetailShow" :id="customerId" :hideActionBtn="true" @on-close="customerDetailShow = false" />
</template>
<script setup lang="tsx">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ColumnProps } from '@/components/ProTable/interface'
import reviewDetail from './components/review-detail.vue'
import {
  getCustomerContractAuditList,
  getContractReviewListByUser,
  getCustomerContractAuditListExport,
  getContractReviewListByUserExport
} from '@/api/contract/contract'
import { useCustomer } from '@/hooks/useCustomer'
import useCommonStore from '@/store/modules/common'
import { getContractDetailById } from '@/api/contract/contract'
import { getBusinessList } from '@/api/business/business'
import InputRange from '@/views/finance/day-book-statement/components/input-range'
import { Download } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()
const useCommon = useCommonStore()
const { customerDetailShow, rowId: customerId, handleShowCustomerDetail, customerDetail } = useCustomer()
const route = useRoute()
const tabs = [
  {
    dictLabel: '待我审批',
    dicValue: '-1'
  },
  {
    dictLabel: '待审批',
    dicValue: '0'
  },
  {
    dictLabel: '通过',
    dicValue: '1'
  },
  {
    dictLabel: '不通过',
    dicValue: '2'
  }
]
const typeMap = {
  '0': '待审批',
  '1': '通过',
  '2': '不通过'
}

const statusMap = {
  '0': '',
  '1': 'success',
  '2': 'danger'
}

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ tabType: '-1' })
const columns: ColumnProps<any>[] = [
  {
    prop: 'customerName',
    label: '客户名称',
    width: 300,
    isShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'contractNo',
    label: '合同编号',
    width: '300',
    search: { el: 'input', order: 4 }
  },
  {
    prop: 'contractType',
    label: '合同类型',
    width: '200',
    enum: [
      {
        label: '记账合同',
        value: '0'
      },
      {
        label: '一次性合同',
        value: '1'
      },
      {
        label: '地址服务协议合同',
        value: '2'
      }
    ],
    search: { el: 'select' }
  },
  {
    prop: 'reviewStatus',
    label: '审批状态',
    width: '150',
    // fixed: 'right',
    enum: [
      {
        label: '待审批',
        value: '0'
      },
      {
        label: '通过',
        value: '1'
      },
      {
        label: '不通过',
        value: '2'
      }
    ]
    // search: { el: 'select' }
  },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300'
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '200'
  },
  // {
  //   prop: 'productName',
  //   width: '200',
  //   label: '服务产品'
  // },
  {
    prop: 'productId',
    width: 200,
    enum: () => {
      return new Promise(async (resolve, reject) => {
        const { data } = await getBusinessList({
          pageNum: 1,
          pageSize: 10000
        })
        // 将后端传回的数据结构进行转换
        const revertData = []
        data.forEach(item => {
          const obj = {
            label: item.typeName,
            value: item.id,
            children: []
          }
          revertData.push(obj)
          if (Array.isArray(item.child) && item.child.length) {
            item.child.forEach(child => {
              obj.children.push({
                label: child.productName,
                value: child.id // 产品类型id
              })
            })
          }
        })
        if (data) {
          resolve({
            data: revertData
          })
        } else {
          reject({
            data: []
          })
        }
      })
    },
    label: '服务产品',
    search: {
      el: 'tree-select',
      order: 2
    },
    render: scope => {
      return <span>{scope.row.productName || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'bp.product_name'
  },
  {
    prop: 'totalCost',
    width: '150',
    label: '合同总金额',
    search: {
      render: ({ searchParam }) => {
        return <InputRange vModel={searchParam.totalCost} />
      }
    },
    sortable: 'custom',
    sortName: 'cc.total_cost'
  },
  {
    prop: 'bizType',
    enum: [
      {
        label: '新增合同',
        value: '0'
      },
      {
        label: '合同变更',
        value: '1'
      }
    ],
    label: '业务类型',
    width: '150',
    search: { el: 'select' },
    sortable: 'custom',
    sortName: 'cc.biz_type'
  },
  {
    prop: 'createBy',
    width: '150',
    label: '提交人',
    search: {
      el: 'input',
      order: 3
    },
    sortable: 'custom',
    sortName: 'su.nick_name'
  },
  {
    prop: 'createTime',
    width: '200',
    label: '提交时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'cc.create_time'
  },

  {
    prop: 'operation',
    width: 150,
    fixed: 'right',
    label: '操作'
  }
]

// 自定义
const transformRequestParams = (data: any) => {
  if (data.createTime) {
    data.createTimeStart = data.createTime[0]
    data.createTimeEnd = data.createTime[1]
  }
  if (data.totalCost) {
    data.totalCostMin = data.totalCost[0]
    data.totalCostMax = data.totalCost[1]
  }
}

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// getTabList 获取tab下的列表
const getTabList = async (data: any) => {
  if (data.tabType === '-1') {
    // 由我审批的
    const result = await getContractReviewListByUser(data)
    return result
  } else {
    const result = await getCustomerContractAuditList({ ...data, reviewStatus: data.tabType })
    return result
  }
}

// reviewShow 审批弹窗展示
const reviewShow = ref(false)
const rowId = ref()
// const originId = ref() // 如果是变更合同 则会有原合同id
const isChange = ref(false) // 默认是新增合同,不是变更合同
const reviewType = ref('detail')

// 存储提交人(合同的创建人)和提交时间（流程的开始时间）

const handleDetail = (row: any, isReview: number) => {
  rowId.value = row.contractId

  isChange.value = row.bizType === '1'
  // 如果是变更合同
  // if (row.bizType === '1') {
  //   originId.value = row.originId
  // }
  // 由我审批的
  if (initParam.tabType === '-1' && isReview === 1) {
    console.log('edit')
    reviewType.value = 'edit'
  } else {
    reviewType.value = 'detail'
  }
  reviewShow.value = true
}
// handleSuccess
const proTable = ref()
const handleSuccess = () => {
  proTable.value?.getTableList()
}

const handleRadioChange = (value: string) => {
  proTable.value.pageable.pageNum = 1
  initParam.tabType = value
}

// 导出列表功能
const handleExport = async () => {
  const data = Object.assign({}, proTable.value.searchParam)
  if (data.createTime) {
    data.createTimeStart = data.createTime[0]
    data.createTimeEnd = data.createTime[1]
    delete data.createTime
  }
  if (data.totalCost) {
    data.totalCostMin = data.totalCost[0]
    data.totalCostMax = data.totalCost[1]
    delete data.totalCost
  }
  let result
  if (initParam.tabType === '-1') {
    // 由我审批的
    result = await getContractReviewListByUserExport({ ...data })
  } else {
    result = await getCustomerContractAuditListExport({
      ...data,
      reviewStatus: initParam.tabType
    })
  }
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}

watch(
  () => useCommon.id,
  async () => {
    if (useCommon.id && useCommon.bizType === 'review') {
      const { data } = await getContractDetailById(useCommon.id)
      if (data) {
        handleDetail(data)
      }
      useCommon.clearId()
      useCommon.clearBizType()
    }
  },
  {
    immediate: true
  }
)

watch(
  () => useCommon.todoTaskFlag,
  () => {
    if (useCommon.todoTaskFlag) {
      nextTick(async () => {
        // reviewStatus
        proTable.value.searchParam.reviewStatus = '0'
        handleRadioChange('2')
        await proTable.value?.requestResult
        proTable.value.search()
        useCommon.clearTodoTaskFlag()
      })
    }
  },
  {
    immediate: true
  }
)
</script>
<style lang="scss" scoped>
.el-tag.el-tag--success {
  height: 24px;
  background: rgba(14, 194, 127, 0.15);
  border-radius: 4px;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #0ec27f;
  padding: 2px 14px;
}
.el-tag--default {
  height: 24px;
  background: rgba(35, 131, 231, 0.15);
  border-radius: 4px;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #2383e7;
  padding: 2px 14px;
}

.el-tag--danger {
  height: 24px;
  background: rgba(245, 108, 108, 0.15);
  border-radius: 4px;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #f56c6c;
}
</style>
