import request from '@/utils/request'

// 详情
export const getFinanceReceiptGetById = params => {
  return request({
    url: '/financeReceipt/getById',
    method: 'get',
    params
  })
}

// 收款审核列表查询
export const getFinanceReceiptGetCheckListPage = params => {
  return request({
    url: '/financeReceipt/getCheckListPage',
    method: 'get',
    params
  })
}
// 收款审核列表查询-上下级关系
export const getFinanceReceiptGetSubmitListPage = params => {
  return request({
    url: '/financeReceipt/getSubmitListPage',
    method: 'get',
    params
  })
}

// 根据关联账单查询账单
export const getFinanceReceiptGetFinanceAssociatedPayment = params => {
  return request({
    url: '/financeReceipt/getFinanceAssociatedPayment',
    method: 'get',
    params
  })
}

// 列表查询
export const getFinanceReceiptList = params => {
  return request({
    url: '/financeReceipt/list',
    method: 'get',
    params
  })
}

// 根据id删除
export const deleteFinanceReceipt = params => {
  return request({
    url: 'financeReceipt/delete',
    method: 'delete',
    params
  })
}

// 保存数据
export const postFinanceReceiptSaveOrUpdate = query => {
  return request({
    url: '/financeReceipt/saveOrUpdate',
    method: 'post',
    data: query
  })
}

// 审核状态修改
export const postFinanceReceiptUpdateCheckStatus = query => {
  return request({
    url: '/financeReceipt/updateCheckStatus',
    method: 'post',
    data: query
  })
}
