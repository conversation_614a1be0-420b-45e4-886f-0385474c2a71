<!--
 * @Description: 标签管理
 * @Author: thb
 * @Date: 2023-07-25 11:16:37
 * @LastEditTime: 2023-08-09 14:27:26
 * @LastEditors: thb
-->
<template>
  <ProTable ref="proTable" title="标签管理" :columns="columns" :request-api="getClueTagList">
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
    </template>
    <template #operation="{ row }">
      <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>

      <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
    </template>
  </ProTable>
</template>
<script setup lang="tsx">
import { getClueTagList, getClueTagDetail, deleteClueTag, saveClueTag, setClueTagStatus } from '@/api/material-manage/tag'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialog'
import { useHandleData } from '@/hooks/useHandleData'
import tagForm from './components/tag-form'
const { showDialog } = useDialog()
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '序号',
    width: 100,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'operation',
    width: 200,
    label: '操作'
  },
  {
    prop: 'name',
    label: '标签名称',
    width: 150,
    search: {
      el: 'input'
    }
  },
  {
    prop: '',
    label: '线索数',
    width: 100
  },
  {
    prop: '',
    width: 100,
    label: '客户数'
  },
  {
    prop: 'remark',
    label: '备注'
  },
  {
    prop: 'status',
    width: 150,
    label: '状态',
    enum: [
      {
        label: '正常',
        value: '1'
      },
      {
        label: '停用',
        value: '0'
      }
    ],
    search: {
      el: 'select'
    },
    render: scope => {
      return (
        <>
          {
            <el-switch
              model-value={scope.row.status}
              active-text={scope.row.status === '1' ? '正常' : '停用'}
              active-value={'1'}
              inactive-value={'0'}
              onClick={() => changeStatus(scope.row)}
            />
          }
        </>
      )
    }
  },
  {
    prop: 'createBy',
    width: 200,
    label: '创建人'
  },
  {
    prop: 'createTime',
    fixed: 'right',
    width: 200,
    label: '创建时间'
  }
]

const proTable = ref('')
// 提交成功之后的回调函数
const submitCallback = () => {
  proTable.value?.getTableList()
}
//
const handleAdd = () => {
  showDialog({
    title: '新增',
    customClass: 'zone-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: tagForm, // 表单组件
    submitApi: saveClueTag, // 提交api
    submitCallback // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 编辑公海配置
const handleEdit = (row: any) => {
  showDialog({
    title: '编辑',
    customClass: 'zone-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: tagForm, // 表单组件
    getApi: getClueTagDetail,
    requestParams: row.id,
    submitApi: saveClueTag, // 提交api
    submitCallback // 提交成功之后的回调函数
  })
}
// 删除公海配置
const handleDelete = async (row: any) => {
  await useHandleData(deleteClueTag, row.id, `删除所选标签 ${row.name} 信息`)
  proTable.value?.getTableList()
}

// 修改状态
const changeStatus = async (row: any) => {
  await useHandleData(setClueTagStatus, row.id, `切换【${row.name}】状态`)
  proTable.value?.getTableList()
}
</script>
<style lang="scss" scoped></style>
