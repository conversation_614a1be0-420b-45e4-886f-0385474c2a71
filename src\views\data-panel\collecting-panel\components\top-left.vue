<!--
 * @Description: 新增户数面板
 * @Author: thb
 * @Date: 2023-09-04 09:44:30
 * @LastEditTime: 2023-11-07 09:20:20
 * @LastEditors: thb
-->
<template>
  <div class="top-left">
    <dataWrap title="新增户数" :selectOptions="selectOptions" :requestApi="getAccountNumbers">
      <template #default="{ data }">
        <div class="data-list">
          <div class="data-item">
            <div class="top">
              <span class="icon account-icon"></span>
              <span>记账客户</span>
            </div>
            <div class="numbers">
              <span class="number number-blue">{{ data.bookkeeping || '--' }}</span>
              户
            </div>
          </div>
          <div class="data-item">
            <div class="top">
              <span class="icon certify-icon"></span>
              <span>办证客户</span>
            </div>
            <div class="numbers">
              <span class="number number-orange">{{ data.license || '--' }}</span>
              户
            </div>
          </div>
          <div class="data-item">
            <div class="top">
              <span class="icon nofee-icon"></span>
              <span>不收费客户</span>
            </div>
            <div class="numbers">
              <span class="number number-green">{{ data.free || '--' }}</span>
              户
            </div>
          </div>
          <div class="data-item">
            <div class="top">
              <span class="icon another-icon"></span>
              <span>其他客户</span>
            </div>
            <div class="numbers">
              <span class="number number-purple">{{ data.other || '--' }}</span>
              户
            </div>
          </div>
        </div>
      </template>
    </dataWrap>
  </div>
</template>
<script setup>
import dataWrap from './data-wrap'
import { getAccountNumbers } from '@/api/panel-data/collecting'
const selectOptions = [
  {
    type: 'select',
    prop: 'period',
    enums: [
      {
        label: '最近一周',
        value: 'week'
      },
      {
        label: '最近一月',
        value: 'month'
      },
      {
        label: '最近三月',
        value: 'tripleMonth'
      },
      {
        label: '本年',
        value: 'year'
      }
    ],
    defaultValue: 'week'
  }
]
</script>
<style lang="scss" scoped>
.top-left {
  flex: 1;
  background: #fff;
  border-radius: 4px;
}
.icon {
  margin-right: 8px;
}
.data-list {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 2%;
  .data-item {
    flex: 0 0 49%;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    padding: 8px;
    .top {
      display: flex;
      align-items: center;
      height: 20px;
      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #333333;
    }
    .numbers {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #7d8592;
    }
  }
}
.number {
  font-size: 24px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  margin-right: 4px;
  color: #45a0ff;
}
.number-green {
  color: #51d9ab;
}

.number-blue {
  color: #45a0ff;
}

.number-orange {
  color: #f5ba52ff;
}

.number-purple {
  color: #736cfaff;
}
</style>
