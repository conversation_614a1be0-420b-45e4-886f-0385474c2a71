import request from '@/utils/request'
export const getAddressList = params => {
  return request({
    url: '/addressApply/list',
    method: 'get',
    params
  })
}

export const saveUpdateAddress = data => {
  return request({
    url: '/addressApply/saveOrUpdate',
    method: 'post',
    data
  })
}

export const getAddressDetailById = id => {
  return request({
    url: '/addressApply/getById',
    method: 'get',
    params: {
      id
    }
  })
}

export const removeAddress = id => {
  return request({
    url: '/addressApply/delete',
    method: 'delete',
    params: {
      id
    }
  })
}

// 绑定房本
export const postAddressApplyAddressApply = data => {
  return request({
    url: '/addressApply/bindProperty',
    method: 'post',
    data
  })
}

export const postAddressApplyFinish = data => {
  return request({
    url: '/addressApply/finish?id=' + data.id,
    method: 'post',
    data
  })
}

/** 出租人 */
// "name": "根据id删除",
// "method": "delete",
// "path": "/dev-api/addressLessor/delete",
export const deleteAddressLessorDelete = data => {
  return request({
    url: '/addressLessor/delete',
    method: 'delete',
    params: data
  })
}

// "name": "详情",
// "method": "get",
// "path": "/dev-api/addressLessor/getById",
export const getAddressLessorGetById = data => {
  return request({
    url: '/addressLessor/getById',
    method: 'get',
    params: data
  })
}

// "name": "列表查询",
// "method": "get",
// "path": "/dev-api/addressLessor/list",
export const getAddressLessorList = data => {
  return request({
    url: '/addressLessor/list',
    method: 'get',
    params: data
  })
}

// "name": "保存数据",
// "method": "post",
// "path": "/dev-api/addressLessor/save",
export const postAddressLessorSave = data => {
  return request({
    url: '/addressLessor/save',
    method: 'post',
    data
  })
}

// "name": "更新数据",
// "method": "post",
// "path": "/dev-api/addressLessor/update",
export const postAddressLessorUpdate = data => {
  return request({
    url: '/addressLessor/update',
    method: 'post',
    data
  })
}

/** 房屋产权证明 */
// "name": "根据id删除",
// "method": "delete",
// "path": "/dev-api/addressPropertyOwnership/delete",
export const deleteAddressPropertyOwnershipDelete = data => {
  return request({
    url: '/addressPropertyOwnership/delete',
    method: 'delete',
    params: data
  })
}

// "name": "启用",
// "method": "post",
// "path": "/dev-api/addressPropertyOwnership/enable",
export const postAddressPropertyOwnershipEnable = data => {
  return request({
    url: '/addressPropertyOwnership/enable?id=' + data.id,
    method: 'post',
    data
  })
}

// "name": "详情",
// "method": "get",
// "path": "/dev-api/addressPropertyOwnership/getById",
export const getAddressPropertyOwnershipGetById = data => {
  return request({
    url: '/addressPropertyOwnership/getById',
    method: 'get',
    params: data
  })
}

// "name": "获取闲置房本",
// "method": "get",
// "path": "/dev-api/addressPropertyOwnership/getIdlePropertyList",
export const getAddressPropertyOwnershipGetIdlePropertyList = data => {
  return request({
    url: '/addressPropertyOwnership/getIdlePropertyList',
    method: 'get',
    params: data
  })
}

// "name": "列表查询",
// "method": "get",
// "path": "/dev-api/addressPropertyOwnership/list",
export const getAddressPropertyOwnershipList = data => {
  return request({
    url: '/addressPropertyOwnership/list',
    method: 'get',
    params: data
  })
}

// "name": "保存数据",
// "method": "post",
// "path": "/dev-api/addressPropertyOwnership/save",
export const postAddressPropertyOwnershipSave = data => {
  return request({
    url: '/addressPropertyOwnership/save',
    method: 'post',
    data
  })
}

// "name": "更新数据",
// "method": "post",
// "path": "/dev-api/addressPropertyOwnership/update",
export const postAddressPropertyOwnershipUpdate = data => {
  return request({
    url: '/addressPropertyOwnership/update',
    method: 'post',
    data
  })
}
