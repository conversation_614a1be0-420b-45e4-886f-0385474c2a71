import request from '@/utils/request'

// 获取销售面板 --数据简报
export const getSaleDataReport = () => {
  return request({
    url: '/kanban/sales/dataBriefing',
    method: 'get'
  })
}
// 获取商机漏斗
export const getBusinessData = () => {
  return request({
    url: '/kanban/sales/businessFunnelVO',
    method: 'get'
  })
}
// 获取收单金额平均客单价
export const getAveragePrice = params => {
  return request({
    url: '/kanban/sales/collectionAmount',
    method: 'get',
    params
  })
}

// 获取线索转化数据
export const getClueTransferData = () => {
  return request({
    url: '/kanban/sales/clueChange',
    method: 'get'
  })
}

// 获取业绩金额饼图
export const getBusinessMoneyPie = params => {
  return request({
    url: '/kanban/sales/performanceAmountPie',
    method: 'get',
    params
  })
}

// 获取业绩金额饼图
export const getBusinessMoneyLine = params => {
  return request({
    url: '/kanban/sales/performanceAmountLine',
    method: 'get',
    params
  })
}

// 获取销售漏斗
export const getSaleRateData = () => {
  return request({
    url: '/kanban/sales/salesFunnelVO',
    method: 'get'
  })
}
