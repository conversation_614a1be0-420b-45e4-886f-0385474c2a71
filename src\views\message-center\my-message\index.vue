<!--
 * @Description: 我的消息
 * @Author: thb
 * @Date: 2023-07-20 09:58:06
 * @LastEditTime: 2023-11-07 15:49:57
 * @LastEditors: thb
-->
<template>
  <ProTable ref="proTable" title="通知公告" :columns="columns" :request-api="getMyMessageList">
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" @click="setIsRead">全部标为已读</el-button>
    </template>

    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handleMark(scope.row)" v-if="scope.row.readStatus === 0">标记为已读</el-button>
      <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
    </template>

    <!-- title -->
    <template #title="{ row }">
      <el-badge :is-dot="row.readStatus === 0"> {{ row.title }}</el-badge>
    </template>
  </ProTable>
  <messageDetail v-if="messageShow" :id="notificationId" :messageId="messageId" @on-close="handleClose" />
</template>
<script setup lang="tsx">
import { inject, ref, watch } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { getMyMessageList, setMessageIsRead } from '@/api/message-center/message-center'
// import { checkContractDetailIsPermission } from '@/api/contract/contract'
import messageDetail from './components/message-detail'
// import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
// import { getCustomerById } from '@/api/customer/file'
import useCommonStore from '@/store/modules/common'
const useCommon = useCommonStore()
import { useCheck } from '@/hooks/useCheck'
const { routerCheckDetail } = useCheck(useCommon)
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '序号',
    width: 100,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'title',
    label: '标题',
    width: 400
  },

  {
    prop: 'content',
    label: '消息内容',

    search: { el: 'input' }
  },
  {
    prop: 'messageType',
    width: 100,
    enum: [
      {
        label: '提醒',
        value: '提醒'
      },
      {
        label: '公告',
        value: '公告'
      }
    ],
    label: '消息类型',
    search: {
      el: 'select'
    }
  },
  {
    prop: 'readStatus',
    label: '已读状态',
    width: 100,
    enum: [
      {
        label: '已读',
        value: 1
      },
      {
        label: '未读',
        value: 0
      }
    ],
    search: {
      el: 'select'
    },
    isShow: false
  },
  {
    prop: 'receiveTime',
    fixed: 'right',
    width: 200,
    label: '发送时间'
  },
  {
    prop: 'operation',
    width: 200,
    label: '操作',
    fixed: 'right'
  }
]
const messageShow = ref(false)
const notificationId = ref()
const messageId = ref()

const handleDetail = (row: any) => {
  if (row.messageType === '提醒') {
    try {
      routerCheckDetail(row)
      // if (row.bizType && titleMap[row.bizType as keyof typeof titleMap]) {
      //   titleMap[row.bizType as keyof typeof titleMap](row)
      // }
      // // 触发已读接口
      // setMessageIsRead([row.id])
    } catch (error) {}
  } else {
    // 公告的详情

    notificationId.value = row.notificationId
    messageId.value = row.id
    messageShow.value = true
  }
}

// watch 监听公告消息
watch(
  () => useCommon.notObj,
  () => {
    const { id, notificationId: noteId } = useCommon.notObj
    if (id && notificationId) {
      // 公告的详情
      notificationId.value = noteId
      messageId.value = id
      messageShow.value = true
      // clear useCommon.notObj
      useCommon.notObj = {
        id: '',
        notificationId: ''
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
)

// 设置标记为已读
const { proxy } = getCurrentInstance()
const setIsRead = () => {
  ElMessageBox.confirm('确定要标记全部消息为已读?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const result: any = await setMessageIsRead()
      if (result.data) {
        // 标记成功
        getList()
        proxy.$modal.msgSuccess(`标记成功!`)
      }
    })
    .catch(() => {})
}

// handleMark 标记为已读
const handleMark = async row => {
  // 触发已读接口
  const result = await setMessageIsRead([row.id])
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`标记成功!`)
    getList()
  } else {
    proxy.$modal.msgError(`标记失败!`)
  }
}
const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}

const handleClose = () => {
  messageShow.value = false
  getList()
}

// 组件销毁时需要删除useCommonStore中的存储数据
// onUnmounted(() => {
//   useCommon.id = ''
// })
</script>
<style lang="scss" scoped>
:deep(.el-badge__content.el-badge__content--danger.is-fixed.is-dot) {
  left: -20px;
  right: none;
  top: 10px;
}
</style>
