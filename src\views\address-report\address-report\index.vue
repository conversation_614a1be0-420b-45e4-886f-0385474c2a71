<!--
 * @Description: 地址申请
 * @Author: thb
 * @Date: 2023-11-30 10:17:06
 * @LastEditTime: 2023-12-04 10:06:08
 * @LastEditors: thb
-->
<template>
  <ProTable :init-param="initParam" ref="proTable" title="地址申请" :columns="columns" :request-api="getAddressList">
    <template #tabs>
      <el-radio-group :model-value="initParam.status" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in statusArr" :key="index" :label="item.value">{{ item.label }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handleShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #paymentNo="{ row }">
      <span class="blue-text" @click="handleShowAccountsDetail(row)">{{ row.paymentNo }}</span>
    </template>
    <template #supplier="{ row }">
      <span>{{ row.supplier || '--' }}</span>
    </template>
    <template #status="{ row }">
      <span v-if="row.status" :style="`color:${statusColorMap[row.status]}`">
        {{ row.status }}
      </span>
    </template>

    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
    </template>
    <!-- 表格操作 -->
    <template #operation="{ row }">
      <!-- 详情 -->
      <el-button type="primary" link @click="handleCheckDetail(row)">详情</el-button>
      <el-button v-if="['已完成', '已结束'].includes(row.status)" type="primary" link @click="handleShowPremisesPermit(row)"
        >房本详情</el-button
      >
      <!-- 编辑 -->
      <el-button v-if="['待选地址', '暂无地址'].includes(row.status)" type="primary" link @click="handleUpdate(row)"
        >编辑</el-button
      >
      <!-- 删除 -->
      <el-button v-if="['待选地址', '暂无地址'].includes(row.status)" type="danger" link @click="handleRemove(row)"
        >删除</el-button
      >
      <el-button v-if="['待选地址'].includes(row.status)" type="primary" link @click="handleChoosePremisesPermit(row)"
        >选择地址</el-button
      >
      <el-button v-if="['已完成'].includes(row.status)" type="primary" link @click="handleFinish(row)">结束使用</el-button>
      <!-- 审批 -->
      <!-- <el-button type="primary" link @click="handleReview(row)" v-hasPermi="['report:review']">审批</el-button> -->
    </template>
  </ProTable>
  <choosePremisesPermit
    ref="choosePremisesPermitRef"
    v-if="choosePremisesPermitShow"
    @ok="getList"
    @close="choosePremisesPermitShow = false"
  ></choosePremisesPermit>
  <customerDetail v-if="customerDetailShow" :id="customerId" :hideActionBtn="true" @on-close="customerDetailShow = false" />
</template>
<script setup lang="tsx">
import { ColumnProps } from '@/components/ProTable/interface'
import {
  getAddressList,
  saveUpdateAddress,
  getAddressDetailById,
  removeAddress,
  postAddressApplyFinish
} from '@/api/address-report'
import { receiveStatusArr } from '@/utils/constants'
import { useDialog } from '@/hooks/useDialog'
import addressReport from './components/address-report'
import reviewModal from './components/review-modal'
import { getBusinessList } from '@/api/business/business'
import { getAddressProviderList } from '@/api/address-provider'
import { useHandleData } from '@/hooks/useHandleData'
import accountsForm from '@/views/finance/accounts-receivable/components/form.vue'
import { financePaymentGetById, financePaymentSaveOrUpdate } from '@/api/finance/accounts-receivable'
import { useCustomer } from '@/hooks/useCustomer'
import { useDialog as useDialogFinance } from '@/hooks/useDialogFinance'
import bus from 'vue3-eventbus'
import { getAreaTreeList } from '@/api/basicData/basicData'
import choosePremisesPermit from '@/views/address-report/premises-permit-manage/components/form-modal.vue'
import { useSetDic } from '@/hooks/useSetDic'
const { setDic } = useSetDic()

const { proxy } = getCurrentInstance()
const { address_tuoguan_fenlei } = proxy.useDict('address_tuoguan_fenlei')

const statusColorMap = {
  已完成: '#409EFF',
  已结束: '#F56C6C',
  待选地址: '#409EFF',
  暂无地址: '#333'
}
const statusMap = {
  '': '全部',
  已完成: '已完成',
  已结束: '已结束',
  待选地址: '待选地址',
  暂无地址: '暂无地址'
}
const statusArr = Object.keys(statusMap).map(key => ({ label: statusMap[key], value: key }))

const { customerDetailShow, rowId: customerId, handleShowCustomerDetail, customerDetail } = useCustomer()
const { showDialog } = useDialog()
const { showDialog: showDialogFinance } = useDialogFinance()

const initParam = reactive({
  status: ''
})
const handleRadioChange = value => {
  proTable.value.pageable.pageNum = 1
  initParam.status = value
}

function transformStructure(structure1) {
  return structure1.map(item => transformItem(item))
}

function transformItem(item) {
  const newItem = {
    label: item.name,
    value: item.id,
    children: (item.child || []).map(child => transformItem(child))
  }
  return newItem
}
const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    width: 100,
    label: '序号',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'customerName',
    label: '企业名称',
    minWidth: 300,
    search: { el: 'input' }
  },
  {
    prop: 'areaName',
    label: '申请区域',
    width: 200,
    isShow: true
  },
  {
    prop: 'areaId',
    label: '申请区域',
    width: 200,
    isShow: false,
    enum: () => {
      return new Promise(async (resolve, reject) => {
        const { data } = await getAreaTreeList({ enable: 1 })
        // [    {name:'11',id:'22',child:[{name:'111',id:'222',child:[{name:'111',id:'222',child:[{name:'111',id:'222',child:[]}]}]}]}  ]
        // [    {label:'11',value:'22',children:[{label:'111',value:'222',children:[{label:'111',value:'222',children:[{label:'111',value:'222',children:[]}]}]}]}  ]
        // 将后端传回的数据结构进行转换
        // const revertData = []
        // data.forEach(item => {
        //   const obj = {
        //     label: item.name,
        //     value: item.id,
        //     children: []
        //   }
        //   revertData.push(obj)
        //   if (Array.isArray(item.child) && item.child.length) {
        //     item.child.forEach(child => {
        //       obj.children.push({
        //         label: child.name,
        //         value: child.id
        //       })
        //       if (Array.isArray(child) && child.length) {
        //         child.forEach(child => {
        //           item.child.children.push({
        //             label: child.name,
        //             value: child.id
        //           })
        //         })
        //       }
        //     })
        //   }
        // })
        const revertData = transformStructure(data)
        if (data) {
          resolve({
            data: revertData
          })
        } else {
          reject({
            data: []
          })
        }
      })
    },
    search: {
      el: 'tree-select',
      props: {
        'check-strictly': true
        // props: {
        //   value: 'id',
        //   label: 'name',
        //   children: 'child'
        // }
      }
    }
  },
  {
    prop: 'paymentNo',
    width: 200,
    label: '地址费账单'
  },
  {
    prop: 'propertyName',
    width: 200,
    label: '房本名称'
  },
  // {
  //   prop: 'receiveStatus',
  //   width: 150,
  //   label: '收费情况',
  //   enum: receiveStatusArr,
  //   search: { el: 'select' }
  // },
  {
    prop: 'createBy',
    width: 150,
    label: '创建人',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'createTime',
    width: 200,
    label: '创建时间'
  },
  {
    prop: 'status',
    width: 100,
    label: '状态'
  },
  {
    prop: 'operation',
    width: 250,
    fixed: 'right',
    label: '操作'
  }
]

// 新增地址
const handleAdd = () => {
  showDialog({
    title: '新增地址申请',
    customClass: 'dialog-1200',
    cancelButtonText: '关闭',
    confirmButtonText: '保存',
    component: addressReport,
    submitApi: saveUpdateAddress,
    handleConvertParams: data => {
      data.feeTypeDefault = feeTypeDefault.value
      console.log('feeTypeDefault', data)
    },
    submitCallback: formData => {
      console.log('formData', formData)

      getList()
      if (address_tuoguan_fenlei.value.findIndex(item => item.value === formData.hostingType) === -1) {
        setDic('address_tuoguan_fenlei', formData.hostingType, formData.hostingType, address_tuoguan_fenlei)
      }
      getAddressDetailById(formData.result_data_id).then(res => {
        if (res.data.status === '待选地址') {
          handleChoosePremisesPermit({ id: formData.result_data_id })
        }
      })
    }
  })
}

// 查看 地址申请 详情
const handleCheckDetail = row => {
  showDialog({
    title: '详情',
    customClass: 'dialog-1200',
    cancelButtonText: '关闭',
    showConfirmButton: false,
    component: addressReport,
    requestParams: row.id,
    handleConvertParams: data => {
      data.isDisabled = true
    },
    getApi: getAddressDetailById
  })
}

// 编辑 地址申请 详情
const handleUpdate = row => {
  showDialog({
    title: '编辑地址申请',
    customClass: 'dialog-1200',
    cancelButtonText: '关闭',
    confirmButtonText: '保存',
    component: addressReport,
    requestParams: row.id,
    getApi: getAddressDetailById,
    submitApi: saveUpdateAddress,
    handleConvertParams: data => {
      data.feeTypeDefault = feeTypeDefault.value
    },
    submitCallback: formData => {
      getList()
      // console.log('formData', formData)
      if (address_tuoguan_fenlei.value.findIndex(item => item.value === formData.hostingType) === -1) {
        setDic('address_tuoguan_fenlei', formData.hostingType, formData.hostingType, address_tuoguan_fenlei)
      }
      // getAddressDetailById(formData.id).then(res => {
      //   if (res.data.status === '待选地址') {
      //     handleChoosePremisesPermit({ id: formData.id })
      //   }
      // })
    }
  })
}

// 审批地址申请
const handleReview = row => {
  showDialog({
    title: '审批',
    customClass: 'address-review-modal',
    cancelButtonText: '关闭',
    confirmButtonText: '保存',
    component: reviewModal,
    requestParams: row.id,
    getApi: getAddressDetailById,
    handleConvertParams: data => {
      data.status = '1' // 通过
    },
    submitApi: saveUpdateAddress,
    handleRevertParams: data => {
      data.isReview = true
    },
    submitCallback: () => {
      getList()
    }
  })
}
// 删除 地址申请
const handleRemove = async row => {
  await useHandleData(removeAddress, row.id, `删除 ${row.customerName}所选地址申请的信息`)
  proTable.value?.getTableList()
}
const proTable = ref()
const getList = () => {
  proTable.value.getTableList()
}

const feeTypeDefault = ref('')
const getBusinesses = async () => {
  const { data } = await getBusinessList({
    pageNum: 1,
    pageSize: 10000
  })
  feeTypeDefault.value = data.filter(item => item.typeName === '地址费')[0]?.id
  console.log('feeTypeDefault', feeTypeDefault.value)
}
getBusinesses()

// 查看账单详情
const handleShowAccountsDetail = row => {
  showDialogFinance({
    title: '账单详情',
    customClass: 'customer-dialog table-none',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: accountsForm,
    getApi: financePaymentGetById,
    requestParams: { id: row.paymentId }
  })
  nextTick(() => {
    bus.emit('paymentNo', row.paymentNo)
    bus.emit('paymentId', row.paymentId)
  })
}

/** 选择地址 */
const choosePremisesPermitShow = ref(false)
const choosePremisesPermitRef = ref()
const handleChoosePremisesPermit = (row: any) => {
  choosePremisesPermitShow.value = true
  nextTick(() => {
    choosePremisesPermitRef.value.onChoose(row)
  })
}

/** 房本详情 */
const handleShowPremisesPermit = (row: any) => {
  choosePremisesPermitShow.value = true
  nextTick(() => {
    choosePremisesPermitRef.value.onDetail1({ id: row.propertyId })
  })
}

const handleFinish = async (row: any) => {
  await useHandleData(postAddressApplyFinish, row, `确认结束${row.customerName}所选地址申请的使用`)
  proTable.value?.getTableList()
}
</script>
<style lang="scss" scoped>
.el-tag.el-tag--success {
  height: 24px;
  background: rgba(14, 194, 127, 0.15);
  border-radius: 4px;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #0ec27f;
  padding: 2px 14px;
}

.el-tag--default {
  height: 24px;
  background: rgba(35, 131, 231, 0.15);
  border-radius: 4px;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #2383e7;
  padding: 2px 14px;
}

.el-tag--danger {
  height: 24px;
  background: rgba(245, 108, 108, 0.15);
  border-radius: 4px;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: #f56c6c;
}
</style>
<style lang="scss">
.table-none {
  .my-table {
    display: none;
  }
}
</style>
