<!--
 * @Description: 进出口表单资料收集 
 * @Author: thb
 * @Date: 2023-09-27 14:33:52
 * @LastEditTime: 2023-12-21 13:19:25
 * @LastEditors: thb
-->
<template>
  <component
    :is="formMap[data.bizType]"
    :hide-required-asterisk="disabled"
    :data="data"
    ref="componentRef"
    @on-preview="downloadFile"
  ></component>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import importExport from './import-export'
import laborOrder from './labor-order'
import verificationForm from './verification-form'
import foodForm from './food-form'
import iFrame from '@/components/iFrame'
import transportForm from './transport-form'

const disabled = inject('disabled')
const props = defineProps({
  data: Object
})
const formMap = {
  permit_in_and_out: importExport, // 许可证 - 进出口
  permit_labor_dispatch: laborOrder, //许可证 - 劳务派遣
  permit_capital_verification: verificationForm, //许可证 - 验资
  permit_food_certificate: foodForm, //许可证 - 食品证
  permit_road_transport: transportForm //许可证 - 道路运输
}

// 校验框选的表单项
const { proxy } = getCurrentInstance()
// 保存
const validateCheckedForm = async () => {
  // 提交检验之前需要清除表单的提交校验
  componentRef.value.formRef.clearValidate()
  // 2023-12-18需求更新 不需要该文件校验
  // 根据rules 中的配置字段进行校验
  // for (let key of Object.keys(componentRef.value.rules)) {
  //   // 排除 法人手机号 操作员手机号 和 邮箱
  //   if (Array.isArray(props.data[key] || []) && props.data[key].checked && !props.data[key].length) {
  //     proxy.$message.warning('打勾后必须上传完整附件或输入文本!')
  //     return false
  //   }
  //   // 单独对法人手机号、操作员手机号 和 邮箱进行校验 是否为空 且校验格式是否正确
  //   if (typeof props.data[key] === 'string') {
  //     if (props.data[`${key}Checked`] && !props.data[key]) {
  //       proxy.$message.warning('打勾后必须上传完整附件或输入文本!')
  //       return false
  //     }
  //   }
  // }
  const strs = ['legalPhone', 'operatorPhone', 'email']
  // 校验 法人手机号 操作手机号 邮箱格式是否正确
  let valResult1 = true,
    valResult2 = true,
    valResult3 = true
  if (props.data['legalPhone']) {
    valResult1 = await componentRef.value.formRef.validateField('legalPhone')
  }
  if (props.data['operatorPhone']) {
    valResult2 = await componentRef.value.formRef.validateField('operatorPhone')
  }
  if (props.data['email']) {
    valResult3 = await componentRef.value.formRef.validateField('email')
  }
  if (!valResult1 || !valResult2 || !valResult3) {
    return false
  }
  return true
}

// 校验表单的方法
const componentRef = ref()
// 提交
const validateForm = async () => {
  //2023-12-18需求更新 提交表单时*项需要打勾但是*项不必填(*项也就是rules)
  for (let key of Object.keys(componentRef.value.rules)) {
    const mapKey = componentRef.value.flagMap?.[key]
    if (!mapKey && !props.data[key + 'Flag']) {
      proxy.$message.warning('打*项请打上勾再进行提交!')
      return false
    }
    if (mapKey && !props.data[mapKey]) {
      proxy.$message.warning('打*项请打上勾再进行提交!')
      return false
    }
  }
  return true
}
// watch data
watch(
  () => props.data,
  () => {
    // nextTick(() => {
    //   for (let key of Object.keys(componentRef.value.rules)) {
    //     if (Array.isArray(props.data[key] || []) && props.data[key].length) {
    //       props.data[key]['checked'] = true
    //     }
    //     if (typeof props.data[key] === 'string' && props.data[key]) {
    //       props.data[`${key}Checked`] = true
    //     }
    //   }
    // })
  },
  {
    immediate: true
  }
)

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}
defineExpose({
  validateCheckedForm,
  validateForm
})
</script>
<style lang="scss" scoped>
:deep(.el-form-item__content) {
  display: block;
}

:deep(.el-checkbox) {
  margin-right: 8px;
}
:deep(.el-col.el-col-12.is-guttered) {
  display: flex;
  align-items: baseline;
  .el-form-item {
    flex: 1;
  }
}
</style>
