<!--
 * @Description: 新增地址供应商
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-11-30 16:04:47
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="供应商名称" prop="supplier">
          <el-input v-model="formData.supplier" maxlength="20" :disabled="isDisabled" :placeholder="isDisabled ? '' : '请输入'" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="地址成本" prop="addressCost">
          <NumberInput v-model="formData.addressCost" :disabled="isDisabled" :placeholder="isDisabled ? '' : '请输入'">
            <template #suffix> 元 </template>
          </NumberInput>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="formData.phone" maxlength="20" :disabled="isDisabled" :placeholder="isDisabled ? '' : '请输入'" />
          <!-- <el-select v-model="formData.contractType" multiple placeholder="请选择">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in typeList" :key="index" />
          </el-select> -->
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="银行账户" prop="bankAccount">
          <el-input
            v-model="formData.bankAccount"
            maxlength="100"
            :disabled="isDisabled"
            :placeholder="isDisabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="收费类型" prop="feeType">
          <el-select
            v-model="formData.feeType"
            :placeholder="isDisabled ? ' ' : '请选择'"
            :disabled="isDisabled"
            clearable
            @change="handleChange"
          >
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in fee_type" :key="index" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="formData.feeType === '每年收费'">
        <el-form-item label="有效期至" prop="validTo">
          <el-date-picker
            v-model="formData.validTo"
            :disabled="isDisabled"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="date"
            :placeholder="isDisabled ? ' ' : '请选择'"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            :disabled="isDisabled"
            maxlength="1000"
            :placeholder="isDisabled ? ' ' : '请输入'"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import { FormValidators } from '@/utils/validate'
import NumberInput from '@/components/NumberInput'
const { proxy } = getCurrentInstance()
const { fee_type } = proxy.useDict('fee_type')
const formData = reactive({
  supplier: '',
  addressCost: '0',
  phone: '',
  bankAccount: '',
  feeType: '',
  validTo: '',
  remark: '',
  id: undefined
})
const isDisabled = ref(false)
watch(
  formData,
  () => {
    if (formData.isDisabled) {
      isDisabled.value = true
    }
  },
  {
    immediate: true
  }
)

const rules = {
  supplier: [{ required: true, message: '请输入', trigger: 'blur' }],
  phone: [{ message: '请输入正确的联系电话', trigger: 'blur', validator: FormValidators.mobilePhone }]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

const handleChange = value => {
  if (value !== '每年收费') {
    formData.validTo = ''
  }
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
:deep(.el-date-editor) {
  width: 100%;
}
</style>
