<!--
 * @Description: 新增产品表格
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2024-03-05 09:48:21
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :disabled="formData.isDetail">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="formData.productName" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="产品报价" prop="isInContract" class="quotation-form-item">
          <NumberInput
            :disabled="formData.isInContract === '1'"
            v-model="formData.quotation"
            @input="changeIsInContract"
            maxlength="20"
            :placeholder="formData.isInContract === '1' ? '' : '请输入'"
          >
            <template #suffix> 元 </template>
          </NumberInput>
          <el-checkbox
            v-model="formData.isInContract"
            @change="handleRadioChange"
            true-label="1"
            false-label="0"
            label="在合同中定义"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="收费类型" prop="feeType">
          <el-select v-model="formData.feeType" placeholder="请选择">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in typeList" :key="index" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="显示排序" prop="sort">
          <NumberInput v-model="formData.sort" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <template v-if="formData.feeType && formData.feeType !== '0'">
      <div style="float: right; margin-bottom: 5px" v-if="!formData.isDetail">
        <el-button plain type="primary" @click="handleAdd">新增</el-button>
      </div>
      <FormTable ref="formTableRef" :formData="formData" :option="option">
        <template #activityQuotation="{ row }"
          ><el-input @blur="handleBlurInput" v-model="row.activityQuotation" :disabled="formData.isDetail"
            ><template #suffix>元</template></el-input
          ></template
        >
        <template #activityDiscountTime="{ row }"
          ><el-input v-model="row.activityDiscountTime" :disabled="formData.isDetail"
            ><template #suffix>月</template></el-input
          ></template
        >
        <template #remark="{ row }"
          ><el-input style="width: 100%" v-model="row.remark" :disabled="formData.isDetail"></el-input
        ></template>
        <template #activityStatus="{ row }"
          ><el-switch active-value="1" inactive-value="0" v-model="row.activityStatus" :disabled="formData.isDetail"
        /></template>
        <template #action="{ row, $index }">
          <el-button type="danger" text @click="handleDelete(row, $index)" :disabled="formData.isDetail">删除</el-button>
        </template>
      </FormTable>
    </template>
  </el-form>
</template>

<script setup>
import { reactive, watch } from 'vue'
import NumberInput from '@/components/NumberInput'
import FormTable from '@/components/FormTable'
// const { proxy } = getCurrentInstance()

const formData = reactive({
  productName: '',
  quotation: '',
  feeType: '',
  isInContract: '0',
  sort: '',
  id: undefined,
  typeId: undefined,
  activityList: [],
  tableData: [],
  rules: {
    activityQuotation: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
  }
})

watch(
  () => formData.id,
  () => {
    formData.rules = {
      activityQuotation: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
    }
    if (formData.activityList?.length) {
      formData.tableData = formData.activityList
    }
  }
)

const formTableRef = ref(null)
const option = [
  {
    prop: 'activityQuotation',
    label: '活动报价',
    width: '150px'
  },
  {
    prop: 'activityDiscountTime',
    label: '时长',
    width: '150px'
  },
  {
    prop: 'remark',
    label: '备注'
  },
  {
    prop: 'activityStatus',
    label: '状态',
    width: '100px'
  },
  {
    prop: 'action',
    label: '操作',
    width: '160px'
  }
]

// 新增行
const handleAdd = () => {
  if (formData?.tableData?.length) {
    formData.tableData.push({
      activityQuotation: '',
      activityDiscountTime: '',
      remark: '',
      activityStatus: 0
    })
  } else {
    formData.tableData = [
      {
        activityQuotation: '',
        activityDiscountTime: '',
        remark: '',
        activityStatus: 0
      }
    ]
  }
}
// 删除行
const handleDelete = (row, index) => {
  formData.tableData.splice(index, 1)
}

// typeList
const typeList = [
  {
    label: '一次性收费',
    value: '0'
  },
  {
    label: '每年收费',
    value: '1'
  },
  {
    label: '每月收费',
    value: '2'
  }
]

const handleQuotationValidate = (rule, value, callback) => {
  if (value === '0') {
    if (!formData.quotation) {
      // 如果不存在quotation 则需要提示
      callback(new Error('请输入'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

const handleBlurInput = () => {
  console.log('handleBlurInput')
  formTableRef.value.handleValidate()
}

const rules = {
  productName: [{ required: true, message: '请输入', trigger: 'blur' }],
  isInContract: [{ required: true, trigger: 'change', validator: handleQuotationValidate }],
  feeType: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  sort: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入'
    }
  ],
  activityQuotation: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入'
    }
  ]
}

// radio change
const handleRadioChange = value => {
  if (value === '1') {
    formData.quotation = ''
  }
}

const changeIsInContract = () => {
  formData.isInContract = '0'
}
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
:deep(.el-table .cell) {
  padding: 0 12px 0 0;
}
:deep(.el-form-item--default) {
  .el-form-item__content {
    width: 100%;
  }
}

.el-select {
  width: 573px;
}

.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
:deep(.quotation-form-item) {
  .el-form-item__content {
    display: flex;
  }
  .el-input {
    width: 244px;
    margin-right: 16px;
  }
}
</style>
