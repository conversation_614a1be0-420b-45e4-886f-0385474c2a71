<div class="vue-office-docx-main">
  <!--docxjs library predefined styles--><style>
    .docx-wrapper {
      background: gray;
      padding: 30px;
      padding-bottom: 0px;
      display: flex;
      flex-flow: column;
      align-items: center;
    }
    .docx-wrapper > section.docx {
      background: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
      margin-bottom: 30px;
    }
    .docx {
      color: black;
    }
    section.docx {
      box-sizing: border-box;
      display: flex;
      flex-flow: column nowrap;
      position: relative;
      overflow: hidden;
    }
    section.docx > article {
      margin-bottom: auto;
    }
    .docx table {
      border-collapse: collapse;
    }
    .docx table td,
    .docx table th {
      vertical-align: top;
    }
    .docx p {
      margin: 0pt;
      min-height: 1em;
    }
    .docx span {
      white-space: pre-wrap;
      overflow-wrap: break-word;
    }
    .docx a {
      color: inherit;
      text-decoration: inherit;
    }</style
  ><!--docxjs document theme values--><style>
    .docx {
      --docx-majorHAnsi-font: Cambria;
      --docx-minorHAnsi-font: Calibri;
      --docx-dk1-color: #000000;
      --docx-lt1-color: #ffffff;
      --docx-dk2-color: #1f497d;
      --docx-lt2-color: #eeece1;
      --docx-accent1-color: #4f81bd;
      --docx-accent2-color: #c0504d;
      --docx-accent3-color: #9bbb59;
      --docx-accent4-color: #8064a2;
      --docx-accent5-color: #4bacc6;
      --docx-accent6-color: #f79646;
      --docx-hlink-color: #0000ff;
      --docx-folHlink-color: #800080;
    }</style
  ><!--docxjs document styles--><style>
    .docx span {
      font-family: var(--docx-minorHAnsi-font);
      min-height: 11pt;
      font-size: 11pt;
    }
    .docx p {
      margin-top: 0pt;
      margin-bottom: 0pt;
      line-height: 1;
      margin-left: 0pt;
      margin-right: 0pt;
      text-align: left;
    }
    .docx table,
    table.docx_tablenormal td {
      padding-top: 0pt;
      padding-left: 0pt;
      padding-bottom: 0pt;
      padding-right: 0pt;
    }
    .docx p,
    p.docx_normal {
    }
    .docx p,
    p.docx_normal span {
      font-family: 宋体;
    }
    p.docx_bodytext {
      margin-top: 0.4pt;
    }
    p.docx_bodytext span {
      font-family: 宋体;
      font-weight: bold;
      min-height: 15pt;
      font-size: 15pt;
    }
    p.docx_listparagraph {
    }
    p.docx_listparagraph span {
      font-family: 宋体;
    }
    p.docx_tableparagraph {
    }
    p.docx_tableparagraph span {
      font-family: 宋体;
    }
  </style>
  <div class="docx-wrapper">
    <section class="docx" style="padding: 66pt 46pt 14pt 45pt; width: 595.5pt; min-height: 842pt">
      <article>
        <p class="docx_bodytext" style="margin-top: 1.45pt; margin-left: 168.1pt; margin-right: 168.7pt; text-align: center">
          <span>单位（场所）信息访查表</span>
        </p>
        <p style="margin-top: 0.4pt; margin-bottom: 0pt; line-height: 1"></p>
        <table class="first-row last-row first-col last-col" style="width: auto; text-align: left; table-layout: auto">
          <colgroup>
            <col style="width: 63.6pt" />
            <col style="width: 17.4pt" />
            <col style="width: 16.2pt" />
            <col style="width: 16.2pt" />
            <col style="width: 37.5pt" />
            <col style="width: 37.5pt" />
            <col style="width: 16.2pt" />
            <col style="width: 16.2pt" />
            <col style="width: 26.1pt" />
            <col style="width: 26.1pt" />
            <col style="width: 26.7pt" />
            <col style="width: 49.5pt" />
            <col style="width: 30.3pt" />
            <col style="width: 16.8pt" />
            <col style="width: 26.1pt" />
            <col style="width: 22.2pt" />
            <col style="width: 47.4pt" />
          </colgroup>
          <tr style="height: 28.65pt">
            <td style="width: 63.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph" style="margin-top: 8.9pt; margin-left: 11.2pt">
                <span style="min-height: 10pt; font-size: 10pt">单位名称</span>
              </p>
            </td>
            <td
              colspan="16"
              style="width: 428.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td style="width: 63.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 11.2pt">
                <span style="min-height: 10pt; font-size: 10pt">单位详址</span>
              </p>
            </td>
            <td
              colspan="16"
              style="width: 428.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              rowspan="2"
              style="width: 63.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph" style="margin-top: 0.3pt"></p>
              <p class="docx_tableparagraph" style="margin-left: 11.2pt">
                <span style="min-height: 10pt; font-size: 10pt">管理类别</span>
              </p>
            </td>
            <td style="width: 17.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 3.7pt; margin-right: 2.25pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">旅馆</span>
              </p>
            </td>
            <td style="width: 16.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 3.1pt; margin-right: 1.7pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">刻章</span>
              </p>
            </td>
            <td style="width: 16.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 3.1pt; margin-right: 1.65pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">印刷</span>
              </p>
            </td>
            <td style="width: 37.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="
                  margin-top: 1.6pt;
                  min-height: 12.6pt;
                  line-height: 12.6pt;
                  text-indent: -5.1pt;
                  margin-left: 8.45pt;
                  margin-right: 0.8pt;
                "
              >
                <span style="min-height: 10pt; font-size: 10pt">机动车修理</span>
              </p>
            </td>
            <td style="width: 37.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="
                  margin-top: 1.6pt;
                  min-height: 12.6pt;
                  line-height: 12.6pt;
                  text-indent: -5.1pt;
                  margin-left: 8.45pt;
                  margin-right: 0.8pt;
                "
              >
                <span style="min-height: 10pt; font-size: 10pt">机动车报废</span>
              </p>
            </td>
            <td style="width: 16.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 3.05pt; margin-right: 1.7pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">旧货</span>
              </p>
            </td>
            <td style="width: 16.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 3.1pt; margin-right: 1.65pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">典当</span>
              </p>
            </td>
            <td style="width: 26.1pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 2.75pt; margin-right: 0.65pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">歌舞娱乐</span>
              </p>
            </td>
            <td style="width: 26.1pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 2.75pt; margin-right: 0.65pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">按摩服务</span>
              </p>
            </td>
            <td style="width: 26.7pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 3.05pt; margin-right: 0.8pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">游戏游艺</span>
              </p>
            </td>
            <td style="width: 49.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 3.95pt; margin-right: 2.55pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">内部（重点）单位</span>
              </p>
            </td>
            <td style="width: 30.3pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 4.9pt; margin-right: 1.7pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">开锁营业</span>
              </p>
            </td>
            <td style="width: 16.8pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p
                class="docx_tableparagraph"
                style="margin-top: 1.6pt; min-height: 12.6pt; line-height: 12.6pt; margin-left: 3.35pt; margin-right: 2pt"
              >
                <span style="min-height: 10pt; font-size: 10pt">其他</span>
              </p>
            </td>
            <td
              colspan="3"
              style="width: 95.7pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 6.4pt">
                <span style="min-height: 10pt; font-size: 10pt">管理细类（可选）</span>
              </p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td style="width: 17.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 16.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 16.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 37.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 37.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 16.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 16.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 26.1pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 26.1pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 26.7pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 49.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 30.3pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 16.8pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 95.7pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              rowspan="2"
              style="width: 63.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph" style="margin-top: 0.3pt"></p>
              <p class="docx_tableparagraph" style="margin-left: 11.2pt">
                <span style="min-height: 10pt; font-size: 10pt">法人信息</span>
              </p>
            </td>
            <td
              colspan="8"
              style="width: 183.3pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 60.65pt">
                <span style="min-height: 10pt; font-size: 10pt">法人身份号码</span>
              </p>
            </td>
            <td
              colspan="3"
              style="width: 102.3pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 30.4pt">
                <span style="min-height: 10pt; font-size: 10pt">法人姓名</span>
              </p>
            </td>
            <td
              colspan="4"
              style="width: 95.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 16.6pt">
                <span style="min-height: 10pt; font-size: 10pt">法人电话号码</span>
              </p>
            </td>
            <td style="width: 47.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 3.05pt">
                <span style="min-height: 10pt; font-size: 10pt">法人职务</span>
              </p>
            </td>
          </tr>
          <tr style="height: 27.15pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="8"
              style="width: 183.3pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 102.3pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="4"
              style="width: 95.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td style="width: 47.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt">
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              rowspan="2"
              style="width: 63.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph" style="margin-top: 0.3pt"></p>
              <p class="docx_tableparagraph" style="margin-left: 6.05pt">
                <span style="min-height: 10pt; font-size: 10pt">治安负责人</span>
              </p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 44.8pt">
                <span style="min-height: 10pt; font-size: 10pt">治安负责人</span>
              </p>
            </td>
            <td
              colspan="5"
              style="width: 144.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 36.1pt">
                <span style="min-height: 10pt; font-size: 10pt">治安负责人电话</span>
              </p>
            </td>
            <td
              colspan="3"
              style="width: 73.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p
                class="docx_tableparagraph"
                style="
                  margin-top: 1.6pt;
                  min-height: 12.6pt;
                  line-height: 12.6pt;
                  text-indent: -26.15pt;
                  margin-left: 31.6pt;
                  margin-right: 4.15pt;
                "
              >
                <span style="min-height: 10pt; font-size: 10pt">是否有营业执</span
                ><span style="min-height: 10pt; font-size: 10pt">照</span>
              </p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 3.7pt">
                <span style="min-height: 10pt; font-size: 10pt">消防是否达标</span>
              </p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="5"
              style="width: 144.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 73.2pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 18.4pt">
                <span style="min-height: 10pt; font-size: 10pt">是 否</span>
              </p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 19.25pt">
                <span style="min-height: 10pt; font-size: 10pt">是 否</span>
              </p>
            </td>
          </tr>
          <tr style="height: 27.15pt">
            <td
              rowspan="13"
              style="width: 63.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph"></p>
              <p class="docx_tableparagraph" style="margin-top: 0.35pt"></p>
              <p class="docx_tableparagraph" style="margin-top: 0.05pt; margin-left: 11.2pt">
                <span style="min-height: 10pt; font-size: 10pt">从业人员</span>
              </p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p
                class="docx_tableparagraph"
                style="margin-top: 8pt; margin-left: 53.8pt; margin-right: 52.65pt; text-align: center"
              >
                <span style="min-height: 10pt; font-size: 10pt">身份证</span>
              </p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p
                class="docx_tableparagraph"
                style="margin-top: 8pt; margin-left: 22.95pt; margin-right: 21.4pt; text-align: center"
              >
                <span style="min-height: 10pt; font-size: 10pt">姓名</span>
              </p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p
                class="docx_tableparagraph"
                style="margin-top: 8pt; margin-left: 41.85pt; margin-right: 40.65pt; text-align: center"
              >
                <span style="min-height: 10pt; font-size: 10pt">电话</span>
              </p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 6.1pt">
                <span style="min-height: 10pt; font-size: 10pt">户籍地</span>
              </p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph" style="margin-top: 8pt; margin-left: 14.2pt">
                <span style="min-height: 10pt; font-size: 10pt">现居住地</span>
              </p>
            </td>
          </tr>
          <tr style="height: 27.15pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.15pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.15pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.15pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.15pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.15pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.15pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
          <tr style="height: 27.2pt">
            <td
              style="
                display: none;
                width: 63.6pt;
                border-top: none;
                border-left: 1pt solid rgb(0, 0, 0);
                border-right: 1pt solid rgb(0, 0, 0);
                border-bottom: 1pt solid rgb(0, 0, 0);
                padding: 0pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="6"
              style="width: 141pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 68.4pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="3"
              style="width: 106.5pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 42.9pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
            <td
              colspan="2"
              style="width: 69.6pt; border-width: 1pt; border-style: solid; border-color: rgb(0, 0, 0); padding: 0pt"
            >
              <p class="docx_tableparagraph"></p>
            </td>
          </tr>
        </table>
      </article>
    </section>
  </div>
</div>
