import request from '@/utils/request'
// 列表查询
export const getUserSuggestionList = params => {
  return request({
    url: '/userSuggestion/list',
    method: 'get',
    params
  })
}

// 详情
export const getUserSuggestionGetById = params => {
  return request({
    url: '/userSuggestion/getById',
    method: 'get',
    params
  })
}

// 回复
export const postUserSuggestionResponse = data => {
  return request({
    url: '/userSuggestion/response',
    method: 'post',
    data
  })
}

// 保存数据
export const postUserSuggestionSave = data => {
  return request({
    url: '/userSuggestion/save',
    method: 'post',
    data
  })
}

// 评价建议
export const postUserSuggestionStar = data => {
  return request({
    url: '/userSuggestion/star',
    method: 'post',
    data
  })
}
