<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-09-11 14:35:14
 * @LastEditTime: 2023-09-14 09:35:14
 * @LastEditors: thb
-->
<!--
 * @Description: 数据简报
 * @Author: thb
 * @Date: 2023-09-05 09:35:06
 * @LastEditTime: 2023-09-05 10:10:58
 * @LastEditors: thb
-->
<template>
  <dataWrap class="top-left" title="数据简报" :request-api="getSaleDataReport">
    <template #default="{ data }">
      <div class="list-wrap">
        <div class="list-item">
          <div class="item-left">
            <div class="left-top">
              <span class="icon clue-icon"></span>
              <span class="name">线索数量</span>
            </div>
            <div class="left-bottom">
              {{ data.clueNum || '--' }}
            </div>
          </div>
          <div class="item-right">
            <span class="right-top name">超过10天未跟进线索</span>
            <div class="left-bottom">
              {{ data.overTenClueNum || '--' }}
            </div>
          </div>
        </div>
        <div class="list-item">
          <div class="item-left">
            <div class="left-top">
              <span class="icon client-icon"></span>
              <span class="name">客户数量（个）</span>
            </div>
            <div class="left-bottom">
              {{ data.cusNum || '--' }}
            </div>
          </div>
          <div class="item-right">
            <span class="right-top name">超过10天未跟进客户</span>
            <div class="left-bottom">
              {{ data.overTenCusNum || '--' }}
            </div>
          </div>
        </div>
        <div class="list-item">
          <div class="item-left">
            <div class="left-top">
              <span class="icon business-icon"></span>
              <span class="name">商机数量</span>
            </div>
            <div class="left-bottom name">
              {{ data.businessNum || '--' }}
            </div>
          </div>
          <div class="item-right">
            <span class="right-top name">转企业数量（家）</span>
            <div class="left-bottom">
              {{ data.changeNum || '--' }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </dataWrap>
</template>
<script setup>
import { getSaleDataReport } from '@/api/panel-data/sale'
import dataWrap from '../../collecting-panel/components/data-wrap'
</script>
<style lang="scss" scoped>
.top-left {
  flex: 1;
  .list-wrap {
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 100%;
    width: 100%;
  }
  .list-item {
    flex: 1;
    display: flex;
    background: #eff7ff;
    border-radius: 2px;
    padding: 10px 16px;
    .name {
      font-size: 16px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #2383e7;
    }
    .item-left {
      flex: 1.2;
    }
    .left-top,
    .right-top {
      display: flex;
      align-items: center;
    }
    .left-bottom,
    .right-bottom {
      font-size: 20px;
      font-family: AlibabaPuHuiTi_2_85_Bold;
      color: #333333;
    }
    .item-right {
      flex: 1;
    }
  }
}
:deep(.content) {
  flex-direction: column;
  gap: 8px;
}

.clue-icon,
.client-icon,
.business-icon {
  margin-right: 8px;
}
</style>
