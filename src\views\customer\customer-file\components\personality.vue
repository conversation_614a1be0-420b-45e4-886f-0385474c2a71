<template>
  <el-form :model="formData" label-position="top">
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="客户性格">
          <el-select
            class="select-input"
            :disabled="disabled"
            v-model="formData.personality"
            clearable
            :placeholder="disabled ? ' ' : '请选择'"
            :hide-required-asterisk="disabled"
          >
            <el-option label="老虎" value="老虎" />
            <el-option label="孔雀" value="孔雀" />
            <el-option label="猫头鹰" value="猫头鹰" />
            <el-option label="鸽子" value="鸽子" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="客户类型">
          <el-select
            class="select-input"
            multiple
            :disabled="disabled"
            v-model="formData.types"
            :placeholder="disabled ? ' ' : '请选择'"
            clearable
          >
            <el-option label="性急/速度" value="性急/速度" />
            <el-option label="谨慎/准确率" value="谨慎/准确率" />
            <el-option label="价格敏感" value="价格敏感" />
            <el-option label="价格不敏感" value="价格不敏感" />
            <el-option label="在乎质量" value="在乎质量" />
            <el-option label="富二代" value="富二代" />
            <el-option label="第一次开公司" value="第一次开公司" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="其他标签">
          <el-select
            class="select-input"
            :disabled="disabled"
            v-model="formData.tags"
            multiple
            filterable
            allow-create
            :placeholder="disabled ? ' ' : '请选择'"
            clearable
          >
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="客户年龄层次">
          <el-select
            class="select-input"
            :disabled="disabled"
            v-model="formData.ageLevel"
            :placeholder="disabled ? ' ' : '请选择'"
            clearable
          >
            <el-option label="95前" value="95前" />
            <el-option label="95-90后" value="95-90后" />
            <el-option label="90-85后" value="90-85后" />
            <el-option label="85-80后" value="85-80后" />
            <el-option label="70后" value="70后" />
            <el-option label="60后" value="60后" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="客户性格补充">
          <el-tooltip
            :content="formData.personalityComplement"
            v-if="disabled && formData.personalityComplement"
            effect="light"
            placement="top-start"
          >
            <el-input
              v-model="formData.personalityComplement"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            v-model="formData.personalityComplement"
            maxlength="100"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="客户股东家庭关系">
          <FileUpload v-if="!disabled" v-model="formData.shareholderFamilyRelationshipFileList" :limit="100" :isShowTip="false" />
          <!-- <span class="download-text" @click="downloadFile(formData.shareholderFamilyRelationshipFile)" v-else>
            {{
              (formData.shareholderFamilyRelationshipFile && formData.shareholderFamilyRelationshipFile?.fileNames) || '暂无文件'
            }}</span
          > -->
          <fileList v-else :list="formData.shareholderFamilyRelationshipFileList" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="资料收取要求">
          <el-tooltip
            :content="formData.collectionRequirement"
            v-if="disabled && formData.collectionRequirement"
            effect="light"
            placement="top-start"
          >
            <el-input
              :disabled="disabled"
              maxlength="100"
              v-model="formData.collectionRequirement"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            :disabled="disabled"
            maxlength="100"
            v-model="formData.collectionRequirement"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="财务处理要求">
          <el-tooltip
            :content="formData.dealRequirement"
            v-if="disabled && formData.dealRequirement"
            effect="light"
            placement="top-start"
          >
            <el-input
              :disabled="disabled"
              maxlength="100"
              v-model="formData.dealRequirement"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            :disabled="disabled"
            v-else
            maxlength="100"
            v-model="formData.dealRequirement"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="开具发票特殊要求">
          <el-tooltip
            :content="formData.billingDemand"
            v-if="disabled && formData.billingDemand"
            effect="light"
            placement="top-start"
          >
            <el-input
              :disabled="disabled"
              maxlength="100"
              v-model="formData.billingDemand"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-tooltip>
          <el-input
            v-else
            :disabled="disabled"
            maxlength="100"
            v-model="formData.billingDemand"
            :placeholder="disabled ? '' : '请输入'"
          />
          <!-- <FileUpload v-if="!disabled" v-model="formData.invoicingSpecialRequirementFile" :isShowTip="false" />
          <span class="download-text" @click="downloadFile(formData.invoicingSpecialRequirementFile)" v-else>
            {{
              (formData.invoicingSpecialRequirementFile && formData.invoicingSpecialRequirementFile?.fileNames) || '暂无文件'
            }}</span
          > -->
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="附件">
          <FileUpload
            v-if="!disabled"
            v-model="formData.personalizedInformationAttachmentFileList"
            :limit="100"
            :isShowTip="false"
          />
          <!-- <span class="download-text" @click="downloadFile(formData.personalizedInformationAttachmentFile)" v-else>
            {{
              (formData.personalizedInformationAttachmentFile && formData.personalizedInformationAttachmentFile?.fileNames) ||
              '暂无文件'
            }}</span
          > -->
          <fileList v-else :list="formData.personalizedInformationAttachmentFileList" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import { saveCustomerPersonal, getCustomerPersonalByCiId } from '@/api/customer/file'
import { useRemote } from '@/hooks/useRemote'
// import { downloadFile } from '@/utils/common'
import iFrame from '@/components/iFrame'
import fileList from '@/components/FileList'
const disabled = inject('disabled')
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const emits = defineEmits(['update:modelValue', 'on-edit'])

const formData = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})
const getDetail = async () => {
  const { data } = await getCustomerPersonalByCiId(props.modelValue.ciId)
  formData.value = Object.assign(formData.value, data)
}
watch(
  disabled,
  () => {
    if (disabled.value) {
      //
      getDetail()
    }
  },
  {
    immediate: true
  }
)
// 监听是否是编辑
const edit = inject('isEdit')
watch(
  edit,
  async () => {
    if (edit.value) {
      await getDetail()
      emits('on-edit')
    }
  },
  {
    immediate: true
  }
)
const saveRemote = async () => {
  const id = await useRemote(
    saveCustomerPersonal,
    formData.value,
    ['personalizedInformationAttachmentFile', 'shareholderFamilyRelationshipFile'],
    '个性化信息',
    ['personalizedInformationAttachmentFileList', 'shareholderFamilyRelationshipFileList']
  )
  formData.value.personalityId = id
  return id
}

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

defineExpose({
  saveRemote
})
</script>
<style lang="scss">
.select-input {
  width: 400px !important;
}
</style>
