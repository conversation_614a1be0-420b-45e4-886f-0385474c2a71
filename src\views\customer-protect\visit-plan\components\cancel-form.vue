<!--
 * @Description: 取消计划
 * @Author: thb
 * @Date: 2023-07-28 14:01:16
 * @LastEditTime: 2023-07-28 14:53:29
 * @LastEditors: thb
-->

<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="取消原因" prop="cancelReason">
          <el-input
            v-model="formData.cancelReason"
            type="textarea"
            maxlength="1000"
            placeholder="请输入"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
const formData = ref({
  cancelReason: ''
})

const rules = {
  cancelReason: [
    {
      required: true,
      message: '请输入与',
      trigger: 'blur'
    }
  ]
}
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped></style>
