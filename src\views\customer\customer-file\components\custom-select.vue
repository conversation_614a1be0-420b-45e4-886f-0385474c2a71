<template>
  <el-popover placement="bottom" v-model:visible="visible" trigger="click">
    <template #reference>
      <el-input v-model="inputValue" readonly v-bind="$attrs" placeholder="请选择" />
    </template>
    <div class="list">
      <div class="list-item" :class="[tag === '0' ? 'active-item' : '']" @click="handleSelect('无')">无</div>
      <div class="list-item" :class="[tag === '1' ? 'active-item' : '']" @click="handleSelect('有')">有</div>
      <div class="list-item">
        <el-input v-model="customValue" placeholder="请输入" />
      </div>
    </div>
  </el-popover>
</template>
<script setup>
import { computed, watch } from 'vue'

const visible = ref(false)
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['update:modelValue'])
const inputValue = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})
const customValue = ref('')
watch(customValue, newVal => {
  tag.value = '2'
  inputValue.value = newVal
})

const tag = ref('0')
// 无-0 有-1 自定义-2
const handleSelect = value => {
  if (value === '无') {
    tag.value = '0'
  } else {
    tag.value = '1'
  }
  inputValue.value = value
  visible.value = false
}

/** 图标外层点击隐藏下拉列表 */
// function hideSelectIcon(event) {
//   const elem = event.relatedTarget || event.srcElement || event.target || event.currentTarget
//   const className = elem.className
//   if (className !== 'el-input__inner') {
//     visible.value = false
//   }
// }
</script>
<style lang="scss" scoped>
.active-item {
  color: #409eff;
}
.list-item {
  &:hover {
    background-color: #f0f2f5;
  }
}
</style>
