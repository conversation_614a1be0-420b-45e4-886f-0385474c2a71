<!--
 * @Description: 商机详情
 * @Author: thb
 * @Date: 2023-08-22 15:06:15
 * @LastEditTime: 2023-11-24 10:01:12
 * @LastEditors: thb
-->
<template>
  <el-dialog
    class="dialog-footer-1"
    align-center
    title="商机详情"
    width="1200"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <div class="container-t">
      <span class="avatar-icon"></span>
      <span class="name text-bold font-18">{{ detail.name }}</span>
      <!-- 进度状态 -->
      <el-tag style="margin-right: 102px">{{ detail.stage }}</el-tag>
      <span class="flex-1"
        >客户名称：

        <span class="text-bold">{{ detail.companyName }}</span>
      </span>
      <template v-if="tabType !== '0'">
        <template v-if="detail.stage !== '赢单' && detail.stage !== '输单' && detail.stage !== '已收定金,待打尾款'">
          <el-button @click="handleEdit">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon></template
            >编辑商机</el-button
          >
          <el-button @click="handleDelete">
            <template #icon>
              <el-icon color="#F56C6C"> <Delete /> </el-icon></template
            >删除商机</el-button
          >
        </template>
        <!-- 销售阶段为95%时 需要手动标记赢单 -->
        <template v-if="detail.stage === '已收定金,待打尾款'">
          <el-button @click="showWinOrder">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon></template
            >标记赢单</el-button
          >
        </template>
      </template>
    </div>
    <div class="container-c">
      <span class="flex-1"
        >跟进人：
        <span class="text-bold">{{ detail.currentUserName || '--' }}</span>
      </span>
      <span class="flex-1"
        >最近跟进时间:
        <span class="text-bold">{{ detail.lastFollowTime || '--' }}</span>
      </span>
      <span class="flex-1" v-if="detail.stage === '赢单' || detail.stage === '已收定金,待打尾款'"
        >实际成交金额:
        <span class="text-bold">{{ detail.actualAmount }} 元</span>
      </span>
      <span class="flex-1" v-if="detail.stage === '赢单' || detail.stage === '已收定金,待打尾款'"
        >成交周期:
        <span class="text-bold">{{ calculateCycleTime(detail.createTime, detail.winTime) }}</span>
      </span>
    </div>
    <!-- 销售阶段 -->
    <saleStage :stage="detail.stage" />
    <div class="container-bottom">
      <div class="left">
        <el-tabs v-model="activeName">
          <el-tab-pane label="商机资料" name="商机资料"
            ><businessForm v-if="activeName === '商机资料'" :detail="detail"
          /></el-tab-pane>
          <el-tab-pane label="操作记录" name="操作记录"
            ><actionRecord :id="detail.id" :request-api="getBusinessListById" v-if="activeName === '操作记录'"
          /></el-tab-pane>
        </el-tabs>
      </div>
      <div class="right">
        <record
          :detail="detail"
          v-if="detail.id"
          :tabType="tabType"
          :request-api="getBusinessRecordList"
          :submit-api="saveBusinessRecord"
          @on-success="getDetail"
        />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>

  <businessAdd
    v-if="editShow"
    :data="detail"
    :detail="detail"
    type="edit"
    @on-close="editShow = false"
    @on-success="handleSuccess"
  />

  <contract v-if="contractShow" :normal="normal" @on-close="contractShow = false" @on-next="handelNext" />
  <normalCreate v-if="normalShow" :productIds="productIds" :associated="associated" @on-close="normalShow = false" />
  <templateCreate v-if="templateShow" :productIds="productIds" :associated="associated" @on-close="templateShow = false" />
</template>
<script setup>
import { EditPen, Delete } from '@element-plus/icons-vue'
import {
  getClientBusinessDetailById,
  saveBusinessRecord,
  getBusinessListById,
  getBusinessRecordList,
  deleteBusiness,
  markWinOrder
} from '@/api/material-manage/client'
import saleStage from './sale-stage'
import businessForm from './business-form'
import actionRecord from '../../clue-manage/components/action-record.vue'
import record from '../../clue-manage/components/record'
import businessAdd from '.../../../../client-manage/components/business-add'
import contract from '@/views/customer/customer-file/components/contract.vue'
import normalCreate from '@/views/contract-manage/contract-list/components/normal-create.vue'
import templateCreate from '@/views/contract-manage/contract-list/components/template-create.vue'
import { useHandleData } from '@/hooks/useHandleData'
import { getCustomerById } from '@/api/customer/file'
import { useDialog } from '@/hooks/useDialog'
import markWin from './mark-win'
const { showDialog } = useDialog()

const tabType = inject('tabType')
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: String
})

const detail = ref({})

const activeName = ref('商机资料')

const calculateCycleTime = (startTime, endTime) => {
  let usedTime = new Date(endTime).getTime() - new Date(startTime).getTime() // 相差的毫秒数
  let days = Math.floor(usedTime / (24 * 3600 * 1000)) // 计算出天数
  let leavel = usedTime % (24 * 3600 * 1000) // 计算天数后剩余的时间
  let hours = Math.floor(leavel / (3600 * 1000)) // 计算剩余的小时数
  let leavel2 = leavel % (3600 * 1000) // 计算剩余小时后剩余的毫秒数
  let minutes = Math.floor(leavel2 / (60 * 1000)) // 计算剩余的分钟数
  return days + '天' + hours + '时' + minutes + '分'
}

const getDetail = async id => {
  const { data } = await getClientBusinessDetailById(id || props.id)
  detail.value = data || {}
  detail.value.businessStr = data.list?.map(item => item.productName).join(',')
  emits('on-success')
}

// 编辑商机
const editShow = ref(false)
const handleEdit = () => {
  editShow.value = true
}
const normalShow = ref(false)
const contractShow = ref(false)
const templateShow = ref(false)
// 传入的productIds(创建的为意向合同)
const productIds = ref([])
// 下一步
const handelNext = createType => {
  // '0'代表 普通新建
  // '1'代表 模板新建
  if (createType === '0') {
    normalShow.value = true
  }
  if (createType === '1') {
    templateShow.value = true
  }
}
// 编辑商机成功之后
const normal = ref(false)
const associated = ref({})
const handleSuccess = async (ids, customerId) => {
  getDetail(props.id)
  // if (ids) {
  //   contractShow.value = true
  //   productIds.value = ids
  // }

  // 通过 customerId 获取档案详情 ，成功获取后 显示 弹窗
  if (customerId) {
    const { data } = await getCustomerById(customerId)
    if (data) {
      associated.value = data
    }
  }

  if (ids) {
    contractShow.value = true
    productIds.value = ids
  }
  // 如果存在多个业务类型 则需要手动录入信息
  if (ids && ids.length > 1) {
    normal.value = true
  } else {
    normal.value = false
  }
}

// 删除商机
const handleDelete = async () => {
  await useHandleData(deleteBusiness, detail.value.id, `删除该商机 ${detail.value.name} 信息`)
  handleClose()
  emits('on-success')
}

// 标记赢单
const showWinOrder = () => {
  showDialog({
    title: '标记赢单',
    customClass: 'mark-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '确认',
    component: markWin, // 表单组件
    submitApi: markWinOrder, // 提交api
    handleConvertParams: data => {
      data.id = props.id
      data.customerId = detail.value.customerId
    },
    // submitCallback: businessSubmitCallback, // 提交成功之后的回调函数
    submitCallback: () => {
      getDetail(props.id)
    } // 处理提交参数
  })
}
onMounted(() => {
  getDetail(props.id)
})
</script>
<style lang="scss" scoped>
.container-t {
  display: flex;
  align-items: center;
  .name {
    margin-right: 24px;
    margin-left: 12px;
  }
}
.flex-1 {
  flex: 1;
}
.container-c {
  display: flex;
  align-items: center;
  margin-top: 25px;
  padding-bottom: 18px;
}

.text-bold {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
}

.container-bottom {
  display: flex;
  min-height: 400px;
  border-top: 1px solid #e8e8e8;
  .left {
    flex: 1;
    overflow: hidden;
    border-right: 1px solid #e8e8e8;
    padding-right: 16px;
  }
  .right {
    width: 310px;
    padding-left: 16px;
  }
}

.flex-25 {
  width: 25%;
}

.el-tag.el-tag--default.el-tag--light {
  background: #ffe8de;
  border-radius: 4px;
  border: 1px solid #ff6721;
  font-size: 15px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #ff6721;
}
.font-18 {
  font-size: 18px;
}
</style>
