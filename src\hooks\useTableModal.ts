import tableModal from '@/components/tableModal'
import { ColumnProps } from '@/components/ProTable/interface'
import { useDic } from '@/hooks/useDic'
import { getCustomers } from '@/api/customer/file'
import { reactive, toRefs } from 'vue'
const { getDic } = useDic()
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    width: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '250',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]
export const useTableModal = () => {
  const state = reactive({
    listShow: false
  })

  return {
    ...toRefs(state),

    tableModal,
    getCustomers,
    columns
  }
}
