<template>
  <ProTable
    ref="proTable"
    title="拜访计划"
    :columns="columns"
    :request-api="getVisitList"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      <el-button v-hasPermi="['customer-protect:visit-plan:export']" :icon="Download" @click="handleExport">导出</el-button>
    </template>
    <!-- planName -->
    <template #planName="{ row }">
      <span class="blue-text" @click="handleDetail(row)">
        {{ row.planName }}
      </span>
    </template>

    <template #customerName="{ row }">
      <span class="blue-text" @click="handleShowCustomer(row.customerId)">
        {{ row.customerName }}
      </span>
    </template>
  </ProTable>

  <planForm
    v-if="planShow"
    :type="type"
    :id="planId"
    :status="status"
    @on-close="planShow = false"
    @on-edit="handleEdit"
    @on-success="getList"
  />
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="customerDetailShow = false"
    @on-list="getList"
  />
</template>
<script setup lang="tsx">
import { ref } from 'vue'
import { CirclePlus, Download } from '@element-plus/icons-vue'
import { getVisitList, customerVisitExport } from '@/api/customer-protect/visit-plan'
import { ColumnProps } from '@/components/ProTable/interface'
import planForm from './components/plan-form.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import useCommonStore from '@/store/modules/common'
import { getReviewerTreeData } from '@/api/process/process'
import { useDic } from '@/hooks/useDic'

const { getDic } = useDic()
const useCommon = useCommonStore()
const { proxy } = getCurrentInstance()

const getTreeData = () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await getReviewerTreeData()
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}

const columns: ColumnProps<any>[] = [
  {
    prop: 'index',
    label: '序号',
    width: '80',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'planName',
    label: '计划标题',
    search: { el: 'input' },
    sortable: 'custom',
    sortName: 'plan_name'
  },

  {
    prop: 'customerName',
    width: '300',
    label: '关联客户',
    search: { el: 'input' },
    sortable: 'custom',
    sortName: 'customer_id'
  },
  {
    prop: 'customerNo',
    width: '150',
    label: '客户编号',
    search: { el: 'input' }
  },
  {
    prop: 'planVisitDate',
    width: '200',
    label: '计划拜访日期',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'plan_visit_date'
  },
  {
    prop: 'visitor',
    width: '150',
    label: '拜访人',
    sortable: 'custom',
    sortName: 'visitor_id'
  },
  {
    prop: 'visitorId',
    width: '150',
    label: '拜访人',
    enum: getTreeData,
    isShow: false,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'id',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    }
  },
  {
    prop: 'planVisitMethod',
    width: '150',
    label: '拜访方式',
    enum: getDic('visit_method'),
    search: {
      el: 'select'
    },
    sortable: 'custom',
    sortName: 'plan_visit_method'
  },
  {
    prop: 'actualPlanDate',
    width: '200',
    label: '完成时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'actual_plan_date'
  },
  {
    prop: 'status',
    label: '计划状态',
    width: '150',
    fixed: 'right',
    enum: [
      {
        label: '待完成',
        value: '0'
      },
      {
        label: '已完成',
        value: '1'
      },
      {
        label: '已取消',
        value: '2'
      }
    ],
    search: {
      el: 'select',
      props: {
        clearable: true
      }
    },
    sortable: 'custom',
    sortName: 'status'
  }
]

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// 自定义
const transformRequestParams = (data: any) => {
  if (data.planVisitDate) {
    data.planVisitDateStart = data.planVisitDate[0]
    data.planVisitDateEnd = data.planVisitDate[1]
  }
  if (data.actualPlanDate) {
    data.actualPlanDateStart = data.actualPlanDate[0]
    data.actualPlanDateEnd = data.actualPlanDate[1]
  }
}

// 新增计划
const type = ref('add')
const status = ref('')
const handleAdd = () => {
  type.value = 'add'
  status.value = '0'
  planShow.value = true
}

const planId = ref()
// 显示详情弹窗
const handleDetail = (row: any) => {
  status.value = row.status
  planId.value = row.id
  type.value = 'detail'
  planShow.value = true
}

// 计划表单弹窗标志
const planShow = ref(false)
// 编辑
const handleEdit = (id: string) => {
  planShow.value = false
  planId.value = id
  status.value = ''
  type.value = 'edit'
  planShow.value = true
}

//
const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}

const customerDetailShow = ref(false)
const rowId = ref()
const isEdit = ref(false)
provide('isEdit', isEdit)
const handleShowCustomer = (id: number) => {
  if (id) {
    rowId.value = id
    customerDetailShow.value = true
  }
}

// 导出
const handleExport = async () => {
  const data = Object.assign({}, proTable.value.searchParam)
  if (data.planVisitDate) {
    data.planVisitDateStart = data.planVisitDate[0]
    data.planVisitDateEnd = data.planVisitDate[1]
    delete data.planVisitDate
  }
  if (data.actualPlanDate) {
    data.actualPlanDateStart = data.actualPlanDate[0]
    data.actualPlanDateEnd = data.actualPlanDate[1]
    delete data.actualPlanDate
  }
  const result = await customerVisitExport(data)
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}

watch(
  () => useCommon.id,
  () => {
    if (useCommon.id && useCommon.bizType === 'visit_plan') {
      // http://************:8082/task-view-3627.html?tid=4vl41aze中“拜访计划：点击转跳到 拜访计划 页面显示计划详情”所需，增加一个前端自定义的bizType'visit_plan'，不经过消息中心
      console.log('handleDetail')
      handleDetail({
        id: useCommon.id
      })
      useCommon.clearId()
      useCommon.clearBizType()
    }
  },
  {
    immediate: true
  }
)
</script>
<style lang="scss" scoped></style>
