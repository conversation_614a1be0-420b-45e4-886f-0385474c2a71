/*
 * @Description:
 * @Author: thb
 * @Date: 2023-05-31 11:19:27
 * @LastEditTime: 2023-11-08 09:41:24
 * @LastEditors: thb
 */
import { ElMessage } from 'element-plus'
import { changeFileData, changeFileList } from '@/utils/common'
export const useRemote = async (
  api: (params: any) => Promise<any>,
  params: any,
  list: string[],
  tabName: string,
  noLimitList: string[] // 不限制文件数量的字段名上传
) => {
  try {
    let query = {
      ...params
    }
    if (list.length) {
      list.forEach(item => {
        if (Array.isArray(query[item]) && query[item][0]) {
          query[item] = changeFileData(query[item])
          delete query[item]['id']
          return
        } else if (query[item]?.constructor === Object) {
          delete query[item]['id']
          return
        } else {
          delete query[item]
        }
      })
    }
    // 对当前的tab下做响应的数据转换
    const map = {
      // 对应后端需要传的字段名
      企业信息: 'customerId',
      银行信息: 'bankId',
      工商信息: 'businessInformationId',
      许可证件: 'licenseId',
      社保公积金: 'id',
      税务信息: 'id',
      个性化信息: 'id',
      联系人: 'id'
    }
    const map1 = {
      // 前端自己确定的需要传的字段名
      企业信息: 'customerId',
      银行信息: 'bankId',
      工商信息: 'businessInformationId',
      许可证件: 'licenseId',
      社保公积金: 'fundId',
      税务信息: 'taxId',
      个性化信息: 'personalityId',
      联系人: 'contractId'
    }
    if (map[tabName as keyof typeof map] && params[map1[tabName as keyof typeof map1]]) {
      query[map[tabName as keyof typeof map]] = params[map1[tabName as keyof typeof map1]]
    } else {
      delete query[map1[tabName as keyof typeof map1]]
    }
    // tab为联系人的时候的传递参数特殊处理
    if (tabName === '联系人') {
      query = query.list
    }

    //  不限制文件数量的字段名上传
    if (Array.isArray(noLimitList) && noLimitList.length) {
      // query.identityDocumentFileList = changeFileList(query.identityDocumentFileList)
      noLimitList.forEach(fileName => {
        query[fileName] = changeFileList(query[fileName])
      })
    }

    const { data } = await api(query)
    if (data > 0) {
      ElMessage({
        message: '保存成功!',
        type: 'success'
      })
      return data
    } else {
      ElMessage({
        message: '保存失败!',
        type: 'error'
      })
    }
  } catch (error) {
    return false
  }
}
