<template>
  <ProTable ref="proTable" title="模板评审" :init-param="initParam" :columns="columns" :request-api="getTabList">
    <!-- 表格tabs -->
    <template #tabs>
      <el-radio-group v-model="initParam.tabType">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <!-- 操作 -->
    <template #operation="{ row }">
      <!-- 变更操作 -->
      <el-button link type="primary" @click="handleDetail(row, 0)">详情</el-button>
      <!-- 审批 -->
      <el-button link type="primary" v-if="initParam.tabType === '2' && row.status === '0'" @click="handleDetail(row, 1)"
        >审批</el-button
      >
    </template>
  </ProTable>

  <tempDetail v-if="tempShow" :id="rowId" :type="reviewType" @on-close="tempShow = false" @on-success="handleSuccess" />
</template>
<script setup lang="tsx">
import { ref, reactive } from 'vue'
import { ColumnProps } from '@/components/ProTable/interface'
import tempDetail from './temp-detail.vue'
import { getContractTempListMyCreate, getContractTempListMyAudit } from '@/api/contract-template/contract-template'

const tabs = [
  {
    dictLabel: '我申请的',
    dicValue: '1'
  },
  {
    dictLabel: '由我审批的',
    dicValue: '2'
  }
]

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ tabType: '1' })
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  {
    prop: 'tempName',
    label: '模板名称',
    width: '300',
    search: { el: 'input' }
  },
  {
    prop: 'contractType',
    label: '合同类型',
    width: '200',
    enum: [
      {
        label: '记账合同',
        value: '0'
      },
      {
        label: '一次性合同',
        value: '1'
      },
      {
        label: '地址服务协议合同',
        value: '2'
      }
    ],
    search: { el: 'select' }
  },
  {
    prop: 'remark',
    label: '备注'
  },
  {
    prop: 'createBy',
    width: '150',
    label: '提交人'
  },
  {
    prop: 'createTime',
    width: '200',
    label: '提交时间'
  },
  {
    prop: 'status',
    label: '审批状态',
    width: '150',
    fixed: 'right',
    enum: [
      {
        label: '待审批',
        value: '0'
      },
      {
        label: '通过',
        value: '1'
      },
      {
        label: '不通过',
        value: '2'
      }
    ],
    search: { el: 'select' }
  },
  {
    prop: 'operation',
    label: '操作',
    isColShow: false,
    fixed: 'right'
  }
]

// getTabList 获取tab下的列表
const getTabList = async (data: any) => {
  // 由我提交的
  if (data.tabType === '1') {
    const result1 = await getContractTempListMyCreate(data)
    return result1
  } else {
    // 由我审批的
    const result2 = await getContractTempListMyAudit(data)
    return result2
  }
}

// reviewShow 审批弹窗展示
const tempShow = ref(false)
const rowId = ref()
const reviewType = ref('detail')
// // 存储提交人(合同的创建人)和提交时间（流程的开始时间）

const handleDetail = (row: any, isReview: number) => {
  rowId.value = row.id
  // 由我审批的
  if (initParam.tabType === '2' && isReview === 1) {
    console.log('edit')
    reviewType.value = 'edit'
  } else {
    reviewType.value = 'detail'
  }
  tempShow.value = true
}
// // handleSuccess
const proTable = ref()
const handleSuccess = () => {
  proTable.value?.getTableList()
}

// const handleRadioChange = () => {
//   proTable.value.pageable.pageNum = 1
// }
</script>
<style lang="scss" scoped></style>
