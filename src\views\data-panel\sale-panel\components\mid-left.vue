<template>
  <dataWrap class="mid-left" title="线索转化" :request-api="getClueTransferData">
    <template #default="{ data }">
      <listItem :data="data" />
    </template>
  </dataWrap>
</template>
<script setup>
import dataWrap from '../../collecting-panel/components/data-wrap'
import listItem from './list-item'
import { getClueTransferData } from '@/api/panel-data/sale'
</script>
<style lang="scss" scoped>
.mid-left {
  flex: 2;
}
:deep(.content) {
  flex-direction: column;
  gap: 24px;
  // overflow-x: auto;
}
</style>
