<!--
 * @Description: 流程设置基础信息
 * @Author: thb
 * @Date: 2023-06-19 09:23:47
 * @LastEditTime: 2023-08-30 15:08:12
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="disabled">
    <el-row :gutter="24">
      <el-col :span="8">
        <el-form-item label="流程名称" prop="name">
          <el-input v-model="formData.name" maxlength="20" :placeholder="disabled ? '' : '请输入'" :disabled="disabled" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="合同类型" prop="contractType">
          <el-select v-model="formData.contractType" :placeholder="disabled ? ' ' : '请选择'" clearable :disabled="disabled">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in typeList" :key="index" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="应用部门" prop="deptIds">
          <el-tree-select
            :disabled="disabled"
            multiple
            style="width: 100%"
            v-model="formData.deptIds"
            :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            value-key="id"
            placeholder="请选择"
            check-strictly
            default-expand-all
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            maxlength="1000"
            :disabled="disabled"
            :placeholder="disabled ? '' : '请输入'"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import { deptTreeSelect } from '@/api/system/user'

const props = defineProps({
  modelValue: {
    type: Object
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['update:modelValue'])
const formData = computed({
  get: () => {
    return props.modelValue
  },
  set: newVal => {
    emits('update:modelValue', newVal)
  }
})
// typeList
const typeList = [
  {
    label: '记账合同',
    value: '0'
  },
  {
    label: '一次性合同',
    value: '1'
  },
  {
    label: '地址服务协议合同',
    value: '2'
  }
]

const rules = {
  name: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  contractType: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  deptIds: [
    {
      required: true,
      message: '请选择',
      trigger: ['blur']
    }
  ]
}

// 表单验证
const formRef = ref()
const handleFormValidate = async () => {
  if (!formRef.value) return
  const result = await formRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
    } else {
      console.log('error submit!', fields)
    }
  })
  return result
}

// 表单重置
const resetForm = () => {
  // formRef.value.resetFields()
  formData.value.name = ''
  formData.value.contractType = ''
  formData.value.remark = ''
}

const deptOptions = ref(undefined)
/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data
  })
}
getDeptTree()

defineExpose({
  handleFormValidate,
  resetForm
})
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
.el-form {
  border-top: 1px solid #e8e8e8ff;
  padding-top: 16px;
}
</style>
