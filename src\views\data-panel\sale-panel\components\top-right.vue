<!--
 * @Description: 销售漏斗
 * @Author: thb
 * @Date: 2023-09-05 09:35:38
 * @LastEditTime: 2023-09-14 13:54:29
 * @LastEditors: thb
-->
<template>
  <dataWrap class="top-right" title="销售漏斗" ref="wrapRef" :request-api="getSaleRateData" :charts="charts">
    <template #default>
      <!-- <div ref="funnelRef" class="funnel-chart"></div> -->
    </template>
  </dataWrap>
</template>
<script setup>
import { onMounted } from 'vue'
import dataWrap from '../../collecting-panel/components/data-wrap'
import { getSaleRateData } from '@/api/panel-data/sale'
import arrowImage from '@/assets/icons/arrow.png'
import lineBg1 from '@/assets/images/line-bg1.png'
import lineBg0 from '@/assets/images/line-bg0.png'
import lineBg2 from '@/assets/images/line-bg2.png'
import * as echarts from 'echarts'
console.log('arrowImage', arrowImage)
const funnelRef = ref()
const wrapRef = ref()
const charts = ref([])
const drawFunnel = () => {
  console.log('wrapRef  drawFunnel', wrapRef.value.panelData)
  let myChart = echarts.init(funnelRef.value)
  charts.value[0] = myChart
  const panelData = wrapRef.value.panelData

  const { clueNum, changeToCusNum, addBusinessNum, changeToCompanyNum } = {
    clueNum: Number(panelData.clueNum),
    changeToCusNum: Number(panelData.changeToCusNum),
    addBusinessNum: Number(panelData.addBusinessNum),
    changeToCompanyNum: Number(panelData.changeToCompanyNum)
  }

  const markLineSetting = {
    normal: {
      show: true,
      borderRadius: 4,
      color: '#333',
      verticalAlign: 'middle',
      offset: [40, 0, 0, 0],
      fontSize: 14,
      padding: [3, 10, 5, 10],
      formatter: function (d) {
        console.log('markLineSetting', d)
        // if (d.value) {
        //   let ins = '{img1|} ' + '{words|' + d.data.itemValue + '}'
        //   return ins
        // }
        return '222'
      }
    }
  }

  const calculatePercentage = (value, total) => {
    console.log('value,total', value, total)
    return ((Number(value) / Number(total)) * 100).toFixed(1) + '%'
  }
  const option = {
    backgroundColor: '#ffffff',
    color: ['#DAEAFCFF', '#D3E6FAFF', '#9DC8F5FF', '#74B1F1FF', '#2383E7CC', '#2383E7FF'],
    grid: {
      top: '0',
      left: '0%',
      right: '0%',
      bottom: '0%'
      //  containLabel: true
    },
    xAxis: [
      {
        show: false,
        inverse: true,
        position: 'top'
      },
      {
        position: 'bottom',
        show: false,
        min: 0,
        max: 200
      }
    ],
    yAxis: [
      {
        position: 'left',
        top: '120',
        show: false,
        boundaryGap: false,
        inverse: true,
        type: 'category',
        min: '转化率1',
        data: ['转化率1', '转化率2', '转化率3', '转化率4', '转化率5']
      }
    ],
    series: [
      {
        top: '10%',
        type: 'funnel',

        gap: 12,
        minSize: 150,
        left: '5%',
        width: '60%',
        // bottom: '10%',
        label: {
          show: true,
          position: 'inside',
          fontSize: '14',
          formatter: function (d) {
            return d.name
          }
        },
        data: [
          {
            name: '线索总数',
            percentage: '100%',
            realNum: clueNum,
            value: '40'
          },
          {
            name: '转客户线索数',
            realNum: changeToCusNum,
            // percentage: calculatePercentage(changeToCusNum, clueNum),
            value: '30'
          },
          {
            name: '添加商机客户数',
            realNum: addBusinessNum,
            // percentage: calculatePercentage(addBusinessNum, clueNum),
            value: '20'
          },
          {
            name: '转企业客户数',
            realNum: panelData.changeToCompanyNum,
            // percentage: calculatePercentage(panelData.changeToCompanyNum, panelData.clueNum),
            value: '10'
          }
        ]
      }

      // {
      //   top: '2%',
      //   type: 'pictorialBar',
      //   name: 'xiaojiantou',
      //   symbolSize: ['12', '12'],

      //   symbolPosition: 'center',
      //   symbol: 'image://' + arrowImage, // 箭头图片
      //   animation: true,
      //   symbolClip: true,
      //   z: 30,
      //   data: [
      //     {
      //       value: 60,
      //       percentage: calculatePercentage(changeToCusNum, clueNum),
      //       label: {
      //         show: true,
      //         backgroundColor: 'transparent',
      //         borderRadius: 4,
      //         color: '#333',
      //         verticalAlign: 'middle',
      //         offset: [-45, 57, 0, 0],
      //         fontSize: 14,
      //         // padding: [3, 10, 5, 10],
      //         formatter: d => {
      //           return d.data.percentage
      //         }
      //       },
      //       symbolOffset: ['-650%', '460%']
      //     },
      //     {
      //       value: 40,
      //       percentage: calculatePercentage(addBusinessNum, changeToCusNum),
      //       label: {
      //         show: true,
      //         backgroundColor: 'transparent',
      //         borderRadius: 4,
      //         color: '#333',
      //         verticalAlign: 'middle',
      //         offset: [-135, 40, 0, 0],
      //         fontSize: 14,
      //         // padding: [3, 10, 5, 10],
      //         formatter: d => {
      //           return d.data.percentage
      //         }
      //       },
      //       symbolOffset: ['-1410%', '340%']
      //     },
      //     {
      //       value: 40,
      //       percentage: calculatePercentage(changeToCompanyNum, addBusinessNum),
      //       label: {
      //         show: true,
      //         backgroundColor: 'transparent',
      //         borderRadius: 4,
      //         color: '#333',
      //         verticalAlign: 'middle',
      //         offset: [-135, 22, 0, 0],
      //         fontSize: 14,
      //         // padding: [3, 10, 5, 10],
      //         formatter: d => {
      //           return d.data.percentage
      //         }
      //       },
      //       symbolOffset: ['-1410%', '170%']
      //     }
      //   ]
      // }
      // {
      //   top: '0%',
      //   name: 'youcejiantou',
      //   type: 'pictorialBar',
      //   symbolPosition: 'center',

      //   symbolClip: true,
      //   xAxisIndex: '1',
      //   z: 1,
      //   data: [
      //     {
      //       value: 300,
      //       symbolSize: ['130', '103'],
      //       symbolOffset: ['0%', '77%'],
      //       itemValue: '50%',
      //       symbol: 'image://' + lineBg1, // 连线图片
      //       label: {
      //         normal: {
      //           show: true,
      //           borderRadius: 4,
      //           color: '#333',
      //           verticalAlign: 'middle',
      //           offset: [-15, 60, 0, 0],
      //           fontSize: 14,
      //           padding: [3, 10, 5, 10],
      //           formatter: function (d) {
      //             return (
      //               `流失线索(${clueNum - changeToCusNum})条 ` + '\n' + calculatePercentage(changeToCusNum - clueNum, clueNum)
      //             )
      //           }
      //         }
      //       }
      //     },
      //     {
      //       value: 350,
      //       itemValue: '40%',
      //       symbolSize: ['83', '55'],
      //       symbolOffset: ['-110%', '-2%'],
      //       itemValue: '50%',
      //       symbol: 'image://' + lineBg0, // 连线图片
      //       label: {
      //         normal: {
      //           show: true,
      //           borderRadius: 4,
      //           color: '#333',
      //           verticalAlign: 'middle',
      //           offset: [-60, 50, 0, 0],
      //           fontSize: 14,
      //           padding: [3, 10, 5, 10],
      //           formatter: function (d) {
      //             return (
      //               `流失线索(${clueNum - addBusinessNum})条 ` + '\n' + calculatePercentage(clueNum - addBusinessNum, clueNum)
      //             )
      //           }
      //         }
      //       }
      //     },
      //     {
      //       value: 350,
      //       itemValue: '40%',
      //       symbolSize: ['160', '140'],
      //       symbolOffset: ['-34%', '-10.9%'],
      //       itemValue: '50%',
      //       symbol: 'image://' + lineBg2, // 连线图片
      //       label: {
      //         normal: {
      //           show: true,
      //           borderRadius: 4,
      //           color: '#333',
      //           verticalAlign: 'middle',
      //           offset: [-30, 35, 0, 0],
      //           fontSize: 14,
      //           padding: [3, 10, 5, 10],
      //           formatter: function (d) {
      //             return (
      //               `流失线索(${clueNum - changeToCompanyNum})条 ` +
      //               '\n' +
      //               calculatePercentage(clueNum - changeToCompanyNum, clueNum)
      //             )
      //           }
      //         }
      //       }
      //     }
      //   ]
      // }
    ]
  }
  myChart.setOption(option)
}
onMounted(async () => {
  await wrapRef.value.requestResult
  // drawFunnel()
})
</script>
<style lang="scss" scoped>
.top-right {
  flex: 1;
}

.funnel-chart {
  flex: 1;
}
</style>
