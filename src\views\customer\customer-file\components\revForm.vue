<!--
 * @Description: 还原客户弹窗1
 * @Author: thb
 * @Date: 2023-05-30 09:56:01
 * @LastEditTime: 2023-11-27 14:08:32
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center title="还原企业" width="400" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-form-item label="企业状态" prop="customerStatus">
        <el-select v-model="formData.customerStatus" placeholder="请选择" clearable>
          <el-option v-for="(option, index) in customer_status" :key="index" :label="option.label" :value="option.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="企业性质" prop="customerProperty">
        <el-select v-model="formData.customerProperty" placeholder="请选择" clearable>
          <el-option v-for="(option, index) in customer_property" :key="index" :label="option.label" :value="option.value" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="primary" @click="handleSubmit(formRef)">保存</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { abandonCustomer } from '@/api/customer/file'
import { customerStatus, customerProperty } from '@/utils/constants'

const { proxy } = getCurrentInstance()
const { customer_status } = proxy.useDict('customer_status')
const { customer_property } = proxy.useDict('customer_property')
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  detail: Object
})

const formData = ref({})

const rules = {
  customerStatus: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  customerProperty: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ]
}

const formRef = ref()

const handleSubmit = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      const { data } = await abandonCustomer({
        ...formData.value,
        discard: 0,
        ciId: props.detail.customerId
      })
      if (data > 0) {
        proxy.$modal.msgSuccess(`还原成功!`)
        handleClose()
        emits('on-success')
      } else {
        proxy.$modal.msgError(`还原失败!`)
      }
    } else {
    }
  })
}
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
