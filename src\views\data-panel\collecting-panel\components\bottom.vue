<template>
  <div class="bottom">
    <dateWrap
      title="月营业额明细"
      ref="wrapRef"
      :selectOptions="selectOptions"
      :request-api="getMonthMoneyDetail"
      @on-select="drawChart"
      :charts="charts"
    >
      <template #default="{ data }">
        <div class="left" ref="leftPie"></div>
        <div class="mid">
          <div class="left-item">
            已收总额：
            <div>
              <span class="number">{{ numberWithCommas(Number(data.total).toFixed(2)) }}</span> <span class="unit">元</span>
            </div>
          </div>
          <div class="left-item">
            已执行额：
            <div>
              <span class="number">{{ numberWithCommas(Number(data.executed).toFixed(2)) }}</span> <span class="unit">元</span>
            </div>
          </div>
          <div class="left-item">
            预收款额：
            <div>
              <span class="number">{{ numberWithCommas(Number(data.total - data.executed).toFixed(2)) }}</span>
              <span class="unit">元</span>
            </div>
          </div>
        </div>
        <!-- <div class="right" ref="lineChart"></div> -->
        <div class="right" ref="barChart"></div>
      </template>
    </dateWrap>
  </div>
</template>
<script setup>
import { getMonthMoneyDetail } from '@/api/panel-data/collecting'
import dateWrap from './data-wrap'
import * as echarts from 'echarts'
import { getRandomColorHex, numberWithCommas } from '@/utils/index'
const selectOptions = [
  {
    prop: 'year',
    type: 'date-picker',
    defaultValue: new Date().getFullYear() + '',
    props: { type: 'year', value: 'YYYY', valueFormat: 'YYYY' }
  }
]

const leftPie = ref()
const wrapRef = ref()
const charts = ref([])

const getLeftPie = () => {
  // 销毁图表组件
  console.log('charts.value[0]', charts.value[0])
  let myChart = echarts.init(leftPie.value)
  const panelData = wrapRef.value.panelData
  charts.value[0] = myChart
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      // orient: 'vertical',
      bottom: '1%',
      icon: 'circle',
      type: 'scroll'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        center: ['50%', '40%'],
        data: (panelData.source || []).filter(item => item.value),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },

        itemStyle: {
          normal: {
            label: {
              show: true,
              formatter: '{d}%'
            },
            labelLine: {
              show: true
            }
          }
        }
        // labelLine: {
        //   length: 2
        // }
      }
    ]
  }
  myChart.setOption(option)
}

const lineChart = ref()
const getLineChart = () => {
  let myChart = echarts.init(lineChart.value)
  const panelData = wrapRef.value.panelData
  charts.value[1] = myChart
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['月营业额']
    },
    grid: {
      left: '0%',
      top: '12%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },

    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '月营业额',
        type: 'line',
        data: (panelData.monthly || []).map(item => item.value)
        // data: [20, 42, 60, 20, 20, 50, 90]
      }
    ]
  }
  myChart.setOption(option)
}

// lineChart---barChart
const barChart = ref()
const getBarChart = () => {
  let myChart = echarts.init(barChart.value)
  const panelData = wrapRef.value.panelData
  charts.value[1] = myChart
  const option = {
    color: ['#5B8FF9FF', '#5AD8A6FF'],
    tooltip: {},
    grid: {
      left: '10%',
      top: '12%',
      right: '4%',
      bottom: '10%'
    },
    legend: {
      data: ['记账营业额', '地址营业额']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '记账营业额',
        type: 'bar',
        stack: 'total',
        data: (panelData.monthly || []).map(item => item.value?.bookkeepingMonthTotal)
      },
      {
        name: '地址营业额',
        type: 'bar',
        stack: 'total',
        data: (panelData.monthly || []).map(item => item.value?.addressMonthTotal)
      },
      //series中push合计的数据
      {
        name: '总计',
        type: 'bar',
        stack: 'X',
        label: {
          normal: {
            show: true,
            position: 'top',
            color: '#000',
            formatter: params => {
              return numberWithCommas(params.value)
            }
          }
        },
        z: 0,
        barGap: '-100%',
        itemStyle: {
          color: 'rgba(0,0,0,0)'
        },
        //不同系列的柱间距离，为百分比,如果想要两个系列的柱子重叠，可以设置 barGap 为 '-100%'。
        data: (panelData.monthly || []).map(item =>
          (item.value?.bookkeepingMonthTotal + item.value?.addressMonthTotal).toFixed(2)
        )
      }
    ]
  }
  myChart.on('legendselectchanged', obj => {
    if (obj.selected['记账营业额'] && !obj.selected['地址营业额']) {
      option.series[2].data = (panelData.monthly || []).map(item => item.value?.bookkeepingMonthTotal)
    }
    if (obj.selected['地址营业额'] && !obj.selected['记账营业额']) {
      option.series[2].data = (panelData.monthly || []).map(item => item.value?.addressMonthTotal)
    }
    if (obj.selected['地址营业额'] && obj.selected['记账营业额']) {
      option.series[2].data = (panelData.monthly || []).map(item =>
        (item.value?.bookkeepingMonthTotal + item.value?.addressMonthTotal).toFixed(2)
      )
    }
    myChart.setOption(option)
  })
  myChart.setOption(option)
}
const drawChart = () => {
  getLeftPie()
  // getLineChart()
  getBarChart()
}
onMounted(async () => {
  await wrapRef.value.requestResult
  // getLeftPie()
  // getLineChart()
  drawChart()
})
</script>
<style lang="scss" scoped>
.bottom {
  flex: 1;
  min-height: 290px;
  .left {
    flex: 1.5;
    padding-right: 16px;
    margin-right: 10px;
    border-right: 1px solid #e8e8e8ff;
    // min-width: 450px;
  }
  .mid {
    flex: 1;
  }
  .right {
    flex: 2;
  }
}

.mid {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 10px;
  .left-item {
    box-sizing: border-box;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 16px;
    padding-right: 16px;
    width: 100%;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    font-size: 16px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    color: #333333;
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }
    .unit {
      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #7d8592;
    }
  }
}
.number {
  font-size: 24px;
  font-family: AlibabaPuHuiTi_2_85_Bold;
  color: #45a0ff;
}
</style>
