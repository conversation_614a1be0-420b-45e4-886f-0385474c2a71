<template>
  <el-form ref="formRef" :hide-required-asterisk="disabled" :model="data" :rules="rules" label-width="120px" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-checkbox :disabled="disabled" v-model="data.copyOfLicenseFileListFlag" />
        <el-form-item label="执照副本" prop="copyOfLicenseFileList">
          <FileUploadBiz
            v-model="data.copyOfLicenseFileList"
            :disabled="!data.copyOfLicenseFileListFlag"
            :limit="10"
            :isShowTip="false"
            v-if="!disabled"
            @on-load-success="validateFormField('copyOfLicenseFileList')"
          />
          <template v-else>
            <!-- <template v-for="(file, index) in data.copyOfLicenseFileList" :key="index">
              <div class="download-text" @click="previewFile(file)">{{ file?.fileNames || '暂无文件' }}</div>
            </template> -->
            <fileList :list="data.copyOfLicenseFileList" />
          </template> </el-form-item
      ></el-col>
      <el-col :span="12">
        <el-checkbox :disabled="disabled" v-model="data.officialSealFileListFlag" />
        <el-form-item label="公章" prop="officialSealFileList">
          <FileUploadBiz
            v-model="data.officialSealFileList"
            :disabled="!data.officialSealFileListFlag"
            :limit="10"
            :isShowTip="false"
            v-if="!disabled"
            @on-load-success="validateFormField('officialSealFileList')"
          />

          <template v-else>
            <!-- <template v-for="(file, index) in data.officialSealFileList" :key="index">
              <div class="download-text" @click="previewFile(file)">{{ file?.fileNames || '暂无文件' }}</div>
            </template> -->
            <fileList :list="data.officialSealFileList" />
          </template> </el-form-item
      ></el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-checkbox :disabled="disabled" v-model="data.legalIdentityFileListFlag" />
        <el-form-item label="法人身份证" prop="legalIdentityFileList">
          <FileUploadBiz
            v-model="data.legalIdentityFileList"
            :disabled="!data.legalIdentityFileListFlag"
            :limit="10"
            :isShowTip="false"
            v-if="!disabled"
            @on-load-success="validateFormField('legalIdentityFileList')"
          />
          <template v-else>
            <!-- <template v-for="(file, index) in data.legalIdentityFileList" :key="index">
              <div class="download-text" @click="previewFile(file)">{{ file?.fileNames || '暂无文件' }}</div>
            </template> -->
            <fileList :list="data.legalIdentityFileList" />
          </template> </el-form-item
      ></el-col>
      <el-col :span="12">
        <el-checkbox :disabled="disabled" v-model="data.legalPhoneFlag" />
        <el-form-item label="法人手机号" prop="legalPhone">
          <el-input v-model="data.legalPhone" :disabled="disabled || !data.legalPhoneFlag" length="20"></el-input> </el-form-item
      ></el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-checkbox :disabled="disabled" v-model="data.operatorIdentityFileListFlag" />
        <el-form-item label="操作员身份证" prop="operatorIdentityFileList">
          <FileUploadBiz
            v-model="data.operatorIdentityFileList"
            :disabled="!data.operatorIdentityFileListFlag"
            :limit="10"
            :isShowTip="false"
            @on-load-success="validateFormField('operatorIdentityFileList')"
            v-if="!disabled" />
          <template v-else>
            <!-- <template v-for="(file, index) in data.operatorIdentityFileList" :key="index">
              <div class="download-text" @click="previewFile(file)">{{ file?.fileNames || '暂无文件' }}</div>
            </template> -->
            <fileList :list="data.operatorIdentityFileList" /> </template></el-form-item
      ></el-col>
      <el-col :span="12">
        <el-checkbox :disabled="disabled" v-model="data.operatorPhoneFlag" />
        <el-form-item label="操作员手机号" prop="operatorPhone">
          <el-input
            v-model="data.operatorPhone"
            :disabled="disabled || !data.operatorPhoneFlag"
            length="20"
          ></el-input> </el-form-item
      ></el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-checkbox :disabled="disabled" v-model="data.emailFlag" />
        <el-form-item label="邮箱号码" prop="email">
          <el-input v-model="data.email" :disabled="disabled || !data.emailFlag" length="20"></el-input> </el-form-item
      ></el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import FileUploadBiz from '@/components/FileUploadBiz'
import { FormValidators } from '@/utils/validate'
import fileList from './file-list'
defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        copyOfLicenseFileList: [],
        officialSealFileList: []
      }
    }
  }
})

// 禁用状态disabled
const disabled = inject('disabled')
// 进出口表单检验规则
const rules = {
  copyOfLicenseFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  officialSealFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  legalIdentityFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  legalPhone: [
    {
      required: true,
      // message: '请输入',
      // validator: FormValidators.mobilePhone,
      validator: (rules, value, callback) => {
        if (!value) {
          callback(new Error('请输入'))
        } else {
          FormValidators.mobilePhone(rules, value, callback)
        }
      },
      trigger: ['blur']
    }
  ],
  operatorIdentityFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  operatorPhone: [
    {
      required: true,
      // message: '请输入',
      // validator: FormValidators.mobilePhone,
      validator: (rules, value, callback) => {
        if (!value) {
          callback(new Error('请输入'))
        } else {
          FormValidators.mobilePhone(rules, value, callback)
        }
      },
      trigger: ['blur']
    }
  ],
  email: [
    {
      required: true,
      // message: '请输入',
      // validator: FormValidators.email,
      validator: (rules, value, callback) => {
        if (!value) {
          callback(new Error('请输入'))
        } else {
          FormValidators.email(rules, value, callback)
        }
      },
      trigger: ['blur']
    }
  ]
}

// 上传文件后检验
const validateFormField = field => {
  formRef.value.validateField(field)
}

// 预览文件
const emits = defineEmits('on-preview')
const previewFile = file => {
  emits('on-preview', file)
}
const formRef = ref()
defineExpose({
  formRef,
  rules
})
</script>
<style lang="scss" scoped></style>
