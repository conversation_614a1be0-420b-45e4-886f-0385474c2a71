<template>
  <el-dialog
    v-model="visible"
    :title="mode"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    top="5vh"
    destroy-on-close
  >
    <div class="my-container">
      <div class="left" v-if="['选择地址'].includes(mode) && lePropertyList.length">
        <div class="tit">{{ areaName }}</div>
        <div class="con">
          <div
            v-for="(item, index) in lePropertyList"
            :key="index"
            :class="propertyId === item.id ? 'item item-current' : 'item'"
            @click="handleClickItem(item, index)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="right">
        <el-form
          v-if="!['选择地址'].includes(mode) || lePropertyList.length"
          ref="formRef"
          :model="formData"
          label-position="top"
          :rules="rules"
          :disabled="['详情', '房本详情', '选择地址'].includes(mode)"
          :hide-required-asterisk="['详情'].includes(mode)"
        >
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="房本名称（供应商名称）" prop="name">
                <el-input
                  v-model="formData.name"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请输入'"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="房本分类" prop="leaseCategory">
                <el-select
                  allow-create
                  filterable
                  style="width: 100%"
                  v-model="formData.leaseCategory"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请选择'"
                  clearable
                  :disabled="['详情'].includes(mode)"
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="(item, index) in address_fangben_fenlei"
                    :key="index"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="租赁分类" prop="propertyCategory">
                <el-select
                  allow-create
                  filterable
                  style="width: 100%"
                  v-model="formData.propertyCategory"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请选择'"
                  clearable
                  :disabled="['详情'].includes(mode)"
                >
                  <el-option :label="item.label" :value="item.value" v-for="(item, index) in address_zulin_fenlei" :key="index" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="地区" prop="areaId">
                <el-cascader
                  style="width: 100%"
                  v-model="formData.areaId"
                  filterable
                  clearable
                  :props="{ label: 'name', value: 'id', children: 'child' }"
                  :options="addressOptions"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="租赁期起" prop="leaseStartDate">
                <el-date-picker
                  style="width: 100%"
                  v-model="formData.leaseStartDate"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  type="date"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请选择'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="租赁期止" prop="leaseEndDate">
                <el-date-picker
                  style="width: 100%"
                  v-model="formData.leaseEndDate"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  type="date"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请选择'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="租赁价格（地址成本）" prop="leasePrice">
                <el-input
                  v-model="formData.leasePrice"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请输入'"
                >
                  <template #suffix> 元 </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="面积" prop="regin">
                <el-input v-model="formData.regin" :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请输入'">
                  <template #suffix> ㎡ </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="产权人" prop="ownerName">
                <el-input
                  v-model="formData.ownerName"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请输入'"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="产权人联系电话" prop="ownerPhone">
                <el-input
                  v-model="formData.ownerPhone"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请输入'"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="出租人" prop="lessorName">
                <el-input
                  @click="handleListSelectShow"
                  v-model="formData.lessorName"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请输入'"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="出租人联系电话" prop="phone">
                <el-input
                  disabled
                  v-model="formData.phone"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请输入'"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="特许行业" prop="licensedIndustry">
                <el-select
                  allow-create
                  filterable
                  style="width: 100%"
                  v-model="formData.licensedIndustry"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请选择'"
                  clearable
                  :disabled="['详情'].includes(mode)"
                >
                  <el-option :label="item.label" :value="item.value" v-for="(item, index) in address_texu_hangye" :key="index" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="禁止行业" prop="prohibitedIndustry">
                <el-select
                  allow-create
                  filterable
                  style="width: 100%"
                  v-model="formData.prohibitedIndustry"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请选择'"
                  clearable
                  :disabled="['详情'].includes(mode)"
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="(item, index) in address_jinzhi_hangye"
                    :key="index"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="其他" prop="other">
                <el-select
                  allow-create
                  filterable
                  style="width: 100%"
                  v-model="formData.other"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请选择'"
                  clearable
                  :disabled="['详情'].includes(mode)"
                >
                  <el-option :label="item.label" :value="item.value" v-for="(item, index) in address_qita" :key="index" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="托管公司分类" prop="xxmanagementCompanyCategoryx2">
                <el-select
                  allow-create
                  filterable
                  style="width: 100%"
                  v-model="formData.managementCompanyCategory"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请选择'"
                  clearable
                  :disabled="['详情'].includes(mode)"
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="(item, index) in address_tuoguangongsi_fenlei"
                    :key="index"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  v-model="formData.remark"
                  :placeholder="['详情', '房本详情', '选择地址'].includes(mode) ? '' : '请输入'"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="!['选择地址'].includes(mode)">
              <el-form-item label="图片附件" prop="imageFileList">
                <ImageUpload
                  v-if="!['详情', '房本详情', '选择地址'].includes(mode) || formData?.imageFileList?.length"
                  :disabled="['详情', '房本详情', '选择地址'].includes(mode)"
                  :limit="['详情', '房本详情', '选择地址'].includes(mode) ? formData?.imageFileList?.length || 1 : 100"
                  v-model="formData.imageFileList"
                  :isShowTip="false"
                />
                <span v-else class="download-text"> 暂无图片 </span>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="!['选择地址'].includes(mode)">
              <el-form-item label="文件附件" prop="fileList">
                <FileUpload
                  v-if="!['详情', '房本详情', '选择地址'].includes(mode)"
                  v-model="formData.fileList"
                  :isShowTip="false"
                  :limit="100"
                />
                <FileList v-else :list="formData.fileList" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-empty v-else description="该区域暂无可选地址" />
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
      <el-button
        v-if="!['详情', '房本详情', '选择地址'].includes(mode) || lePropertyList.length"
        type="primary"
        @click="handleSubmit"
        :loading="loading"
      >
        保存
      </el-button>
    </template>
  </el-dialog>
  <tableModal
    :init-param="{}"
    v-if="listSelectShow"
    rowKey="id"
    title="出租人"
    :columns="columns"
    :request-api="getAddressLessorList"
    @on-close="listSelectShow = false"
    @on-select="handleSelect"
  />
</template>

<script setup>
import {
  getAddressPropertyOwnershipGetById,
  postAddressPropertyOwnershipSave,
  postAddressPropertyOwnershipUpdate,
  getAddressLessorList,
  getAddressLessorGetById,
  getAddressPropertyOwnershipGetIdlePropertyList,
  postAddressApplyAddressApply
} from '@/api/address-report/index.js'
import { getBankTreeList } from '@/api/basicData/basicData'
import FileUpload from '@/components/FileUpload'
import FileList from '@/components/FileList'
import ImageUpload from '@/components/ImageUpload'
import { getAreaTreeList } from '@/api/basicData/basicData'
import tableModal from '@/components/tableModal'
import { useSetDic } from '@/hooks/useSetDic'
const { setDic } = useSetDic()

const { proxy } = getCurrentInstance()
const emit = defineEmits()

const { address_fangben_fenlei } = proxy.useDict('address_fangben_fenlei')
const { address_zulin_fenlei } = proxy.useDict('address_zulin_fenlei')
const { address_texu_hangye } = proxy.useDict('address_texu_hangye')
const { address_jinzhi_hangye } = proxy.useDict('address_jinzhi_hangye')
const { address_qita } = proxy.useDict('address_qita')
const { address_tuoguangongsi_fenlei } = proxy.useDict('address_tuoguangongsi_fenlei')

const addressOptions = ref([])
const getAddressCascader = async () => {
  const { data } = await getAreaTreeList({
    enable: '1'
  })
  addressOptions.value = data || []
}
getAddressCascader()

const mode = ref('')
const formRef = ref()
const formData = reactive({})
const visible = ref(true)

const rules = {
  name: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  leaseStartDate: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  leaseEndDate: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  leasePrice: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  ownerPhone: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ]
}

async function getDetail(row) {
  await getAddressPropertyOwnershipGetById({ id: row.id }).then(res => {
    Object.assign(formData, res.data)
  })
  if (formData.lessorId) {
    await getAddressLessorGetById({ id: formData.lessorId }).then(res => {
      formData.lessorName = res.data.lessorName
      formData.phone = res.data.phone
    })
  }
}

const onAdd = () => {
  mode.value = '新增'
  formData.leasePrice = 0
}
const onDetail = row => {
  getDetail(row)
  mode.value = '详情'
}
const onDetail1 = row => {
  getDetail(row)
  mode.value = '房本详情'
}
const onEdit = row => {
  getDetail(row)
  mode.value = '编辑'
}

/** 选择地址 */
const lePropertyList = ref([])
const propertyId = ref('')
const addressApplyId = ref('')
const areaName = ref('')
const onChoose = row => {
  // row.areaId = 32
  // row.leaseEndDate = '2024-12-07'
  console.log('row', row)
  areaName.value = row.areaName
  addressApplyId.value = row.id
  getAddressPropertyOwnershipGetIdlePropertyList({
    applyId: row.id,
    leaseEndDate: row.leaseEndDate
  }).then(res => {
    lePropertyList.value = res.data
    if (res.data[0]) {
      propertyId.value = res.data[0].id
      getDetail({ id: res.data[0].id })
    }
  })
  mode.value = '选择地址'
}
function handleClickItem(item) {
  propertyId.value = item.id
  getDetail({ id: item.id })
}

const handleClose = () => {
  emit('close')
}

const loading = ref(false)
const handleSubmit = async () => {
  console.log('formData', formData)
  if (loading.value) return
  await formRef.value.validate()
  loading.value = true
  if (Array.isArray(formData.areaId) && formData.areaId.length > 0) {
    formData.areaId = formData.areaId[formData.areaId.length - 1]
  }
  if (['选择地址'].includes(mode.value)) {
    postAddressApplyAddressApply({
      id: addressApplyId.value,
      propertyId: propertyId.value
    })
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
      .finally(() => {
        loading.value = false
      })
  } else if (formData.id) {
    postAddressPropertyOwnershipUpdate(formData)
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
      .finally(() => {
        loading.value = false
      })
  } else {
    postAddressPropertyOwnershipSave(formData)
      .then(res => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('保存成功')
          emit('ok')
          handleClose()
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
  if (address_fangben_fenlei.value.findIndex(item => item.value === formData.leaseCategory) === -1) {
    // setDic会影响finance_review_category当前值
    setDic('address_fangben_fenlei', formData.leaseCategory, formData.leaseCategory, address_fangben_fenlei)
  }
  if (address_zulin_fenlei.value.findIndex(item => item.value === formData.propertyCategory) === -1) {
    setDic('address_zulin_fenlei', formData.propertyCategory, formData.propertyCategory, address_zulin_fenlei)
  }
  if (address_texu_hangye.value.findIndex(item => item.value === formData.licensedIndustry) === -1) {
    setDic('address_texu_hangye', formData.licensedIndustry, formData.licensedIndustry, address_texu_hangye)
  }
  if (address_jinzhi_hangye.value.findIndex(item => item.value === formData.prohibitedIndustry) === -1) {
    setDic('address_jinzhi_hangye', formData.prohibitedIndustry, formData.prohibitedIndustry, address_jinzhi_hangye)
  }
  if (address_qita.value.findIndex(item => item.value === formData.other) === -1) {
    setDic('address_qita', formData.other, formData.other, address_qita)
  }
  if (address_tuoguangongsi_fenlei.value.findIndex(item => item.value === formData.managementCompanyCategory) === -1) {
    setDic(
      'address_tuoguangongsi_fenlei',
      formData.managementCompanyCategory,
      formData.managementCompanyCategory,
      address_tuoguangongsi_fenlei
    )
  }
}

const bank_list = ref([])
function onGetBasicData() {
  getBankTreeList({ enable: 1 }).then(res => {
    bank_list.value = res.data // 已验证，新增下拉有对应数据
  })
}
onGetBasicData()

/** 出租人列表弹窗显示 */
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  listSelectShow.value = true
}
const handleSelect = data => {
  formData.lessorName = data.lessorName
  formData.phone = data.phone
  formData.lessorId = data.id
}
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'lessorName',
    label: '出租人名称',
    search: { el: 'input' },
    minWidth: 300
  },
  {
    prop: 'phone',
    label: '出租人手机号',
    minWidth: 150
  },
  {
    prop: 'bankName',
    label: '银行',
    minWidth: 150
  },
  {
    prop: 'bankAccount',
    label: '银行账户',
    minWidth: 150
  },
  {
    prop: 'propertyCount',
    label: '房本数量',
    minWidth: 150
  }
]

defineExpose({
  onAdd,
  onEdit,
  onChoose,
  onDetail,
  onDetail1
})
</script>

<style lang="scss" scoped>
.con {
  margin-top: 15px;

  .item {
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
    padding: 8px;
    cursor: pointer;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .item-current {
    background-color: #f5f7fa;
  }
}

.my-tree {
  margin-top: 10px;

  :deep(.el-icon) {
    display: none;
  }
}

:deep(.vertical-radio-group) {
  display: block;
  margin-bottom: 10px;
}

.my-container {
  display: flex;

  .left {
    width: 200px;
    flex: 0 0 auto;
    border-right: 1px solid #eee;
    padding-right: 20px;
    margin-right: 20px;
  }

  .right {
    width: 100%;
  }
}

.tips {
  color: #aaa;
}
</style>
