/*
 * @Description:
 * @Author: thb
 * @Date: 2023-08-07 16:24:53
 * @LastEditTime: 2023-09-19 14:17:22
 * @LastEditors: thb
 */
import { reactive, toRefs, ref, provide } from 'vue'
import contractDetail from '@/views/contract-manage/contract-list/components/contract-detail.vue'
import changeContract from '@/views/contract-manage/contract-list/components/change-contract.vue'
import templateCreate from '@/views/contract-manage/contract-list/components/template-create.vue'
import { checkContractDetailIsPermission } from '@/api/contract/contract'
export const useContract = () => {
  const rowData = ref()
  const state = reactive({
    // rowData: null,
    detailShow: false,
    contractId: '',
    isChange: false,
    changeShow: false,
    templateShow: false
  })
  provide('customerData', rowData)

  const handleChange = (id: string | number, type: string) => {
    if (type && type === '1') {
      state.contractId = id as string
      state.templateShow = true
    } else {
      state.contractId = id as string
      state.changeShow = true
    }
  }
  const handleShowContractDetail = async (row: any) => {
    try {
      const result: any = await checkContractDetailIsPermission(row.contractId)
      // 是否删除 如果没有删除 才进去 删除的code为500
      if (result.code === 200) {
        rowData.value = {
          ...row
        }
        state.detailShow = true
        state.contractId = row.contractId
        state.isChange = row.bizType === '1'
      }
    } catch (error) {
      //
    }
  }
  return {
    ...toRefs(state),
    rowData,
    handleShowContractDetail,
    contractDetail,
    changeContract,
    templateCreate,
    handleChange
  }
}
