<div class="vue-office-docx-main">
  <!--docxjs library predefined styles--><style>
    .docx-wrapper {
      background: gray;
      padding: 30px;
      padding-bottom: 0px;
      display: flex;
      flex-flow: column;
      align-items: center;
    }
    .docx-wrapper > section.docx {
      background: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
      margin-bottom: 30px;
    }
    .docx {
      color: black;
    }
    section.docx {
      box-sizing: border-box;
      display: flex;
      flex-flow: column nowrap;
      position: relative;
      overflow: hidden;
    }
    section.docx > article {
      margin-bottom: auto;
    }
    .docx table {
      border-collapse: collapse;
    }
    .docx table td,
    .docx table th {
      vertical-align: top;
    }
    .docx p {
      margin: 0pt;
      min-height: 1em;
    }
    .docx span {
      white-space: pre-wrap;
      overflow-wrap: break-word;
    }
    .docx a {
      color: inherit;
      text-decoration: inherit;
    }</style
  ><!--docxjs document theme values--><style>
    .docx {
      --docx-majorHAnsi-font: Cambria;
      --docx-minorHAnsi-font: Calibri;
      --docx-dk1-color: #000000;
      --docx-lt1-color: #ffffff;
      --docx-dk2-color: #1f497d;
      --docx-lt2-color: #eeece1;
      --docx-accent1-color: #4f81bd;
      --docx-accent2-color: #c0504d;
      --docx-accent3-color: #9bbb59;
      --docx-accent4-color: #8064a2;
      --docx-accent5-color: #4bacc6;
      --docx-accent6-color: #f79646;
      --docx-hlink-color: #0000ff;
      --docx-folHlink-color: #800080;
    }</style
  ><!--docxjs document styles--><style>
    .docx span {
      font-family: Times New Roman;
    }
    .docx p,
    p.docx_1 {
      text-align: justify;
    }
    .docx p,
    p.docx_1 span {
      min-height: 10.5pt;
      font-size: 10.5pt;
    }
    .docx table,
    table.docx_6 td {
      padding-top: 0pt;
      padding-left: 5.4pt;
      padding-bottom: 0pt;
      padding-right: 5.4pt;
    }
    p.docx_2 {
      background-color: #000080;
      text-align: justify;
    }
    p.docx_2 span {
      min-height: 10.5pt;
      font-size: 10.5pt;
    }
    p.docx_3 span {
      min-height: 9pt;
      font-size: 9pt;
    }
    p.docx_3 {
      text-align: justify;
    }
    p.docx_4 {
      text-align: left;
    }
    p.docx_4 span {
      min-height: 9pt;
      font-size: 9pt;
    }
    p.docx_4 span {
      min-height: 9pt;
      font-size: 9pt;
    }
    p.docx_5 {
      border-bottom: 0.75pt solid black;
      text-align: center;
    }
    p.docx_5 span {
      min-height: 9pt;
      font-size: 9pt;
    }
    p.docx_5 span {
      min-height: 9pt;
      font-size: 9pt;
    }
    table.docx_7 p {
      text-align: justify;
    }
    table.docx_7 td {
      border-top: 0.5pt solid black;
      border-left: 0.5pt solid black;
      border-bottom: 0.5pt solid black;
      border-right: 0.5pt solid black;
      padding-top: 0pt;
      padding-left: 5.4pt;
      padding-bottom: 0pt;
      padding-right: 5.4pt;
    }
    span.docx_9 {
      min-height: 9pt;
      font-size: 9pt;
    }
    span.docx_9 p {
      text-align: left;
    }
    span.docx_9 {
      min-height: 9pt;
      font-size: 9pt;
    }
    span.docx_10 {
      min-height: 9pt;
      font-size: 9pt;
    }
    span.docx_10 p {
      border-bottom: 0.75pt solid black;
      text-align: center;
    }
    span.docx_10 {
      min-height: 9pt;
      font-size: 9pt;
    }
  </style>
  <div class="docx-wrapper">
    <section
      class="docx"
      style="padding: 56.7pt 56.7pt 72pt; width: 595.3pt; min-height: 841.9pt; column-count: 1; column-gap: 36pt"
    >
      <header><p class="docx_5" style="border-bottom: 0pt solid black; text-align: justify"></p></header>
      <article>
        <p style="line-height: 1.5; text-indent: 34.45pt; margin-left: -44.95pt">
          <span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 14pt; font-size: 14pt">“</span
          ><span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 14pt; font-size: 14pt">三</span
          ><span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 14pt; font-size: 14pt"
            >实”信息采集表二</span
          >
        </p>
        <p style="text-align: center">
          <span style="font-family: 宋体; font-weight: bold; color: rgb(0, 0, 0); min-height: 22pt; font-size: 22pt"
            >实 有 房 屋</span
          >
        </p>
        <p style="min-height: 23pt; line-height: 23pt; margin-left: -44.95pt; text-align: center">
          <span style="font-family: 宋体; font-weight: bold">临汾市</span
          ><span style="font-family: 宋体; font-weight: bold"> </span
          ><span style="font-family: 宋体; font-weight: bold; text-decoration: underline"> </span
          ><span style="font-family: 宋体; font-weight: bold">县（市、</span
          ><span style="font-family: 宋体; font-weight: bold">区</span><span style="font-family: 宋体; font-weight: bold">）</span
          ><span style="font-family: 宋体; font-weight: bold">_________街办(乡、镇) _______社区居委会(村)______小区（组）</span>
        </p>
        <table class="docx_6" style="width: 499.75pt; table-layout: auto; margin-left: auto; margin-right: auto">
          <colgroup>
            <col style="width: 66.25pt" />
            <col style="width: 58.15pt" />
            <col style="width: 25.3pt" />
            <col style="width: 19.6pt" />
            <col style="width: 19.65pt" />
            <col style="width: 3.9pt" />
            <col style="width: 15.7pt" />
            <col style="width: 6.45pt" />
            <col style="width: 13.2pt" />
            <col style="width: 14.05pt" />
            <col style="width: 2.2pt" />
            <col style="width: 21.3pt" />
            <col style="width: 21.3pt" />
            <col style="width: 21.75pt" />
            <col style="width: 8.9pt" />
            <col style="width: 9.35pt" />
            <col style="width: 2pt" />
            <col style="width: 10.65pt" />
            <col style="width: 7.6pt" />
            <col style="width: 1.4pt" />
            <col style="width: 10.55pt" />
            <col style="width: 9.1pt" />
            <col style="width: 2.95pt" />
            <col style="width: 6.2pt" />
            <col style="width: 10.45pt" />
            <col style="width: 10.5pt" />
            <col style="width: 9.15pt" />
            <col style="width: 14.7pt" />
            <col style="width: 4.9pt" />
            <col style="width: 18.35pt" />
            <col style="width: 1.3pt" />
            <col style="width: 19.6pt" />
            <col style="width: 19.65pt" />
            <col style="width: 13.65pt" />
          </colgroup>
          <tr style="height: 25.85pt; text-align: center">
            <td
              rowspan="6"
              style="
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">房屋</span>
              </p>
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">地址详址</span>
              </p>
            </td>
            <td
              rowspan="3"
              colspan="18"
              style="
                width: 281.05pt;
                border-width: 2.25pt 0.5pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">临汾市</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"> </span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">县（市、</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">区</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">）</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">街办（乡、镇）</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">街路巷</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">门牌号</span>
              </p>
              <p></p>
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline">
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">小区</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">楼号</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">单元</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">室 </span>
              </p>
            </td>
            <td
              colspan="15"
              style="
                width: 152.45pt;
                border-width: 2.25pt 2.25pt 0.5pt 0.5pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 40.5pt">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">单元（门）数</span>
              </p>
            </td>
          </tr>
          <tr style="height: 20.65pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="18"
              style="
                display: none;
                width: 281.05pt;
                border-width: 2.25pt 0.5pt 2.25pt 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="9"
              style="
                width: 75pt;
                border-width: 0.5pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">地上</span>
              </p>
            </td>
            <td
              colspan="6"
              style="
                width: 77.45pt;
                border-width: 0.5pt 2.25pt 0.5pt 0.5pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">地下</span>
              </p>
            </td>
          </tr>
          <tr style="height: 10.5pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="18"
              style="
                display: none;
                width: 281.05pt;
                border-bottom: none;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                border-left: 2.25pt solid black;
                border-top: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 24pt;
                border-top: 0.5pt solid black;
                border-left: 0.5pt solid black;
                border-bottom: none;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="5"
              style="
                width: 51pt;
                border-top: 0.5pt solid black;
                border-left: 0.5pt solid black;
                border-bottom: none;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="2"
              style="
                width: 23.25pt;
                border-top: 0.5pt solid black;
                border-left: 0.5pt solid black;
                border-bottom: none;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 54.2pt;
                border-top: 0.5pt solid black;
                border-left: 0.5pt solid black;
                border-bottom: none;
                vertical-align: middle;
                border-right: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
          </tr>
          <tr style="height: 14.75pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              rowspan="3"
              colspan="11"
              style="
                width: 199.5pt;
                border-top: none;
                border-bottom: 0.75pt solid black;
                border-right: none;
                vertical-align: middle;
                border-left: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: -81pt; margin-left: 81pt">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">临汾市</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"> </span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">县（市、</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">区</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">）</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">街办（乡、镇）</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"> </span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: none"
                  >村</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: none">
                </span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">组</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">号</span>
              </p>
              <p></p>
            </td>
            <td
              rowspan="2"
              colspan="7"
              style="
                width: 81.55pt;
                border-top: none;
                border-left: none;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                border-bottom: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 24pt;
                border-top: none;
                border-left: 0.5pt solid black;
                border-bottom: 0.5pt solid black;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">层高</span></p>
            </td>
            <td
              colspan="5"
              style="
                width: 51pt;
                border-top: none;
                border-left: 0.5pt solid black;
                border-bottom: 0.5pt solid black;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">单层最多户数</span></p>
            </td>
            <td
              colspan="2"
              style="
                width: 23.25pt;
                border-top: none;
                border-left: 0.5pt solid black;
                border-bottom: 0.5pt solid black;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">层高</span></p>
            </td>
            <td
              colspan="4"
              style="
                width: 54.2pt;
                border-top: none;
                border-left: 0.5pt solid black;
                border-bottom: 0.5pt solid black;
                vertical-align: middle;
                border-right: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">单层最多户数</span>
              </p>
            </td>
          </tr>
          <tr style="height: 1.5pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="11"
              style="
                display: none;
                width: 199.5pt;
                border-top: none;
                border-bottom: 0.75pt solid black;
                border-right: none;
                vertical-align: middle;
                border-left: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="7"
              style="
                display: none;
                width: 81.55pt;
                border-left: none;
                border-bottom: none;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                border-top: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 24pt;
                border-top: 0.5pt solid black;
                border-left: 0.5pt solid black;
                border-bottom: none;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="5"
              style="
                width: 51pt;
                border-top: 0.5pt solid black;
                border-left: 0.5pt solid black;
                border-bottom: none;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="2"
              style="
                width: 23.25pt;
                border-top: 0.5pt solid black;
                border-left: 0.5pt solid black;
                border-bottom: none;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="4"
              style="
                width: 54.2pt;
                border-top: 0.5pt solid black;
                border-left: 0.5pt solid black;
                border-bottom: none;
                vertical-align: middle;
                border-right: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
          </tr>
          <tr style="height: 6.75pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="11"
              style="
                display: none;
                width: 199.5pt;
                border-top: 0.75pt solid black;
                border-bottom: 2.25pt solid black;
                border-right: none;
                vertical-align: middle;
                border-left: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="7"
              style="
                width: 81.55pt;
                border-top: none;
                border-left: none;
                border-bottom: 2.25pt solid black;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: left"></p>
            </td>
            <td
              colspan="4"
              style="
                width: 24pt;
                border-top: none;
                border-left: 0.5pt solid black;
                border-bottom: 2.25pt solid black;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: left"></p>
            </td>
            <td
              colspan="5"
              style="
                width: 51pt;
                border-top: none;
                border-left: 0.5pt solid black;
                border-bottom: 2.25pt solid black;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: left"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 23.25pt;
                border-top: none;
                border-left: 0.5pt solid black;
                border-bottom: 2.25pt solid black;
                border-right: 0.5pt solid black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: left"></p>
              <p style="text-align: left"></p>
            </td>
            <td
              colspan="4"
              style="
                width: 54.2pt;
                border-top: none;
                border-left: 0.5pt solid black;
                border-bottom: 2.25pt solid black;
                vertical-align: middle;
                border-right: 2.25pt solid black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: left"></p>
            </td>
          </tr>
          <tr style="height: 16.2pt; text-align: center">
            <td
              style="
                width: 66.25pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="line-height: 1.5; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">房屋类型</span>
              </p>
            </td>
            <td
              colspan="33"
              style="
                width: 433.5pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: top;
                padding: 0pt 5.4pt;
              "
            >
              <p style="line-height: 1.5">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□平房 □楼房 □地下建筑 □公共设施 □临时建筑 □违章建筑 □其它</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 19.4pt; text-align: center">
            <td
              style="
                width: 66.25pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 9pt">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">房屋用途</span>
              </p>
            </td>
            <td
              colspan="33"
              style="
                width: 433.5pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□办公 □经营 □生产 □居住 □仓储 □公共设施 □临时工棚 □其它</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 19.05pt; text-align: center">
            <td
              style="
                width: 66.25pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">房屋来源</span>
              </p>
            </td>
            <td
              colspan="33"
              style="
                width: 433.5pt;
                border-width: 2.25pt 2.25pt 0.5pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□单位用房 □购买商品房 □购买经济适用房 □个人自建房 □其它形式取得</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 19.3pt; text-align: center">
            <td
              style="
                width: 66.25pt;
                border-width: 2.25pt 2.25pt 0.5pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">房屋名称</span>
              </p>
            </td>
            <td
              colspan="11"
              style="
                width: 199.5pt;
                border-width: 2.25pt 1pt 1.5pt 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p></p>
            </td>
            <td
              colspan="12"
              style="
                width: 111.75pt;
                border-width: 2.25pt 2.25pt 1.5pt 1pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">建成年份</span></p>
            </td>
            <td
              colspan="10"
              style="
                width: 122.25pt;
                border-width: 2.25pt 2.25pt 1.5pt 1pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">_______</span
                ><span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">年</span>
              </p>
            </td>
          </tr>
          <tr style="height: 24.25pt; text-align: center">
            <td
              rowspan="4"
              style="
                width: 66.25pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">产权人</span>
              </p>
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">信息</span>
              </p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">姓名/名称</span>
              </p>
            </td>
            <td
              colspan="9"
              style="
                width: 116.05pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="6"
              style="
                width: 73.95pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">产权人类型</span>
              </p>
            </td>
            <td
              colspan="16"
              style="
                width: 160.05pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□单位 □个人</span>
              </p>
            </td>
          </tr>
          <tr style="height: 24.25pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">证件类别</span>
              </p>
            </td>
            <td
              colspan="31"
              style="
                width: 350.05pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□ 房产证号 □ 身份证 □驾照 □其他</span
                ><span
                  style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt; text-decoration: underline"
                >
                </span>
              </p>
            </td>
          </tr>
          <tr style="height: 18.3pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                background-color: inherit;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                background-color: inherit;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">证件号码</span>
              </p>
            </td>
            <td
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">1</span>
              </p>
            </td>
            <td
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 16.25pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 21.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 21.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 21.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 20.25pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 13.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 13.65pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">联系方式</span>
              </p>
            </td>
            <td
              colspan="31"
              style="
                width: 350.05pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 24.25pt; text-align: center">
            <td
              rowspan="5"
              style="
                width: 66.25pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">现使用人</span>
              </p>
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">信息</span>
              </p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">使用人类别</span>
              </p>
            </td>
            <td
              colspan="7"
              style="
                width: 92.55pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□个人 □单位</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 75.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">姓 名</span>
              </p>
            </td>
            <td
              colspan="19"
              style="
                width: 182.05pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 24.25pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">性别</span>
              </p>
            </td>
            <td
              colspan="7"
              style="
                width: 92.55pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 4.5pt">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□男 □女</span>
              </p>
            </td>
            <td
              colspan="5"
              style="
                width: 75.45pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">性 质</span>
              </p>
            </td>
            <td
              colspan="19"
              style="
                width: 182.05pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□产权人□承租人□其它</span>
              </p>
            </td>
          </tr>
          <tr style="height: 11.8pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                background-color: inherit;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                background-color: inherit;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >公民身份号码（单位法人）</span
                >
              </p>
            </td>
            <td
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 16.25pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 21.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 21.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 21.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 20.25pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 13.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 18pt; text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 24.25pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">单位名称</span>
              </p>
            </td>
            <td
              colspan="31"
              style="
                width: 350.05pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 18.3pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">法定代表人</span>
              </p>
            </td>
            <td
              colspan="7"
              style="
                width: 92.55pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="6"
              style="
                width: 84.8pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">联系方式</span>
              </p>
            </td>
            <td
              colspan="18"
              style="
                width: 172.7pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 27.75pt; text-align: center">
            <td
              rowspan="6"
              style="
                width: 66.25pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">承租</span>
              </p>
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">信息</span>
              </p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">承租用途</span>
              </p>
            </td>
            <td
              colspan="31"
              style="
                width: 350.05pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >□居住 □办公 □经营 □商用 □生产 □仓储 □商住两用 □其它</span
                >
              </p>
            </td>
          </tr>
          <tr style="height: 21.8pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="5"
              style="
                width: 126.6pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">承租人（代理人）姓名</span>
              </p>
            </td>
            <td
              colspan="10"
              style="
                width: 134.2pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="10"
              style="
                width: 71.4pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 9pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">性 别</span>
              </p>
            </td>
            <td
              colspan="8"
              style="
                width: 101.3pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-indent: 9pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□男 □女</span>
              </p>
            </td>
          </tr>
          <tr style="height: 22.85pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">承租单位名称</span>
              </p>
            </td>
            <td
              colspan="31"
              style="
                width: 350.05pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 19.7pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                background-color: inherit;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 83.45pt;
                background-color: inherit;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >公民身份号码（单位法人）</span
                >
              </p>
            </td>
            <td
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 16.25pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 21.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 21.3pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 21.75pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 20.25pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="3"
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              colspan="2"
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 19.6pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 19.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
            <td
              style="
                width: 13.65pt;
                background-color: rgb(255, 255, 255);
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="min-height: 12pt; line-height: 12pt; text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 20.75pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              style="
                width: 58.15pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">联系电话</span>
              </p>
            </td>
            <td
              colspan="8"
              style="
                width: 117.85pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="11"
              style="
                width: 117pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p>
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">房产出租登记备案证号</span>
              </p>
            </td>
            <td
              colspan="13"
              style="
                width: 140.5pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
          </tr>
          <tr style="height: 18.2pt; text-align: center">
            <td
              style="
                display: none;
                width: 66.25pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"></p>
            </td>
            <td
              colspan="7"
              style="
                width: 148.75pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt"
                  >治安（消防）防范责任书</span
                >
              </p>
            </td>
            <td
              colspan="26"
              style="
                width: 284.75pt;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                vertical-align: middle;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">□未签 □已签</span>
              </p>
            </td>
          </tr>
          <tr style="height: 17.9pt; text-align: center">
            <td
              style="
                width: 66.25pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center">
                <span style="font-family: 黑体; color: rgb(0, 0, 0); min-height: 9pt; font-size: 9pt">备注</span>
              </p>
            </td>
            <td
              colspan="33"
              style="
                width: 433.5pt;
                vertical-align: middle;
                border-width: 2.25pt;
                border-style: solid;
                border-color: black;
                padding: 0pt 5.4pt;
              "
            >
              <p style="text-align: center"><span id="_GoBack"></span></p>
            </td>
          </tr>
        </table>
        <p>
          <span style="font-family: 仿宋_GB2312; font-weight: bold">采集人：</span
          ><span style="font-family: 仿宋_GB2312; font-weight: bold"> </span
          ><span style="font-family: 仿宋_GB2312; font-weight: bold">采集时间：</span
          ><span style="font-family: 仿宋_GB2312; font-weight: bold"> </span><span style="font-family: 仿宋_GB2312">年</span
          ><span style="font-family: 仿宋_GB2312"> </span><span style="font-family: 仿宋_GB2312">月</span
          ><span style="font-family: 仿宋_GB2312"> </span><span style="font-family: 仿宋_GB2312">日</span
          ><span style="font-family: 仿宋_GB2312; font-weight: bold"> </span
          ><span style="font-family: 仿宋_GB2312; font-weight: bold"> 审核人：</span>
        </p>
        <p style="text-align: center">
          <span style="font-family: 宋体; font-weight: bold; min-height: 22pt; font-size: 22pt">《实有房屋》</span
          ><span style="font-family: 宋体; font-weight: bold; min-height: 22pt; font-size: 22pt">填表说明</span>
        </p>
        <p style="text-align: center"></p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 31.5pt">
          <span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 16pt; font-size: 16pt">一、填表范围</span>
        </p>
        <p style="min-height: 23pt; line-height: 23pt; text-indent: 32pt">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">凡本辖区内独立</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">建筑物、独立院落是多户用房的，</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">如</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >多户居民用房、多户商业用房、商住两用、独立院落出租多户使用等</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >，应填写《实有房屋》信息采集表，一户一表。</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"> </span>
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 31.5pt">
          <span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 16pt; font-size: 16pt">二、填表要求</span>
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 32pt; text-align: left">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">1、统一使用黑色中性笔填写。</span>
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 31.35pt; margin-left: 0.05pt; text-align: left">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >2、表中所有项均为必填项，若无符合对应信息，则填“无”。</span
          >
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 32pt; text-align: left">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">3、凡有“</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">口”的</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">项目，在对应项目内打“</span
          ><span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 16pt; font-size: 16pt">√</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">”，可以多选。</span>
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 31.5pt">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >4、本表填写后由采集人签字，填写采集时间，交由带队民警审核签字。</span
          >
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 31.5pt">
          <span style="font-family: 仿宋_GB2312; font-weight: bold; min-height: 16pt; font-size: 16pt">三、注意事项</span>
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 32pt">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >1、产权人信息：是指房屋所有人的信息，包括单位和个人。</span
          >
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 32pt">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >2、现使用人信息：房屋现使用人与房屋产权人不存在租赁关系，如，产权人是父母、现使用人是子女，应填写房屋现使用人信息。</span
          >
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 32pt">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >3、承租信息：存在租赁关系的填写此项信息。填写承租信息的，不再填写现使用人信息。</span
          >
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 32pt">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">4、</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">产权人信息：是指</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">房屋</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">所有人的信息，包括单位和个人。</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >房屋没有独立产权的，则“产权人信息”不用填写。</span
          >
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 32pt">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >5、每户房屋都必须绘制《实有房屋平面结构图》。已有房屋平面结构图的可附后，不再绘制。每栋建筑物都必须采集正门照片和全景照片。</span
          >
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 32pt">
          <span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">6、</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">房屋</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">名称：填写独立</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt">房屋</span
          ><span style="font-family: 仿宋_GB2312; min-height: 16pt; font-size: 16pt"
            >的称谓，如ＸＸ大厦、ＸＸ商业大楼等；无明确称谓的建筑物，以其用途或楼栋地址为名称，如ＸＸ单位办公楼、ＸＸ学校宿舍、ＸＸ商店、ＸＸ小区Ｘ栋等；农村住宅房屋填私人住宅。</span
          >
        </p>
        <p style="min-height: 25pt; line-height: 25pt; text-indent: 32pt"></p>
      </article>
    </section>
  </div>
</div>
