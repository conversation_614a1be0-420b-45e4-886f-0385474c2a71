<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-07-03 10:31:41
 * @LastEditTime: 2023-12-20 14:37:17
 * @LastEditors: thb
-->
<template>
  <el-dialog align-center width="1200" :title="getTitle()" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <div class="t-container">
      <div class="t-left">
        <div v-if="isChange" style="height: 20px; margin-bottom: 24px">
          <!-- 变更自 -->
          <span class="text-change"> 变更自：{{ originDetail.contractNo }} </span>
          <span class="blue-text text-color" @click="handleCheckOrigin" v-if="!originShow"> 查看原合同 ></span>
          <span class="blue-text" @click="handleCheckBack" v-else>
            返回
            {{ getTitle() === '合同审批' ? ' 审批' : '' }}
            ></span
          >
        </div>
        <iframe
          :src="urlPreview"
          ref="iframeRef"
          frameborder="no"
          :style="{
            width: '100%',
            height: isChange ? 'calc(' + 100 + '% - 90px)' : 'calc(' + 100 + '% - 45px)'
          }"
          scrolling="auto"
        />

        <el-button :icon="ZoomIn" @click="previewShow = true">放大合同</el-button>
      </div>
      <div class="t-right">
        <el-form label-position="top" label-width="100px" ref="formRef" :model="detail" :rules="rules">
          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="合同编号">
                <el-input v-model="detail.contractNo" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="合同类型"> <el-input v-model="detail.contractTypeName" disabled /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="24">
            <!-- <el-col :span="16">
              <el-form-item label="关联客户">
                <el-input v-model="detail.customerName" disabled />
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="创建人">
                <el-input v-model="detail.createBy" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 2023-12-08新需求 将下列表单内容改为合同表单内容 -->
          <component
            :isChange="false"
            :changeDisabled="false"
            :rowChange="true"
            :isShow="false"
            :allDisabled="true"
            :productTreeData="[]"
            :is="formMap[detail.contractTypeName]"
            v-model="detail"
          />

          <el-row :gutter="24">
            <el-col :span="16">
              <el-form-item label="创建时间">
                <el-input v-model="detail.createTime" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="detail.reviewStatus === '1' || detail.reviewStatus === '2'">
              <el-form-item label="审批结果">
                <el-radio-group v-model="detail.reviewStatus" disabled>
                  <el-radio label="1">通过</el-radio>
                  <el-radio label="2">驳回</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="detail.reason">
            <el-col :span="24">
              <el-form-item label="驳回原因">
                <el-input v-model="detail.reason" disabled type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="detail.reviewStatus === '1' || detail.reviewStatus === '2'">
            <el-col :span="24">
              <el-form-item label="审批时间">
                <el-input v-model="detail.reviewTime" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 变更原因 -->
          <el-form-item label="变更原因" v-if="isChange && !originShow">
            <el-input v-model="detail.changeReason" disabled type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
          </el-form-item>

          <!-- 驳回原因 -->
          <!-- <el-form-item label="驳回原因" v-if="detail.reason">
            <el-input v-model="detail.reason" disabled type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
          </el-form-item> -->
          <template v-if="reviewType === 'edit'">
            <el-form-item label="通过" prop="isPass">
              <el-radio-group v-model="detail.isPass">
                <el-radio label="1">通过</el-radio>
                <el-radio label="2">驳回</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="驳回原因" prop="refuseReason" v-if="detail.isPass === '2'">
              <el-input v-model="detail.refuseReason" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
            </el-form-item>
          </template>
        </el-form>

        <!-- 审批记录 -->
        <Collapse title="审批记录">
          <!-- 审批流程 -->
          <processSet
            v-if="reviewList.length && !originShow"
            type="detail"
            :isReview="true"
            :isHorizontal="true"
            :createTime="detail.createTime"
            v-model="reviewList"
          />
          <processSet
            v-if="originShow"
            type="detail"
            :isReview="true"
            :isHorizontal="true"
            :createTime="detail.createTime"
            v-model="reviewList"
          />
        </Collapse>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <template v-if="reviewType === 'edit'">
        <el-button type="primary" @click="handleSave(formRef)">保存</el-button>
      </template>
    </template>
  </el-dialog>

  <iFrame :src="detail?.file?.urls" v-model="previewShow" />
</template>
<script setup>
import Collapse from '@/components/Collapse'
import iFrame from '@/components/iFrame'
import { getContractDetailById, handlePassContractReview, handleChangeReviewAudit } from '@/api/contract/contract'
import { usePreview } from '@/hooks/usePreview'
import processSet from '@/views/process-manage/contract-review/components/process-set.vue'

import { ZoomIn } from '@element-plus/icons-vue'
import accountingForm from '../../contract-list/components/accounting-form'
import oneOff from '../../contract-list/components/one-off.vue'
import addressService from '../../contract-list/components/address-service.vue'

// 合同类型组件map
const formMap = {
  记账合同: accountingForm,
  一次性合同: oneOff,
  地址服务协议合同: addressService
}
const previewShow = ref(false)

const urlPreview = ref('')
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  reviewType: {
    type: String,
    default: 'detail' //默认为detail 详情 其次为'edit'为编辑状态
  },
  isChange: {
    type: Boolean,
    default: false
  }, // 新增合同还是变更合同
  id: Number
})

const titleMap = {
  detail: '审批详情',
  edit: '合同审批'
}
const getTitle = () => {
  return titleMap[props.reviewType]
}

const rules = {
  isPass: [
    {
      required: 'true',
      message: '请选择',
      trigger: 'change'
    }
  ],
  refuseReason: [
    {
      required: 'true',
      message: '请选择',
      trigger: 'change'
    }
  ]
}

const detail = ref({
  isPass: '1' // 默认为'1' 为通过,'2'为不通过
})
// const isPass = ref('1')
const reviewList = ref([])
const baseReviewList = ref([])
const baseDetail = ref()
const getContractDetail = async () => {
  const { data } = await getContractDetailById(props.id)
  const { isInContract, feeType } = data
  detail.value =
    Object.assign(data, {
      rowData: {
        isInContract,
        feeType
      }
    }) || {}
  baseDetail.value =
    Object.assign(data, {
      rowData: {
        isInContract,
        feeType
      }
    }) || {}
  reviewList.value = data.reviewList || []
  getReview(reviewList.value, detail)
  baseReviewList.value = data.reviewList || []
  getReview(baseReviewList.value, baseDetail)

  // 找到驳回原因
  detail.value.reason = reviewList.value.filter(item => item.reviewStatus === '2')[0]?.reason || ''
  detail.value.isPass = '1' //默认为'1' 为通过,'2'为不通过
  // 后续恢复一下注释
  if (data?.file?.urls) {
    const previewUrl = await usePreview(data?.file?.urls)
    urlPreview.value = previewUrl
  }
}

// handleSave 保存
const formRef = ref()
const { proxy } = getCurrentInstance()
const handleSave = async formEl => {
  if (!formEl) return
  await formEl.validate(async valid => {
    if (valid) {
      if (props.isChange) {
        //
        let queryData
        if (originShow) {
          // 如果查看了原合同，此时detail的值是原合同的需要将其转化成现有合同
          queryData = baseDetail.value
        } else {
          queryData = detail.value
        }
        const result = await handleChangeReviewAudit({
          mainId: props.id, // 合同id
          type: '2', // 变更还是新增都是传'2'
          reviewStatus: queryData.isPass, // '1'-通过 '2'-驳回"
          reason: queryData.refuseReason //驳回原因
        })
        if (result.code === 200) {
          proxy.$modal.msgSuccess(`保存成功!`)
          handleClose()
          emits('on-success')
        } else {
          proxy.$modal.msgError(`保存失败!`)
        }
      } else {
        const result = await handlePassContractReview({
          mainId: props.id, // 合同id
          type: '2', // 变更还是新增都是传'2'
          reviewStatus: detail.value.isPass, // '1'-通过 '2'-驳回"
          reason: detail.value.refuseReason //驳回原因
        })
        if (result.code === 200) {
          proxy.$modal.msgSuccess(`保存成功!`)
          handleClose()
          emits('on-success')
        } else {
          proxy.$modal.msgError(`保存失败!`)
        }
      }
    } else {
    }
  })
}

const originDetail = ref({})
const originReviewList = ref([])
const originShow = ref(false)
// 查看原合同
const handleCheckOrigin = async () => {
  originShow.value = true
  // 查看原合同的时候需要将 通过还是驳回的信息保存下来的

  detail.value = Object.assign(originDetail.value, {
    isPass: detail.value.isPass,
    refuseReason: detail.value.refuseReason
  })

  // originReviewList
  reviewList.value = originReviewList.value
  if (detail.value?.file?.urls) {
    const previewUrl = await usePreview(detail.value?.file?.urls)
    urlPreview.value = previewUrl
  }
}
// 返回审批
const handleCheckBack = async () => {
  originShow.value = false
  detail.value = baseDetail.value
  reviewList.value = baseReviewList.value

  if (detail.value?.file?.urls) {
    const previewUrl = await usePreview(detail.value?.file?.urls)
    urlPreview.value = previewUrl
  }
}

const iframeRef = ref()

const getReview = (list, target) => {
  if (list.length) {
    target.value.reviewStatus = list[list.length - 1]?.reviewStatus
    target.value.reviewTime = list[list.length - 1]?.auditTime
  }
}
onMounted(async () => {
  // 获取合同详情
  await getContractDetail()

  // 获取原合同详情
  if (detail.value.originId) {
    const { data } = await getContractDetailById(detail.value.originId)
    const { isInContract, feeType } = data
    // originDetail.value = data || {}
    originDetail.value = Object.assign(data, {
      rowData: {
        isInContract,
        feeType
      }
    })
    originReviewList.value = data.reviewList || []
    getReview(originReviewList.value, originDetail)
  }
})
</script>
<style lang="scss" scoped>
.t-container {
  display: flex;
  // height: 100%;
  // height: 500px;
  .t-left {
    // flex: 1.5;
    width: 500px;
    // flex:  0 0 1;
    padding-right: 16px;
    border-right: 1px solid #e8e8e8ff;
  }
  .t-right {
    margin-left: 16px;
    flex: 1;
  }
}
:deep(.process-wrap) {
  justify-content: left;
}
.collapse {
  margin-bottom: 20px;
}

:deep(.el-collapse-item__wrap) {
  background: #f3f4f7;
  .el-collapse-item__content {
    height: 262px;
    display: flex;
    padding: 0 24px;
    align-items: center;
  }
}

.text-change {
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
  cursor: pointer;
}

.text-color {
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #2383e7;
}
</style>
