<!--
 * @Description: 客户详情信息组件
 * @Author: thb
 * @Date: 2023-05-29 21:44:33
 * @LastEditTime: 2023-11-13 14:22:06
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    class="cus-detail-dialog"
    title="企业详情"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <div class="top">
      <div class="title">
        <span class="icon person-icon"></span>
        {{ detail?.customerName }}
        <el-tag v-if="detail?.customerNo">{{ detail?.customerNo }}</el-tag>
      </div>

      <div class="action-btns" v-if="!hideActionBtn">
        <template v-if="detail?.discard !== 1">
          <el-button v-hasPermi="['customer:customer-file:edit']" type="primary" pain @click="handleEdit"> 编辑 </el-button>
          <el-button v-hasPermi="['customer:customer-file:abandon']" type="danger" pain @click="abandonShow = true">
            废弃</el-button
          >
        </template>
        <template v-else>
          <el-button type="primary" @click="handleDelete"> 删除 </el-button>
          <el-button @click="handleRevivification"> 还原</el-button>
        </template>
      </div>
    </div>
    <el-row justify="between" class="tag-list" v-if="!hideActionBtnForRiskCustomer">
      <div class="tag-name">
        财税顾问：<span> {{ detail?.manger || '暂无' }}</span>
        <el-icon
          class="cursor-point"
          :size="14"
          color="#0368CD"
          v-if="isShowChangebtn(detail?.manger, detail?.discard)"
          @click="changeShow = true"
          ><EditPen
        /></el-icon>
      </div>
      <div class="tag-name">
        主办会计：<span> {{ detail?.sponsorAccounting || '暂无' }}</span>
        <!-- <el-tag
          class="cursor-point"
          v-if="isShowChangebtn(detail?.sponsorAccounting, detail?.discard)"
          @click="handlePersonChange(0)"
          >变更</el-tag
        > -->
        <el-icon
          :size="14"
          color="#0368CD"
          class="cursor-point"
          v-if="isShowChangebtn(detail?.sponsorAccounting, detail?.discard)"
          @click="handlePersonChange(0)"
          ><EditPen
        /></el-icon>
      </div>
      <div class="tag-name">
        开票员：<span>{{ detail?.counselor || '暂无' }}</span>
        <!-- <el-tag class="cursor-point" v-if="isShowChangebtn(detail?.counselor, detail?.discard)" @click="handlePersonChange(1)"
          >变更</el-tag
        > -->

        <el-icon
          :size="14"
          color="#0368CD"
          class="cursor-point"
          v-if="isShowChangebtn(detail?.counselor, detail?.discard)"
          @click="handlePersonChange(1)"
          ><EditPen
        /></el-icon>
      </div>
      <div class="tag-name">
        客户成功：<span>{{ detail?.customerSuccess || '暂无' }}</span>
        <!-- <el-tag
          class="cursor-point"
          v-if="isShowChangebtn(detail?.customerSuccess, detail?.discard)"
          @click="handlePersonChange(2)"
          >变更</el-tag
        > -->

        <el-icon
          :size="14"
          color="#0368CD"
          class="cursor-point"
          v-if="isShowChangebtn(detail?.customerSuccess, detail?.discard)"
          @click="handlePersonChange(2)"
          ><EditPen
        /></el-icon>
      </div>
    </el-row>

    <div class="middle">
      <div class="left">
        <el-card class="data-card">
          <div class="wrap">
            <div class="left">
              <div class="list-item">
                <span class="color-blue">记账月营业额：</span>
                <span class="number">{{ dataBase.bookkeepingMonthlyTurnover || 0 }} </span>元
              </div>
              <div class="list-item">
                <span class="color-blue">账款总额：</span>
                <span class="number">{{ dataBase.totalPayment || 0 }}</span
                >元
              </div>
              <div class="list-item">
                <span class="color-blue">已收款：</span>
                <span class="number">{{ dataBase.payment || 0 }}</span
                >元
              </div>
              <el-progress
                class="progress list-item-b"
                :percentage="dataBase.totalReturnRate && multiply(dataBase.totalReturnRate, 100)"
              >
                <span class="color-blue"> 总回款率： </span>
                <span class="number"> {{ dataBase.totalReturnRate && multiply(dataBase.totalReturnRate, 100) }}</span>
                %</el-progress
              >
              <div class="list-item list-item-m" v-if="dataBase.totalArrears">
                <span class="color-red">总欠费合计：</span>
                <span class="number">{{ dataBase.totalArrears || 0 }}</span
                >元
              </div>
              <template v-for="(item, index) in dataBase.arrearageAmonut" :key="index">
                <div class="list-item list-item-m" v-if="item.arrearageAmount">
                  <span class="color-blue">{{ item.feeTypeName }}：</span>
                  <span class="number">{{ item.arrearageAmount || 0 }}</span
                  >元
                </div>
              </template>
            </div>
          </div>
        </el-card>
        <complexForm ref="complexRef" @on-success="getDetail" />
      </div>
      <div class="right">
        <el-tabs v-model="activeName" class="tab-pane">
          <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.label">
            <component
              v-if="activeName === item.label && detail?.customerId"
              :is="componentsMap[item.label]"
              :ciId="detail?.customerId"
              :discard="detail?.discard"
              :detail="detail"
              @on-success="getDetail"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <div>
          <span style="margin-right: 20px">
            <span class="label">提交人：</span>
            {{ detail.createBy }}</span
          >
          <span style="margin-right: 20px"> <span class="label">提交时间：</span>{{ detail.createTime }}</span>
          <span v-if="detail.clientName"> <span class="label">转化自客户：</span>{{ detail.clientName }}</span>
        </div>
        <el-button @click="handleClose"> 关闭 </el-button>
      </div>
    </template>
  </el-dialog>
  <abandonForm :id="detail.customerId" v-if="abandonShow" @on-close="abandonShow = false" @on-success="handleAbandonSuccess" />
  <managerChange :id="detail.customerId" v-if="changeShow" @on-close="changeShow = false" @on-success="getDetail" />
  <revivification :detail="detail" v-if="revShow" @on-close="revShow = false" @on-success="handleSuccess" />
  <!-- <complexWrap v-if="complexShow" @on-close="complexShow = false" /> -->

  <!-- 主办会计/财税顾问/客户成功变更 -->
  <personChange v-if="personShow" :id="detail.customerId" :type="type" @on-close="personShow = false" @on-success="getDetail" />
</template>
<script setup>
import { provide, watch } from 'vue'
import complexForm from './complex-form.vue'
import abandonForm from './abandon-form.vue'
// import complexWrap from './complex-wrap.vue'
import recordForm from './record-form.vue'
import recordList from './record-list.vue'
import managerChange from './manager-change.vue'
import { getCustomerById, deleteCustomers } from '@/api/customer/file'
import { useHandleData } from '@/hooks/useHandleData'
import revivification from './revivification.vue'
import personChange from './person-change.vue'
import { multiply } from '@/utils/math'
import { getCustomerBillDataById } from '@/api/finance/account-statement'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-edit', 'on-delete', 'on-list'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number,
  hideActionBtn: Boolean,
  hideActionBtnForRiskCustomer: Boolean
})

// 用于详情状态下禁用
const isDisabled = ref(true)
provide('disabled', isDisabled)

const onRefresh = ref(0)
provide('onRefresh', onRefresh)

const detail = ref({})

const getDetail = async () => {
  console.log('getDetail')
  try {
    const { data } = await getCustomerById(props.id)
    detail.value = data
    nextTick(() => {
      complexRef.value.setBasicData(
        {
          ...data,
          ciId: data.customerId
        } || {}
      )
    })
    onRefresh.value++
  } catch (error) {
    console.log('error', error)
    nextTick(() => {
      complexRef.value.setBasicData({})
    })
  }
}
watch(
  () => props.id,
  async () => {
    //获取客户的基础信息
    getDetail()
  },
  {
    immediate: true
  }
)

const handleSuccess = async () => {
  revShow.value = false
  getDetail()
}

// 废弃管理
const abandonShow = ref(false)

// 变更经理
const changeShow = ref(false)

//  主办会计/开票员/客户成功变更
//personShow
const personShow = ref(false)
const type = ref()
const handlePersonChange = typeNum => {
  personShow.value = true
  type.value = typeNum
}
// 高级新建
// const complexShow = ref(false)
const handleEdit = () => {
  // complexShow.value = true
  emits('on-edit', detail.value)
}

// 跟进
const activeName = ref('跟进记录')
const tabList = [
  {
    label: '跟进记录',
    value: '跟进记录'
  },
  {
    label: '快速跟进',
    value: '快速跟进'
  }
]
const componentsMap = {
  跟进记录: recordList,
  快速跟进: recordForm
}
const complexRef = ref()

// 删除客户
const handleDelete = async () => {
  await useHandleData(deleteCustomers, [detail.value.customerId], `删除所选客户${detail.value.customerName}信息`)
  emits('on-delete')
}

// 还原客户
const revShow = ref(false)
const handleRevivification = () => {
  revShow.value = true
}
// 废弃客户成功回调
const handleAbandonSuccess = () => {
  getDetail()
}

// 变更功能按钮是否展示
const isShowChangebtn = (value1, value2) => {
  return value1 && value2 !== 1
}
const dataBase = ref({})
const format = percentage => `总回款率 ${percentage}%`
const getDataBase = () => {
  getCustomerBillDataById({ customerId: props.id }).then(res => {
    dataBase.value = res.data
  })
}
getDataBase()
</script>
<style lang="scss" scoped>
.cus-detail-dialog {
  .top {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 26px;
    .icon {
      margin-right: 8px;
    }
    .title {
      display: flex;
      align-items: center;
      flex: 1;
      font-size: 18px;
      font-family: AlibabaPuHuiTi_2_65_Medium;
      color: #333333;
    }
    .el-tag {
      margin-left: 10px;
      font-size: 15px;
    }

    .change-btn {
      color: #409eff;
      cursor: pointer;
    }
  }
  .el-dialog__body {
    // .action-btns {
    //   margin-bottom: 24px;
    // }

    .middle {
      display: flex;
      // height: 400px;
      & > .left {
        flex: 1;
        width: 872px;
        border-right: 1px solid #e8e8e8ff;
        padding-right: 16px;
        flex-shrink: 0;
      }
      & > .right {
        flex: 1;
        margin-left: 20px;
      }
    }
    .num-list {
      display: flex;
      gap: 10px;
      .l-left,
      .l-right {
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        flex: 1;
        border: 1px solid #dcdfe6;
      }
      .l-left .l-item {
        width: 33%;
      }
      .l-item {
        margin-bottom: 5px;
      }
    }
  }
}
.cursor-point {
  cursor: pointer;
  margin-left: 10px;
  vertical-align: middle;
}

.tag-name {
  flex: 1;
  span:first-child {
    font-size: 16px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    color: #333333;
  }
}

.blue-text {
  color: #409eff;
}

.red-text {
  color: #f56c6c;
}
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.el-tag {
  font-size: 15px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #2383e7;
}

.wrap {
  display: flex;
  width: 100%;

  // padding: 25px 25px 10px;

  .left,
  .right {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    .list-item-b {
      // width: 40%;
      flex: 0 0 28%;
      display: flex;
      align-items: center;
    }
    .list-item {
      width: 24%;

      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #7d8592;
    }
  }
  .color-blue {
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: #2383e7;
  }
  .color-red {
    color: #f56c6c;
  }
  .color-gray {
    // color: #999;
  }
}

.tag-list {
  padding-bottom: 18px;
  border-bottom: 1px solid #e8e8e8;
}
</style>

<style lang="scss" scoped>
.data-card {
  margin-top: 8px;
  background: #eff7ffff;
  border: none;
  box-shadow: none;
  :deep(.el-card__body) {
    padding: 12px 8px !important;
  }
}

.wrap .left .list-item,
.wrap .right .list-item-m {
}
.list-item-m {
  margin-top: 24px;
}
.number {
  font-size: 16px;
  font-family: Barlow-Bold, Barlow;
  font-weight: bold;
  color: #333333;
  margin-right: 5px;
}

:deep(.progress) {
  .el-progress-bar__inner {
    background: #2383e7ff;
  }
  .el-progress__text {
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: #2383e7;
  }
}
:deep(.el-date-editor.el-date-editor--date) {
  width: 100%;
}
:deep(.el-select) {
  width: 100%;
}

:deep(.tab-pane) {
  .el-tabs__item.is-top {
    width: 139px;
    text-align: center;
    padding: 0;
  }
  .el-tabs__active-bar {
    left: 16%;
    height: 2px;
    width: 48px !important;
  }
}
.label {
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #6a7697;
}
</style>
