<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-06-21 14:00:02
 * @LastEditTime: 2024-03-11 13:22:59
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    width="1200"
    class="table-dialog"
    :title="title"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <ProTable
      ref="proTable"
      :class="[multiple ? '' : 'table-checkbox']"
      :init-param="initParam"
      :columns="columns"
      :request-api="requestApi"
      :row-class-name="tableRowClassName"
      v-bind="$attrs"
      @select="handleSingleSelect"
      @select-all="handleMultipleSelect"
    >
      <template v-for="(slot, index) in Object.keys($slots)" :key="index" #[slot]="{ row }">
        <template v-if="slot">
          <slot v-bind="{ row }" :name="slot"></slot>
        </template>
      </template>
    </ProTable>
    <template #footer>
      <!-- columns-{{ columns }} -->
      <el-button type="primary" @click="confirmSelect" v-if="!multiple">确认选择</el-button>
      <el-button type="primary" @click="confirmMultipleSelect" v-else>确认选择</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="tsx">
import { useSlots } from 'vue'
// import { getCustomers } from '@/api/customer/file'
// import { ProTableProps } from '@/components/ProTable/index.vue'
import { ColumnProps } from '@/components/ProTable/interface'
const slots = useSlots()
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-select', 'on-multiple-select'])
const handleClose = () => {
  emits('on-close')
}

interface ProTableProps {
  columns: ColumnProps[] // 列配置项  ==> 必传
  data?: any[] // 静态 table data 数据，若存在则不会使用 requestApi 返回的 data ==> 非必传
  requestApi?: (params: any) => Promise<any> // 请求表格数据的 api ==> 非必传
  requestAuto?: boolean // 是否自动执行请求 api ==> 非必传（默认为true）
  requestError?: (params: any) => void // 表格 api 请求错误监听 ==> 非必传
  dataCallback?: (data: any) => any // 返回数据的回调函数，可以对数据进行处理 ==> 非必传
  title?: string // 表格标题，目前只在打印的时候用到 ==> 非必传
  pagination?: boolean // 是否需要分页组件 ==> 非必传（默认为true）
  initParam?: any // 初始化请求参数 ==> 非必传（默认为{}）
  border?: boolean // 是否带有纵向边框 ==> 非必传（默认为true）
  toolButton?: boolean // 是否显示表格功能按钮 ==> 非必传（默认为true）
  // rowKey?: string // 行数据的 Key，用来优化 Table 的渲染，当表格数据多选时，所指定的 id ==> 非必传（默认为 id）
  searchCol?: number | Record<BreakPoint, number> // 表格搜索项 每列占比配置 ==> 非必传 { xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }
  tableRowClassName?: (row: any, index: number) => string
  multiple?: boolean // 是否多选
}

// 接受父组件参数，配置默认值
const props = withDefaults(defineProps<ProTableProps>(), {
  columns: () => [],
  requestAuto: true,
  pagination: true,
  title: '',
  initParam: {},
  border: true,
  rowKey: 'id',
  multiple: false
})

// 简化修改
if (props.initParam && props.initParam.discard === 0) {
  props.initParam.tableModal = true
  props.initParam.discard = ''
}
// 单选
let selectRow: any = null
const proTable = ref()
const handleSingleSelect = (selection, row) => {
  // 如果是多选
  if (props.multiple) {
    handleMultipleSelect(selection)
    return
  }
  if (row?.paymentStatus === 'close') {
    proxy.$modal.msgWarning('已关闭的账单不可选择!')
    proTable.value?.clearSelection()
    return
  }
  if (row?.digest && row?.receiptId) {
    proxy.$modal.msgWarning('已关联收款单的记账不可选择!')
    proTable.value?.clearSelection()
    return
  }
  selectRow = row
  proTable.value?.clearSelection()
  if (selection.length === 0) return
  proTable.value?.toggleRowSelection(row, true)
}
// 确认选择
const { proxy } = getCurrentInstance()
const confirmSelect = () => {
  console.log('selectRow', selectRow)
  if (!selectRow) {
    proxy.$modal.msgWarning('请选择某一行!')
  } else {
    handleClose()
    emits('on-select', selectRow)
  }
}

// 表格多选

const handleMultipleSelect = (selection: any) => {
  selectRow = selection
}
// 多选确认
const confirmMultipleSelect = () => {
  if (Array.isArray(selectRow) && selectRow.length) {
    handleClose()
    emits('on-multiple-select', selectRow)
  } else {
    proxy.$modal.msgWarning('请选择行!')
  }
}
</script>
<style lang="scss" scoped>
// :deep(.el-table__append-wrapper) {
//   min-height: 110px;
// }
</style>

<style lang="scss">
.table-dialog {
  .el-dialog__body {
    min-height: 400px;
    display: flex;
    & > div {
      display: flex;
      width: 100%;
    }
  }
}
</style>
<style lang="scss" scoped>
:deep(.table-checkbox) {
  thead tr {
    th:nth-child(1) .el-checkbox {
      display: none;
    }
    th:nth-child(2) .el-checkbox {
      display: none;
    }
  }
}
:deep(.el-table) {
  .disabled-row {
    --el-table-tr-bg-color: var(--el-color-info-light-9);
  }
  .warning-row {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
  }
  .success-row {
    --el-table-tr-bg-color: var(--el-color-success-light-9);
  }
}
</style>
