<template>
  <div class="home">
    <div class="top">
      <div class="my-card" @click="go('/material-manage/clue-zone')">
        <img src="@/assets/icons/index-1.png" alt="" />
        <div class="t">新建线索</div>
      </div>
      <div class="my-card" @click="go('/contract-manage/contract-list')">
        <img src="@/assets/icons/index-2.png" alt="" />
        <div class="t">新建合同</div>
      </div>
      <div class="my-card" @click="go('/material-manage/client-zone')">
        <img src="@/assets/icons/index-3.png" alt="" />
        <div class="t">新建客户</div>
      </div>
      <div class="my-card" @click="go('/finance/accounts-receivable')">
        <img src="@/assets/icons/index-4.png" alt="" />
        <div class="t">新建账单</div>
      </div>
      <div class="my-card" @click="go('/contract-template/form')">
        <img src="@/assets/icons/index-5.png" alt="" />
        <div class="t">合同模版</div>
      </div>
      <div class="my-card" @click="go('/customer-protect/visit-plan')">
        <img src="@/assets/icons/index-6.png" alt="" />
        <div class="t">拜访计划</div>
      </div>
    </div>
    <div class="bottom">
      <div class="left">
        <div class="my-con">
          <div class="title">消息提醒</div>
          <myMessage :go="hasPermission(routes, '')"></myMessage>
        </div>
        <div class="my-con">
          <div class="title">待办任务</div>
          <todoTask />
        </div>

        <!-- <div class="my-con">
          <div class="title">我的待办</div>
          <myDeal :go="hasPermission(routes, 'my-deal')"></myDeal>
        </div>
        <div class="my-con">
          <div class="title">我的发起</div>
          <myCreate :go="hasPermission(routes, 'my-create')"></myCreate>
        </div> -->
      </div>
      <div class="right">
        <div class="my-con right-top">
          <div class="title">日程日历</div>
          <el-calendar v-model="queryParams.planVisitDate" />
        </div>
        <div class="my-con right-bottom">
          <div class="title">拜访计划（{{ visitData.total }}）</div>
          <div class="my-list" v-if="visitData?.records?.length">
            <div class="my-item" v-for="item in visitData.records" :key="item.id" @click="handleClickVisit(item)">
              <div class="line-1">
                <div class="line-left">
                  <el-icon color="#2383E7"><Memo /></el-icon>
                  <span class="tel">{{ item.planVisitMethod }}</span>
                  <span>{{ item.planName }}</span>
                </div>
                <div class="line-right">
                  <el-tag :type="visitStatus.find(itemx => itemx.value === item.status)?.type">{{
                    visitStatus.find(itemx => itemx.value === item.status)?.label
                  }}</el-tag>
                </div>
              </div>
              <div class="line-2">
                <div class="line-left">
                  <el-icon><User /></el-icon><span>{{ item.customerName }}</span>
                </div>
                <div class="line-right">
                  <el-icon><Phone /></el-icon><span>{{ item.phone }}</span>
                </div>
              </div>
            </div>
          </div>
          <el-empty v-else description="该日没有拜访计划" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getVisitList } from '@/api/customer-protect/visit-plan'
import myMessage from './components/my-message.vue'
import myDeal from './components/my-deal.vue'
import myCreate from './components/my-create.vue'
import todoTask from './components/todo-task.vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { watch } from 'vue'
import usePermissionStore from '@/store/modules/permission'
import useCommonStore from '@/store/modules/common'
const useCommon = useCommonStore()

const router = useRouter()
const { proxy } = getCurrentInstance()
const routes = computed(() => usePermissionStore().routes)

function go(path) {
  const pathTemp = path.split('/').at(-1)
  if (hasPermission(routes.value, pathTemp)) {
    router.push(path)
  } else {
    proxy.$modal.msgWarning('当前暂无访问权限')
  }
}

// 递归判断item.path是否有本按钮的path
function hasPermission(arr, path) {
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    if (item.path.includes(path)) {
      return true
    }
    if (item.children && item.children.length > 0) {
      if (hasPermission(item.children, path)) {
        return true
      }
    }
  }
  return false
}

const queryParams = ref({
  pageNum: 1,
  pageSize: 999,
  planVisitDate: dayjs().format('YYYY-MM-DD')
})
const visitStatus = [
  {
    label: '待完成',
    type: '',
    value: '0'
  },
  {
    label: '已完成',
    type: 'success',
    value: '1'
  },
  {
    label: '已取消',
    type: 'info',
    value: '2'
  }
]
const visitData = ref([])

function handleClickVisit(row) {
  router.push('/customer-protect/visit-plan')
  useCommon.setId(row.id)
  useCommon.setBizType('visit_plan')
}
function onGetVisitList() {
  getVisitList(queryParams.value).then(res => {
    visitData.value = res.data
  })
}
onGetVisitList()
watch(
  () => queryParams.value.planVisitDate,
  val => {
    queryParams.value.planVisitDate = dayjs(val).format('YYYY-MM-DD')
    if (/^(\d{4})-(\d{2})-(\d{2})$/.test(val)) {
      onGetVisitList()
    }
  }
)

onMounted(() => {})
</script>

<style scoped lang="scss">
.home {
  display: flex;
  flex-direction: column;
  flex: 1;
  // height: 100%;
  width: 100%;
  gap: 16px;
  // overflow-y: hidden;
  .top {
    display: flex;
    gap: 16px;
  }
  .bottom {
    // flex: 1 1 auto;
    flex: 1;
    display: flex;
    gap: 16px;
    width: 100%;
    .my-con {
      padding: 16px;
      background-color: #ffffff;
      flex: 1;
    }
    .left {
      flex: 1;
      width: 70%;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;
      .my-con:first-child {
        padding: 16px;
        background-color: #ffffff;
        flex: 0 1 auto;
      }
    }
    .right {
      flex: 0 0 auto;
      width: calc(30% - 16px);
      height: 100%;
      display: flex;
      flex-direction: column;
      .my-con + .my-con {
        margin-top: 0;
      }
      .right-top {
        flex: 1 1 auto;
      }
      .right-bottom {
        flex: 1 1 0;
        height: 100%;
        position: relative;
        .my-list {
          font-size: 14px;
          line-height: 21px;
          height: 300px; // flex下无法获取合适高度以扩大这个高度去达成更好效果
          overflow-y: scroll;
          margin-right: -10px;
          padding-right: 10px;
          .my-item {
            cursor: pointer;
            .line-1 {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .line-left {
                .el-icon {
                  vertical-align: middle;
                  margin-right: 8px;
                }
                .tel {
                  color: #2383e7;
                  border-right: 1px solid #e8e8e8;
                  padding-right: 8px;
                  margin-right: 8px;
                }
              }
              .line-right {
              }
            }
            .line-2 {
              margin-top: 10px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              .line-left {
                .el-icon {
                  vertical-align: middle;
                  margin-right: 8px;
                }
              }
              .line-right {
                .el-icon {
                  vertical-align: middle;
                  margin-right: 8px;
                }
              }
            }
          }
          .my-item + .my-item {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #e8e8e8;
          }
        }
      }
    }
  }
}
.my-card {
  width: 100%;
  height: 120px;
  background: #ffffff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  img {
    width: 56px;
    height: 56px;
  }
}

.title {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 14px;
    background: #2383e7;
    margin-right: 12px;
    // margin-left: 2px;
  }
}
:deep(.table-box) {
  .card {
    padding: 0;
    border: none;
  }
  .el-table__row {
    height: auto;
  }
  .el-table__cell {
    padding: 5px 0;
  }
}
:deep(.el-calendar__body) {
  padding-bottom: 0;
  .el-calendar-table {
    .el-calendar-day {
      height: auto;
      text-align: center;
    }
  }
}
</style>
