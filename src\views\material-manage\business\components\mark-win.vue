<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-alert title="请确认账款已完成到账后进行标记" type="warning" show-icon :closable="false" style="margin-bottom: 12px" />
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="关联账单" prop="paymentNos">
          <div @click="showBill" style="width: 100%">
            <el-input v-model="formData.paymentNos" maxlength="20" readonly placeholder="请选择" style="cursor: pointer" />
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <tableModal
    v-if="listSelectShow"
    :init-param="initParamPayment"
    title="关联账单"
    :multiple="true"
    :columns="columns"
    :request-api="financePaymentList"
    @on-close="listSelectShow = false"
    @on-multiple-select="handleMultipleSelect"
  />
</template>
<script setup lang="tsx">
import { reactive } from 'vue'
import NumberInput from '@/components/NumberInput'
import { ColumnProps } from '@/components/ProTable/interface'
import tableModal from '@/components/tableModal'

// import feeTypeTree from '@/views/finance/accounts-receivable/components/fee-type-tree'
import { receiveStatusArr } from '@/utils/constants'
import { getBusinessList } from '@/api/business/business'
import { financePaymentList } from '@/api/finance/accounts-receivable'
const columns: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'paymentNo',
    width: 200,
    label: '账单编号',
    search: { el: 'input' }
  },
  {
    prop: 'feeType',
    width: '150',
    enum: (() => {
      return () => {
        return new Promise(async (resolve, reject) => {
          const { data } = await getBusinessList({
            pageNum: 1,
            pageSize: 10000
          })
          if (data) {
            const revertData = (data || []).map(item => {
              return {
                ...item,
                label: item.typeName,
                value: item.id
              }
            })
            resolve({
              data: revertData
            })
          } else {
            resolve({
              data: []
            })
          }
        })
      }
    })(),
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    },
    search: {
      el: 'select'
    },
    label: '费用类别'
  },
  {
    prop: 'receiveStatus',
    label: '收款情况',
    enum: receiveStatusArr
  },
  {
    prop: 'paymentAmount',
    label: '账款金额', // 账款金额->账款金额总计
    width: '120',
    render: scope => {
      return <span>{scope.row.paymentAmount >= 0 ? `${scope.row.paymentAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'allReceiptAmount',
    label: '已付款金额', // 已收款->已付款金额
    width: '100',
    isColShow: false,
    render: scope => {
      return <span>{scope.row.allReceiptAmount >= 0 ? `${scope.row.allReceiptAmount}元` : '--'}</span>
    }
  },
  {
    prop: 'allReceivableAmount',
    label: '应待收款',
    width: '200',
    render: scope => {
      return <span>{scope.row.allReceivableAmount >= 0 ? `${scope.row.allReceivableAmount}元` : '--'}</span>
    }
  }
]
const formData = reactive({
  id: undefined, // 该商机
  paymentNos: '',
  paymentIds: [] // 账单集合
})
const rules = {
  paymentNos: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

// 显示关联账单弹窗
const listSelectShow = ref(false)
const initParamPayment = reactive({
  receiveStatus: '已收款'
})
const showBill = () => {
  initParamPayment.customerId = formData.customerId
  listSelectShow.value = true
}

const handleMultipleSelect = selection => {
  formData.paymentNos = selection.map(item => item.paymentNo).join(';')
  formData.paymentIds = selection.map(item => item.id)
}

defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped></style>
