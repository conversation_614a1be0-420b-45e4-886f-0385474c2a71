<!--
 * @Description: 存储户数、注销户数、消失户数、迁移户数表格面板
 * @Author: thb
 * @Date: 2023-09-04 09:47:17
 * @LastEditTime: 2023-11-07 09:20:42
 * @LastEditors: thb
-->
<template>
  <div class="top-right">
    <el-table :data="tableData" height="100%">
      <el-table-column prop="name" label="">
        <template #default="{ row, $index }">
          <div class="name">
            <span class="icon" :class="[$index === 0 ? 'account-icon' : 'certify-icon']"></span>
            <span>{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="existing" label="存量户数" align="center">
        <template #default="{ row }">
          <span class="number-bold">{{ row.existing }}</span
          >户
        </template>
      </el-table-column>
      <el-table-column prop="cancelled" label="注销户数" align="center">
        <template #default="{ row }">
          <div class="flex">
            <div>
              <span class="number-bold">{{ row.cancelled }}</span
              >户
            </div>
            <span
              >对应存量{{
                Number(row.existing) === 0 ? '--' : ((Number(row.cancelled) / Number(row.existing)) * 100).toFixed(0) + '%'
              }}</span
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="churned" label="流失户数" align="center">
        <template #default="{ row }">
          <div class="flex">
            <div>
              <span class="number-bold">{{ row.churned }}</span
              >户
            </div>
            <span
              >对应存量{{
                Number(row.existing) === 0 ? '--' : ((Number(row.churned) / Number(row.existing)) * 100).toFixed(0) + '%'
              }}</span
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="migrated" label="迁移户数" align="center">
        <template #default="{ row }">
          <div class="flex" v-if="row.migrated">
            <div>
              <span class="number-bold">{{ row.migrated }}</span
              >户
            </div>
            <span
              >对应存量{{
                Number(row.existing) === 0 ? '--' : ((Number(row.migrated) / Number(row.existing)) * 100).toFixed(0) + '%'
              }}</span
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup>
import { getAccountTypesNumbers } from '@/api/panel-data/collecting'
const tableData = ref([])

const getTableData = async () => {
  const { data } = await getAccountTypesNumbers()
  const { bookkeeping, license } = data || {}
  // tableData.value = data.records || []
  // 假数据测试
  tableData.value = [
    {
      name: '记账企业',
      cancelled: bookkeeping.cancelled, // 注销户数
      churned: bookkeeping.churned, //流失户数
      existing: bookkeeping.existing, //存量
      migrated: bookkeeping.migrated //迁移
    },
    {
      name: '办证企业',
      cancelled: license.cancelled, // 注销户数
      churned: license.churned, //流失户数
      existing: license.existing, //存量
      migrated: license.migrated //迁移
    }
  ]
}
onMounted(() => {
  getTableData()
})
</script>
<style lang="scss" scoped>
.icon {
  margin-right: 8px;
}
.top-right {
  flex: 2;
  border-radius: 4px;
  padding: 0 20px 15px 20px;
  background: #fff;
  display: flex;
  overflow: hidden; // 防止子项目撑大
}

:deep(.el-table) {
  // flex: 1;
  th.el-table__cell.is-leaf {
    background-color: #fff !important;
    border-bottom: none;
    font-size: 18px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #333333;
  }
  .el-scrollbar__view {
    height: 100%;
  }
  .el-table__body {
    height: 100%;
  }

  tbody tr:hover > td {
    background-color: unset !important; //修改成自己想要的颜色即可
  }

  td.el-table__cell:nth-child(2) {
    position: relative;
    &::after {
      content: '';
      width: 1px;
      position: absolute;
      height: 33px;
      right: 0;
      top: 36%;
      background: #d8d8d8;
    }
  }
  td.el-table__cell:nth-child(3) {
    position: relative;
    &::after {
      content: '';
      width: 1px;
      position: absolute;
      height: 33px;
      right: 0;
      top: 36%;
      background: #d8d8d8;
    }
  }
  td.el-table__cell:nth-child(4) {
    position: relative;
    &::after {
      content: '';
      width: 1px;
      position: absolute;
      height: 33px;
      right: 0;
      top: 36%;
      background: #d8d8d8;
    }
  }
  td.el-table__cell.is-center .cell {
    justify-content: center;
  }
  td.el-table__cell .cell {
    height: 80px;
    background: #fafafa;
    border-radius: 4px;
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: #7d8592;
    display: flex;
    align-items: center;
    // justify-content: center;
    & > .name {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #333333;
      // justify-content: start;
    }
  }
}

.number-bold {
  font-size: 24px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  color: #333333;
}

.flex {
  display: flex;
  flex-direction: column;
}
</style>
