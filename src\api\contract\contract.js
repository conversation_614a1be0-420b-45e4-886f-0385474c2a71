/*
 * @Description:
 * @Author: thb
 * @Date: 2023-06-20 09:50:46
 * @LastEditTime: 2023-11-14 15:19:29
 * @LastEditors: thb
 */
// 合同管理api
import request from '@/utils/request'

// 合同台账列表查询
export function getContractList(params) {
  params.totalCost = undefined
  params.startTime = undefined
  params.endTime = undefined
  params.createTime = undefined
  return request({
    url: '/customerContract/list',
    method: 'get',
    params
  })
}
// 合同保存
export function saveContract(data) {
  return request({
    url: '/customerContract/saveOrUpdate',
    method: 'post',
    data
  })
}
// 转正式合同
export function customerContractChangeToFormal(data) {
  return request({
    url: '/customerContract/changeToFormal',
    method: 'post',
    data
  })
}
// 合同批量保存
export function saveBatchContract(data) {
  return request({
    url: '/customerContract/saveOrUpdateBatch',
    method: 'post',
    data
  })
}
// 批量删除合同
export function deleteBatchContract(ids) {
  return request({
    url: '/customerContract/deleteByIds?ids=' + ids.join(','),
    method: 'delete'
  })
}

// 获取合同详情
export function getContractDetailById(id) {
  return request({
    url: '/customerContract/getById',
    method: 'get',
    params: {
      id,
      showFlag: true
    }
  })
}
// 合同评审
// 由当前用户提交的合同评审
export function getContractReviewCommitByUser(query) {
  return request({
    url: '/customerContract/listMyCreate',
    method: 'get',
    params: query
  })
}

// 由当前用户审批的合同评审
export function getContractReviewListByUser(query) {
  query.createTime = undefined
  query.totalCost = undefined
  return request({
    url: '/customerContract/listMyAudit',
    method: 'get',
    params: query
  })
}

export function getContractReviewListByUserExport(query) {
  query.createTime = undefined
  query.totalCost = undefined
  return request({
    url: '/customerContract/listMyAuditExport',
    method: 'post',
    data: query
  })
}

// 审核列表
export function getCustomerContractAuditList(query) {
  query.createTime = undefined
  query.totalCost = undefined
  return request({
    url: '/customerContract/auditList',
    method: 'get',
    params: query
  })
}

export function getCustomerContractAuditListExport(query) {
  query.createTime = undefined
  query.totalCost = undefined
  return request({
    url: '/customerContract/auditListExport',
    method: 'post',
    data: query
  })
}

// 合同评审通过还是驳回
export function handlePassContractReview(data) {
  return request({
    url: '/bizNodeHistory/reviewAudit',
    method: 'post',
    data
  })
}

// 合同变更时候的审核接口
export function handleChangeReviewAudit(data) {
  return request({
    url: '/bizNodeHistory/changeReviewAudit',
    method: 'post',
    data
  })
}

// 客户关联合同接口
export function getCustomerAssociatedContract(params) {
  return request({
    url: '/customerContract/listByCiId',
    method: 'get',
    params
  })
}
// 借阅申请
export function reportContractBorrow(data) {
  return request({
    url: '/borrowRecord/saveOrUpdate',
    method: 'post',
    data
  })
}

// 借阅管理---用户提交的借阅列表
export function getReportContractBorrowList(params) {
  return request({
    url: '/borrowRecord/listMyCreate',
    method: 'get',
    params
  })
}
// 借阅管理---用户审批的
export function getReportContractBorrowListByAudit(params) {
  return request({
    url: '/borrowRecord/listMyAudit',
    method: 'get',
    params
  })
}

// 查看合同权限校验
export function checkContractDetailIsPermission(id) {
  return request({
    url: '/customerContract/getByIdCheck',
    method: 'get',
    params: {
      id
    }
  })
}

// 查看合同是否有变更权限
export function checkContractChangeIsPermission(id) {
  return request({
    url: '/customerContract/getByIdCheckChange',
    method: 'get',
    params: {
      id
    }
  })
}
// 借阅详情
export function getBorrowContractDetail(id) {
  return request({
    url: '/borrowRecord/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 借阅流程审批
export function handleBorrowReviewAudit(data) {
  return request({
    url: '/bizNodeHistory/borrowReviewAudit',
    method: 'post',
    data
  })
}

// 添加合同附件
export const customerContractAddFile = query => {
  return request({
    url: '/customerContract/addFile',
    method: 'post',
    data: query
  })
}
// 编辑归档号
export const updateDocumentNo = query => {
  return request({
    url: '/customerContract/updateDocumentNo',
    method: 'post',
    data: query
  })
}
// 获取合同是否有关联账单
export const checkContractBillExisted = params => {
  return request({
    url: '/customerContract/isExistBill',
    method: 'post',
    params
  })
}

// 终止合同
export const stopContract = params => {
  return request({
    url: '/customerContract/terminateContract',
    method: 'post',
    params
  })
}
