<!--
 * @Description: 工商办证弹窗
 * @Author: thb
 * @Date: 2023-09-27 11:11:10
 * @LastEditTime: 2024-02-22 09:30:23
 * @LastEditors: thb
-->
<template>
  <el-dialog
    align-center
    class="business-dialog license-dialog"
    title="业务办理"
    width="1200"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <div class="top">
      <span class="icon com-icon"></span>
      <span class="name">{{ detail.customerName }}</span>
      <el-tag class="tag">{{ detail.customerNo }}</el-tag>
      <span class="place"></span>
      <template v-if="detail.bizStatus === 'processing'">
        <el-button v-hasPermi="['license:todo:transfer']" type="primary" @click="handleTransfer">转单</el-button>
        <el-button v-hasPermi="['license:todo:delete']" type="danger" @click="handleDel" v-if="actionType === 'todo'"
          >废弃</el-button
        >
      </template>
      <template v-if="detail.bizStatus === 'deprecated'">
        <span style="margin-right: 10px">{{ detail.updateTime }}</span>
        <el-button disabled plain type="info"> 作废 </el-button>
      </template>
      <template v-if="detail.bizStatus === 'completed'">
        <span style="margin-right: 10px">{{ detail.updateTime }}</span>
        <el-tag type="success">已完成</el-tag>
      </template>
    </div>
    <div class="middle">
      <span class="mid-1"
        >业务名称：
        <span class="mid-text">
          {{ getLabel(detail.bizType) }}
        </span>
      </span>

      <span class="mid-1"
        >流程编号：
        <span class="mid-text">
          {{ detail.code }}
        </span></span
      >

      <span class="mid-1" v-if="detail.bizType === 'business_cancellation'"
        >其他附件：
        <span class="blue-text" v-if="detail.otherDocumentFileList?.length" @click="showOtherFiles">
          共 {{ detail.otherDocumentFileList?.length }} 份</span
        >
        <span v-else class="mid-text">暂无</span>
      </span>

      <span class="mid-1" v-if="detail.bizType === 'business_change'"
        >办理科目：

        <span class="blue-text" v-if="detail.changeSubject"> {{ detail.changeSubject }}</span>
        <span v-else class="mid-text">暂无</span>
      </span>
    </div>
    <el-steps finish-status="success" :active="active" simple class="step-list">
      <template v-for="(value, key, index) in stepMap[detail.bizType]" :key="value">
        <el-step :title="`步骤${index + 1}/${value.label}`" @click="checkStep(index, key)" v-if="hideDeprecatedStep(index)">
          <template #icon>
            <span class="icon circle-icon circle-icon-default">{{ index + 1 }}</span>
          </template>
        </el-step>
      </template>
    </el-steps>
    <div
      class="title-top"
      :class="[
        detail.bizType === 'business_change' &&
        (detail.bizStage === 'data_updates' || detail.bizStage === 'completed') &&
        active === activeBase
          ? 'top-bt'
          : ''
      ]"
    >
      <span class="icon title-icon"></span>
      <span class="title">{{ stepMap[detail.bizType]?.[currentBizStage]?.labelAs }}</span>
    </div>
    <!-- component map -->
    <component
      :is="stepMap[detail.bizType]?.[currentBizStage]?.component"
      ref="componentRef"
      :disabled="active < activeBase"
      :data="detail"
    >
    </component>
    <!-- footer -->
    <template #footer>
      <el-button @click="handleClose"> 关闭 </el-button>
      <template v-if="active === activeBase && detail.bizStatus !== 'completed' && detail.bizStatus !== 'deprecated'">
        <el-button
          v-hasPermi="['license:todo:save']"
          type="primary"
          @click="handleSave"
          v-if="stepMap[detail.bizType]?.[detail.bizStage]?.saveRemote"
        >
          保存
        </el-button>

        <el-button v-hasPermi="['license:todo:submit']" type="primary" @click="handleSubmit"> 提交 </el-button>
      </template>
    </template>
  </el-dialog>
  <transferFormModal ref="transferFormModalRef" @ok="handleClose" />

  <!-- 其他附件弹窗 -->
  <otherFiles :list="detail.otherDocumentFileList" v-if="fileShow" @on-close="fileShow = false" />
</template>

<script setup>
import workOrder from './work-order'
import dataCollection from './data-collection'
import licenseHandle from './license-handle'
import dataUpload from './data-upload'
import transferFormModal from './transfer-form-modal'
import businessLogout from './business-logout'
import bankLogout from './bank-logout'
import otherFiles from './other-files'
import changeProcess from './changeProcess'
import fileUpdate from './fileUpdate'
import { bizTypeArr } from '@/utils/constants'
import {
  bizAssign,
  bizGetById,
  bizDeprecate,
  saveLicenseDataCollection,
  submitLicenseDataCollection,
  saveLicenseProcessReport,
  submitLicenseProcessReport,
  saveLicenseDataUpload,
  submitLicenseDataUpload,
  saveLogoutDataUpload,
  submitLogoutDataUpload,
  saveBankLogoutDataUpload,
  submitBankLogoutDataUpload,
  saveBusinessProcessChange,
  submitBusinessProcessChange,
  saveBusinessUpdateChange, // 保存接口
  submitBusinessUpdateChange // 提交接口
} from '@/api/certificate/certificate'

const getLabel = bizType => {
  let label = bizTypeArr.filter(item => item.value === bizType)[0]?.label
  if (detail.value.changeSubject) {
    label = label + `(${detail.value.changeSubject} )`
  }

  if (props.productName?.includes('单办证')) {
    label = label + `(单办证)`
  }
  return label || '--'
}

const visible = ref(true)
const props = defineProps({
  id: String,
  actionType: {
    type: String,
    default: 'todo'
  },
  productName: {
    type: String,
    default: ''
  }
})
const emits = defineEmits('on-close')
const handleClose = () => {
  emits('on-close')
}

// active step当前激活的步骤
const active = ref(0)

// 禁用disabled
let activeBase = ''
const disabledCom = computed(() => {
  // 作废或者已完成的情况下 disabled为true
  return active.value < activeBase || detail.value.bizStatus === 'completed' || detail.value.bizStatus === 'deprecated'
})
const disabled = provide('disabled', disabledCom)
// 许可证step进度map

const permit_capital = {
  pending: {
    label: '办证派工',
    labelAs: '办证派工',
    component: workOrder,
    saveRemote: null, // 保存接口
    submitRemote: bizAssign // 提交接口
  },
  data_collection: {
    label: '资料收集',
    labelAs: '许可证办理',
    component: dataCollection,
    saveRemote: saveLicenseDataCollection, // 保存接口
    submitRemote: submitLicenseDataCollection // 提交接口
  },
  license_processing: {
    label: '证照办理',
    labelAs: '办理进度汇报',
    component: licenseHandle,
    saveRemote: saveLicenseProcessReport, // 保存接口
    submitRemote: submitLicenseProcessReport // 提交接口
  },
  info_uploading: {
    label: '资料上传',
    labelAs: '资料上传',
    component: dataUpload,
    saveRemote: saveLicenseDataUpload, // 保存接口
    submitRemote: submitLicenseDataUpload // 提交接口
  }
}

const stepMap = reactive({
  permit_in_and_out: permit_capital, // 许可证 - 进出口
  permit_labor_dispatch: permit_capital, //许可证 - 劳务派遣
  permit_capital_verification: permit_capital, //许可证 - 验资
  permit_food_certificate: permit_capital, //许可证 - 食品证
  permit_road_transport: permit_capital, //许可证 - 道路运输
  // 自测工商注销的字段名
  business_cancellation: {
    pending: {
      label: '派工',
      labelAs: '工商注销派工',
      component: workOrder,
      saveRemote: null, // 保存接口
      submitRemote: bizAssign // 提交接口
    },
    // 新增工商注销
    business_cancellation: {
      label: '工商注销',
      labelAs: '工商注销',
      component: businessLogout,
      saveRemote: saveLogoutDataUpload, // 保存接口
      submitRemote: submitLogoutDataUpload // 提交接口
    },
    // // 新增银行注销
    bank_cancellation: {
      label: '银行注销',
      labelAs: '银行注销',
      component: bankLogout,
      saveRemote: saveBankLogoutDataUpload, // 保存接口
      submitRemote: submitBankLogoutDataUpload // 提交接口
    }
  },
  // 工商变更
  business_change: {
    pending: {
      label: '变更派工',
      labelAs: '变更派工',
      component: workOrder,
      saveRemote: null, // 保存接口
      submitRemote: bizAssign // 提交接口
    },
    change_progress: {
      label: '变更进度',
      labelAs: '变更进度',
      component: changeProcess,
      saveRemote: saveBusinessProcessChange, // 保存接口
      submitRemote: submitBusinessProcessChange // 提交接口
    },
    data_updates: {
      label: '资料更新',
      labelAs: '资料更新',
      component: fileUpdate,
      saveRemote: saveBusinessUpdateChange, // 保存接口
      submitRemote: submitBusinessUpdateChange // 提交接口
    }
  }
})

// 动态组件Ref
const componentRef = ref()
// 阶段保存
const handleSave = async () => {
  const { saveRemote } = stepMap[detail.value.bizType]?.[detail.value.bizStage]
  console.log('saveRemote', saveRemote)
  // 保存时需要检验 框选的 字段
  const valResult = await componentRef.value.validateCheckedForm()
  console.log('valResult', valResult)
  if (valResult && saveRemote) {
    console.log('detail', detail.value)
    const result = await saveRemote(detail.value)
    if (result.code === 200) {
      proxy.$message.success(result.msg || result.message)
      getDetail(props.id)
    }
  }
}

// 阶段提交
const { proxy } = getCurrentInstance()
const handleSubmit = async () => {
  const { submitRemote } = stepMap[detail.value.bizType]?.[detail.value.bizStage]
  console.log('submitRemote', submitRemote)
  const valResult = await componentRef.value.validateForm()
  // 提交时需要检验表单
  if (valResult && submitRemote) {
    const result = await submitRemote(detail.value)
    if (result.code === 200) {
      proxy.$message.success(result.msg || result.message)
      // 同时查询获取详情接口 跳到下一个阶段
      // 如果是最后一个阶段的提交 需要
      const steps = Object.keys(stepMap[detail.value.bizType])
      if (detail.value.bizStage === steps?.[steps.length - 1]) {
        handleClose()
      } else {
        getDetail(props.id)
      }
    }
  }
}

// 查看step详情
const checkStep = (index, key) => {
  if (index <= activeBase) {
    active.value = index
    currentBizStage = key
  }
}

// 获取业务详情
const detail = ref({})

let currentBizStage = ''
const getDetail = async id => {
  const { data } = await bizGetById({
    id
  })
  const taskFileVO = data.taskFileVO
  // 默认赋值空数组 以防checked报错
  for (const [key, value] of Object.entries(taskFileVO)) {
    if (value === null) {
      taskFileVO[key] = []
    }
  }
  const { legalPhone, operatorPhone, email } = data.dataCollection
  detail.value =
    {
      ...data,
      ...data.dataCollection,
      ...taskFileVO,
      userId: data.handleUserId,
      legalPhone: legalPhone || '',
      operatorPhone: operatorPhone || '',
      email: email || '',
      customerName: data.customerName,
      stageNameList: (data.permitProcessRecordList || []).map(item => item.stageName),
      stageName1: (data.permitProcessRecordList || []).filter(item => item.stageName === '资料递交')[0]?.stageName,
      stageName2: (data.permitProcessRecordList || []).filter(item => item.stageName === '颁发证件')[0]?.stageName,
      bankCancellationFlag: Boolean(data.dataCollection.bankCancellationFlag)
      // 工商变更自测
      // // permitProcessRecordList: [] // 办理进度汇报字段
      // processNameList: (data.processNameList || []).map(item => item.stageName),
      // processName1: (data.processNameList || []).filter(item => item.stageName === '提交')[0]?.stageName,
      // processName2: (data.processNameList || []).filter(item => item.stageName === '审核')[0]?.stageName,
      // processName3: (data.processNameList || []).filter(item => item.stageName === '完成')[0]?.stageName
    } || {}
  // 工商注销
  if (data.bizType === 'business_cancellation') {
    Object.assign(detail.value, {
      stageNameList: (data.businessCancellationProcessRecordList || []).map(item => item.stageName),
      logoutStageName: (data.businessCancellationProcessRecordList || []).filter(item => item.stageName === '完成')[0]?.stageName
    })
  }
  // 工商变更
  if (data.bizType === 'business_change') {
    Object.assign(detail.value, {
      stageNameList: (data.businessChangeProcessRecordList || []).map(item => item.stageName),
      processName1: (data.businessChangeProcessRecordList || []).filter(item => item.stageName === '提交')[0]?.stageName,
      processName2: (data.businessChangeProcessRecordList || []).filter(item => item.stageName === '审核')[0]?.stageName,
      processName3: (data.businessChangeProcessRecordList || []).filter(item => item.stageName === '完成')[0]?.stageName
    })
  }
  // 特殊对 法人手机号 操作员手机号 邮箱 Checked设置
  const strs = ['legalPhone', 'operatorPhone', 'email']
  strs.forEach(str => {
    if (detail.value[`${str}`]) {
      detail.value[`${str}Checked`] = true
    }
  })

  detail.value.taskId = id
  // 激活当前的step active
  const { deprecatedBizStage, bizStage, bankCancellationFlag, bizType } = detail.value
  const currentStep = stepMap[detail.value.bizType]
  let defaultIndex = 1
  active.value = Object.values(currentStep).findIndex(step => {
    // 如果是工商注销且不需要银行注销的前提下
    if (bizType === 'business_cancellation' && !bankCancellationFlag && bizStage === 'completed') {
      defaultIndex = 2
    }
    const stepIndex = deprecatedBizStage
      ? deprecatedBizStage
      : bizStage === 'completed'
      ? Object.keys(currentStep)[Object.keys(currentStep).length - defaultIndex]
      : bizStage

    return step === currentStep?.[stepIndex]
  })
  // 为了切换step 用的activeBase
  activeBase = active.value
  console.log('activeBase', activeBase)
  //currentBizStage 为了切换step component
  currentBizStage = deprecatedBizStage
    ? deprecatedBizStage
    : bizStage === 'completed'
    ? Object.keys(currentStep)[Object.keys(currentStep).length - defaultIndex]
    : bizStage
}

// 转单
const transferFormModalRef = ref()
const handleTransfer = () => {
  transferFormModalRef.value.onShow({ taskId: props.id })
}

// 作废
const handleDel = () => {
  proxy.$modal
    .confirm('是否确认作废本次业务办理？')
    .then(function () {
      bizDeprecate({ id: props.id }).then(res => {
        if (res.code === 200) {
          proxy.$message.success('作废成功')
          handleClose()
        }
      })
      return
    })
    .catch(() => {})
}

// 隐藏废弃step
const hideDeprecatedStep = index => {
  return !(detail.value.bizStatus === 'deprecated' && index > activeBase)
}

// 监听watch
watch(detail, () => {
  const { bizType, bankCancellationFlag, bizStage } = detail.value
  if (bizType === 'business_cancellation' && !bankCancellationFlag && bizStage === 'completed') {
    stepMap[bizType] = {
      pending: {
        label: '派工',
        labelAs: '工商注销派工',
        component: workOrder,
        saveRemote: null, // 保存接口
        submitRemote: bizAssign // 提交接口
      },
      // 新增工商注销
      business_cancellation: {
        label: '工商注销',
        labelAs: '工商注销',
        component: businessLogout,
        saveRemote: saveLogoutDataUpload, // 保存接口
        submitRemote: submitLogoutDataUpload // 提交接口
      }
    }
  }
})
// 查看其他附件
const fileShow = ref(false)
const showOtherFiles = () => {
  fileShow.value = true
}

onMounted(() => {
  getDetail(props.id)
})
</script>
<style lang="scss" scoped>
.top {
  display: flex;
  align-items: center;
  gap: 14px;
  .name {
    font-size: 18px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    color: #333333;
  }
  .place {
    flex: 1;
  }
}
.middle {
  margin-top: 26px;
  display: flex;
  align-items: center;
  .mid-1 {
    flex: 1;
    .mid-text {
      font-size: 16px;
      font-family: AlibabaPuHuiTi_2_65_Medium;
      color: #333333;
    }
  }
}
.step-list {
  margin-top: 20px;
  margin-bottom: 25px;
  border-top: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
  :deep(.el-step) {
    cursor: pointer;
  }
}

.title-top {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  .icon {
    margin-right: 8px;
  }
}
.top-bt {
  margin-bottom: 8px;
}
</style>
