/**
 * 通用css样式布局处理
 * Copyright (c) 2019 ruoyi
 */

/** 基础通用 **/
.pt5 {
  padding-top: 5px;
}
.pr5 {
  padding-right: 5px;
}
.pb5 {
  padding-bottom: 5px;
}
.mt5 {
  margin-top: 5px;
}
.mr5 {
  margin-right: 5px;
}
.mb5 {
  margin-bottom: 5px;
}
.mb8 {
  margin-bottom: 8px;
}
.ml5 {
  margin-left: 5px;
}
.mt10 {
  margin-top: 10px;
}
.mr10 {
  margin-right: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
.ml10 {
  margin-left: 10px;
}
.mt20 {
  margin-top: 20px;
}
.mr20 {
  margin-right: 20px;
}
.mb20 {
  margin-bottom: 20px;
}
.ml20 {
  margin-left: 20px;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}
.el-form .el-form-item__label {
  font-weight: 300;
  color:#333333;
}
.el-dialog:not(.is-fullscreen) {
  // margin-top: 6vh !important;
}
.el-dialog.scrollbar .el-dialog__body {
  max-height: 70vh;
  padding: 10px 20px 0;
  overflow: auto;
  overflow-x: hidden;
}
.el-table {
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      height: 40px !important;
      font-size: 13px;
      color: #515a6e;
      word-break: break-word;
      background-color: #f8f8f9 !important;
    }
  }
  .el-table__body-wrapper {
    .el-button [class*="el-icon-"] + span {
      margin-left: 1px;
    }
  }
}

/** 表单布局 **/
.form-header {
  padding-bottom: 5px;
  margin: 8px 10px 25px;
  font-size: 15px;
  color: #6379bb;
  border-bottom: 1px solid #dddddd;
}

/** 表格布局 **/
.pagination-container {
  position: relative;
  height: 25px;
  padding: 10px 20px !important;
  margin-top: 15px;
  margin-bottom: 10px;
  box-sizing: content-box;
}

/* tree border */
.tree-border {
  width: 100%;
  margin-top: 5px;
  background: #ffffff none;
  border: 1px solid #e5e6e7;
  border-radius: 4px;
}
.pagination-container .el-pagination {
  position: absolute;
  right: 10px;
}

@media (width <= 768px) {
  .pagination-container .el-pagination > .el-pagination__jump {
    display: none !important;
  }
  .pagination-container .el-pagination > .el-pagination__sizes {
    display: none !important;
  }
}
.el-table .fixed-width .el-button--small {
  width: inherit;
  padding-right: 0;
  padding-left: 0;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
  margin-left: 10px;
  color: #409eff;
  cursor: pointer;
}
.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 12px;
}
.el-tree-node__content > .el-checkbox {
  margin-right: 8px;
}
.list-group-striped > .list-group-item {
  padding-right: 0;
  padding-left: 0;
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.list-group {
  padding-left: 0;
  list-style: none;
}
.list-group-item {
  padding: 11px 0;
  margin-bottom: -1px;
  font-size: 13px;
  border-top: 1px solid #e7eaec;
  border-bottom: 1px solid #e7eaec;
}
.pull-right {
  float: right !important;
}
.el-card__header {
  min-height: 40px;
  padding: 14px 15px 7px !important;
}
.el-card__body {
  padding: 15px 20px 20px !important;
}
.card-box {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  color: #ffffff;
  background: #20b2aa;
  border-color: #20b2aa;
}
.el-button--cyan:focus,
.el-button--cyan:hover {
  color: #ffffff;
  background: #48d1cc;
  border-color: #48d1cc;
}
.el-button--cyan {
  color: #ffffff;
  background-color: #20b2aa;
  border-color: #20b2aa;
}

/* text color */
.text-navy {
  color: #1ab394;
}
.text-primary {
  color: inherit;
}
.text-success {
  color: #1c84c6;
}
.text-info {
  color: #23c6c8;
}
.text-warning {
  color: #f8ac59;
}
.text-danger {
  color: #ed5565;
}
.text-muted {
  color: #888888;
}

/* image */
.img-circle {
  border-radius: 50%;
}
.img-lg {
  width: 120px;
  height: 120px;
}
.avatar-upload-preview {
  position: absolute;
  top: 50%;
  width: 200px;
  height: 200px;
  overflow: hidden;
  border-radius: 50%;
  box-shadow: 0 0 4px #cccccc;
  transform: translate(50%, -50%);
}

/* 拖拽列样式 */
.sortable-ghost {
  color: #ffffff !important;
  background: #42b983 !important;
  opacity: 0.8;
}

/* 表格右侧工具栏样式 */
.top-right-btn {
  margin-left: auto;
}
