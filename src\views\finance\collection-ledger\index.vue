<template>
  <div class="main-wrap">
    <ProTable
      ref="proTable"
      title="收款台账"
      :init-param="initParam"
      :columns="columns"
      :toolButton="false"
      :transformRequestParams="transformRequestParams"
      @sort-change="sortChange"
      rowKey="id"
      :request-api="getFinanceReceiptList"
    >
      <template #tabs>
        <el-radio-group :model-value="initParam.isChecked" @change="handleRadioChange">
          <el-radio-button :label="null">全部</el-radio-button>
          <el-radio-button :label="0">未审核</el-radio-button>
          <el-radio-button :label="1">通过</el-radio-button>
          <el-radio-button :label="2">驳回</el-radio-button>
        </el-radio-group></template
      >
      <!-- 客户 -->
      <template #customerName="{ row }">
        <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
      </template>
      <!-- 收款单 -->
      <template #receiptNo="{ row }">
        <span class="blue-text" @click="handleShowCollectionDetail(row.id)">{{ row.receiptNo }}</span>
      </template>
      <!-- 账单 -->
      <template #paymentNo="{ row }">
        <span class="blue-text" @click="handleShowAccountsDetail(row, row.paymentId)">{{ row.paymentNo }}</span>
      </template>
      <!-- 合同 -->
      <template #contractNo="{ row }">
        <span class="blue-text" @click="handlShowContractDetail(row.contractId)">{{ row.contractNo }}</span>
      </template>
      <template #action="{ row }">
        <el-button v-if="[0, 1].includes(row.isChecked)" type="danger" text @click="handleDelete(row)">删除</el-button>
        <div v-else>--</div>
      </template>
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
      </template>
    </ProTable>
  </div>
  <!-- 此处的客户详情，不要有编辑按钮，只是查看的详情 -->
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <contractDetail v-if="contractDetailShow" :id="contractId" :hideActionBtn="true" @on-close="contractDetailShow = false" />
  <div v-show="false"><feeTypeTree></feeTypeTree></div>
</template>

<script setup lang="tsx">
import { financePaymentGetById } from '@/api/finance/accounts-receivable'
import {
  postFinanceReceiptSaveOrUpdate,
  getFinanceReceiptList,
  getFinanceReceiptGetById,
  deleteFinanceReceipt
} from '@/api/finance/collection-ledger'
import { ref, reactive, nextTick } from 'vue'
import collectionForm from '@/views/finance/collection-ledger/components/form.vue'
import accountsForm from '@/views/finance/accounts-receivable/components/form.vue'
import { useDialog } from '@/hooks/useDialogFinance'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus } from '@element-plus/icons-vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import feeTypeTree from '@/views/finance/accounts-receivable/components/fee-type-tree'
import bus from 'vue3-eventbus'
import contractDetail from '@/views/contract-manage/contract-list/components/contract-detail.vue'
import { useHandleData } from '@/hooks/useHandleData'

const { proxy } = getCurrentInstance()

const proTable = ref()

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({
  isChecked: null
})

const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 },
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'receiptNo',
    label: '收款单编号',
    width: '200',
    sortable: 'custom',
    fixed: 'left',
    isColShow: false,
    search: { el: 'input' }
  },

  {
    prop: 'feeType',
    label: '费用类别',
    sortable: 'custom',
    width: '150',
    isColShow: false,
    search: {
      render: ({ searchParam }) => {
        return <feeTypeTree vModel={searchParam.feeType} />
      }
    },
    render: scope => {
      return <span>{scope.row.feeType || '--'}</span>
    }
  },
  {
    prop: 'customerName',
    label: '客户名称',
    sortable: 'custom',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    sortable: 'custom',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },

  {
    prop: 'payee',
    width: '100',
    sortable: 'custom',
    label: '收款人',
    search: { el: 'input' }
  },
  {
    prop: 'receiptAmount',
    width: '150',
    label: '收款金额',
    sortable: 'custom',
    sortable: 'custom',
    render: scope => {
      return <span>{scope.row.receiptAmount || 0}元</span>
    }
  },
  {
    prop: 'receiptDate',
    width: '180',
    sortable: 'custom',
    label: '收款时间'
  },
  {
    prop: 'receiptMethod',
    width: '150',
    sortable: 'custom',
    label: '收款渠道'
  },
  {
    prop: 'paymentNo',
    width: '150',
    label: '关联账单',
    search: { el: 'input' }
  },
  {
    prop: 'paymentStartTime',
    width: '150',
    sortable: 'custom',
    label: '账期开始时间'
  },
  {
    prop: 'paymentEndTime',
    width: '150',
    label: '账期结束时间',
    sortable: 'custom',
    search: {
      el: 'date-picker',
      props: { type: 'monthrange', valueFormat: 'YYYY-MM-DD' }
    }
  },
  {
    prop: 'mark',
    minWidth: '180',
    sortable: 'custom',
    label: '收款备注'
  },
  {
    prop: 'contractNo',
    label: '关联合同',
    width: '150'
  },
  {
    prop: 'action',
    label: '操作',
    width: 100,
    isColShow: false,
    fixed: 'right'
  }
]

const transformRequestParams = data => {
  console.log('currentColumn', currentColumn)
  if (currentColumn) {
    const label = currentColumn
    if (labelMap[label]['key']) {
      const order = labelMap[label]['order']

      if (order === 'descending') {
        data.isAsc = 'desc'
        data.orderByColumn = labelMap[label]['key']
      } else if (order === 'ascending') {
        data.isAsc = 'asc'
        data.orderByColumn = labelMap[label]['key']
      } else {
        data.isAsc = ''
        data.orderByColumn = ''
      }
    }
  }
}
const { showDialog } = useDialog()
// 处理表单提交参数
const handleRevertParamsCollection = (data: any) => {
  if (Array.isArray(data.receiptVoucherFile) && data.receiptVoucherFile[0]) {
    const file = data.receiptVoucherFile[0]
    data.receiptVoucherFile = {
      fileSize: file.uploadSize,
      fileNames: file.newFileName,
      // bizType: 'dddd', // 假数据
      uploadBy: file.uploadBy,
      uploadTime: file.uploadTime,
      urls: file.url
    }
  } else {
    delete data.receiptVoucherFile
  }
}

const handleAdd = () => {
  showDialog({
    title: '新增收款',
    component: collectionForm,
    customClass: 'customer-dialog',
    submitApi: postFinanceReceiptSaveOrUpdate,
    submitCallback,
    handleRevertParams: handleRevertParamsCollection
  })
}

const handleShowCollectionDetail = (id: any) => {
  console.log('id', id)
  showDialog({
    title: '收款详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: collectionForm,
    submitApi: postFinanceReceiptSaveOrUpdate,
    getApi: getFinanceReceiptGetById,
    requestParams: { id },
    submitCallback
  })
}

const submitCallback = () => {
  proTable.value?.getTableList()
}

const getList = () => {
  proTable.value?.getTableList()
}

const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.isChecked = e
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  console.log('handlShowCustomerDetail-id', id)
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
}
/* 客户详情弹窗 ---end--- */

/* 账单详情弹窗 ---start--- */
const handleShowAccountsDetail = (row: any, id: any) => {
  console.log('id', id)
  showDialog({
    title: '账单详情',
    customClass: 'customer-dialog',
    cancelButtonText: '关闭',
    showConfirmButton: 'hide',
    component: accountsForm,
    getApi: financePaymentGetById,
    requestParams: { id },
    submitCallback
  })
  nextTick(() => {
    bus.emit('paymentNo', row.paymentNo)
    bus.emit('paymentId', row.paymentId || row.id)
  })
}
/* 账单弹窗 ---end--- */

/* 合同详情弹窗 ---start--- */
const contractDetailShow = ref(false)
const contractId = ref()
const handlShowContractDetail = (id: number) => {
  // proxy.$modal.msgWarning(`合同详情模块建设中!`)
  contractDetailShow.value = true
  contractId.value = id
}
/* 合同弹窗 ---end--- */

// 表头字段排序
const labelMap = {
  收款单编号: {
    key: 'receipt.receipt_no',
    order: ''
  },
  费用类别: {
    key: 'type.type_name',
    order: ''
  },
  客户名称: {
    key: 'information.customer_name',
    order: ''
  },
  客户编号: {
    key: 'receipt.customer_no',
    order: ''
  },
  收款人: {
    key: 'receipt.payee',
    order: ''
  },
  收款金额: {
    key: 'receipt.receipt_amount',
    order: ''
  },
  收款时间: {
    key: 'receipt.receipt_date',
    order: ''
  },
  收款渠道: {
    key: 'receipt.receipt_method',
    order: ''
  },
  账期开始时间: {
    key: 'payment.payment_start_time',
    order: ''
  },
  账期结束时间: {
    key: 'payment.payment_end_time',
    order: ''
  },
  收款备注: {
    key: 'receipt.mark',
    order: ''
  }
}

let currentColumn = ''
const sortByColumn = (label, order) => {
  // 如果存在可排序的列名
  if (labelMap[label]['key']) {
    currentColumn = label
    labelMap[label]['order'] = order
    if (order === 'descending') {
      proTable.value.searchParam.isAsc = 'desc'
      proTable.value.searchParam.orderByColumn = labelMap[label]['key']
      proTable.value.search()
    } else if (order === 'ascending') {
      proTable.value.searchParam.isAsc = 'asc'
      proTable.value.searchParam.orderByColumn = labelMap[label]['key']
      proTable.value.search()
    } else {
      proTable.value.searchParam.isAsc = ''
      proTable.value.searchParam.orderByColumn = ''
      proTable.value.search()
    }
  }
}
const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  sortByColumn(column.label, order)
}

const handleDelete = async (row: any) => {
  await useHandleData(deleteFinanceReceipt, { id: row.id }, `删除收款单${row.receiptNo}`)
  proTable.value?.getTableList()
}
</script>

<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
  // padding: 20px;
}
.blue-text {
  color: #409eff;
  cursor: pointer;
}
</style>
