<!--
 * @Description: 企业信息组件
 * @Author: thb
 * @Date: 2023-05-26 10:27:31
 * @LastEditTime: 2024-03-14 09:29:32
 * @LastEditors: thb
-->

<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :hide-required-asterisk="disabled">
    <Collapse title="企业联系信息">
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="企业名称" prop="customerName">
            <el-input
              v-model="formData.customerName"
              maxlength="100"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input
              v-model="formData.contactPerson"
              maxlength="20"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="手机号" prop="contactPhone">
            <el-input
              v-model="formData.contactPhone"
              maxlength="20"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="财税顾问" prop="mangerUserId">
            <!-- <el-select v-model="formData.manger" :disabled="disabled" :placeholder="disabled ? ' ' : '请选择'" clearable>
              <el-option :label="item.nickName" :value="item.nickName" v-for="(item, index) in userList" :key="index" />
            </el-select> -->
            <template v-if="!isDisabled">
              <SelectTree
                v-model="formData.manger"
                :disabled="disabled"
                :placeholder="disabled ? ' ' : '请选择'"
                clearable
                filterable
                highlight-current
                @on-node-click="handleChangeManger"
                @clear="
                  () => {
                    formData.manger = ''
                    formData.mangerUserId = ''
                  }
                "
              />
            </template>
            <template v-else>
              <el-input v-model="formData.manger" maxlength="20" :disabled="disabled" />
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="所属分公司" prop="branchOffice">
            <el-select v-model="formData.branchOffice" :disabled="disabled" :placeholder="disabled ? ' ' : '请选择'" clearable>
              <!-- <el-option label="公司1" value="公司1" /> -->
              <el-option v-for="(option, index) in branch_office" :key="index" :label="option.name" :value="option.name" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="开票员" prop="counselor">
            <!-- <el-select v-model="formData.counselor" :disabled="disabled" :placeholder="disabled ? ' ' : '请选择'" clearable>
              <el-option :label="item.nickName" :value="item.nickName" v-for="(item, index) in userList" :key="index" />
            </el-select> -->

            <template v-if="!isDisabled">
              <SelectTree
                v-model="formData.counselor"
                :disabled="disabled"
                filterable
                :placeholder="disabled ? ' ' : '请选择'"
                clearable
                highlight-current
                @on-node-click="handleChangeCounselor"
                @clear="
                  () => {
                    formData.counselor = ''
                    formData.counselorUserId = ''
                  }
                "
              />
            </template>
            <template v-else>
              <el-input v-model="formData.counselor" maxlength="20" :disabled="disabled" />
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户成功" prop="customerSuccess">
            <!-- <el-select v-model="formData.customerSuccess" :disabled="disabled" :placeholder="disabled ? ' ' : '请选择'" clearable>
              <el-option :label="item.nickName" :value="item.nickName" v-for="(item, index) in userList" :key="index" />
            </el-select> -->
            <template v-if="!isDisabled">
              <SelectTree
                v-model="formData.customerSuccess"
                :disabled="disabled"
                filterable
                :placeholder="disabled ? ' ' : '请选择'"
                clearable
                @on-node-click="handleChangeCustomerSuccess"
                highlight-current
                @clear="
                  () => {
                    formData.customerSuccess = ''
                    formData.customerSuccessUserId = ''
                  }
                "
              />
            </template>
            <template v-else>
              <el-input v-model="formData.customerSuccess" maxlength="20" :disabled="disabled" />
            </template>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主办会计" prop="sponsorAccounting">
            <!-- <el-select
              v-model="formData.sponsorAccounting"
              :disabled="disabled"
              :placeholder="disabled ? ' ' : '请选择'"
              clearable
            >
              <el-option :label="item.nickName" :value="item.nickName" v-for="(item, index) in userList" :key="index" />
            </el-select> -->

            <template v-if="!isDisabled">
              <SelectTree
                v-model="formData.sponsorAccounting"
                filterable
                :disabled="disabled"
                :placeholder="disabled ? ' ' : '请选择'"
                clearable
                highlight-current
                @on-node-click="handleChangeSponsorAccounting"
                @clear="
                  () => {
                    formData.sponsorAccounting = ''
                    formData.sponsorAccountingUserId = ''
                  }
                "
              />
            </template>
            <template v-else>
              <el-input v-model="formData.sponsorAccounting" maxlength="20" :disabled="disabled" />
            </template>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="实际经营地址" prop="address">
            <el-input
              v-model="formData.address"
              maxlength="100"
              type="textarea"
              :disabled="disabled"
              :placeholder="disabled ? '' : '请输入'"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="备注" prop="informationMark">
            <el-input
              v-model="formData.informationMark"
              :disabled="disabled"
              type="textarea"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
    <Collapse title="企业服务信息">
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="企业状态" prop="customerStatus">
            <el-select v-model="formData.customerStatus" :disabled="disabled" :placeholder="disabled ? ' ' : '请选择'" clearable>
              <el-option v-for="(option, index) in customer_status" :key="index" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="企业性质" prop="customerProperty">
            <el-select
              v-model="formData.customerProperty"
              :disabled="disabled"
              :placeholder="disabled ? ' ' : '请选择'"
              clearable
            >
              <el-option v-for="(option, index) in customer_property" :key="index" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="从事行业" prop="industry">
            <el-select
              v-model="formData.industry"
              :disabled="disabled"
              :placeholder="disabled ? ' ' : '请选择'"
              clearable
              filterable
              allow-create
              default-first-option
              @change="handleChange"
            >
              <el-option v-for="(option, index) in industry" :key="index" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- <el-row>
        <el-col :span="24">
          <el-form-item label="客户开票资料" prop="customerBill">
            <FileUpload v-if="!disabled" :limit="100" v-model="formData.invoiceInfoFileList" :isShowTip="false" />
            <fileList :list="formData.invoiceInfoFileList" v-else />
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="8">
          <el-form-item label="企业认定" prop="companyIdentificationList">
            <el-select
              v-model="formData.companyIdentificationList"
              :disabled="disabled"
              :placeholder="disabled ? ' ' : '请选择'"
              clearable
              multiple
            >
              <el-option
                v-for="(option, index) in company_identification"
                :key="index"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="下户表(企业走访表)" prop="visit">
            <FileUpload v-if="!disabled" v-model="formData.interviewFileList" :limit="100" :isShowTip="false" />
            <!-- <span class="download-text" v-else @click="downloadFile(formData.interviewFile)">
              {{ (formData.interviewFile && formData.interviewFile?.fileNames) || '暂无文件' }}</span
            > -->
            <fileList :list="formData.interviewFileList" v-else />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="不收费原因" prop="noFree">
            <!-- <FileUpload v-if="!disabled" :limit="1" v-model="formData.nofeeReasonFile" :isShowTip="false" />
            <span class="download-text" @click="downloadFile(formData.nofeeReasonFile)" v-else>
              {{ (formData.nofeeReasonFile && formData.nofeeReasonFile?.fileNames) || '暂无文件' }}</span
            > -->
            <el-select v-model="formData.nofeeReason" :disabled="disabled" :placeholder="disabled ? ' ' : '请选择'" clearable>
              <el-option v-for="(option, index) in nofeeReasonList" :key="index" :label="option.label" :value="option.value" />
              <!-- <el-option label="财税顾问1" value="财税顾问1" />
              <el-option label="财税顾问2" value="财税顾问2" /> -->
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="不收费原因备注" prop="nofeeReasonMark">
            <el-input
              limit="1"
              v-model="formData.nofeeReasonMark"
              :disabled="disabled"
              type="textarea"
              :placeholder="disabled ? '' : '请输入'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
  </el-form>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import Collapse from '@/components/Collapse'
import { saveCustomer } from '@/api/customer/file'
import { getCompanyTreeList } from '@/api/basicData/basicData'
import { customerStatus, customerProperty, customerIndustry } from '@/utils/constants'
import { useRemote } from '@/hooks/useRemote'
// import { downloadFile } from '@/utils/common'
import { listUser } from '@/api/system/user'
import SelectTree from '@/components/SelectTree'
import { FormValidators } from '@/utils/validate'
import iFrame from '@/components/iFrame'
import fileList from '@/components/FileList'
import { useSetDic } from '@/hooks/useSetDic'
const { setDic } = useSetDic()
const handleChange = value => {
  setDic('industry', value, value, industry)
}
const { proxy } = getCurrentInstance()
const { industry } = proxy.useDict('industry')
const { customer_status } = proxy.useDict('customer_status')
const { customer_property } = proxy.useDict('customer_property')
const { company_identification } = proxy.useDict('company_identification')
const nofeeReasonList = [
  {
    label: '自己公司',
    value: '自己公司'
  },
  {
    label: '免费白干',
    value: '免费白干'
  }
]
const disabled = inject('disabled')

const isDisabled = computed(() => {
  return disabled.value
})
const rules = {
  customerName: [{ required: !isDisabled.value, message: '请输入', trigger: 'blur' }],
  contactPerson: [{ required: !isDisabled.value, message: '请输入', trigger: 'blur' }],
  contactPhone: [{ message: '请输入正确格式的手机号', trigger: 'blur', validator: FormValidators.mobilePhone }],
  customerStatus: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  customerProperty: [
    {
      required: true,
      message: '请选择',
      trigger: 'change'
    }
  ],
  // mangerUserId: [
  //   {
  //     required: !isDisabled.value,
  //     message: '请选择',
  //     trigger: 'change'
  //   }
  // ],
  branchOffice: [
    {
      required: !isDisabled.value,
      message: '请选择',
      trigger: 'change'
    }
  ]
  // address: [{ required: !isDisabled.value, message: '请输入', trigger: 'blur' }],
  // customerStatus: [
  //   {
  //     required: !isDisabled.value,
  //     message: '请选择',
  //     trigger: 'change'
  //   }
  // ],
  // customerProperty: [
  //   {
  //     required: !isDisabled.value,
  //     message: '请选择',
  //     trigger: 'change'
  //   }
  // ],
  // industry: [
  //   {
  //     required: !isDisabled.value,
  //     message: '请选择',
  //     trigger: 'change'
  //   }
  // ]
}
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const emits = defineEmits(['update:modelValue', 'on-change'])
const formData = computed({
  get: () => {
    return props.modelValue
  },
  set: val => {
    emits('update:modelValue', val)
  }
})

const formRef = ref()

// const { branch_office } = proxy.useBasicDict('branch_office')
const branch_office = ref([])
function onGetBasicData() {
  getCompanyTreeList({ enable: 1 }).then(res => {
    branch_office.value = res.data.records // 未验证
  })
}
onGetBasicData()
const saveRemote = async () => {
  const result = await formRef.value.validate(valid => {
    if (valid) {
    } else {
    }
  })
  console.log('result', result)
  if (result) {
    const id = await useRemote(saveCustomer, formData.value, [], '企业信息', ['interviewFileList', 'invoiceInfoFileList'])
    // 说明保存成功,如果保存成功，那么就要改变联系人的联系电话
    if (id) {
      formData.value.contactTable.tableData[0].phone = formData.value.contactPhone
    }
    return id
  }
}
const userList = ref([])
const getUserList = async () => {
  const result = await listUser()
  userList.value = result.rows || []
}
// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}
const handleChangeManger = (node, node1) => {
  // formData.mangerUserId = node.id
  // formData.value.manger = node.label
  const names = parentNameSearch()(node1)
  nextTick(() => {
    formData.value.mangerUserId = node.id
    formData.value.manger = names.reverse().join('/')
  })
}
// const handleChangeManger = userId => {
//   formData.mangerUserId = userId
//   formData.manger = userList.value.find(item => item.userId === userId).nickName
// }
const handleChangeCounselor = (node, node1) => {
  // formData.value.counselor = node.label
  const names = parentNameSearch()(node1)
  nextTick(() => {
    formData.value.counselorUserId = node.id
    formData.value.counselor = names.reverse().join('/')
  })
}
const handleChangeCustomerSuccess = (node, node1) => {
  const names = parentNameSearch()(node1)
  nextTick(() => {
    formData.value.customerSuccessUserId = node.id
    formData.value.customerSuccess = names.reverse().join('/')
  })
}

const parentNameSearch = () => {
  let nameStr = []
  const searchNameByNode = node => {
    // nameStr = nameStr + '/' + node.data.label
    if (node.level === 1) {
      return nameStr
    } else {
      nameStr.push(node.data.label)
    }
    return searchNameByNode(node.parent)
  }
  return searchNameByNode
}
const handleChangeSponsorAccounting = (node, node1) => {
  const names = parentNameSearch()(node1)
  nextTick(() => {
    formData.value.sponsorAccountingUserId = node.id
    formData.value.sponsorAccounting = names.reverse().join('/')
  })
}
getUserList()
defineExpose({
  formRef,
  saveRemote
})
</script>
<style lang="scss" scoped>
.el-select {
  // width: 573px;
  width: 100%;
}
</style>
