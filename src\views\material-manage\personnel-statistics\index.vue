<template>
  <ProTable v-if="show" ref="proTable" :initParam="initParam" :columns="columns" :request-api="getTabList">
    <template #tabs>
      <el-radio-group :model-value="initParam.tabType" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
  </ProTable>
</template>
<script setup lang="jsx">
import {
  clueAnalyseStaffAll,
  clueAnalyseStaffChannel,
  clueAnalyseStaffCustomerIntroduction,
  clueAnalyseStaffDirect,
  clueAnalyseStaffOther,
  clueAnalyseStaffPlatform
} from '@/api/material-manage/clue-analyse'
import dayjs from 'dayjs'
import { multiply, divide } from '@/utils/math'
import { deptTreeSelect } from '@/api/system/user'

const proTable = ref('')
const initParam = reactive({ tabType: '0' })

const tabs = [
  {
    dicValue: '10',
    dictLabel: '全部'
  },
  {
    dicValue: '0',
    dictLabel: '平台统计'
  },
  {
    dicValue: '1',
    dictLabel: '直投统计'
  },
  {
    dicValue: '2',
    dictLabel: '客户介绍统计'
  },
  {
    dicValue: '3',
    dictLabel: '渠道统计'
  },
  {
    dicValue: '11',
    dictLabel: '其他'
  }
]

const show = ref(true) // 重新渲染让ProTable能重新获取columns
const handleRadioChange = value => {
  proTable.value.pageable.pageNum = 1
  switch (value) {
    case '10':
      columns.value = columns_10
      break
    case '0':
      columns.value = columns_0
      break
    case '1':
      columns.value = columns_1
      break
    case '2':
      columns.value = columns_2
      break
    case '3':
      columns.value = columns_3
      break
    case '11':
      columns.value = columns_11
      break
  }
  show.value = false
  nextTick(() => {
    show.value = true
  })
  initParam.tabType = value
}

// getTabList 获取tab下的列表
const getTabList = async data => {
  console.log('data', data)
  switch (data.tabType) {
    case '10':
      return clueAnalyseStaffAll(data)
    case '0':
      return clueAnalyseStaffPlatform(data)
    case '1':
      return clueAnalyseStaffDirect(data)
    case '2':
      return clueAnalyseStaffCustomerIntroduction(data)
    case '3':
      return clueAnalyseStaffChannel(data)
    case '11':
      return clueAnalyseStaffOther(data)
  }
}

/** 查询部门下拉树结构 */
const getDeptTree = async () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await deptTreeSelect({})
    resolve({ data })
  })
}
// 表格配置项
const columns_10 = [
  {
    prop: 'index',
    label: '序号',
    width: 70,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '人员姓名',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'deptName',
    minWidth: 150,
    label: '所属部门'
  },
  {
    prop: 'deptId',
    minWidth: 150,
    label: '所属部门',
    isShow: false,
    enum: getDeptTree,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true, props: { value: 'id', label: 'label', children: 'children' } }
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索总量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    // 线索转化率：成交量/线索量
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'dealDays',
    minWidth: 150,
    label: '总成交天数',
    render: scope => {
      return <span>{scope.row.dealDays || 0} 天</span>
    }
  },
  {
    prop: 'avgDealDays',
    minWidth: 150,
    label: '平均成交天数',
    render: scope => {
      return <span>{scope.row.avgDealDays || 0} 天</span>
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '收款额',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  }
]
const columns_0 = [
  {
    prop: 'index',
    label: '序号',
    width: 70,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '人员姓名',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'deptName',
    minWidth: 150,
    label: '所属部门'
  },
  {
    prop: 'deptId',
    minWidth: 150,
    label: '所属部门',
    isShow: false,
    enum: getDeptTree,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true, props: { value: 'id', label: 'label', children: 'children' } }
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'appealCount',
    minWidth: 150,
    label: '申述量',
    render: scope => {
      return <span>{scope.row.appealCount || 0} 条</span>
    }
  },
  {
    prop: 'appealSuccessCount',
    minWidth: 150,
    label: '申述成功',
    render: scope => {
      return <span>{scope.row.appealSuccessCount || 0} 条</span>
    }
  },
  {
    prop: 'credibleClueCount',
    minWidth: 150,
    label: '有效线索',
    render: scope => {
      return <span>{scope.row.credibleClueCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索有效率',
    render: scope => {
      return (
        <span>
          {scope.row.credibleClueCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.credibleClueCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    // 线索转化率：成交量/线索量
    render: scope => {
      // return <span>{scope.row.clueCount > 0 ? scope.row.clueConversionCount / scope.row.clueCount : 0}</span>
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '有效线索转化率',
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.credibleClueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.credibleClueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '收款额',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  },
  {
    prop: 'cost',
    minWidth: 150,
    label: '线索成本',
    render: scope => {
      return <span>{scope.row.cost || 0} 元</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: 'ROI',
    // ROI：收款额/线索成本
    render: scope => {
      // return <span>{scope.row.cost > 0 ? scope.row.gmv / scope.row.cost : 0}</span>
      return (
        <span>
          {scope.row.gmv > 0 && scope.row.cost > 0 ? `${multiply(divide(scope.row.gmv, scope.row.cost), 100).toFixed(2)}%` : '--'}
        </span>
      )
    }
  }
]
const columns_1 = [
  {
    prop: 'index',
    label: '序号',
    width: 70,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '人员姓名',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'deptName',
    minWidth: 150,
    label: '所属部门'
  },
  {
    prop: 'deptId',
    minWidth: 150,
    label: '所属部门',
    isShow: false,
    enum: getDeptTree,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true, props: { value: 'id', label: 'label', children: 'children' } }
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    // 线索转化率：成交量/线索量
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '收款额',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  },
  {
    prop: 'cost',
    minWidth: 150,
    label: '线索成本',
    render: scope => {
      return <span>{scope.row.cost || 0} 元</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: 'ROI',
    // ROI：收款额/线索成本
    render: scope => {
      return (
        <span>
          {scope.row.gmv > 0 && scope.row.cost > 0 ? `${multiply(divide(scope.row.gmv, scope.row.cost), 100).toFixed(2)}%` : '--'}
        </span>
      )
    }
  }
]
const columns_2 = [
  {
    prop: 'index',
    label: '序号',
    width: 70,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '人员姓名',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'deptName',
    minWidth: 150,
    label: '所属部门'
  },
  {
    prop: 'deptId',
    minWidth: 150,
    label: '所属部门',
    isShow: false,
    enum: getDeptTree,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true, props: { value: 'id', label: 'label', children: 'children' } }
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    // 线索转化率：成交量/线索量
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '收款额',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  }
]
const columns_3 = [
  {
    prop: 'index',
    label: '序号',
    width: 70,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '人员姓名',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'deptName',
    minWidth: 150,
    label: '所属部门'
  },
  {
    prop: 'deptId',
    minWidth: 150,
    label: '所属部门',
    isShow: false,
    enum: getDeptTree,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true, props: { value: 'id', label: 'label', children: 'children' } }
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    // 线索转化率：成交量/线索量
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '收款额',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  }
]
const columns_11 = [
  {
    prop: 'index',
    label: '序号',
    width: 70,
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'name',
    minWidth: 150,
    label: '人员姓名',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'deptName',
    minWidth: 150,
    label: '所属部门'
  },
  {
    prop: 'deptId',
    minWidth: 150,
    label: '所属部门',
    isShow: false,
    enum: getDeptTree,
    search: {
      el: 'tree-select',
      props: { 'default-expand-all': true, filterable: true, props: { value: 'id', label: 'label', children: 'children' } }
    }
  },
  {
    prop: 'yearMonth',
    isShow: false,
    label: '分析月份',
    search: {
      el: 'date-picker',
      defaultValue: dayjs().format('YYYY-MM'),
      props: {
        type: 'month',
        valueFormat: 'YYYY-MM',
        format: 'YYYY-MM'
      }
    }
  },
  {
    prop: 'clueCount',
    minWidth: 150,
    label: '线索量',
    render: scope => {
      return <span>{scope.row.clueCount || 0} 条</span>
    }
  },
  {
    prop: 'clueConversionCount',
    minWidth: 150,
    label: '成交量',
    render: scope => {
      return <span>{scope.row.clueConversionCount || 0} 条</span>
    }
  },
  {
    prop: 'xxx',
    minWidth: 150,
    label: '线索转化率',
    // 线索转化率：成交量/线索量
    render: scope => {
      return (
        <span>
          {scope.row.clueConversionCount > 0 && scope.row.clueCount > 0
            ? `${multiply(divide(scope.row.clueConversionCount, scope.row.clueCount), 100).toFixed(2)}%`
            : '--'}
        </span>
      )
    }
  },
  {
    prop: 'gmv',
    minWidth: 150,
    label: '收款额',
    render: scope => {
      return <span>{scope.row.gmv || 0} 元</span>
    }
  }
]

const columns = ref(columns_0)
</script>
<style lang="scss" scoped></style>
