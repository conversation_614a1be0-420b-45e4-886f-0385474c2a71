<!--
 * @Description: 通知公告
 * @Author: thb
 * @Date: 2023-07-20 09:58:06
 * @LastEditTime: 2023-11-07 15:50:40
 * @LastEditors: thb
-->
<template>
  <ProTable ref="proTable" title="通知公告" :columns="columns" :request-api="getMessageNoticeList">
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
    </template>

    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
      <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
      <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
    </template>

    <template #scopeDepts="{ row }">
      <span>{{ row.scopeType === '1' ? '全部用户' : row.scopeDepts || '--' }}</span>
    </template>
  </ProTable>
</template>
<script setup lang="tsx">
import { ref } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialog'
import { useHandleData } from '@/hooks/useHandleData'
import {
  getMessageNoticeList,
  saveMessageNotice,
  getMessageNoticeDetail,
  deleteMessageNotice
} from '@/api/message-center/message-center'
import noticeForm from './components/notice-form.vue'
import { changeFileList } from '@/utils/common'
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: 'title',
    label: '标题',
    width: 300,
    search: { el: 'input' }
  },

  {
    prop: 'scopeDepts',

    label: '发送范围'
  },
  {
    prop: 'createTime',

    label: '发布时间'
  },
  {
    prop: 'createBy',
    label: '发布人',

    search: {
      el: 'input'
    }
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right'
  }
]

const { showDialog } = useDialog()

const proTable = ref()
const submitCallback = () => {
  // 刷新列表
  proTable.value?.getTableList()
}
// handleDelete
const handleDelete = async (row: any) => {
  await useHandleData(deleteMessageNotice, row.id, `删除所选公告 ${row.title} 信息`)
  proTable.value?.getTableList()
}
// handleDetail
const handleConvertParams = (data: any) => {
  data.type = 'detail'
  console.log('data', data)
}
const handleDetail = (row: any) => {
  showDialog({
    title: '通知详情',
    customClass: 'notice-dialog',
    requestParams: row.id,
    cancelButtonText: '关闭',
    showConfirmButton: false,
    // confirmButtonText: '发布',
    component: noticeForm, // 表单组件
    getApi: getMessageNoticeDetail,
    handleConvertParams
  })
}

const handleConvertParams1 = (data: any) => {
  data.type = 'edit'
  console.log('data', data)
}
// handleEdit
const handleEdit = (row: any) => {
  showDialog({
    title: '编辑通知',
    customClass: 'notice-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '发布',
    component: noticeForm, // 表单组件
    requestParams: row.id,
    handleConvertParams: handleConvertParams1,
    getApi: getMessageNoticeDetail,
    submitApi: saveMessageNotice, // 提交api
    submitCallback, // 提交成功之后的回调函数
    handleRevertParams // 处理提交参数
  })
}
const handleRevertParams = (data: any) => {
  if (data.notificationFiles && data.notificationFiles.length) {
    data.notificationFiles = changeFileList(data.notificationFiles)
  }
}
// handleAdd
const handleAdd = () => {
  showDialog({
    title: '新增通知',
    customClass: 'notice-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '发布',
    component: noticeForm, // 表单组件
    submitApi: saveMessageNotice, // 提交api
    submitCallback, // 提交成功之后的回调函数
    handleRevertParams // 处理提交参数
  })
}
</script>
<style lang="scss" scoped></style>
