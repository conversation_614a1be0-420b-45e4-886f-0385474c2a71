<template>
  <el-form ref="formRef" :hide-required-asterisk="disabled" :model="data" :rules="rules" label-width="120px" label-position="top">
    <el-checkbox-group v-model="data.stageNameList">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-checkbox
            label="资料递交"
            @change="changeCheckbox1"
            :disabled="data.permitProcessRecordList.filter(item => item.stageName === '资料递交')[0]?.completeTime || disabled"
          />
          <el-form-item label=" " prop="stageName1">
            {{ data.permitProcessRecordList.filter(item => item.stageName === '资料递交')[0]?.completeTime?.replace('T', ' ') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-checkbox
            label="颁发证件"
            @change="changeCheckbox2"
            :disabled="data.permitProcessRecordList.filter(item => item.stageName === '颁发证件')[0]?.completeTime || disabled"
          />
          <el-form-item label=" " prop="stageName2">
            {{
              data.permitProcessRecordList.filter(item => item.stageName === '颁发证件')[0]?.completeTime?.replace('T', ' ')
            }}</el-form-item
          ></el-col
        >
      </el-row>
    </el-checkbox-group>
  </el-form>
</template>
<script setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        copyOfLicenseFileList: [],
        officialSealFileList: []
      }
    }
  }
})

const disabled = inject('disabled')
const changeCheckbox1 = value => {
  props.data.stageName1 = value ? '资料递交' : ''
}

const changeCheckbox2 = value => {
  props.data.stageName2 = value ? '颁发证件' : ''
}
// 证照办理校验规则
const rules = {
  stageName1: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  stageName2: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
const formRef = ref()

const validateForm = async () => {
  const result = await formRef.value.validate(valid => {
    if (valid) {
      console.log('检验成功')
    } else {
      console.log('校验失败')
      return false
    }
  })
  return result
}

const validateCheckedForm = async () => {
  return true
}
defineExpose({
  formRef,
  rules,
  validateForm,
  validateCheckedForm
})
</script>
<style lang="scss" scoped>
:deep(.el-checkbox) {
  margin-right: 8px;
}
:deep(.el-col.el-col-12.is-guttered) {
  display: flex;
  align-items: baseline;
  .el-form-item {
    flex: 1;
  }
}
</style>
