<template>
  <ProTable
    ref="proTable"
    row-key="id"
    :columns="columns"
    :request-api="cusSourceTree"
    :dataCallback="dataCallback"
    :pagination="false"
    :transformRequestParams="transformRequestParams"
    @sort-change="sortChange"
  >
    <template #enable="{ row }">
      <span> <el-switch active-value="1" inactive-value="0" :model-value="row.enable" @click="handleChangeEnable(row)" /></span>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
      <el-button
        v-if="scope.row.parentId === '0' && ['1', '2', '4'].includes(scope.row.id)"
        type="primary"
        link
        @click="handleAdd(scope.row)"
        >新增</el-button
      >
      <el-button
        v-if="!['1', '2', '3', '4'].includes(scope.row.id) && !scope.row.children?.length"
        type="danger"
        link
        @click="handleDelete(scope.row)"
        >删除</el-button
      >
    </template>
  </ProTable>
</template>
<script setup lang="tsx">
import { ref } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  cusSourceDelete,
  cusSourceTree,
  cusSourceSaveOrUpdate,
  cusSourceDetail,
  cusSourceEnable
} from '@/api/material-manage/source'
import { ColumnProps } from '@/components/ProTable/interface'
import { CirclePlus, Upload } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialogFinance'
import Form from './components/form.vue'
import { useHandleData } from '@/hooks/useHandleData'
import { getReviewerTreeData } from '@/api/process/process'

const { proxy } = getCurrentInstance()
const getTreeData = () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await getReviewerTreeData()
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'name',
    label: '来源名称',
    align: 'left',
    search: { el: 'input' },
    sortable: 'custom',
    sortName: 'name'
  },
  {
    prop: 'clueCount',
    label: '线索数',
    width: 100
  },
  {
    prop: 'customerCount',
    label: '客户数',
    width: 100
  },
  {
    prop: 'remark',
    label: '备注',
    search: { el: 'input' }
  },
  {
    prop: 'enable',
    enum: [
      {
        label: '正常',
        value: '1'
      },
      {
        label: '禁用',
        value: '0'
      }
    ],
    search: { el: 'select' },
    label: '状态',
    width: 100,
    sortable: 'custom',
    sortName: 'enable'
  },
  {
    prop: 'createBy',
    label: '创建人',
    width: 100,
    enum: getTreeData,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'label',
          label: 'label',
          children: 'children',
          defaultProps: {
            value: 'label',
            label: 'label',
            children: 'children',
            disabled: (data, node) => {
              return data.type === '0' && Array.isArray(data.children) && data.children.length === 0
            }
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.createBy || '--'}</span>
    },
    sortable: 'custom',
    sortName: 'create_by'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180,
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'create_time'
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 150
  }
]

// 自定义
const transformRequestParams = (data: any) => {
  if (data.createTime) {
    data.createTimeStart = data.createTime[0]
    data.createTimeEnd = data.createTime[1]
  }
}

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

function renameChildToChildren(items: any) {
  return items.map((item: any) => {
    if (item.child) {
      return {
        ...item,
        children: renameChildToChildren(item.child),
        child: undefined
      }
    }
    return item
  })
}
const dataCallback = (data: any) => {
  if (data) {
    data = renameChildToChildren(data)
    console.log('data', data)
    return data
  }
}

function handleChangeEnable(row: any) {
  console.log('handleChangeEnable', row)
  if (!row) return
  nextTick(() => {
    cusSourceEnable({ id: row.id }).then(() => {
      proxy.$message.success('操作成功')
      proTable.value?.search()
    })
  })
}

// 新增数据
const handleAdd = (row: any) => {
  showDialog({
    title: '新增',
    customClass: 'medium-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: Form, // 表单组件
    rowFormData: { parentId: row.id },
    submitApi: cusSourceSaveOrUpdate, // 提交api
    submitCallback: submitCallback // 提交成功之后的回调函数
  })
}

const { showDialog } = useDialog()

const proTable = ref()
const submitCallback = () => {
  proTable.value?.getTableList()
}

// 编辑数据
const handleEdit = (row: any) => {
  showDialog({
    title: '编辑',
    customClass: 'medium-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '提交',
    component: Form, // 表单组件
    requestParams: { id: row.id },
    getApi: cusSourceDetail,
    submitApi: cusSourceSaveOrUpdate, // 提交api
    submitCallback: submitCallback // 提交成功之后的回调函数
  })
}

// 删除数据
const handleDelete = async (row: any) => {
  await useHandleData(cusSourceDelete, { id: row.id }, `删除所选 ${row.name} 的信息`)
  proTable.value?.getTableList()
}
</script>
<style lang="scss" scoped></style>
