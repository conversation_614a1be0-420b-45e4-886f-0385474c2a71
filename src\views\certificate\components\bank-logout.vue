<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-12-14 08:25:16
 * @LastEditTime: 2023-12-14 10:15:27
 * @LastEditors: thb
-->
<template>
  <el-form
    ref="formRef"
    :hide-required-asterisk="disabled"
    :disabled="disabled"
    :model="data"
    :rules="rules"
    label-width="120px"
    label-position="top"
  >
    <el-checkbox-group v-model="data.stageNameList">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-checkbox
            label="完成"
            @change="changeCheckbox2"
            :disabled="
              data.businessCancellationProcessRecordList?.filter(item => item.stageName === '完成')[0]?.completeTime || disabled
            "
          />
          <el-form-item label=" " prop="logoutStageName">
            {{
              data.businessCancellationProcessRecordList
                ?.filter(item => item.stageName === '完成')[0]
                ?.completeTime?.replace('T', ' ')
            }}</el-form-item
          ></el-col
        >
      </el-row>
    </el-checkbox-group>
  </el-form>
</template>
<script setup>
const props = defineProps({
  data: Object
})

const disabled = inject('disabled')
const changeCheckbox2 = value => {
  props.data.logoutStageName = value ? '完成' : ''
}
const rules = {
  logoutStageName: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

const formRef = ref()
const validateForm = async () => {
  const result = await formRef.value.validate(valid => {
    if (valid) {
      console.log('检验成功')
    } else {
      console.log('校验失败')
      return false
    }
  })
  return result
}

const validateCheckedForm = async () => {
  formRef.value.clearValidate()
  return true
}

defineExpose({
  formRef,
  rules,
  validateForm,
  validateCheckedForm
})
</script>
<style lang="scss" scoped>
:deep(.el-checkbox) {
  margin-right: 8px;
}
:deep(.el-col.el-col-12.is-guttered) {
  display: flex;
  align-items: baseline;
  .el-form-item {
    flex: 1;
  }
}
</style>
