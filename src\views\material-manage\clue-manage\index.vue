<template>
  <div class="main-wrap">
    <el-card style="margin-bottom: 20px; flex-shrink: 0">
      <div class="wrap">
        <div class="list-item">
          <div class="color-blue">
            总计创建线索：<span>{{ topData.totalCreate }} </span> 条
          </div>
          <div class="color-blue">
            其中有 <span>{{ topData.totalCreateWaitAssign }}</span> 条在公海中待领取/分配
          </div>
        </div>
        <div class="list-item">
          <div class="color-blue">
            本日创建线索：<span>{{ topData.todayCreate }} </span> 条
          </div>
          <div class="color-blue">
            其中有 <span>{{ topData.todayCreateWaitAssign }}</span> 条在公海中待领取/分配
          </div>
        </div>
      </div>
    </el-card>
    <ProTable
      v-if="show"
      ref="proTable"
      title="线索管理"
      :initParam="initParam"
      :columns="columns"
      :request-api="getTabList"
      :transformRequestParams="transformRequestParams"
      @sort-change="sortChange"
    >
      <!-- 表格tabs -->
      <template #tabs>
        <el-radio-group :model-value="initParam.tabType" @change="handleRadioChange">
          <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dicValue">{{
            item.dictLabel
          }}</el-radio-button>
        </el-radio-group></template
      >
      <template #contactName="{ row }">
        <span class="blue-text" @click="handleShowDetail(row)">{{ row.contactName }}</span>
      </template>
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
        <el-button :icon="Download" @click="handleExport" v-hasPermi="['material-manage:clue-manage:export']">导出</el-button>
      </template>
    </ProTable>
    <ClueDetail
      v-if="detailShow"
      :id="clueId"
      :tabType="tabType"
      @on-close="handleClose"
      @on-edit="handleEditClue"
      @on-success="getList"
    />
  </div>
</template>
<script setup lang="tsx">
import {
  getMyClueList,
  getSharedClueList,
  getClueDetail,
  saveClue,
  getFollowedClueList,
  cusCustomerOrClueAllClueListExport,
  cusCustomerOrClueMyClueListExport,
  cusCustomerOrClueShareClueListExport,
  getClueHeaderStatistic
} from '@/api/material-manage/clue'
import { ColumnProps } from '@/components/ProTable/interface'
import { useDic } from '@/hooks/useDic'
import { CirclePlus, Download } from '@element-plus/icons-vue'
import { useDialog } from '@/hooks/useDialog'
import ClueForm from './components/clue-form'
import ClueDetail from './components/clue-detail'
import useCommonStore from '@/store/modules/common'
import { cusSourceTree } from '@/api/material-manage/source'
import { getBusinessList } from '@/api/business/business'

const { proxy } = getCurrentInstance()
const useCommon = useCommonStore()
// 获取当前路由
const route = useRoute()
// const transformRequestParams = data => {
//   if (route.query.id) {
//     data.id = route.query.id
//   }
// }
// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ tabType: '1' })
const tabs = [
  {
    dicValue: '0',
    dictLabel: '跟进线索'
  },
  {
    dicValue: '1',
    dictLabel: '我的线索'
  },
  {
    dicValue: '2',
    dictLabel: '共享线索'
  }
]

// getTabList 获取tab下的列表
const getTabList = async (data: any) => {
  // 我的线索查询
  if (data.tabType === '1') {
    const result1 = await getMyClueList(data)
    return result1
  } else if (data.tabType === '0') {
    const result3 = await getFollowedClueList(data)
    return result3
  } else {
    // 查询共享线索
    const result2 = await getSharedClueList(data)
    return result2
  }
}

const getList = () => {
  proTable.value?.getTableList()
}
const { getDic } = useDic()
const { showDialog } = useDialog()
const getTree = async () => {
  return new Promise(async (resolve, reject) => {
    const { data } = await cusSourceTree({ enable: 1 })
    // 将后端传回的数据结构进行转换
    const revertTreeData = (data = []) => {
      data.forEach(item => {
        item.label = item.name
        item.value = item.id
        item.children = item.child
        if (Array.isArray(item.children) && item.children.length) {
          revertTreeData(item.children)
        }
      })
    }
    revertTreeData(data)
    if (data) {
      resolve({
        data
      })
    } else {
      reject({
        data: []
      })
    }
  })
}
// 表格配置项
const defaultColumns = [
  {
    prop: 'index',
    width: 100,
    label: '序号',
    render: scope => {
      return <span>{scope.$index + 1}</span>
    }
  },
  {
    prop: 'contactName',
    width: 150,
    label: '姓名',
    search: {
      el: 'input'
    },
    sortable: 'custom',
    sortName: 'ccc.contact_name'
  },
  {
    prop: 'contactPhone',
    width: 200,
    label: '手机号',
    search: {
      el: 'input'
    },
    sortable: 'custom',
    sortName: 'ccc.contact_phone'
  },
  {
    prop: 'sourceId',
    width: 200,
    label: '线索来源',
    enum: getTree,
    search: {
      el: 'tree-select',
      props: {
        'default-expand-all': true,
        filterable: true,
        props: {
          value: 'id',
          label: 'name',
          children: 'child',
          disabled: data => {
            return data.child
          }
        }
      }
    },
    render: scope => {
      return <span>{scope.row.sourceName}</span>
    },
    sortable: 'custom',
    sortName: 'source.name'
  },

  {
    prop: 'followStatus',
    width: 150,
    enum: [
      {
        label: '未跟进',
        value: '0'
      },
      {
        label: '跟进中',
        value: '1'
      },
      {
        label: '已转企业',
        value: '2'
      },
      {
        label: '申述中',
        value: '3'
      }
    ],
    search: {
      el: 'select'
    },
    label: '跟进状态',
    sortable: 'custom',
    sortName: 'ccoc.follow_status'
  },
  {
    prop: 'lastFollowTime',
    width: 200,
    label: '最近跟进时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'ccoc.last_modified_time'
  },
  {
    prop: 'tagsName',
    width: 200,
    label: '标签'
  },
  {
    prop: 'remark',
    width: 250,
    label: '备注',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'createBy',
    width: 100,
    search: {
      el: 'input'
    },

    label: '创建人',
    sortable: 'custom',
    sortName: 'ccoc.create_by'
  },
  {
    prop: 'createTime',
    fixed: 'right',
    width: 200,
    label: '创建时间',
    search: {
      el: 'date-picker',
      props: { type: 'datetimerange', valueFormat: 'YYYY-MM-DD HH:mm:ss' }
    },
    sortable: 'custom',
    sortName: 'ccoc.create_time'
  },
  {
    prop: 'companyName',
    width: 200,
    label: '公司名称',
    search: {
      el: 'input'
    }
  },
  {
    prop: 'productId',
    width: 200,
    label: '业务类型',
    enum: () => {
      return new Promise(async (resolve, reject) => {
        const { data } = await getBusinessList({
          pageNum: 1,
          pageSize: 10000
        })
        // 将后端传回的数据结构进行转换
        const revertData = []
        data.forEach(item => {
          const obj = {
            label: item.typeName,
            value: item.id,
            children: []
          }
          revertData.push(obj)
          if (Array.isArray(item.child) && item.child.length) {
            item.child.forEach(child => {
              obj.children.push({
                label: child.productName,
                value: child.id // 产品类型id
              })
            })
          }
        })
        if (data) {
          resolve({
            data: revertData
          })
        } else {
          reject({
            data: []
          })
        }
      })
    },
    search: {
      el: 'tree-select'
    },
    render: scope => {
      return <span>{scope.row.productName || '--'}</span>
    }
  },
  {
    prop: 'taxNature',
    width: 200,
    label: '税务性质',
    enum: getDic('customer_property'),
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.taxNature || '--'}</span>
    }
  },
  {
    prop: 'firstFollowTime',
    width: 200,
    label: '首次跟进时间'
  },
  {
    prop: 'post',
    width: 200,
    label: '职位',
    sortable: 'custom',
    sortName: 'ccc.post'
  },
  {
    prop: 'industry',
    width: 200,
    label: '行业',
    sortable: 'custom',
    sortName: 'ccoc.industry'
  },
  {
    prop: 'contactPhone', // 后端意思列表里手机号电话号都用contactPhone
    width: 200,
    label: '电话',
    sortable: 'custom',
    sortName: 'ccc.contact_phone'
  },
  {
    prop: 'email',
    width: 200,
    label: '邮箱'
  },
  {
    prop: 'wx',
    width: 200,
    label: '微信',
    sortable: 'custom',
    sortName: 'ccc.wx'
  },
  {
    prop: 'qq',
    width: 200,
    label: 'QQ',
    sortable: 'custom',
    sortName: 'ccc.qq'
  },
  {
    prop: 'sex',
    width: 100,
    label: '性别',
    enum: [
      { label: '未知', value: '0' },
      { label: '男', value: '1' },
      { label: '女', value: '2' }
    ],
    sortable: 'custom',
    sortName: 'ccc.sex'
  },
  {
    prop: 'birthday',
    width: 200,
    label: '生日',
    sortable: 'custom',
    sortName: 'ccc.birthday'
  },
  {
    prop: 'area',
    width: 200,
    label: '地区',
    sortable: 'custom',
    sortName: 'ccoc.area'
  },
  {
    prop: 'address',
    width: 200,
    label: '详细地址',
    sortable: 'custom',
    sortName: 'ccoc.address'
  }
]
const columns: ColumnProps<any>[] = ref(defaultColumns)

const proTable = ref('')
// 提交成功之后的回调函数
const submitCallback = () => {
  proTable.value?.getTableList()
  getClueHeaderStatisticFunc()
}

const handleClose = () => {
  detailShow.value = false
  getList()
}

// 自定义参数
const handleRevertParams = (data: any) => {
  if (data.tags.length) {
    data.tags = data.tags.map(item => {
      if (item !== null && typeof item === 'object') {
        return {
          tagId: item.tagId
        }
      } else {
        return {
          tagId: item
        }
      }
    })
  }

  data.entryType = '0' // 手动录入
  data.type = '0' // 代表线索
}
// 新增线索
const handleAdd = () => {
  showDialog({
    title: '新增',
    customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: ClueForm, // 表单组件
    submitApi: saveClue, // 提交api
    handleRevertParams,
    submitCallback // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

const handleConvertParams = data => {
  data.tags = data.tags.map(item => item.tagId)
}
// 编辑线索
// const handleEdit = (row: any) => {
//   showDialog({
//     title: '编辑',
//     customClass: 'clue-dialog',
//     cancelButtonText: '取消',
//     confirmButtonText: '保存',
//     component: ClueForm, // 表单组件
//     getApi: getClueDetail,
//     requestParams: row.id,
//     handleConvertParams,
//     handleRevertParams,
//     submitApi: saveClue, // 提交api
//     submitCallback // 提交成功之后的回调函数
//   })
// }
// handleShowDetail 显示线索详情
const detailShow = ref(false)
const clueId = ref()
const tabType = ref('1') // 代表是我的线索 // '2'代表是共享线索
const handleShowDetail = row => {
  clueId.value = row.id
  tabType.value = initParam.tabType
  detailShow.value = true
}
// 编辑线索详情
const handleEditClue = id => {
  detailShow.value = false
  showDialog({
    title: '编辑',
    customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: ClueForm, // 表单组件
    getApi: getClueDetail,
    requestParams: id,
    handleConvertParams,
    handleRevertParams,
    submitApi: saveClue, // 提交api
    submitCallback // 提交成功之后的回调函数
  })
}

// 导出列表功能
const handleExport = async () => {
  const params = Object.assign({}, proTable.value.searchParam)
  if (params.lastFollowTime) {
    params.startLastFollowTime = params.lastFollowTime[0]
    params.endLastFollowTime = params.lastFollowTime[1]
    delete params.lastFollowTime
  }
  if (params.createTime) {
    params.startCreateTime = params.createTime[0]
    params.endCreateTime = params.createTime[1]
    delete params.createTime
  }
  let result
  if (initParam.tabType === '1') {
    result = await cusCustomerOrClueMyClueListExport(params)
  } else if (initParam.tabType === '0') {
    result = await cusCustomerOrClueAllClueListExport(params)
  } else {
    // 查询共享线索
    result = await cusCustomerOrClueShareClueListExport(params)
  }
  // const result = await downloadFinancePaymentExport({
  //   ...proTable.value.searchParam,
  //   // tab的参数
  //   tabType: initParam.tabType
  // })
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`导出成功，请至传输进度中进行查看!`)
  } else {
    proxy.$modal.msgError(`导出失败!`)
  }
}

watch(
  () => useCommon.id,
  () => {
    if (useCommon.id && useCommon.bizType === 'clue') {
      handleShowDetail({
        id: useCommon.id
      })
      useCommon.clearId()
      useCommon.clearBizType()
    }
  },
  {
    immediate: true
  }
)

const show = ref(true)
const handleRadioChange = value => {
  proTable.value.pageable.pageNum = 1
  initParam.tabType = value
  console.log(
    'handleRadioChange',
    value,
    initParam.tabType,
    columns.value.findIndex(item => item.label === '线索来源')
  )
  if (initParam.tabType === '1') {
    const index = columns.value.findIndex(item => item.label === '跟进人')
    if (index > 0) {
      columns.value.splice(index, 1)
    }
  } else {
    // 共享线索列表里没出现这个字段，是不是错误
    const index = columns.value.findIndex(item => item.label === '线索来源')
    const index_currentUserName = columns.value.findIndex(item => item.label === '跟进人')
    if (index > 0 && index_currentUserName === -1) {
      columns.value.splice(index + 1, 0, {
        prop: 'currentUserName',
        width: 150,
        isColShow: true,
        isShow: true,
        label: '跟进人',
        search: {
          el: 'input'
        },
        sortable: 'custom',
        sortName: 'su.nick_name'
      })
    }
  }
  show.value = false
  nextTick(() => {
    show.value = true
  })
}

const sortChange = ({ column, prop, order }) => {
  console.log('column', column)
  console.log('prop', prop)
  console.log('order', order)
  // proTable.value.search()
  if (order === 'descending') {
    // 降序 isAsc === 'desc'
    proTable.value.searchParam.isAsc = 'desc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else if (order === 'ascending') {
    // 升序 isAsc === 'asc'
    proTable.value.searchParam.isAsc = 'asc'
    proTable.value.searchParam.orderByColumn = columns.value.find(item => item.prop === prop)?.sortName
    proTable.value.search()
  } else {
    proTable.value.searchParam.isAsc = ''
    proTable.value.searchParam.orderByColumn = ''
    proTable.value.search()
  }
}

// 自定义
const transformRequestParams = (data: any) => {
  // 创建时间
  if (data.createTime) {
    data.createTimeStart = data.createTime[0]
    data.createTimeEnd = data.createTime[1]
  }

  if (data.lastFollowTime) {
    data.lastFollowTimeStart = data.lastFollowTime[0]
    data.lastFollowTimeEnd = data.lastFollowTime[1]
  }
}

// 线索管理页面头部统计
const topData = ref({})
function getClueHeaderStatisticFunc() {
  getClueHeaderStatistic().then(res => {
    topData.value = res.data
  })
}
getClueHeaderStatisticFunc()
</script>
<style lang="scss" scoped>
.main-wrap {
  display: flex;
  width: 100%;
  flex-direction: column;
}
.wrap {
  display: flex;
  gap: 8px;
  .list-item {
    flex: 1;
    height: 72px;
    padding: 7px 12px;
    background: #eff7ff;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}
.color-blue {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #2383e7;
  margin-bottom: 8px;
  span {
    color: #333;
  }
}
</style>
