<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-11-13 10:11:47
 * @LastEditTime: 2023-11-17 13:34:04
 * @LastEditors: thb
-->
<template>
  <template v-if="list.length">
    <div class="download-text" v-for="(file, index) in list" :key="index" @click="downloadFile(file)">
      <span class="file-name"> {{ file?.fileNames || '暂无文件' }}</span>
      <span v-if="isDownload" class="blue-text load-btn" @click.stop="handleDownload(file)"> 下载</span>
    </div>
  </template>
  <template v-else>
    <span class="download-text"> 暂无文件 </span>
  </template>
  <iFrame :src="previewUrl" v-model="previewShow" />
</template>
<script setup>
import iFrame from '@/components/iFrame'
import { getFileUrlByOss } from '@/api/file/file.js'
defineProps({
  list: {
    type: Array,
    default: () => {
      return []
    }
  },
  isDownload: {
    type: Boolean,
    default: false
  }
})

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
// 音频的格式mp3、m4a、wav
const mpList = ['mp3', 'wav', 'm4a']
const downloadFile = file => {
  if (file && (file.name || file.fileNames)) {
    const name = file.name || file.fileNames
    // 判断 文件后缀 如果是音频 不需要预览
    const fileName = name.split('.')
    const fileExt = fileName[fileName.length - 1]
    if (mpList.includes(fileExt)) return
  }
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

// 下载文件handleDownload
const handleDownload = async file => {
  const { data } = await getFileUrlByOss(file.urls)
  window.open(data)
}
</script>
<style lang="scss" scoped>
.download-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.load-btn {
  flex: 0 0 35px;
  margin-left: 10px;
}
</style>
