<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-12-14 08:25:16
 * @LastEditTime: 2023-12-14 11:08:34
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="data" label-width="120px" :disabled="isDisabled" label-position="top">
    <el-alert
      title="在此处填写或上传的信息将覆盖至企业档案中，仅填写发生变更的信息即可"
      type="info"
      show-icon
      :closable="false"
    />
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="注册地址">
          <el-input v-model="data.newRegisteredAddress" :disabled="isDisabled" maxlength="250" type="textarea" placeholder=" " />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="经营范围">
          <el-input v-model="data.newScope" maxlength="1000" :disabled="isDisabled" type="textarea" placeholder=" " />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="注册资金">
          <el-input v-model="data.newRegisteredCapital" :disabled="isDisabled" maxlength="100" placeholder=" " />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="股份比例">
          <el-input v-model="data.newShare" :disabled="isDisabled" maxlength="20" placeholder=" " />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="营业执照">
          <FileUpload v-if="!isDisabled" v-model="data.newBusinessLicenseFileList" :isShowTip="false" :limit="100" />
          <FileList v-else :list="data.newBusinessLicenseFileList" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="地址">
          <FileUpload v-if="!isDisabled" v-model="data.newAddressFileList" :isShowTip="false" :limit="100" />
          <FileList v-else :list="data.newAddressFileList" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import FileUpload from '@/components/FileUpload'
import FileList from './file-list'
const isDisabled = inject('disabled')
const props = defineProps({
  data: Object
})

const formRef = ref()

const validateForm = async () => {
  const result = await formRef.value.validate(valid => {
    if (valid) {
      console.log('检验成功')
    } else {
      console.log('校验失败')
      return false
    }
  })
  return result
}

const validateCheckedForm = async () => {
  return true
}
defineExpose({
  formRef,
  validateForm,
  validateCheckedForm
})
</script>
<style lang="scss" scoped></style>
