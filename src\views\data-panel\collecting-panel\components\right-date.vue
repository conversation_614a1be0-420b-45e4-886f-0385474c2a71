<!--
 * @Description: 数据面板right 选择框
 * @Author: thb
 * @Date: 2023-09-04 10:55:56
 * @LastEditTime: 2023-09-15 15:27:46
 * @LastEditors: thb
-->
<template>
  <el-date-picker v-model="selectValue" placeholder="请选择" v-bind="$attr" />
</template>
<script setup>
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emits = defineEmits('update:modelValue')
const selectValue = computed({
  get: () => {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  }
})
</script>
<style lang="scss" scoped></style>
