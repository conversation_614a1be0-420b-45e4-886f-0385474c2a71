<template>
  <ProTable
    v-if="columnsShow"
    :init-param="initParam"
    ref="proTable"
    title="资料库存"
    row-key="id"
    :columns="columns"
    :request-api="materialStockRecordList"
    :dataCallback="dataCallback"
  >
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <!-- 增加导入联系人信息 -->
      <el-button :icon="Upload" @click="handleImport">导入</el-button>
    </template>
    <!-- 表格 header 按钮 -->
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #detailList="{ row }">
      <span v-for="(item, index) in row.detailList" :key="item.id">
        <span>{{ item.category }}</span>
        <span v-if="index < row.detailList.length - 1">，</span>
      </span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handlDetail(scope.row)">详情</el-button>
      <el-button v-hasPermi="['customer:material-stock:edit']" type="primary" link @click="handlEdit(scope.row)">编辑</el-button>
    </template>
  </ProTable>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
  <ImportExcel ref="dialogRef" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { materialStockRecordList, materialInboundRecordImportMaterial } from '@/api/customer/material.js'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from './components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
import { useDic } from '@/hooks/useDic'
import useCommonStore from '@/store/modules/common'
import { CirclePlus, Upload } from '@element-plus/icons-vue'
import ImportExcel from '@/components/ImportExcel/index.vue'

const userStore = useUserStore()
// console.log('userStore?.user?.userId', userStore?.user?.userId)
const useCommon = useCommonStore()
const { getDic } = useDic()
const { proxy } = getCurrentInstance()

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}
const columnsShow = ref(false)
materialStockRecordList().then(res => {
  const list = res.data.records?.[0]?.stockList
  // console.log('stockList', list)
  list.forEach((item: any) => {
    columns.push({
      prop: `${item.category}_stockNum`,
      label: item.category,
      width: 150
    })
  })
  // console.log('columns', columns)
  columnsShow.value = true
})
const dataCallback = (data: any) => {
  if (data) {
    data.records.forEach((item: any) => {
      item.stockList.forEach((itemx: any) => {
        item[`${itemx.category}_stockNum`] = itemx.stockNum
      })
    })
    // data.records[0]['营业执照正本_stockNum'] = 5
    // console.log('data.records[0]', data.records[0])
    return data
  }
}
const initParam = reactive({}) // customerName: '浙江哈曼机电科技有限公司'
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'customerName',
    label: '企业名称',
    search: { el: 'input' },
    minWidth: 300
  },
  {
    prop: 'customerNo',
    label: '企业编号',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 150
  }
]

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handlDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}
const handlEdit = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onEdit(row)
  })
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

// 导入
const dialogRef = ref<InstanceType<typeof ImportExcel> | null>(null)
const handleImport = () => {
  const params = {
    title: '资料库存',
    tempApiUrl: '/资料库存导入.xlsx',
    // tempApi: exportUserInfo, // 下载模板接口
    importApi: materialInboundRecordImportMaterial, // 导入家口
    getTableList: proTable.value?.getTableList
  }
  dialogRef.value?.acceptParams(params)
}

// onMounted(() => {
// })
</script>
<style lang="scss" scoped></style>
