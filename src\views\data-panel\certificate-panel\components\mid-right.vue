<template>
  <dataWrap title="工本费" class="mid-right" :request-api="getCertificateNums">
    <template #default="{ data }">
      <div class="row-item">
        <div class="item-top">
          <span class="icon other-icon"></span>
          其它业务
        </div>
        <div class="item-bottom">
          <span class="number text-blue">{{ data.otherBusiness || '--' }}</span>
          <span class="unit">件</span>
        </div>
      </div>
    </template>
  </dataWrap>
</template>
<script setup>
import dataWrap from '../../collecting-panel/components/data-wrap'
import { getCertificateNums } from '@/api/panel-data/certificate'
</script>
<style lang="scss" scoped>
.mid-right {
  flex: 1;
}

.row-item {
  flex: 1;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  padding: 10px 12px;
  padding-bottom: 0;
}
.icon {
  margin-right: 8px;
}

.item-top {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #333333;
  // margin-bottom: 12px;
}

.item-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.number {
  font-size: 24px;
  font-family: SourceHanSansCN-Bold, SourceHanSansCN;
  font-weight: bold;
  margin-right: 4px;
  color: #45a0ff;
}
.unit {
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #7d8592;
}
</style>
