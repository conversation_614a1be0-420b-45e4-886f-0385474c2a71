<template>
  <ProTable :init-param="initParam" ref="proTable" row-key="id" :columns="columns" :request-api="getAddressLessorList">
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button v-hasPermi="['address-report:leaser-manage:add']" type="primary" :icon="CirclePlus" @click="handleAdd"
        >新增</el-button
      >
    </template>
    <!-- 表格 header 按钮 -->
    <template #lessorName="{ row }">
      <span class="blue-text" @click="handlDetail(row)">{{ row.lessorName }}</span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button v-hasPermi="['address-report:leaser-manage:edit']" type="primary" link @click="handlEdit(scope.row)"
        >编辑</el-button
      >
      <el-button v-hasPermi="['address-report:leaser-manage:delete']" type="danger" link @click="handleDelete(scope.row)"
        >删除</el-button
      >
    </template>
  </ProTable>
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { getAddressLessorList, deleteAddressLessorDelete } from '@/api/address-report/index.js'
import { removeAddressProvider } from '@/api/address-provider'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from './components/form-modal.vue'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { useHandleData } from '@/hooks/useHandleData'

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}

const initParam = reactive({}) // x
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'lessorName',
    label: '出租人名称',
    search: { el: 'input' },
    minWidth: 300
  },
  {
    prop: 'phone',
    label: '出租人手机号',
    minWidth: 150
  },
  {
    prop: 'bankName',
    label: '银行',
    minWidth: 150
  },
  {
    prop: 'bankAccount',
    label: '银行账户',
    minWidth: 150
  },
  {
    prop: 'propertyCount',
    label: '房本数量',
    minWidth: 150
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 150
  }
]

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handlDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}
const handleAdd = () => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onAdd()
  })
}
const handlEdit = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onEdit(row)
  })
}
const handleDelete = async (row: any) => {
  await useHandleData(deleteAddressLessorDelete, row, `删除所选出租人 ${row.lessorName} 信息`)
  proTable.value?.getTableList()
}
// onMounted(() => {
// })
</script>
<style lang="scss" scoped></style>
