{"name": "ruoyi", "version": "3.8.5", "description": "三优CRM", "author": "若依", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@bpmn-io/properties-panel": "^2.2.1", "@element-plus/icons-vue": "2.0.10", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue-office/docx": "^1.1.3", "@vue/eslint-config-typescript": "^11.0.3", "@vueup/vue-quill": "1.1.0", "@vueuse/core": "9.5.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "0.27.2", "bpmn-js": "^13.2.0", "bpmn-js-properties-panel": "^2.1.0", "camunda-bpmn-moddle": "^7.0.1", "dayjs": "^1.11.7", "echarts": "5.4.0", "element-plus": "2.2.27", "file-saver": "2.0.5", "fuse.js": "6.6.2", "html-docx-js-typescript": "^0.1.5", "js-base64": "^3.7.5", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "lodash": "^4.17.21", "mathjs": "^11.8.2", "nprogress": "0.2.0", "pinia": "2.0.22", "print-js": "^1.6.0", "typescript": "^5.0.4", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-demi": "^0.14.5", "vue-router": "4.1.4", "vue-tsc": "^1.6.5", "vue3-eventbus": "^2.0.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.0", "@types/file-saver": "^2.0.5", "@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.14.0", "fast-glob": "3.3.0", "postcss-html": "^1.5.0", "prettier": "^2.8.8", "sass": "1.56.1", "stylelint": "^15.6.2", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.0.0", "stylelint-config-recommended-scss": "^12.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^33.0.0", "stylelint-config-standard-scss": "^9.0.0", "unplugin-auto-import": "0.11.4", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svg-icons": "2.0.1"}}