.
<template>
  <el-dialog align-center width="800" title="高级查询" :close-on-click-modal="false" v-model="visible" @close="handleClose">
    <el-form ref="formRef" :model="formData" label-position="top">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="手机号">
            <el-input v-model="formData['cc.phone']" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="统一社会信用代码">
            <el-input v-model="formData['biz_info.credi_code']" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="法定代表人">
            <el-input v-model="formData['biz_info.legal_person']" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="注册地址">
            <el-input v-model="formData['biz_info.registered_address']" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="联系方式">
            <el-input v-model="formData['biz_info.contract']" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="财税顾问">
            <!-- <el-input v-model="formData['manager_user.nick_name']" placeholder="请输入" /> -->
            <SelectTree
              v-model="formData['managerUserId']"
              placeholder="请选择"
              clearable
              filterable
              @on-node-click="handleChangeManger"
              @clear="clearManager"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主办会计">
            <!-- <el-input v-model="formData['sponsor_accounting_user.nick_name']" placeholder="请输入" /> -->
            <SelectTree
              v-model="formData['sponsorAccountingUserId']"
              placeholder="请选择"
              clearable
              filterable
              @on-node-click="handleChangeSponsorAccounting"
              @clear="clearSponsorAccounting"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分配主办会计时间">
            <el-date-picker
              v-model="formData['sponsorAccountingAllocationDate']"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户成功">
            <SelectTree
              v-model="formData['customerSuccessUserId']"
              placeholder="请选择"
              clearable
              filterable
              @on-node-click="handleChangeCustomerSuccess"
              @clear="clearCustomerSuccess"
            />
            <!-- <el-input v-model="formData['customer_success_user.nick_name']" placeholder="请输入" /> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分配客户成功时间">
            <el-date-picker
              v-model="formData['customerSuccessAllocationDate']"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开票员">
            <!-- <el-input v-model="formData['counselor_user.nick_name']" placeholder="请输入" /> -->
            <SelectTree
              v-model="formData['counselorUserId']"
              placeholder="请选择"
              clearable
              filterable
              @on-node-click="handleChangeCounselor"
              @clear="clearCounselor"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业成立时间">
            <el-date-picker
              v-model="formData['establishDate']"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import SelectTree from '@/components/SelectTree'
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-search'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  queryColumAndValue: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const formData = ref({})
watch(
  () => props.queryColumAndValue,
  () => {
    if (props.queryColumAndValue) {
      Object.assign(formData.value, props.queryColumAndValue)
    }
  },
  {
    immediate: true
  }
)

const handleChangeManger = node => {
  formData.value['manager_user.nick_name'] = node.label
}
const clearManager = () => {
  formData.value['manager_user.nick_name'] = ''
  formData.value['managerUserId'] = ''
}

const handleChangeSponsorAccounting = node => {
  formData.value['sponsor_accounting_user.nick_name'] = node.label
  // formData.value['sponsorAccountingUserId'] = node.id
}

const clearSponsorAccounting = () => {
  formData.value['sponsor_accounting_user.nick_name'] = ''
  formData.value['sponsorAccountingUserId'] = ''
}

const handleChangeCounselor = node => {
  formData.value['counselor_user.nick_name'] = node.label
}

const clearCounselor = () => {
  formData.value['counselor_user.nick_name'] = ''
  formData.value['counselorUserId'] = ''
}

const handleChangeCustomerSuccess = node => {
  formData.value['customer_success_user.nick_name'] = node.label
}

const clearCustomerSuccess = () => {
  formData.value['customer_success_user.nick_name'] = ''
  formData.value['customerSuccessUserId'] = ''
}
const handleSearch = () => {
  handleClose()
  emits('on-search', formData.value)
}
</script>
<style lang="scss" scoped>
:deep(.el-select) {
  width: 100%;
}
</style>
