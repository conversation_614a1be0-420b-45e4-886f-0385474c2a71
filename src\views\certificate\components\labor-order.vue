<template>
  <el-form ref="formRef" v-bind="$attrs" :model="data" :rules="rules" label-width="120px" label-position="top">
    <el-row :gutter="24">
      <rowCheck
        v-for="(value, key) in formItemMap"
        :data="data"
        :key="key"
        :label="value"
        :labelKey="key"
        :flagKey="flagMap[key]"
        @on-preview="previewFile"
        @on-load-success="validateFormFiled"
      ></rowCheck>
    </el-row>
  </el-form>
</template>
<script setup>
import { FormValidators } from '@/utils/validate'
import rowCheck from './row-check'
defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const formItemMap = {
  copyOfLicenseFileList: '执照正副本',
  officialSealFileList: '公章',
  legalIdentityFileList: '法人身份证',
  propertyOwnershipCertificateFileList: '房产证',
  leaseContractFileList: '租赁合同',
  laborDispatchManagementFileList: '劳务派遣管理制度',
  laborDispatchContractFileList: '劳务派遣合同范本',
  laborDispatchAgreementFileList: '劳务派遣协议书范本',
  officeEquipmentFileList: '办公设备设施清单',
  powerOfAttorneyFileList: '委托书',
  capitalVerificationReportFileList: '验资报告',
  managementInfoFileList: '与开展业务相适应的信息管理系统清单',
  associationArticlesFileList: '公司章程'
}

// flagMap // 默认是原有key+'Flag',但是保证有特殊的字段存在
const flagMap = {
  capitalVerificationReportFileList: 'capitalVerificationFileListFlag',
  propertyOwnershipCertificateFileList: 'propertyCertificateFileListFlag'
}
// 劳务派遣表单检验规则
const rules = {
  copyOfLicenseFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  officialSealFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  legalIdentityFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  propertyOwnershipCertificateFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  leaseContractFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  laborDispatchManagementFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  laborDispatchContractFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  laborDispatchAgreementFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  officeEquipmentFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],

  capitalVerificationReportFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  powerOfAttorneyFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  managementInfoFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  associationArticlesFileList: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
// 预览文件
const emits = defineEmits('on-preview')
const previewFile = file => {
  emits('on-preview', file)
}
const formRef = ref()

// 文件上传后检验
const validateFormFiled = field => {
  formRef.value.validateField(field)
}
defineExpose({
  formRef,
  rules,
  flagMap
})
</script>
<style lang="scss" scoped>
.el-checkbox {
  margin-right: 8px;
}
:deep(.el-col.el-col-12.is-guttered) {
  display: flex;
  align-items: baseline;
  .el-form-item {
    flex: 1;
  }
}
</style>
