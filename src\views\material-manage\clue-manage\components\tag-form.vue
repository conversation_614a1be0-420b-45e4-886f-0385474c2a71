<!--
 * @Description: 新增业务表格
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-08-28 09:23:46
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="标签" prop="tags">
          <el-select v-model="formData.tags" multiple placeholder="请选择">
            <el-option
              v-for="(item, index) in tagsList"
              :disabled="item.status === '0'"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import { getClueTagList } from '@/api/material-manage/tag'
const formData = reactive({
  tags: [],
  id: undefined
})
const rules = {
  tags: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
const tagsList = ref([])
const getTagsList = async () => {
  const { data } = await getClueTagList({
    pageSize: 10000,
    pageNum: 1
  })
  tagsList.value = data.records || []
}
getTagsList()
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
