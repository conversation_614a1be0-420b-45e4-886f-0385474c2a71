<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" :disabled="disabled">
    <el-form-item label="付款倾向" prop="receiveAnalyse">
      <el-tree-select
        ref="treeSelectRef"
        style="width: 100%"
        v-model="formData.receiveAnalyse"
        :props="{ value: 'totalName', label: 'name', children: 'children' }"
        filterable
        :data="receive_analyse"
        :render-after-expand="false"
        default-expand-all
        @current-change="node => handleSelectChange(node, row)"
      />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { useBasicDictAnalyse } from '@/utils/dict'
import { onMounted, watch } from 'vue'

const { proxy } = getCurrentInstance()
const { receive_analyse } = useBasicDictAnalyse('receive_analyse')

const formData = reactive({
  id: undefined,
  receiveAnalyse: undefined
})

const treeSelectRef = ref(null)
const confirmBtnRef = ref(null)
onMounted(() => {
  treeSelectRef.value.focus()
  confirmBtnRef.value = document.querySelector('.el-message-box__btns .el-button--primary') // 获取确认按钮DOM
  // console.log('confirmBtnRef.value', confirmBtnRef.value)
})

const handleSelectChange = async (node, row) => {
  if (node?.type === '业务类型' || node.children.length) return
  console.log('handleSelectChange', node, row)
  await nextTick()
  confirmBtnRef.value.click()
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

const rules = {
  receiveAnalyse: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
}

defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped></style>
