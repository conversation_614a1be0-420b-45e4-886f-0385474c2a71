/*
 * @Description: 工单管理api
 * @Author: thb
 * @Date: 2023-07-17 14:03:23
 * @LastEditTime: 2024-03-25 10:01:31
 * @LastEditors: thb
 */
import request from '@/utils/request'

// 我的发起 列表查询
export function getWorkListByMyCreate(params) {
  params.createTime = undefined
  params.completeTime = undefined
  return request({
    url: '/order/listMyCreate',
    method: 'get',
    params
  })
}

// 我的待办 列表查询
export function getWorkListByToDo(params) {
  params.createTime = undefined
  params.completeTime = undefined
  return request({
    url: '/order/listMyToDo',
    method: 'get',
    params
  })
}

// 工单指派提交
export function assignWorkOrder(data) {
  return request({
    url: '/order/save',
    method: 'post',
    data
  })
}

// 工单详情
export function getWorkOrderDetail(id) {
  return request({
    url: '/order/getById',
    method: 'get',
    params: {
      id
    }
  })
}
// 工单类型设置
export function addOrder(data) {
  return request({
    url: '/orderType/save',
    method: 'post',
    data
  })
}

// 工单类型列表查询
export function getOrderList(params) {
  return request({
    url: '/orderType/tree',
    method: 'get',
    params
  })
}

// 工单类型详情
export function getOrderDetail(id) {
  return request({
    url: '/orderType/getById',
    method: 'get',
    params: {
      id
    }
  })
}

// 删除工单类型
export function deleteOrder(id) {
  return request({
    url: '/orderType/delete',
    method: 'delete',
    params: {
      id
    }
  })
}

// 获取工单的下拉列表查询
export function getOrderDropdownList() {
  return request({
    url: '/orderType/getList',
    method: 'get'
  })
}
// 工单指派
export function orderAssign(data) {
  return request({
    url: '/order/orderAssign',
    method: 'post',
    data
  })
}

// 工单回退
export function orderBack(data) {
  return request({
    url: '/order/orderBack',
    method: 'post',
    data
  })
}

// 工单完成
export function orderComplete(data) {
  return request({
    url: '/order/orderComplete',
    method: 'post',
    data
  })
}
// 改变工单类型状态
export function changeOrderTypeStatus(id) {
  return request({
    url: '/orderType/setStatus',
    method: 'post',
    params: {
      id
    }
  })
}
// 由我指派的列表查询
export const getListMyAssign = params => {
  params.createTime = undefined
  params.completeTime = undefined
  return request({
    url: '/order/listMyAssign',
    method: 'get',
    params
  })
}
// 将标记异常状态 改为 待完成状态
export const changeOrderStatus = id => {
  return request({
    url: '/order/orderContinue',
    method: 'post',
    params: {
      orderId: id
    }
  })
}
// 标记异常功能
export const exceptionOrder = data => {
  return request({
    url: '/order/orderAbnormal',
    method: 'post',
    data
  })
}
// 我发起的 --查询列表全部
export const getListAll = params => {
  params.createTime = undefined
  params.completeTime = undefined
  return request({
    url: '/order/listAll',
    method: 'get',
    params
  })
}

// 我发起的--获取异常工单列表
export const getListException = params => {
  params.createTime = undefined
  params.completeTime = undefined
  return request({
    url: '/order/listAbnormal',
    method: 'get',
    params
  })
}

// 工单导出
export const orderExport = query => {
  query.createTime = undefined
  query.completeTime = undefined
  return request({
    url: '/order/export',
    method: 'post',
    data: query
  })
}

// 删除工单
export function deleteOrderById(id) {
  return request({
    url: '/order/delete',
    method: 'delete',
    params: {
      id
    }
  })
}

// /order/saveBatch 批量保存工单
export const orderSaveBatch = query => {
  return request({
    url: '/order/saveBatch',
    method: 'post',
    data: query
  })
}
