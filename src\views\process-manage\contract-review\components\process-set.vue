<template>
  <div class="process-wrap">
    <div class="step-list" :class="dynamicClass">
      <div v-for="(step, index) in viewProcess" :key="step" class="step-item">
        <!-- 如果是开始节点 -->
        <div v-if="step.type === 'startNode'" class="start-node">
          <span>{{ step.label }}</span>
          <!--  表示审核过程 -->
          <div v-if="isReview">
            <!-- 显示审批时间 -->
            {{ step.auditTime }}
          </div>
        </div>
        <div v-if="step.type === 'reviewNode'" class="review-node">
          <div class="review-title">审批人</div>

          <div class="review-name" v-if="type === 'detail' && !isReview">{{ step.reviewer || '--' }}</div>
          <template v-if="type !== 'detail'">
            <div class="blue-text review-name name-default d-flex" :class="[step.reviewer ? 'font-color' : '']">
              {{ step.reviewer || step.label }}
              <el-popover :visible="step.visible" placement="right" :width="300" popper-style="padding:0">
                <p class="p-title">
                  审批人设置
                  <span class="icon close-icon-small" @click="handleCancel(step)"></span>
                </p>
                <div class="p-content">
                  <el-tree-select
                    v-model="reviewer"
                    :props="defaultProps"
                    filterable
                    :data="reviewerData"
                    :render-after-expand="false"
                    @node-click="setReviewer"
                  />
                </div>
                <div class="p-footer">
                  <el-button @click="handleCancel(step)">取消</el-button>
                  <el-button type="primary" @click="handleConfirm(step)">确定</el-button>
                </div>
                <template #reference>
                  <!-- <el-button @click="visible = true">Delete</el-button> -->
                  <span class="icon arrow-icon-right" @click="showReviewerSelect(index, step)"> </span>
                </template>
              </el-popover>
            </div>
            <el-icon color="red" class="delete-icon" @click="removeReviewerNode(index)"><Delete /></el-icon>
          </template>
          <template v-if="isReview">
            <div class="review-content">
              <!-- 是否通过 -->
              <div class="review-middle">
                <span>{{ step.reviewer }}</span>
                <el-tag
                  v-if="step.reviewStatus"
                  :type="step.reviewStatus === '0' ? '' : step.reviewStatus === '1' ? 'success' : 'danger'"
                >
                  <!-- 是否通过 -->
                  {{ step.reviewStatus === '0' ? '待审批' : step.reviewStatus === '1' ? '通过' : '驳回' }}
                </el-tag>
              </div>

              <div class="review-bottom">
                <!-- 显示审批时间 -->
                {{ step.auditTime }}
              </div>
            </div>
          </template>
        </div>

        <div v-if="step.type === 'addNode' && type !== 'detail'" class="add-node" @click="addStepNode(index)">
          <!-- <span>{{ step.label }}</span> -->
          <el-icon color="#fff"><Plus /></el-icon>
        </div>
        <div v-if="step.type === 'endNode'" class="end-node">
          <span>{{ step.label }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- <div class="review-wrap" v-if="selectShow">
    <p class="p-title">审批人设置</p>
    <el-tree-select
      v-model="reviewer"
      :props="defaultProps"
      :data="reviewerData"
      :render-after-expand="false"
      @node-click="setReviewer"
    />
  </div> -->
</template>
<script setup>
import { getReviewerTreeData } from '@/api/process/process'
import { Delete } from '@element-plus/icons-vue'
const props = defineProps({
  modelValue: {
    type: Array
  },
  type: {
    type: String,
    default: 'add'
  },
  isHorizontal: {
    type: Boolean,
    default: false
  }, // 流程布局是否水平还是垂直
  isReview: {
    type: Boolean,
    default: false
  },
  createTime: {
    type: String,
    default: ''
  }
})

// 动态样式设置
const dynamicClass = computed(() => {
  return {
    'step-Horizontal': props.isHorizontal
  }
})

const emits = defineEmits(['update:modelValue'])
const processList = computed({
  get: () => {
    return props.modelValue
  },
  set(newVal) {
    emits('update:modelValue', newVal)
  }
})
console.log('processList', processList)
// 初始化流程
/*
  type === startNode 为起始节点 只有一个起始节点
  type === addNode : 为新增节点功能 新增一个审核节点
  type === reviewNode ： 为审核节点  审核人
  type === endNode : 为结束节点 只有一个结束节点
*/
const viewProcess = ref([])
const initViewProcess = () => {
  console.log('processList', processList.value)

  if (!processList.value.length) {
    // 创建初始节点
    const startNode = {
      type: 'startNode',
      label: '开始'
    }
    // 创建结束节点
    const endNode = {
      type: 'endNode',
      label: '结束'
    }
    // 创建新增节点
    const stepNode = {
      type: 'addNode',
      label: '新增'
    }
    // 默认有一个流程节点
    processList.value.push(stepNode)
    // 视图上的节点集合
    viewProcess.value = processList.value
    // 默认新增初始节点
    viewProcess.value.unshift(startNode)
    // 默认新增结束节点
    viewProcess.value.push(endNode)
  } else {
    // 视图上的节点集合

    const filterNodes = processList.value.filter(
      item => item.type === 'startNode' || item.type === 'endNode' || item.type === 'addNode'
    )
    // 如果不存在开始 或者 结束节点 或者 新增节点
    if (!filterNodes.length) {
      // 在编辑的情况下没有开始 结束 和新增节点主要 自己组装
      let arr2 = []
      // 将原有数组变成二维数组
      processList.value.forEach(node => {
        arr2.push([
          {
            ...node,
            label: '请设置审批人',
            reviewer: node.nickName,
            reviewerId: node.userId,
            type: 'reviewNode'
          }
        ])
      })
      console.log('将原有数组变成二维数组', arr2)
      // 修改二维数组
      arr2.forEach(node => {
        const addNode = {
          type: 'addNode',
          label: '新增'
        }
        node.unshift(addNode)
      })
      console.log('修改二维数组', arr2)
      // 将二维数组平铺
      while (arr2.some(item => Array.isArray(item))) {
        arr2 = [].concat(...arr2)
      }

      console.log('arr2', arr2)
      const startNode = {
        type: 'startNode',
        label: '开始',
        auditTime: props.createTime || undefined
      }
      // 创建结束节点
      const endNode = {
        type: 'endNode',
        label: '结束'
      }
      const addNode = {
        type: 'addNode',
        label: '新增'
      }
      arr2.unshift(startNode)
      arr2.push(addNode, endNode)
      processList.value = arr2
      viewProcess.value = arr2
    } else {
      viewProcess.value = processList.value
    }
  }
}
// 新增节点 type==reviewNode addNode
const addStepNode = addIndex => {
  console.log('addIndex', addIndex)

  // 增加一个审核节点以及新增节点
  // 创建新增节点
  const addNode = {
    type: 'addNode',
    label: '新增'
  }
  // 创建一个审核节点
  const reviewNode = {
    type: 'reviewNode',
    label: '请设置审核人'
  }
  viewProcess.value.splice(addIndex + 1, 0, reviewNode, addNode)
  // 重新将processList 赋值
  // processList.value = viewProcess.value
  emits('update:modelValue', viewProcess.value)
}

// 删除流程节点
const removeReviewerNode = index => {
  viewProcess.value.splice(index, 2)
}

// showReviewerSelect 显示审批人设置选择框
const reviewer = ref('')
const reviewerData = ref()
const selectShow = ref(false)
const defaultProps = {
  value: 'id',
  label: 'label',
  children: 'children'
}
let reviewerIndex
const visible = ref(false)
const showReviewerSelect = (index, step) => {
  // selectShow.value = true
  step.visible = true

  // 存储需要存的index
  reviewerIndex = index
  // 重置
  reviewer.value = ''
  console.log('reviewerIndex', reviewerIndex)
}
// 获取reviewerTreeData
const getReviewerData = async () => {
  const { data } = await getReviewerTreeData()
  reviewerData.value = data || []
}
// 节点设置审核人
let selectReviewer = ''
let selectReviewerId = ''
const setReviewer = node => {
  console.log('setReviewer', node)
  if (node.type === '1') {
    // viewProcess.value[reviewerIndex].reviewer = node.label
    // viewProcess.value[reviewerIndex].reviewerId = node.id
    selectReviewer = node.label
    selectReviewerId = node.id
  }
}

const handleConfirm = step => {
  if (selectReviewer && selectReviewerId) {
    viewProcess.value[reviewerIndex].reviewer = selectReviewer
    viewProcess.value[reviewerIndex].reviewerId = selectReviewerId
    step.visible = false
  }
}

const handleCancel = step => {
  step.visible = false
  selectReviewer = ''
  selectReviewerId = ''
}

defineExpose({
  viewProcess
})
onMounted(() => {
  initViewProcess()
  getReviewerData()
})
</script>
<style lang="scss" scoped>
.start-node,
.end-node {
  width: 100px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid black;
  position: relative;
  height: 57px;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  border: 1px solid #b2b5b9;
}
.end-node {
  margin-top: 49px;
  &::before {
    content: '';
    display: inline-block;
    width: 2px;
    height: 50px;
    background-color: #b2b5b9ff;
    position: absolute;
    top: -50px;
  }
}
.start-node {
  &::after {
    content: '';
    display: inline-block;
    width: 2px;
    height: 50px;
    background-color: #b2b5b9ff;
    position: absolute;
    top: 57px;
  }
}
.review-node {
  width: 150px;
  // height: 55px;
  justify-content: center;
  align-items: center;
  // border: 1px solid black;
  display: flex;
  flex-direction: column;
  margin-top: 50px;
  position: relative;

  // height: 88px;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  &::before {
    content: '';
    display: inline-block;
    width: 2px;
    height: 50px;
    background-color: #b2b5b9ff;
    position: absolute;
    top: -51px;
  }
  &::after {
    content: '';
    display: inline-block;
    width: 2px;
    height: 50px;
    background-color: #b2b5b9ff;
    position: absolute;
    top: 88px;
  }
}
.add-node {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  // border: 1px solid black;
  background: #2383e7ff;
  display: flex;
  flex-direction: column;
  margin-top: 49px;
  z-index: 1;
  cursor: pointer;
}

.step-list {
  width: 150px;
}
.step-item {
  display: flex;
  justify-content: center;
}
.process-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f3f4f7;
  padding: 60px 24px;
}

.blue-text {
  color: #409eff;
  cursor: pointer;
}
.review-wrap {
  // position: absolute;
  // top: calc(40%);
  // right: 20px;
}
.p-title {
  font-size: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
  height: 56px;
  border-bottom: 1px solid #ddddddff;
  background: #ffffff;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0;
}
.p-content {
  padding: 8px 16px;
  border-bottom: 1px solid #ddddddff;
}

.p-footer {
  padding: 12px 20px;
  display: flex;
  justify-content: end;
}
.delete-icon {
  position: absolute;
  right: -40px;
  bottom: 10px;
  cursor: pointer;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
}

// 水平样式设置
.step-Horizontal {
  display: flex;
  align-items: center;
  width: 100%;
  overflow-x: auto;
  .start-node {
    width: 150px;
    flex-direction: column;
    justify-content: start;
    height: 85px;
    color: #ffffff;
    span {
      display: inline-block;
      width: 100%;
      padding: 8px 13px;
      // height: 35px;
      text-align: left;
      background: #b2b5b9;
    }
    div {
      flex: 1;
      display: flex;
      align-items: center;
      color: #333333;
    }
    &::after {
      content: '';
      display: inline-block;
      width: 50px;
      height: 2px;
      background-color: #b2b5b9ff;
      position: absolute;
      top: calc(50%);
      left: 149px;
    }
  }
  .end-node {
    margin-top: 0;
    margin-left: 50px;
    &::before {
      content: '';
      display: inline-block;
      width: 50px;
      height: 2px;
      background-color: #b2b5b9ff;
      position: absolute;
      // top: 21px;
      top: calc(50%);
      right: 98px;
    }
  }

  .review-node {
    width: 188px;
    // height: 80px;
    justify-content: center;
    align-items: center;
    // border: 1px solid black;
    display: flex;
    flex-direction: column;
    margin-top: 0;
    position: relative;

    margin-left: 50px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    &::before {
      content: '';
      display: inline-block;
      width: 50px;
      height: 2px;
      background-color: #b2b5b9ff;
      position: absolute;
      // top: 21px;
      display: none;
    }
    &::after {
      content: '';
      display: inline-block;
      width: 50px;
      height: 2px;
      background-color: #b2b5b9ff;
      position: absolute;
      top: calc(50%);
      left: 188px;
    }
  }
}

.yellow-text {
  color: #e6a23c;
}
.success-text {
  color: #67c23a;
}
.danger-text {
  color: #f56c6c;
}

.review-title {
  // height: 34px;
  background: #3183e2;
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #ffffff;
  width: 100%;
  padding: 6px 11px;
  border-radius: 4px;
}

.review-name {
  height: 54px;
  background: #fff;
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #333333;
  padding: 16px 11px;
}

.name-default {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #b2b5b9;
}

.d-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.font-color {
  color: #333333;
}

.review-content {
  padding: 12px 11px;
  width: 100%;
}
.review-middle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #333333;
  padding-bottom: 11px;
  border-bottom: 1px solid #f8f8f8ff;
}
.review-bottom {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  color: #b2b5b9;
  margin-top: 11px;
}
</style>
