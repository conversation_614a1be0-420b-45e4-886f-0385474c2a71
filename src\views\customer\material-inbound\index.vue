<template>
  <ProTable
    :init-param="initParam"
    ref="proTable"
    title="入库记录"
    row-key="id"
    :columns="columns"
    :request-api="materialInboundRecordList"
  >
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
    </template>
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #detailList="{ row }">
      <span v-for="(item, index) in row.detailList" :key="item.id">
        <span>{{ item.category }}</span>
        <span v-if="index < row.detailList.length - 1">，</span>
      </span>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button type="primary" link @click="handlDetail(scope.row)">详情</el-button>
    </template>
  </ProTable>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { materialInboundRecordList } from '@/api/customer/material.js'
import { ColumnProps } from '@/components/ProTable/interface'
import formModal from './components/form-modal.vue'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import dayjs from 'dayjs'
import useUserStore from '@/store/modules/user'
import { useDic } from '@/hooks/useDic'
import useCommonStore from '@/store/modules/common'
import { CirclePlus } from '@element-plus/icons-vue'

const userStore = useUserStore()
// console.log('userStore?.user?.userId', userStore?.user?.userId)
const useCommon = useCommonStore()
const { getDic } = useDic()
const { proxy } = getCurrentInstance()

const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}
const initParam = reactive({})
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: 'index', fixed: 'left', width: 50 }, // id
  {
    prop: 'customerName',
    label: '企业名称',
    search: { el: 'input' },
    minWidth: 300
  },
  {
    prop: 'customerNo',
    label: '企业编号',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'operatorUserName',
    label: '创建人',
    search: { el: 'input' },
    width: 150
  },
  {
    prop: 'inboundType',
    label: '入库类型',
    width: 150
  },
  {
    prop: 'detailList',
    label: '入库内容',
    minWidth: 400
  },
  {
    prop: 'operateTime',
    label: '创建时间',
    width: 180
  },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: 100
  }
]

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handleAdd = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onAdd()
  })
}

const handlDetail = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onDetail(row)
  })
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

// onMounted(() => {
// })
</script>
<style lang="scss" scoped></style>
