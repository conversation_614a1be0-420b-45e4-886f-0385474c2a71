<template>
  <el-dialog
    class="dialog-footer-1"
    align-center
    title="线索详情"
    width="1200"
    :close-on-click-modal="false"
    v-model="visible"
    @close="handleClose"
  >
    <div class="container-t">
      <span class="avatar-icon"></span>
      <span class="name">{{ detail.contactName }}</span>
      <!-- 进度状态 -->
      <div class="flex-1">
        <el-tag>{{
          detail.followStatus === '0'
            ? '未跟进'
            : detail.followStatus === '1'
            ? '跟进中'
            : detail.followStatus === '2'
            ? '已转企业'
            : '申述中'
        }}</el-tag>
      </div>
    </div>
    <!-- 20240112 此处按钮栏增加了按钮，一行排版放不下，按钮栏整体挪到新一行 -->
    <div class="btn-con">
      <template v-if="detail.sourceAppealStatus === '申述中'">
        <el-button @click="handleFailClueAppeal" type="danger" plain>
          <template #icon>
            <el-icon> <Close /> </el-icon
          ></template>
          申述失败</el-button
        >
        <el-button @click="handleSuccessClueAppeal" type="success" plain>
          <template #icon>
            <el-icon> <Check /> </el-icon
          ></template>
          申述成功</el-button
        >
      </template>
      <template v-if="tabType === '1' && detail.sourceAppealStatus !== '申述中'">
        <!-- 私有线索的操作 -->
        <template v-if="!isSea">
          <el-button @click="handleEdit">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon
            ></template>
            编辑线索</el-button
          >
          <el-button @click="handleShare">
            <template #icon>
              <el-icon color="#3DB954"> <Share /> </el-icon
            ></template>

            共享线索</el-button
          >
          <el-button @click="handleTransform">
            <template #icon>
              <el-icon color="#F5AC21"> <Switch /> </el-icon
            ></template>

            转为客户</el-button
          >
          <el-button @click="handleTransfer">
            <template #icon>
              <el-icon color="#F5AC21"> <Connection /> </el-icon
            ></template>
            转让线索</el-button
          >
          <el-button @click="handleRecycle">
            <template #icon>
              <el-icon color="#F5AC21"> <Refresh /> </el-icon
            ></template>

            回收公海</el-button
          >

          <el-button @click="handleAddBusiness">
            <template #icon>
              <el-icon color="#3DB954"> <CirclePlus /> </el-icon
            ></template>

            新增商机</el-button
          >

          <el-button
            @click="handleAddClueAppeal"
            v-if="detail.sourceBiz === '平台' && !detail.sourceAppealStatus && ['0', '1'].includes(detail.followStatus)"
          >
            <template #icon>
              <el-icon color="#F56C6C"> <Service /> </el-icon
            ></template>

            线索申述</el-button
          >

          <!-- 编辑附加信息 v-if="detail.isExtra"-->
          <el-button @click="handleEditExtra" v-if="detail.isExtra">
            <template #icon>
              <el-icon color="#F5AC21"> <EditPen /> </el-icon
            ></template>

            编辑附加信息</el-button
          >
        </template>

        <!-- 公有线索的操作 -->
        <template v-else>
          <el-button @click="handleEdit" v-if="userStore.user.userId === detail.manageId">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon
            ></template>
            编辑线索</el-button
          >

          <el-button @click="handleReceive" v-if="isCapableGet(isGet, detail.deptIds)">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon
            ></template>
            领取线索</el-button
          >
          <el-button @click="handleDistribute" v-if="isCapableDispatch(isDivide)">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon
            ></template>
            分配线索</el-button
          >

          <el-button @click="handleTransferToZone" v-if="userStore.user.userId === detail.manageId">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon
            ></template>
            转移公海</el-button
          >

          <el-button @click="handleDelete" v-if="userStore.user.userId === detail.manageId">
            <template #icon>
              <el-icon color="#2383E7FF"> <EditPen /> </el-icon
            ></template>
            删除线索</el-button
          >
        </template>
      </template>
    </div>
    <div class="container-c">
      <span class="flex-1"
        >公司名称：
        <span class="text-bold">{{ detail.companyName }}</span>
      </span>

      <span class="flex-1"
        >标签:
        <template v-if="!isSea">
          <span class="text-bold" v-if="detail.tagsName">{{ detail.tagsName }}</span>
          <span v-else class="blue-text" @click="showTag">
            <el-icon style="vertical-align: middle" color="#2383E7FF"> <EditPen /> </el-icon>打标签</span
          >
        </template>
        <template v-else>
          <span class="text-bold">{{ detail.tagsName || '--' }}</span>
        </template>
      </span>

      <span class="flex-1" v-if="detail.timeStr"
        >掉保时长:
        <span class="text-bold">{{ detail.timeStr }}</span>
      </span>
    </div>
    <div class="container-bottom">
      <div class="left">
        <el-tabs v-model="activeName">
          <el-tab-pane label="线索资料" name="线索资料">
            <el-form ref="formRef" :model="detail" label-position="top">
              <Collapse title="基础信息">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="姓名" prop="contactName">
                      <el-input v-model="detail.contactName" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="手机号" prop="contactPhone">
                      <el-input v-model="detail.contactPhone" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="线索来源" prop="sourceName">
                      <el-input v-model="detail.sourceName" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="detail.sourceId === '3'">
                    <el-form-item label="介绍来源" prop="introductionCustomerName">
                      <el-input v-model="detail.introductionCustomerName" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="公司名称" prop="companyName">
                      <el-input v-model="detail.companyName" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="业务类型" prop="productName">
                      <el-input v-model="detail.productName" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                      <el-input
                        v-model="detail.remark"
                        disabled
                        maxlength="1000"
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </Collapse>

              <Collapse title="更多信息">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="职位" prop="post">
                      <el-input v-model="detail.post" maxlength="10" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="行业" prop="industry">
                      <el-select
                        v-model="detail.industry"
                        placeholder=" "
                        clearable
                        disabled
                        filterable
                        allow-create
                        default-first-option
                        @change="handleChange"
                      >
                        <el-option v-for="(item, index) in industry" :key="index" :label="item.label" :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="电话" prop="phone">
                      <el-input v-model="detail.phone" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="邮箱" prop="email">
                      <el-input v-model="detail.email" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="微信" prop="wx">
                      <el-input v-model="detail.wx" maxlength="100" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="QQ" prop="qq">
                      <el-input v-model="detail.qq" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="性别" prop="sex">
                      <el-radio-group v-model="detail.sex" disabled>
                        <el-radio label="0">未知</el-radio>
                        <el-radio label="1">男</el-radio>
                        <el-radio label="2">女</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="生日" prop="birthday">
                      <el-date-picker
                        disabled
                        v-model="detail.birthday"
                        type="date"
                        placeholder=" "
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="地区" prop="area">
                      <el-input v-model="detail.area" maxlength="20" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="详细地址" prop="address">
                      <el-input v-model="detail.address" maxlength="100" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="税务性质" prop="taxNature">
                      <el-select v-model="detail.taxNature" placeholder=" " clearable disabled>
                        <el-option
                          v-for="(item, index) in customer_property"
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="首次跟进时间" prop="firstFollowTime">
                      <el-date-picker
                        disabled
                        v-model="detail.firstFollowTime"
                        type="date"
                        placeholder=" "
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </Collapse>
              <Collapse title="其他信息">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="创建时间" prop="createTime">
                      <el-input v-model="detail.createTime" disabled></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="创建人" prop="createBy">
                      <el-input v-model="detail.createBy" disabled></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最近修改时间" prop="lastModifiedTime">
                      <el-input v-model="detail.lastModifiedTime" disabled></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="最近跟进时间" prop="lastFollowTime">
                      <el-input v-model="detail.lastFollowTime" disabled></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="领取/分配时间" prop="getTime">
                      <el-input v-model="detail.getTime" disabled></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="isSea ? '前跟进人' : '跟进人'" prop="currentUserName">
                      <el-input v-model="detail.currentUserName" disabled></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="录入方式" prop="entryType">
                      <el-input :value="detail.entryType === '0' ? '手动录入' : '公海录入'" disabled></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="所属公海" prop="seaName">
                      <el-input :value="detail.seaName || '无'" disabled></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </Collapse>
            </el-form>
          </el-tab-pane>
          <!-- 新增商机详情 -->
          <el-tab-pane label="商机详情" name="商机详情">
            <businessInfo :timestamp="timestamp" :id="detail.id" v-if="activeName === '商机详情'" />
          </el-tab-pane>
          <el-tab-pane label="操作记录" name="操作记录">
            <actionRecord ref="actionRecordRef" :id="detail.id" v-if="activeName === '操作记录'" />
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="right">
        <record :detail="detail" :isSea="isSea" :tabType="tabType" v-if="detail.id" @on-success="getDetail" />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
  <customerTransform v-if="transformShow" :data="detail" @on-close="transformShow = false" @on-success="submitCallbackTransfer" />
  <businessAdd v-if="businessShow" :detail="detail" @on-close="businessShow = false" @on-success="businessSuccessfully" />

  <contract v-if="contractShow" :normal="normal" @on-close="contractShow = false" @on-next="handelNext" />
  <normalCreate v-if="normalShow" :productIds="productIds" :associated="associated" @on-close="normalShow = false" />
  <templateCreate v-if="templateShow" :productIds="productIds" :associated="associated" @on-close="templateShow = false" />

  <clueAppealFormModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="updateDetail" />
</template>
<script setup>
import {
  getClueDetail,
  getClueShareIds,
  saveClueShare,
  transferClue,
  recycleClue,
  divideClue,
  transferClueToZone,
  editTags
} from '@/api/material-manage/clue'
import businessInfo from '@/views/material-manage/client-manage/components/business-info.vue'
import businessAdd from '@/views/material-manage/client-manage/components/business-add.vue'
import extraForm from '@/views/material-manage/client-manage/components/extra-form'
import Collapse from '@/components/Collapse'
import { useDialog } from '@/hooks/useDialog'
import record from './record'
import tagForm from './tag-form'
import actionRecord from './action-record'
import clueShare from './clue-share'
import clueTransfer from './clue-transfer'
import zonRecycle from './zone-recycle'
import zoneTransfer from './zone-transfer'
import customerTransform from './customer-transform.vue'
import clueDistribute from './clue-distribute.vue'
import useUserStore from '@/store/modules/user'
import { getClientDetail, editCertificationFiles, getCertificationFiles } from '@/api/material-manage/client.js'
import { useDept } from '@/hooks/useDept'

import { useSetDic } from '@/hooks/useSetDic'
import { changeFileList } from '@/utils/common'
import { getCustomerById } from '@/api/customer/file'

import contract from '@/views/customer/customer-file/components/contract.vue'
import normalCreate from '@/views/contract-manage/contract-list/components/normal-create.vue'
import templateCreate from '@/views/contract-manage/contract-list/components/template-create.vue'
import clueAppealFormModal from '@/views/material-manage/clue-appeal-record/components/form-modal.vue'
import { ElMessageBox } from 'element-plus'
import { postClueAppealRecordUpdate } from '@/api/material-manage/clue-appeal-record'

const timestamp = ref(new Date().getTime())
const { setDic } = useSetDic()
const handleChange = value => {
  setDic('industry', value, value, industry)
}
const { isCapableGet, isCapableDispatch } = useDept()

const userStore = useUserStore()
const { showDialog } = useDialog()
// 自定义参数
const handleRevertParams = data => {
  if (data.tags.length) {
    data.tags = data.tags.map(item => {
      return {
        tagId: item
      }
    })
  }
  data.id = detail.value.id
}

const showTag = () => {
  showDialog({
    title: '打标签',
    // customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: tagForm, // 表单组件
    submitApi: editTags, // 提交api
    handleRevertParams,
    submitCallback: () => {
      getDetail()
    } // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}
const visible = ref(true)
const emits = defineEmits(['on-close', 'on-success', 'on-edit', 'on-delete', 'on-receive'])
const handleClose = () => {
  emits('on-close')
}

const props = defineProps({
  id: Number,
  isSea: {
    type: Boolean,
    default: false
  }, // 是否是公海
  isDivide: {
    type: Boolean,
    default: false
  }, // 是否被分配
  isGet: {
    type: Boolean,
    default: false
  }, // 是否可领取
  tabType: {
    type: String,
    default: '1'
  } // tabType 为 '2' 代表是共享
})

const activeName = ref('线索资料')

const { proxy } = getCurrentInstance()

const { clue_source } = proxy.useDict('clue_source')
const { industry } = proxy.useDict('industry')
const { customer_property } = proxy.useDict('customer_property')
// 获取线索详情
const detail = ref({})
const followStatus = ref('')
provide('followStatus', followStatus)
const getDetail = async () => {
  const { data } = await getClueDetail(props.id)
  detail.value = data || {}
  detail.value.tagsName = data.tags
    .map(item => {
      return item.name
    })
    .join(',')
  followStatus.value = data.followStatus
}

// 编辑线索
const handleEdit = () => {
  handleClose()
  emits('on-edit', detail.value.id)
}

// 共享线索参数自定义
const handleRevertParamsShare = data => {
  data.id = detail.value.id
}
// 共享线索
const handleShare = () => {
  showDialog({
    title: '共享线索',
    // customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: clueShare, // 表单组件
    getApi: getClueShareIds,
    requestParams: detail.value.id,
    submitApi: saveClueShare, // 提交api
    handleRevertParams: handleRevertParamsShare
    // submitCallback // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 转让线索
const submitCallbackTransfer = () => {
  handleClose()
  emits('on-success', {
    tabType: '1'
  })
}
const handleTransfer = () => {
  showDialog({
    title: '转让线索',
    // customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: clueTransfer, // 表单组件
    submitApi: transferClue, // 提交api
    handleRevertParams: handleRevertParamsShare,
    submitCallback: submitCallbackTransfer // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 回收公海
const handleRecycle = () => {
  showDialog({
    title: '回收公海',
    // customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: zonRecycle, // 表单组件
    submitApi: recycleClue, // 提交api
    handleRevertParams: handleRevertParamsShare,
    submitCallback: submitCallbackTransfer // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}

// 转化客户
const transformShow = ref(false)
const handleTransform = () => {
  transformShow.value = true
}

// 领取线索
const handleReceive = () => {
  emits('on-receive', {
    id: detail.value.id
  })
}

// 删除线索
const handleDelete = () => {
  emits('on-delete', detail.value.id)
}

// 转移至公海
const handleTransferToZone = () => {
  showDialog({
    title: '转移公海',
    // customClass: 'clue-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: zoneTransfer, // 表单组件
    submitApi: transferClueToZone, // 提交api
    handleRevertParams: handleRevertParamsShare,
    handleConvertParams: data => {
      data.currentSeaId = detail.value.seaId
    },
    submitCallback: () => {
      handleClose()
      emits('on-success')
    } // 提交成功之后的回调函数
    // handleRevertParams: handleRevertBusinessParams // 处理提交参数
  })
}
const handleDistribute = () => {
  showDialog({
    title: '分配线索',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: clueDistribute, // 表单组件
    submitApi: divideClue, // 提交api
    handleRevertParams: handleRevertParamsShare,
    handleConvertParams: data => {
      data.deptIds = detail.value.deptIds
    },
    submitCallback: () => {
      handleClose()
      emits('on-success')
    } // 提交成功之后的回调函数
  })
}
const handleSuccess = () => {
  handleClose()
  emits('on-success')
}

// 新增商机
const businessShow = ref(false)

const handleAddBusiness = () => {
  businessShow.value = true
}
// 新增商机成功
const normal = ref(false) // 是否能够选择模板创建合同
const associated = ref({})

// 跳转至创建新合同
// 手动新建还是模板新建的弹窗显示标志
const normalShow = ref(false)
const contractShow = ref(false)
const templateShow = ref(false)
// 传入的productIds(创建的为意向合同)
const productIds = ref([])
// 下一步
const handelNext = createType => {
  // '0'代表 普通新建
  // '1'代表 模板新建
  if (createType === '0') {
    normalShow.value = true
  }
  if (createType === '1') {
    templateShow.value = true
  }
}
const businessSuccessfully = async (ids, customerId) => {
  timestamp.value = new Date().getTime()
  // 跳出是否新建合同
  console.log('customerId', customerId)
  // 通过 customerId 获取档案详情 ，成功获取后 显示 弹窗
  if (customerId) {
    const { data } = await getCustomerById(customerId)
    if (data) {
      associated.value = data
    }
  }

  if (ids) {
    contractShow.value = true
    productIds.value = ids
  }
  // 如果存在多个业务类型 则需要手动录入信息
  if (ids && ids.length > 1) {
    normal.value = true
  } else {
    normal.value = false
  }

  // 获取详情接口更新数据
  getDetail()
}
// 股东信息数据做处理，去除存在股东名称、手机号以及文件都不存在的行
const translateShareholderInfoList = list => {
  return list.filter(
    item => item.shareholderName || item.shareholderPhone || (item.shareholderFileList && item.shareholderFileList.length)
  )
}
// 编辑附加资料
const handleEditExtra = () => {
  showDialog({
    title: '编辑办证资料',
    customClass: 'certification-dialog',
    cancelButtonText: '取消',
    confirmButtonText: '保存',
    component: extraForm, // 表单组件
    getApi: getCertificationFiles,
    requestParams: detail.value.id,
    handleConvertParams: data => {
      data.shareholderInfoList = data.shareholderInfoList || []
    },
    handleRevertParams: data => {
      if (data.legalIdentityFileList && data.legalIdentityFileList.length) {
        data.legalIdentityFileList = changeFileList(data.legalIdentityFileList)
      }

      if (data.supervisorIdentityFileList && data.supervisorIdentityFileList.length) {
        data.supervisorIdentityFileList = changeFileList(data.supervisorIdentityFileList)
      }
      // 传列表
      if (Array.isArray(data.otherDocumentFileList) && data.otherDocumentFileList.length) {
        data.otherDocumentFileList = changeFileList(data.otherDocumentFileList)
      }

      // 处理 股东信息表格 去重空行数据
      data.shareholderInfoList = translateShareholderInfoList(data.shareholderInfoList)
      data.id = detail.value.id
    },
    submitApi: editCertificationFiles // 提交api editCertificationFiles , getCertificationFiles
  })
}

/** 更新detail */
const actionRecordRef = ref()
const updateDetail = () => {
  getDetail()
  actionRecordRef.value?.getList()
}

/** 线索申述 */
const formModalRef = ref()
const formModalShow = ref(false)
const formModalClose = () => {
  formModalShow.value = false
}
const handleAddClueAppeal = () => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onAdd(detail.value)
  })
}
const handleFailClueAppeal = () => {
  ElMessageBox.confirm('是否确认将当前线索标记为申述失败？', '线索申述')
    .then(() => {
      postClueAppealRecordUpdate({
        clueId: detail.value.id,
        success: false
      }).then(res => {
        proxy.$modal.msgSuccess(res.msg)
        updateDetail()
      })
    })
    .catch(() => {
      // catch error
    })
}
const handleSuccessClueAppeal = () => {
  ElMessageBox.confirm('是否确认将当前线索标记为申述成功？', '线索申述')
    .then(() => {
      postClueAppealRecordUpdate({
        clueId: detail.value.id,
        success: true
      }).then(res => {
        proxy.$modal.msgSuccess(res.msg)
        updateDetail()
      })
    })
    .catch(() => {
      // catch error
    })
}

onMounted(() => {
  getDetail()
})
</script>
<style lang="scss" scoped>
.container-t {
  display: flex;
  align-items: center;
  .el-button {
    color: #333333;
  }
  .avatar-icon {
    flex: 0 0 auto;
  }
  .name {
    margin-right: 24px;
    margin-left: 12px;
    font-size: 18px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    color: #333333;
    flex: 0 0 auto;
  }
  .el-tag {
    font-size: 15px;
    font-family: AlibabaPuHuiTi_2_65_Regular;
    color: #2383e7;
  }
}
.flex-1 {
  flex: 1;
}
.container-c {
  display: flex;
  align-items: center;
  margin-top: 25px;
  padding-bottom: 18px;
  border-bottom: 1px solid #e8e8e8;
}

.text-bold {
  font-size: 16px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  color: #333333;
}

.container-bottom {
  display: flex;
  min-height: 400px;
  .left {
    flex: 1;
    overflow: hidden;
    border-right: 1px solid #e8e8e8;
    padding-right: 16px;
  }
  .right {
    width: 310px;
    padding-left: 16px;
  }
}
:deep(.el-select) {
  width: 100%;
}
.btn-con {
  margin-top: 12px;
  display: flex;
  // flex-wrap: wrap;
}

.el-button--danger.is-plain {
  --el-button-text-color: var(--el-color-danger);
  --el-button-bg-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-danger);
  }
}
</style>
