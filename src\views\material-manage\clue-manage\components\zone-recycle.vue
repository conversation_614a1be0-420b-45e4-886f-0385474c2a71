<!--
 * @Description: 回收公海
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-11-21 14:12:42
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="选择公海" prop="seaId">
          <el-select v-model="formData.seaId" placeholder="请选择" clearable>
            <el-option
              v-for="item in seaOptions"
              :key="item.id"
              :label="item.name"
              :disabled="includeDept(item.deptIds)"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="回收原因" prop="reason">
          <el-select v-model="formData.reason" placeholder="请选择" clearable>
            <el-option v-for="item in sea_reason" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import { getZoneSettingList } from '@/api/material-manage/zone-setting'
import { useDept } from '@/hooks/useDept'
const { includeDept } = useDept()

const { proxy } = getCurrentInstance()
const { sea_reason } = proxy.useDict('sea_reason')
const formData = reactive({
  seaId: '',
  reason: '',
  type: '0', // 线索回收
  id: undefined
})
// 获取公海列表
const seaOptions = ref([])
const getSeaOptions = async () => {
  const { data } = await getZoneSettingList({
    pageSize: 1000,
    pageNum: 1,
    status: '1',
    type: formData.type
  })
  seaOptions.value = data.records || []
}

const selectRef = ref()
const rules = {
  seaId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  reason: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}

onMounted(() => {
  nextTick(() => {
    console.log('formData', formData.type)
    getSeaOptions()
  })
})
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
