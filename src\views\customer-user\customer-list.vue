<template>
  <ProTable
    :init-param="initParam"
    :requestAuto="true"
    ref="proTable"
    row-key="id"
    :columns="columns"
    :request-api="getUserCustomerBindRecordList"
  >
    <template #tabs>
      <el-radio-group :model-value="initParam.bindStatus" @change="handleRadioChange">
        <el-radio-button v-for="(item, index) in tabs" :key="index" :label="item.dictValue">{{ item.dictLabel }}</el-radio-button>
      </el-radio-group></template
    >
    <template #customerName="{ row }">
      <span class="blue-text" @click="handlShowCustomerDetail(row.customerId)">{{ row.customerName }}</span>
    </template>
    <template #bindStatus="{ row }">
      <span :class="row.bindStatus === 'bind' ? 'blue-text' : ''">{{ row.bindStatus === 'bind' ? '正常' : '解绑' }}</span>
    </template>
    <!-- 表格 header 按钮 -->
    <template #tableHeader>
      <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增</el-button>
    </template>
    <!-- 表格操作 -->
    <template #operation="scope">
      <el-button v-if="scope.row.bindStatus === 'bind'" type="primary" link @click="handleUnBind(scope.row, scope.$index)"
        >解绑</el-button
      >
    </template>
  </ProTable>
  <customerDetail
    v-if="customerDetailShow"
    :id="rowId"
    :hideActionBtn="true"
    @on-close="handleCloseCustomerDetail"
    @on-list="getList"
  />
  <formModal v-if="formModalShow" ref="formModalRef" @close="formModalClose" @ok="getList" />
</template>
<script setup lang="tsx">
import { ref, reactive, nextTick, onMounted } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { financeReviewDelete } from '@/api/finance-review/finance-review.js'
import { getUserCustomerBindRecordList, postUserCustomerBindRecordUnbind } from '@/api/customer-user/customer-list.js'
import { CirclePlus } from '@element-plus/icons-vue'
import formModal from './components/form-modal.vue'
import { useHandleData } from '@/hooks/useHandleData'
import customerDetail from '@/views/customer/customer-file/components/customer-detail.vue'
import { ElMessageBox, ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()

const proTable = ref()

const getList = async (newDic: any) => {
  proTable.value?.getTableList()
}
const initParam = reactive({
  bindStatus: 'bind'
})
const tabs = ref([
  {
    dictLabel: '全部',
    dictValue: ''
  },
  {
    dictLabel: '正常',
    dictValue: 'bind'
  },
  {
    dictLabel: '解绑',
    dictValue: 'unbind'
  }
])
const handleRadioChange = (e: any) => {
  proTable.value.pageable.pageNum = 1
  initParam.bindStatus = e
}
function columnsFun(newDic?: any) {
  return [
    { type: 'index', fixed: 'left', width: 50 },
    {
      prop: 'userName',
      label: '用户姓名',
      search: { el: 'input' },
      minWidth: 100
    },
    {
      prop: 'phone',
      label: '手机号',
      search: { el: 'input' },
      minWidth: 100
    },
    {
      prop: 'customerName',
      label: '所属企业',
      search: { el: 'input' },
      minWidth: 300
    },
    {
      prop: 'bindStatus',
      label: '状态',
      minWidth: 100
    },
    {
      prop: 'updateTime',
      label: '最近更新时间',
      minWidth: 200
    },
    {
      prop: 'operation',
      label: '操作',
      fixed: 'right',
      width: 200
    }
  ]
}
// 表格配置项
const columns = ref(columnsFun())

// ------------main弹窗---------------
const formModalRef = ref()
const formModalShow = ref(false)

const formModalClose = () => {
  formModalShow.value = false
}

const handleAdd = (row: any) => {
  formModalShow.value = true
  nextTick(() => {
    formModalRef.value.onAdd()
  })
}

const handleUnBind = async (row: any, index: number) => {
  ElMessageBox.confirm(`是否确认将 ${row.userName} 与 ${row.customerName} 解除授权绑定？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      postUserCustomerBindRecordUnbind([row.id]).then(res => {
        if (res.code === 200) {
          ElMessage.success('解绑成功')
          proTable.value?.getTableList()
        }
      })
    })
    .catch(() => {})
}

const rowId = ref()
// 用于编辑状态下
const isEdit = ref(false)
provide('isEdit', isEdit)
/* 客户详情弹窗 ---start--- */
const customerDetailShow = ref(false)
const handlShowCustomerDetail = (id: number) => {
  rowId.value = id
  customerDetailShow.value = true
}
const handleCloseCustomerDetail = () => {
  customerDetailShow.value = false
  // proTable.value?.getTableList()
  // staticsRef.value?.getList()
}
/* 客户详情弹窗 ---end--- */

onMounted(() => {})
</script>
<style lang="scss" scoped></style>
