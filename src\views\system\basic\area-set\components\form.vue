<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="上级菜单" prop="parentId">
          <el-tree-select
            v-model="formData.parentId"
            :default-expanded-keys="[0]"
            :data="menuOptions"
            :props="{
              value: 'id',
              label: 'name',
              children: 'child'
            }"
            value-key="id"
            placeholder="选择上级菜单"
            check-strictly
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="数据名称" prop="name">
          <el-input v-model="formData.name" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="显示排序" prop="sort">
          <el-input-number style="width: 100%" v-model="formData.sort" class="mx-4" :min="1" controls-position="right" />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status" class="ml-4">
            <el-radio label="1">正常</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col> -->
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { getAreaTreeList } from '@/api/basicData/basicData'
import { reactive } from 'vue'
const formData = reactive({
  parentId: undefined,
  name: '',
  sort: '',
  // enable: '1', // 默认为正常
  remark: '',
  id: undefined
})

const rules = {
  parentId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  name: [{ required: true, trigger: 'blur', message: '请输入' }],
  sort: [
    {
      required: true,
      trigger: 'blur',
      message: '请输入'
    }
  ]
}
const menuOptions = ref([])

/** 查询菜单下拉树结构 */
const getTreeselect = () => {
  getAreaTreeList().then(res => {
    const temp = [
      {
        id: '0',
        name: '全部',
        child: res.data
      }
    ]
    menuOptions.value = temp
  })
}

getTreeselect()
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
