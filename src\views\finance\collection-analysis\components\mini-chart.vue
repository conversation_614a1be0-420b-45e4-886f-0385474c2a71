<template>
  <!-- bignumber-{{ bignumber(0.1 + 0.2) }} number-{{ 0.1 + 0.2 }} -->
  <div ref="chartRef" style="height: 200px" />
</template>

<script setup>
import * as echarts from 'echarts'
// import { bignumber } from 'mathjs'
import { multiply } from '@/utils/math'

const props = defineProps({
  dataSource: Array
})
let data = []
let optionData = {}
let option = {}
const chartRef = ref(null)

watch(
  () => props.dataSource,
  val => {
    if (val) {
      data = val
      data.forEach(item => {
        const temp = multiply(item.value, 100)
        console.log('temp', temp)
        item.value = temp.toString()
      })
      // console.log('data', data)
    }
  },
  { immediate: true }
)

// console.log('已经开始生命周期')
// 生命周期

function getData(data) {
  let res = {
    series: [],
    yAxis: []
  }
  for (let i = 0; i < data.length; i++) {
    // console.log([70 - i * 15 + '%', 67 - i * 15 + '%']);
    // 值
    res.series.push({
      name: '',
      type: 'pie',
      clockWise: false, //顺时加载
      hoverAnimation: false, //鼠标移入变大
      radius: [95 - i * 15 + '%', 83 - i * 15 + '%'],
      center: ['50%', '50%'],
      label: {
        show: false
      },
      // itemStyle: {
      //   label: {
      //     show: false
      //   },
      //   labelLine: {
      //     show: false
      //   },
      //   borderWidth: 20
      // },
      data: [
        {
          value: data[i].value,
          name: data[i].name
        },
        {
          value: 100 - data[i].value,
          name: '',
          itemStyle: {
            color: 'rgba(0,0,0,0)', // 透明
            borderWidth: 0
          },
          tooltip: {
            show: false
          },
          hoverAnimation: false
        }
      ]
    })
    // 背景
    res.series.push({
      name: '',
      type: 'pie',
      silent: true,
      z: 1,
      clockWise: false, //顺时加载
      hoverAnimation: false, //鼠标移入变大
      radius: [95 - i * 15 + '%', 83 - i * 15 + '%'],
      center: ['50%', '50%'],
      label: {
        show: false
      },
      // itemStyle: {
      //   label: {
      //     show: false
      //   },
      //   labelLine: {
      //     show: false
      //   },
      //   borderWidth: 20
      // },
      data: [
        {
          value: 7.5,
          itemStyle: {
            color: '#eee',
            borderWidth: 0
          },
          tooltip: {
            show: false
          },
          hoverAnimation: false
        },
        {
          value: 2.5,
          name: '',
          itemStyle: {
            color: 'rgba(0,0,0,0)', // 透明
            borderWidth: 0
          },
          tooltip: {
            show: false
          },
          hoverAnimation: false
        }
      ]
    })
    // res.yAxis.push(((data[i].value / sumValue) * 100).toFixed(2) + '%')
    // res.yAxis.push(data[i].name)
    // res.yAxis.push(`${data[i].name}: ${data[i].value}`)
  }
  return res
}

function getOption() {
  return {
    // backgroundColor: '#000',
    legend: {
      show: true,
      icon: 'circle',
      top: 'center',
      left: 'center',
      width: 50,
      padding: [0, 5],
      itemGap: 2
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: '{b}:{c}%' // {a}<br>{b}:{c}({d}%)
      // formatter: `{b}:{${multiply(c, 100)}}%` // {a}<br>{b}:{c}({d}%)
    },
    color: ['rgb(101, 119, 151)', 'rgb(98, 218, 171)', 'rgb(99, 149, 249)'],
    grid: {
      top: '10%',
      bottom: '72%',
      left: '58%',
      containLabel: false
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          interval: 0,
          inside: false,
          align: 'left',
          textStyle: {
            color: '#333',
            fontSize: 14
          },
          show: true
        },
        data: optionData.yAxis
      }
    ],
    xAxis: [
      {
        show: false
      }
    ],
    series: optionData.series
  }
}

const drawChart = () => {
  // data->optionData->option->setOption
  optionData = getData(data)
  option = getOption()
  const intance = echarts.init(chartRef.value)
  intance.setOption(option, { notMerge: true })
}

defineExpose({
  drawChart
})
</script>

<style lang="scss" scoped></style>
