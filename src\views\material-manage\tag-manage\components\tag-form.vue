<!--
 * @Description: 标签表单
 * @Author: thb
 * @Date: 2023-05-25 13:48:46
 * @LastEditTime: 2023-08-09 14:25:49
 * @LastEditors: thb
-->
<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="formData.name" maxlength="20" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            maxlength="1000"
            type="textarea"
            placeholder="请输入"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import NumberInput from '@/components/NumberInput'

import SelectTree from '@/components/SelectTree'
const { proxy } = getCurrentInstance()

const { zone_setting } = proxy.useDict('zone_setting')
const formData = reactive({
  name: '',
  remark: '',
  id: undefined
})

const rules = {
  name: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ]
}

// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
defineExpose({
  formData,
  getFormRef
})
</script>

<style lang="scss" scoped>
.el-select {
  width: 573px;
}
</style>
