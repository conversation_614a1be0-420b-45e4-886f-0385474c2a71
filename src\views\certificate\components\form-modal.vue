<!--
 * @Description: 
 * @Author: thb
 * @Date: 2023-09-25 13:15:41
 * @LastEditTime: 2024-02-22 10:13:57
 * @LastEditors: thb
-->
<template>
  <el-dialog
    v-model="visible"
    title="业务办理"
    class="business-dialog license-dialog"
    align-center
    :close-on-click-modal="false"
    :before-close="onHandleClose"
    width="1200px"
    destroy-on-close
    append-to-body
  >
    <div class="my-container">
      <div class="top">
        <div class="line1">
          <div class="title-con">
            <img src="@/assets/icons/business-title-1.png" alt="" />
            <span class="tit">{{ formData.customerNametemp }}</span>
            <el-tag v-if="formData.customerNo">{{ formData.customerNo }}</el-tag>
          </div>
          <div>
            <span v-if="!['deprecated', 'completed'].includes(formData.bizStatus)">
              <el-button v-hasPermi="['license:todo:transfer']" plain type="primary" @click="handleTransfer"> 转单 </el-button>
              <el-button
                v-hasPermi="['license:todo:delete']"
                :disabled="false"
                plain
                type="danger"
                @click="handleDel"
                v-if="actionType === 'todo'"
              >
                作废
              </el-button>
            </span>
            <span v-if="['deprecated'].includes(formData.bizStatus)">
              <span style="margin-right: 10px">{{ formData.updateTime }}</span>
              <el-button disabled plain type="info"> 作废 </el-button>
            </span>
            <span v-if="['completed'].includes(formData.bizStatus)">
              <span style="margin-right: 10px">{{ formData.updateTime }}</span>
              <span v-if="formData.accountantUserName">已通知至会计：{{ formData.accountantUserName }}</span>
              <el-tag class="ml-2" type="success">已完成</el-tag>
            </span>
          </div>
        </div>
        <div class="line2">
          <div class="item">
            <span class="t1">业务名称：</span>
            <!-- <span class="t2">{{ formData.bizType && bizTypeArr.find(item => item.value === formData.bizType).label }}</span> -->
            <span class="t2">{{ getLabel(formData.bizType) }}</span>
          </div>
          <div class="item">
            <span class="t1">流程编号：</span>
            <span class="t2">{{ formData.code }}</span>
          </div>
          <div class="item" v-if="['domestic_business_registration', 'foreign_business_registration'].includes(formData.bizType)">
            <span class="t1">股份比例：</span>
            <span class="t2">{{ formData.shareholding }}</span>
          </div>
          <div
            class="item item-s"
            v-if="['domestic_business_registration', 'foreign_business_registration'].includes(formData.bizType)"
          >
            <span class="t1"
              >其他附件：

              <span class="blue-text" v-if="formData.otherFileList.length" @click="showOtherFiles">
                共 {{ formData.otherFileList.length }} 份</span
              >
              <span v-else class="t2">暂无</span>
            </span>
            <!-- <span class="t2">
              <span v-for="item in formData.otherFileList" :key="item" style="margin-right: 10px"
                ><span class="download-text" @click="downloadFile(item)"> {{ item.fileNames }}</span></span
              >
            </span> -->
          </div>
        </div>
      </div>
      <div class="middle">
        <el-steps :active="active" simple finish-status="success" class="step-list">
          <el-step
            v-for="(item, index) in typeSteps"
            :key="index"
            :title="`${stepMap[formData.bizType][index]}`"
            @click="handleClick(item)"
          >
            <template #icon>
              <span class="icon circle-icon circle-icon-default">{{ index + 1 }}</span>
            </template></el-step
          >
        </el-steps>
      </div>
      <div class="main">
        <!-- 步骤1 -->
        <el-form
          v-if="active === 0"
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-position="top"
          :disabled="disabledSteps.includes(0)"
          :hide-required-asterisk="disabledSteps.includes(0)"
        >
          <div class="title-con">
            <img src="@/assets/icons/business-title-2.png" alt="" />
            <span class="tit">{{ ['bank_account_open'].includes(formData.bizType) ? '银行开户派工' : '办证派发' }}</span>
          </div>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item :label="['bank_account_open'].includes(formData.bizType) ? '开户人员' : '办证人员'" prop="userId">
                <SelectTree style="width: 100%" v-model="formData.userId" placeholder="请选择" clearable /> </el-form-item
            ></el-col>
          </el-row>
        </el-form>
        <!-- 步骤2 -->
        <el-form
          v-if="active === 1"
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-position="top"
          :disabled="disabledSteps.includes(1)"
          :hide-required-asterisk="disabledSteps.includes(1)"
        >
          <div class="title-con">
            <img src="@/assets/icons/business-title-2.png" alt="" />
            <span class="tit">办证办税</span>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-checkbox
                :disabled="formData.legalIdentityFileList.length"
                :class="disabledSteps.includes(1) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                v-model="formData.legalIdentityFileListFlag"
                label="法人身份证"
              />
              <el-row :gutter="24">
                <el-col :span="24">
                  <el-form-item prop="legalIdentityFileList" :class="[disabledSteps.includes(1) ? 'flex-column' : '']">
                    <FileUploadBiz
                      v-if="!disabledSteps.includes(1)"
                      :disabled="!formData.legalIdentityFileListFlag"
                      v-model="formData.legalIdentityFileList"
                      :limit="100"
                      :isShowTip="false"
                      @on-load-success="validateFormField('legalIdentityFileList')"
                    />
                    <template v-else>
                      <!-- <template v-if="formData.legalIdentityFileList?.length">
                        <span
                          class="download-text"
                          v-for="(file, index) in formData.legalIdentityFileList"
                          :key="index"
                          @click="downloadFile(file)"
                        >
                          {{ file?.fileNames || '暂无文件' }}</span
                        >
                      </template>
                      <template v-else>
                        <span class="download-text"> 暂无文件 </span>
                      </template> -->
                      <fileList :list="formData.legalIdentityFileList" />
                    </template>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="12">
              <el-checkbox
                :disabled="formData.supervisorIdentityFileList.length"
                :class="disabledSteps.includes(1) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                v-model="formData.supervisorIdentityFileListFlag"
                label="监事身份证"
              />
              <el-row :gutter="24">
                <el-col :span="24">
                  <el-form-item prop="supervisorIdentityFileList" :class="[disabledSteps.includes(1) ? 'flex-column' : '']">
                    <FileUploadBiz
                      :disabled="!formData.supervisorIdentityFileListFlag"
                      v-model="formData.supervisorIdentityFileList"
                      :limit="100"
                      :isShowTip="false"
                      v-if="!disabledSteps.includes(1)"
                      @on-load-success="validateFormField('supervisorIdentityFileList')"
                    />

                    <template v-else>
                      <!-- <template v-if="formData.supervisorIdentityFileList?.length">
                        <span
                          class="download-text"
                          v-for="(file, index) in formData.supervisorIdentityFileList"
                          :key="index"
                          @click="downloadFile(file)"
                        >
                          {{ file?.fileNames || '暂无文件' }}</span
                        >
                      </template>
                      <template v-else>
                        <span class="download-text"> 暂无文件 </span>
                      </template> -->
                      <fileList :list="formData.supervisorIdentityFileList" />
                    </template>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="12">
              <el-checkbox
                :disabled="formData.legalPhone.length"
                :class="disabledSteps.includes(1) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                v-model="formData.legalPhoneFlag"
                label="法人手机号"
              />
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item prop="legalPhone">
                    <el-input :disabled="!formData.legalPhoneFlag" v-model="formData.legalPhone" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="12">
              <el-checkbox
                :disabled="formData.supervisorPhone.length"
                :class="disabledSteps.includes(1) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                v-model="formData.supervisorPhoneFlag"
                label="监事手机号"
              />
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item prop="supervisorPhone">
                    <el-input :disabled="!formData.supervisorPhoneFlag" v-model="formData.supervisorPhone" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="12">
              <el-checkbox
                :disabled="formData.ownershipCertificateFileList.length"
                :class="disabledSteps.includes(1) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                v-model="formData.ownershipCertificateFileListFlag"
                label="产权证明"
              />
              <el-form-item prop="ownershipCertificateFileList">
                <FileUploadBiz
                  v-if="!disabledSteps.includes(1)"
                  :disabled="!formData.ownershipCertificateFileListFlag"
                  v-model="formData.ownershipCertificateFileList"
                  :isShowTip="false"
                  :limit="100"
                  @on-load-success="validateFormField('ownershipCertificateFileList')"
                />
                <!-- <span class="download-text" @click="downloadFile(formData.ownershipCertificateFileList[0])" v-else>
                  {{ formData.ownershipCertificateFileList[0]?.fileNames || '暂无文件' }}</span
                > -->
                <fileList v-else :list="formData.ownershipCertificateFileList" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-checkbox
                :disabled="formData.consultationMaterialFileList.length"
                v-model="formData.consultationMaterialFileListFlag"
                label="会商资料"
              />
              <el-form-item prop="consultationMaterialFileList">
                <FileUploadBiz
                  v-if="!disabledSteps.includes(1)"
                  :disabled="!formData.consultationMaterialFileListFlag"
                  v-model="formData.consultationMaterialFileList"
                  :isShowTip="false"
                  @on-load-success="validateFormField('consultationMaterialFileList')"
                />

                <!-- <span class="download-text" @click="downloadFile(formData.consultationMaterialFileList[0])" v-else>
                  {{ formData.consultationMaterialFileList[0]?.fileNames || '暂无文件' }}</span
                > -->
                <fileList v-else :list="formData.consultationMaterialFileList" />
              </el-form-item>
            </el-col>
            <template v-if="formData.bizType === 'foreign_business_registration'">
              <el-col :span="12">
                <el-checkbox
                  :class="disabledSteps.includes(1) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                  :disabled="formData.foreignEnterpriseCertificateFileList.length"
                  v-model="formData.foreignEnterpriseCertificateFileListFlag"
                  label="外资企业证明"
                />
                <el-form-item prop="foreignEnterpriseCertificateFileList">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(1)"
                    :disabled="!formData.foreignEnterpriseCertificateFileListFlag"
                    v-model="formData.foreignEnterpriseCertificateFileList"
                    :isShowTip="false"
                    @on-load-success="validateFormField('foreignEnterpriseCertificateFileList')"
                  />

                  <fileList v-else :list="formData.foreignEnterpriseCertificateFileList" />
                  <!-- <span class="download-text" @click="downloadFile(formData.foreignEnterpriseCertificateFileList[0])" v-else>
                    {{ formData.foreignEnterpriseCertificateFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-checkbox
                  :class="disabledSteps.includes(1) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                  :disabled="formData.translatedDocumentFileList.length"
                  v-model="formData.translatedDocumentFileListFlag"
                  label="翻译件"
                />
                <el-form-item prop="translatedDocumentFileList">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(1)"
                    :disabled="!formData.translatedDocumentFileListFlag"
                    v-model="formData.translatedDocumentFileList"
                    :isShowTip="false"
                    @on-load-success="validateFormField('translatedDocumentFileList')"
                  />

                  <!-- <span class="download-text" @click="downloadFile(formData.translatedDocumentFileList[0])" v-else>
                    {{ formData.translatedDocumentFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->
                  <fileList v-else :list="formData.translatedDocumentFileList" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-checkbox
                  :class="disabledSteps.includes(1) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                  :disabled="formData.foreignPassportFileList.length"
                  v-model="formData.foreignPassportFileListFlag"
                  label="外籍护照"
                />
                <el-form-item prop="foreignPassportFileList">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(1)"
                    :disabled="!formData.foreignPassportFileListFlag"
                    v-model="formData.foreignPassportFileList"
                    :isShowTip="false"
                    @on-load-success="validateFormField('foreignPassportFileList')"
                  />

                  <!-- <span class="download-text" @click="downloadFile(formData.foreignPassportFileList[0])" v-else>
                    {{ formData.foreignPassportFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->
                  <fileList v-else :list="formData.foreignPassportFileList" />
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="12">
              <el-checkbox
                :disabled="formData.newRegistrationInformationConfirmationDocumentFileList.length"
                :class="disabledSteps.includes(1) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                v-model="formData.newRegistrationFileListFlag"
                label="新注册信息确认单"
              />
              <el-form-item prop="newRegistrationInformationConfirmationDocumentFileList">
                <FileUploadBiz
                  v-if="!disabledSteps.includes(1)"
                  :disabled="!formData.newRegistrationFileListFlag"
                  v-model="formData.newRegistrationInformationConfirmationDocumentFileList"
                  :isShowTip="false"
                  @on-load-success="validateFormField('newRegistrationInformationConfirmationDocumentFileList')"
                />

                <!-- <span
                  class="download-text"
                  @click="downloadFile(formData.newRegistrationInformationConfirmationDocumentFileList[0])"
                  v-else
                >
                  {{ formData.newRegistrationInformationConfirmationDocumentFileList[0]?.fileNames || '暂无文件' }}</span
                > -->

                <fileList v-else :list="formData.newRegistrationInformationConfirmationDocumentFileList" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 新增股东信息表格 -->
          <el-row>
            <el-col :span="24">
              <el-checkbox
                :disabled="formData.shareholderInfoList.length"
                v-model="formData.shareholderInfoListFlag"
                label="股东信息"
              />
              <el-button
                v-if="!disabledSteps.includes(1)"
                type="primary"
                :disabled="!formData.shareholderInfoListFlag"
                style="margin-left: 24px"
                @click="handleAddRow"
                >新增</el-button
              >
              <el-form-item prop="shareholderInfoList">
                <FormTable
                  ref="formTableRef"
                  style="width: 100%"
                  :formData="{
                    tableData: formData.shareholderInfoList,
                    rules: formData.rules
                  }"
                  :option="option"
                  :class="[disabledSteps.includes(1) ? 'invisible-star' : '']"
                >
                  <template #shareholderName="{ row }">
                    <el-input v-model="row.shareholderName" length="20" :disabled="disabledSteps.includes(1)"></el-input>
                  </template>
                  <template #shareholderPhone="{ row }">
                    <el-input v-model="row.shareholderPhone" length="20" :disabled="disabledSteps.includes(1)"></el-input>
                  </template>
                  <template #shareholderFileList="{ row, $index }">
                    <FileUploadBiz
                      v-model="row.shareholderFileList"
                      :isShowTip="false"
                      :limit="10"
                      @on-load-success="() => validateFile($index)"
                      v-if="!disabledSteps.includes(1)"
                    />

                    <template v-else>
                      <!-- <div
                        class="download-text"
                        v-for="(file, index) in row.shareholderFileList"
                        :key="index"
                        @click="downloadFile(file)"
                      >
                        {{ file?.fileNames || '暂无文件' }}
                      </div> -->
                      <fileList :list="row.shareholderFileList" />
                    </template>
                  </template>
                  <template #action="{ $index }">
                    <el-button type="danger" @click="handleDeleteRow($index)" :disabled="disabledSteps.includes(1)"
                      >删除</el-button
                    >
                  </template>
                </FormTable>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <!-- 步骤3 -->
        <el-form
          v-if="active === 2"
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-position="top"
          :disabled="disabledSteps.includes(2)"
          :hide-required-asterisk="disabledSteps.includes(2)"
        >
          <div class="title-con">
            <img src="@/assets/icons/business-title-2.png" alt="" />
            <span class="tit">办理进度汇报</span>
          </div>
          <!-- 复用步骤二的表单 -->
          <collectionForm
            :formData="formDataBase"
            ref="collectionFormRef"
            :collectionOrHandleStepValidateSubmit="collectionOrHandleStepValidateSubmit"
            :disabledSteps="disabledSteps"
          />

          <!-- todo 再次进入后不可对已勾的阶段进行修改 -->
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item prop="stageNameList_0">
                <el-checkbox
                  :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                  :disabled="formData.stageNameList_0_bool"
                  v-model="formData.stageNameList_0"
                  label="提交"
                />
                <span class="time">{{ formData.stageNameList_0_time }}</span></el-form-item
              ></el-col
            >
            <el-col :span="8">
              <el-form-item prop="stageNameList_1">
                <el-checkbox :disabled="formData.stageNameList_1_bool" v-model="formData.stageNameList_1" label="驳回" /><span
                  class="time"
                  >{{ formData.stageNameList_1_time }}</span
                >
              </el-form-item></el-col
            >
            <el-col :span="8">
              <el-form-item prop="stageNameList_2">
                <el-checkbox :disabled="formData.stageNameList_2_bool" v-model="formData.stageNameList_2" label="五方会审" /><span
                  class="time"
                  >{{ formData.stageNameList_2_time }}</span
                >
              </el-form-item></el-col
            >
            <el-col :span="8">
              <el-form-item prop="stageNameList_3">
                <el-checkbox
                  :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                  :disabled="formData.stageNameList_3_bool"
                  v-model="formData.stageNameList_3"
                  label="完成"
                />
                <span class="time">{{ formData.stageNameList_3_time }}</span></el-form-item
              ></el-col
            >
            <el-col :span="8">
              <el-form-item prop="stageNameList_4">
                <el-checkbox
                  :class="disabledSteps.includes(2) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                  :disabled="formData.stageNameList_4_bool"
                  v-model="formData.stageNameList_4"
                  label="办税"
                /><span class="time">{{ formData.stageNameList_4_time }}</span>
              </el-form-item></el-col
            >
          </el-row>
        </el-form>
        <!-- 步骤4 -->
        <el-form
          v-if="active === 3"
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-position="top"
          :disabled="disabledSteps.includes(3)"
          :hide-required-asterisk="disabledSteps.includes(3)"
        >
          <Collapse title="资料上传">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item prop="customerName" label="企业名称"> <el-input v-model="formData.customerName" /> </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="naturalPersonPwd" label="自然人密码">
                  <el-input v-model="formData.naturalPersonPwd" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="businessLicenseFileList" label="营业执照">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(3)"
                    v-model="formData.businessLicenseFileList"
                    :isShowTip="false"
                    :limit="100"
                  />
                  <!-- <span class="download-text" @click="downloadFile(formData.businessLicenseFileList[0])" v-else>
                    {{ formData.businessLicenseFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->
                  <fileList :list="formData.businessLicenseFileList" v-else />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item prop="taxProcessingDocumentFileList" label="税务办理凭证">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(3)"
                    v-model="formData.taxProcessingDocumentFileList"
                    :isShowTip="false"
                  />
                  <span class="download-text" @click="downloadFile(formData.taxProcessingDocumentFileList[0])" v-else>
                    {{ formData.taxProcessingDocumentFileList[0]?.fileNames || '暂无文件' }}</span
                  >
                </el-form-item>
              </el-col> -->

              <el-col :span="12">
                <el-form-item prop="registrationInformationFileList" label="注册资料">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(3)"
                    v-model="formData.registrationInformationFileList"
                    :isShowTip="false"
                    :limit="100"
                  />
                  <!-- <span class="download-text" @click="downloadFile(formData.registrationInformationFileList[0])" v-else>
                    {{ formData.registrationInformationFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->

                  <fileList :list="formData.registrationInformationFileList" v-else />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="businessConstitutionFileList" label="公司章程">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(3)"
                    v-model="formData.businessConstitutionFileList"
                    :isShowTip="false"
                    :limit="100"
                  />
                  <!-- <span class="download-text" @click="downloadFile(formData.businessConstitutionFileList[0])" v-else>
                    {{ formData.businessConstitutionFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->
                  <fileList :list="formData.businessConstitutionFileList" v-else />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="shareholderCommitteeResolutionFileList" label="股东会议决议">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(3)"
                    v-model="formData.shareholderCommitteeResolutionFileList"
                    :isShowTip="false"
                    :limit="100"
                  />
                  <!-- <span class="download-text" @click="downloadFile(formData.shareholderCommitteeResolutionFileList[0])" v-else>
                    {{ formData.shareholderCommitteeResolutionFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->
                  <fileList :list="formData.shareholderCommitteeResolutionFileList" v-else />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="addressFileList" label="地址">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(3)"
                    :limit="100"
                    v-model="formData.addressFileList"
                    :isShowTip="false"
                  />
                  <!-- <span class="download-text" @click="downloadFile(formData.addressFileList[0])" v-else>
                    {{ formData.addressFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->
                  <fileList :list="formData.addressFileList" v-else />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="otherDocumentFileList" label="其它">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(3)"
                    :limit="100"
                    v-model="formData.otherDocumentFileList"
                    :isShowTip="false"
                  />
                  <!-- <span class="download-text" @click="downloadFile(formData.otherDocumentFileList[0])" v-else>
                    {{ formData.otherDocumentFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->
                  <fileList :list="formData.otherDocumentFileList" v-else />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-row :gutter="24">
                  <el-col :span="24">
                    <el-form-item
                      prop="identityDocumentFileList"
                      label="身份证件"
                      :class="[disabledSteps.includes(3) ? 'flex-column' : '']"
                    >
                      <!-- <ImageUploadBiz
                        v-model="formData.identityDocumentFileList_0"
                        myTip="上传身份证正面"
                        :limit="1"
                        :isShowTip="false"
                      /> -->
                      <FileUploadBiz
                        v-if="!disabledSteps.includes(3)"
                        v-model="formData.identityDocumentFileList"
                        :isShowTip="false"
                        :limit="100"
                        @on-load-success="validateFormField('identityDocumentFileList')"
                      />

                      <template v-else>
                        <!-- <template v-if="formData.identityDocumentFileList?.length">
                          <div
                            class="download-text"
                            v-for="(file, index) in formData.identityDocumentFileList"
                            :key="index"
                            @click="downloadFile(file)"
                          >
                            {{ file?.fileNames || '暂无文件' }}
                          </div>
                        </template>
                        <template v-else>
                          <span class="blue-text">暂无文件</span>
                        </template> -->
                        <fileList :list="formData.identityDocumentFileList" />
                      </template>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="12">
                    <el-form-item prop="identityDocumentFileList_1">
                      <ImageUploadBiz
                        v-model="formData.identityDocumentFileList_1"
                        myTip="上传身份证反面"
                        :limit="1"
                        :isShowTip="false"
                      />
                    </el-form-item>
                  </el-col> -->
                </el-row>
              </el-col>
            </el-row></Collapse
          >
          <Collapse title="银行开户">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item prop="openBankAccountFlag" label="客户是否需要进行银行开户">
                  <el-radio-group v-model="formData.openBankAccountFlag">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="formData.openBankAccountFlag">
                <el-form-item prop="openBankAccountAssignUserId" label=" 开户派工">
                  <SelectTree style="width: 100%" v-model="formData.openBankAccountAssignUserId" placeholder="请选择" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="!formData.openBankAccountFlag">
                <el-form-item prop="handoverDocumentFileList" label="交接单">
                  <FileUploadBiz
                    v-if="!disabledSteps.includes(3)"
                    v-model="formData.handoverDocumentFileList"
                    :isShowTip="false"
                    :limit="100"
                  />
                  <!-- <span class="download-text" @click="downloadFile(formData.handoverDocumentFileList[0])" v-else>
                    {{ formData.handoverDocumentFileList[0]?.fileNames || '暂无文件' }}</span
                  > -->
                  <fileList :list="formData.handoverDocumentFileList" v-else />
                </el-form-item>
              </el-col> </el-row
          ></Collapse>
        </el-form>
        <!-- 步骤5 -->
        <el-form
          v-if="active === 4"
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-position="top"
          :disabled="disabledSteps.includes(4)"
          :hide-required-asterisk="disabledSteps.includes(4)"
        >
          <Collapse title="办理进度汇报">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item>
                  <el-checkbox v-model="formData.stageNameList_bank_0" label="银行开户预约" />
                  <span class="time">{{ formData.stageNameList_bank_0_time }}</span></el-form-item
                ></el-col
              >
              <el-col :span="8">
                <el-form-item prop="stageNameList_bank_1">
                  <el-checkbox
                    :class="disabledSteps.includes(4) ? 'my-required-label my-required-label-extra-hide' : 'my-required-label'"
                    v-model="formData.stageNameList_bank_1"
                    label="银行开户"
                  />
                  <span class="time">{{ formData.stageNameList_bank_1_time }}</span></el-form-item
                ></el-col
              >
            </el-row>
            <el-row :gutter="24" v-if="formData.stageNameList_bank_0">
              <el-col :span="6">
                <el-form-item label="银行预约时间" prop="bankBookingTime">
                  <el-date-picker
                    style="width: 100%"
                    v-model="formData.bankBookingTime"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    :default-value="goodTime"
                    :default-time="goodTime"
                    type="datetime"
                    placeholder="请选择"
                  /> </el-form-item
              ></el-col>
            </el-row>
            <template v-if="formData.stageNameList_bank_1">
              <el-row :gutter="24">
                <el-col :span="6">
                  <el-form-item label="基本户开户银行" prop="bankBaseName">
                    <el-cascader
                      class="select-class"
                      v-model="formData.bankBaseName"
                      filterable
                      clearable
                      :placeholder="disabled ? ' ' : '请选择'"
                      :disabled="disabled"
                      :props="{
                        value: 'name',
                        label: 'name',
                        children: 'child'
                      }"
                      :options="bank_list"
                      :show-all-levels="false"
                    /> </el-form-item
                ></el-col>
                <el-col :span="6">
                  <el-form-item label="基本户账号" prop="bankBaseAccount">
                    <el-input v-model="formData.bankBaseAccount" /> </el-form-item
                ></el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="6">
                  <el-form-item label="回单卡" prop="receiptCardFlag">
                    <el-radio-group v-model="formData.receiptCardFlag">
                      <el-radio label="0">无</el-radio>
                      <el-radio label="1">有</el-radio>
                    </el-radio-group>
                  </el-form-item></el-col
                >
                <el-col :span="6">
                  <el-form-item v-show="formData.receiptCardFlag === '1'" label="回单卡类型" prop="receiptCardType"
                    ><el-radio-group v-model="formData.receiptCardType" @change="changeClear">
                      <el-radio label="卡">卡</el-radio>
                      <el-radio label="账号密码">账号密码</el-radio>
                      <el-radio label="自动获取">自动获取</el-radio>
                    </el-radio-group>
                  </el-form-item></el-col
                >
                <el-col :span="6">
                  <el-form-item
                    v-show="formData.receiptCardFlag === '1' && formData.receiptCardType === '账号密码'"
                    label="回单卡账号"
                    prop="receiptCardAccount"
                  >
                    <el-input v-model="formData.receiptCardAccount" /> </el-form-item
                ></el-col>
                <el-col :span="6">
                  <el-form-item
                    v-show="
                      formData.receiptCardFlag === '1' &&
                      (formData.receiptCardType === '账号密码' || formData.receiptCardType === '卡')
                    "
                    label="回单卡密码"
                    prop="receiptCardPassword"
                  >
                    <el-input v-model="formData.receiptCardPassword" /> </el-form-item
                ></el-col>
                <bankFormTable v-model="formData.bankInfoList" :disabled="disabledSteps.includes(4)" />
                <el-col :span="12">
                  <el-form-item label="基本存款账户信息表（开户许可证）" prop="bankAccountOpenLicenseFileList">
                    <FileUploadBiz
                      v-if="!disabledSteps.includes(4)"
                      v-model="formData.bankAccountOpenLicenseFileList"
                      :limit="100"
                      @on-load-success="validateFormField('bankAccountOpenLicenseFileList')"
                      :isShowTip="false"
                    />
                    <!-- <span class="download-text" @click="downloadFile(formData.bankAccountOpenLicenseFileList[0])" v-else>
                      {{ formData.bankAccountOpenLicenseFileList[0]?.fileNames || '暂无文件' }}</span
                    > -->
                    <fileList :list="formData.bankAccountOpenLicenseFileList" v-else />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="其他附件" prop="bankOtherDocumentFileList">
                    <FileUploadBiz
                      v-if="!disabledSteps.includes(4)"
                      :limit="100"
                      v-model="formData.bankOtherDocumentFileList"
                      :isShowTip="false"
                    />
                    <!-- <span class="download-text" @click="downloadFile(formData.bankOtherDocumentFileList[0])" v-else>
                      {{ formData.bankOtherDocumentFileList[0]?.fileNames || '暂无文件' }}</span
                    > -->

                    <fileList :list="formData.bankOtherDocumentFileList" v-else />
                  </el-form-item>
                </el-col>
                <!-- 交接单 -->
                <el-col :span="12">
                  <el-form-item label="交接单" prop="handoverDocumentFileList">
                    <FileUploadBiz
                      :limit="100"
                      v-if="!disabledSteps.includes(4)"
                      v-model="formData.handoverDocumentFileList"
                      @on-load-success="validateFormField('handoverDocumentFileList')"
                      :isShowTip="false"
                    />
                    <!-- <span class="download-text" @click="downloadFile(formData.handoverDocumentFileList[0])" v-else>
                      {{ formData.handoverDocumentFileList[0]?.fileNames || '暂无文件' }}</span
                    > -->
                    <fileList :list="formData.handoverDocumentFileList" v-else />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
          </Collapse>
        </el-form>
      </div>
    </div>
    <template #footer>
      <el-button @click="onHandleClose"> 关闭 </el-button>
      <template v-if="!disabledSteps.includes(active)">
        <!-- todo 000 -->
        <el-button v-hasPermi="['license:todo:save']" v-show="active !== 0" plain type="primary" @click="handleSave">
          保存
        </el-button>
        <el-button v-hasPermi="['license:todo:submit']" type="primary" @click="handleSubmit"> 提交 </el-button>
      </template>
    </template>
  </el-dialog>
  <transferFormModal ref="transferFormModalRef" @ok="onHandleClose" />
  <noticeFormModal ref="noticeFormModalRef" />

  <iFrame :src="previewUrl" v-model="previewShow" />
  <!-- 其他附件弹窗 -->
  <otherFiles :list="formData.otherFileList" v-if="fileShow" @on-close="fileShow = false" />
</template>

<script setup>
import {
  bizAssign,
  bizBankAccountOpen,
  bizBankAccountOpenComplete,
  bizDataCollection,
  bizDataCollectionComplete,
  bizDataUpload,
  bizDataUploadComplete,
  bizDeprecate,
  bizGetById,
  bizProcessReport,
  bizProcessReportComplete
} from '@/api/certificate/certificate'
import { getBankTreeList } from '@/api/basicData/basicData'
import { bizStageArr, bizTypeArr } from '@/utils/constants'
import dayjs from 'dayjs'
import bankFormTable from './bank-form-table'
import transferFormModal from './transfer-form-modal'
import noticeFormModal from './notice-form-modal'
import Collapse from '@/components/Collapse'
import SelectTree from '@/components/SelectTree'
import ImageUploadBiz from '@/components/ImageUploadBiz'
import FileUploadBiz from '@/components/FileUploadBiz'
import FileUpload from '@/components/FileUpload'
import iFrame from '@/components/iFrame'
import useUserStore from '@/store/modules/user'
import { cloneDeep } from 'lodash'
import { watch } from 'vue'
import { FormValidators } from '@/utils/validate'
import FormTable from '@/components/FormTable'
import collectionForm from './collectionForm'
import otherFiles from './other-files'
import fileList from './file-list'

const getLabel = bizType => {
  let label = bizTypeArr.filter(item => item.value === bizType)[0]?.label
  if (formData.changeSubject) {
    label = label + `(${formData.changeSubject} )`
  }

  if (props.productName?.includes('单办证')) {
    label = label + `(单办证)`
  }
  return label || '--'
}
// foreign_business_registration
// domestic_business_registration
// bank_account_open
// 文件上传后检验
const validateFormField = field => {
  formRef.value.validateField(field)
}

const stepMap = {
  foreign_business_registration: {
    0: '办证派工',
    1: '资料收集',
    2: '执照办理',
    3: '资料上传',
    4: '银行开户'
  },
  domestic_business_registration: {
    0: '办证派工',
    1: '资料收集',
    2: '执照办理',
    3: '资料上传',
    4: '银行开户'
  },
  bank_account_open: {
    0: '开户派工',
    1: '银行开户'
  }
}
const userStore = useUserStore()
const { proxy } = getCurrentInstance()
const emit = defineEmits()

const props = defineProps({
  actionType: {
    type: String,
    default: 'todo'
  },
  productName: {
    type: String,
    default: ''
  }
})
const licenseProcessRecordListDict = ['提交', '驳回', '五方会审', '完成', '办税']
const bankProcessRecordListDict = ['银行开户预约', '银行开户']

const active = ref(0)
const flowActive = ref(0)
const finishSteps = ref([])
const disabledSteps = ref([])
const couldSteps = ref([])
const typeSteps = ref([])
function handleClick(index) {
  console.log('index', index)
  if (couldSteps.value.includes(index)) {
    active.value = index
  }
  // 步骤四的操作
  if (index === 3) {
    // 法人身份证
    // 监事身份证
    // 股东身份证
  }
}

const setDefaultIdentityCard = () => {
  const { legalIdentityFileList, supervisorIdentityFileList, shareholderInfoList } = formData
  const fileList = []
  shareholderInfoList.forEach(item => {
    if (Array.isArray(item.shareholderFileList) && item.shareholderFileList.length) {
      fileList.push(...item.shareholderFileList)
    }
  })
  const list = [...legalIdentityFileList, ...supervisorIdentityFileList, ...fileList]

  formData.identityDocumentFileList = list.map(item => {
    return {
      ...item,
      id: undefined
    }
  })
}

const formRef = ref(null)
const formTableRef = ref()
const formData = reactive({
  taskId_temp: undefined,
  legalIdentityFileList: [], // 法人身份证
  supervisorIdentityFileList: [], // 监事身份证,
  shareholderInfoList: [
    // {
    //   taskId: '', // 任务id
    //   shareholderName: '', // 股东姓名
    //   shareholderPhone: '', // 股东手机号
    //   shareholderFileList: [] // 股东附件
    // }
  ], // 股东信息表格
  identityDocumentFileList: [], // 身份证件list集合
  openBankAccountFlag: false,
  rules: {
    shareholderName: [
      {
        required: false,
        message: '请输入',
        trigger: ['blur']
      }
    ],
    shareholderPhone: [
      {
        required: false,
        validator: FormValidators.mobilePhone,
        trigger: ['blur']
      }
    ],
    shareholderFileList: [
      {
        required: false,
        message: '请选择',
        trigger: ['change']
      }
    ]
  } // 股东表格信息校验规则
})

const validateFile = index => {
  console.log('formRef', formTableRef.value.formRef, index)
  formTableRef.value.formRef.validateField(`tableData.${index}.shareholderFileList`)
}
const option = ref([
  {
    prop: 'shareholderName',
    label: '股东姓名'
  },
  {
    prop: 'shareholderPhone',
    label: '股东手机号'
  },
  {
    prop: 'shareholderFileList',
    label: '股东身份证'
  },
  {
    prop: 'action',
    label: '操作'
  }
])

const handleAddRow = () => {
  formData.shareholderInfoList.push({
    taskId: formData.taskId_temp, // 任务id
    shareholderName: '', // 股东姓名
    shareholderPhone: '', // 股东手机号
    shareholderFileList: [] // 股东附件
  })
}

const handleDeleteRow = index => {
  formData.shareholderInfoList.splice(index, 1)
}
const formData_step_0 = reactive({
  /* 步骤1 */
  userId: undefined
})
const formData_step_1 = reactive({
  /* 步骤2 */
  legalIdentityFileList: [],
  supervisorIdentityFileList: [],
  legalPhone: '',
  supervisorPhone: '',
  ownershipCertificateFileList: [],
  consultationMaterialFileList: [],
  foreignEnterpriseCertificateFileList: [],
  translatedDocumentFileList: [],
  foreignPassportFileList: [],
  newRegistrationInformationConfirmationDocumentFileList: []
})
const formData_step_2 = reactive({
  /* 步骤3 */
  stageNameList: []
})
const formData_step_3 = reactive({
  /* 步骤4 */
  addressFileList: [],
  businessConstitutionFileList: [],
  businessLicenseFileList: [],
  customerName: '',
  handoverDocumentFileList: [],
  identityDocumentFileList: [],
  naturalPersonPwd: '',
  openBankAccountAssignUserId: undefined,
  openBankAccountFlag: undefined,
  otherDocumentFileList: [],
  registrationInformationFileList: [],
  shareholderCommitteeResolutionFileList: [],
  taxProcessingDocumentFileList: []
})
const formData_step_4 = reactive({
  /* 步骤5 */
  bankAccountOpenLicenseFileList: [],
  bankBaseAccount: '',
  bankBaseName: '',
  bankBookingTime: '',
  bankId: undefined,
  bankInfoList: [],
  bankOtherDocumentFileList: [],
  receiptCardAccount: '',
  receiptCardFlag: '',
  receiptCardPassword: '',
  receiptCardType: '',
  stageNameList: [], // 只在提交时在formDataTemp里临时使用，在formData里不会与步骤3的stageNameList冲突
  deliveryReceiptFileList: [],
  // 交接单
  handoverDocumentFileList: []
})

const rules = {
  /* 步骤1 */
  userId: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  /* 步骤2 */
  legalIdentityFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  legalIdentityFileList_0: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  legalIdentityFileList_1: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  supervisorIdentityFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  supervisorIdentityFileList_0: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  supervisorIdentityFileList_1: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  legalPhone: [
    { required: true, message: '请输入', trigger: ['blur', 'change'] },
    {
      trigger: 'blur',
      validator: FormValidators.mobilePhone
    }
  ],
  supervisorPhone: [
    { required: true, message: '请输入', trigger: ['blur', 'change'] },
    {
      trigger: 'blur',
      validator: FormValidators.mobilePhone
    }
  ],
  ownershipCertificateFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  // consultationMaterialFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  foreignEnterpriseCertificateFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  translatedDocumentFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  foreignPassportFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  newRegistrationInformationConfirmationDocumentFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  /* 步骤3 */
  stageNameList_0: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  stageNameList_3: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  stageNameList_4: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  /* 步骤4 */
  addressFileList: [{ required: false, message: '请选择', trigger: ['blur', 'change'] }],
  businessConstitutionFileList: [{ required: false, message: '请选择', trigger: ['blur', 'change'] }],
  businessLicenseFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  customerName: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  handoverDocumentFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  identityDocumentFileList: [{ required: false, message: '请选择', trigger: ['blur', 'change'] }],
  identityDocumentFileList_0: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  identityDocumentFileList_1: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  naturalPersonPwd: [{ required: false, message: '请输入', trigger: ['blur', 'change'] }],
  openBankAccountAssignUserId: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  openBankAccountFlag: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  otherDocumentFileList: [{ required: false, message: '请选择', trigger: ['blur', 'change'] }],
  registrationInformationFileList: [{ required: false, message: '请选择', trigger: ['blur', 'change'] }],
  shareholderCommitteeResolutionFileList: [{ required: false, message: '请选择', trigger: ['blur', 'change'] }],
  taxProcessingDocumentFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  /* 步骤5 */
  stageNameList_bank_1: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  bankBookingTime: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  bankAccountOpenLicenseFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
  deliveryReceiptFileList: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }]
}

const visible = ref(false)
const onShow = async row => {
  formData.taskId_temp = row.id
  await onGetDetail() // 要在active赋值前把数据拼装好
  visible.value = true
}

const formDataBase = ref({})
async function onGetDetail() {
  await bizGetById({ id: formData.taskId_temp }).then(res => {
    Object.assign(formData, res.data, res.data.dataCollection, res.data.taskFileVO, {
      shareholderInfoList: res.data.shareholderInfoList || []
    })
    formData.customerNametemp = res.data.customerName
    formData.customerName = res.data.dataCollection.customerName
    // 处理步骤1
    formData.userId = formData.handleUserId
    // 在初次进入步骤2前，处理数据类型
    for (const key in formData_step_1) {
      formData[key] = ![undefined, null].includes(formData[key]) ? formData[key] : formData_step_1[key]
    }
    // console.log('formData.ownershipCertificateFileList', JSON.stringify(formData.ownershipCertificateFileList))
    // 处理步骤2
    for (const key in formData_step_1) {
      if (formData[key] && formData[key].length) {
        formData[`${key}Bool`] = true
      } else {
        formData[`${key}Bool`] = false
      }
    }
    // 处理步骤3
    licenseProcessRecordListDict.forEach((item, index) => {
      // console.log('item', item, index)
      formData[`stageNameList_${index}`] = res.data.licenseProcessRecordList.find(itemx => itemx.stageName === item)
        ? true
        : undefined
      formData[`stageNameList_${index}_bool`] = res.data.licenseProcessRecordList.find(itemx => itemx.stageName === item)
        ? true
        : undefined
      formData[`stageNameList_${index}_time`] = res.data.licenseProcessRecordList
        .find(itemx => itemx.stageName === item)
        ?.completeTime?.replace('T', ' ')
    })
    console.log('onGetDetail', JSON.parse(JSON.stringify(formData)))
    // 处理步骤4
    for (const key in formData_step_3) {
      formData[key] = formData[key] !== undefined ? formData[key] : formData_step_3[key]
    }
    // 注释默认填充身份证文件逻辑
    // if (Array.isArray(formData.identityDocumentFileList) && !formData.identityDocumentFileList.length) {
    //   setDefaultIdentityCard()
    // }

    // formData['identityDocumentFileList_0'] = formData['identityDocumentFileList']
    //   ? formData['identityDocumentFileList'][0]
    //   : undefined
    // formData['identityDocumentFileList_1'] = formData['identityDocumentFileList']
    //   ? formData['identityDocumentFileList'][1]
    //   : undefined
    console.log('onGetDetail', JSON.parse(JSON.stringify(formData)))
    formData.customerName = formData.dataCollection.customerName || res.data.customerName
    // 处理步骤5
    for (const key in formData_step_4) {
      formData[key] = ![undefined, null].includes(formData[key]) ? formData[key] : formData_step_4[key]
    }
    bankProcessRecordListDict.forEach((item, index) => {
      formData[`stageNameList_bank_${index}`] = res.data.bankProcessRecordList.find(itemx => itemx.stageName === item)
        ? true
        : undefined
      formData[`stageNameList_bank_${index}_time`] = res.data.bankProcessRecordList
        .find(itemx => itemx.stageName === item)
        ?.completeTime?.replace('T', ' ')
    })
    formData.bankBaseName = formData.bankBaseName?.split(',') || []
    formData.bankInfoList.forEach(item => {
      item.commonBankName = item.commonBankName?.split(',') || []
    })
    console.log('onGetDetail', JSON.parse(JSON.stringify(formData)))
    // 处理步骤条
    active.value = bizStageArr.find(item => item.value === formData.bizStage).active
    flowActive.value = bizStageArr.find(item => item.value === formData.bizStage).active
    finishSteps.value = bizStageArr.find(item => item.value === formData.bizStage).finishSteps
    disabledSteps.value = bizStageArr.find(item => item.value === formData.bizStage).disabledSteps // todo 000
    couldSteps.value = bizStageArr.find(item => item.value === formData.bizStage).couldSteps
    typeSteps.value = bizTypeArr.find(item => item.value === formData.bizType).typeSteps
    if (formData.bizType !== 'bank_account_open' && formData.bizStage === 'completed' && !formData.openBankAccountFlag) {
      active.value = 3
      flowActive.value = 3
      typeSteps.value = [0, 1, 2, 3]
    }
    // 银行开户
    // if (formData.bizType === 'bank_account_open' && formData.bizStage === 'completed' && formData.bizStage === 'completed') {
    //   active.value = 1
    //   typeSteps.value = [0, 1]
    // }
    if (formData.bizStage === 'deprecated' && formData.deprecatedBizStage) {
      typeSteps.value = bizStageArr.find(item => item.value === formData.deprecatedBizStage).couldSteps
      active.value = bizStageArr.find(item => item.value === formData.deprecatedBizStage).active
      flowActive.value = bizStageArr.find(item => item.value === formData.deprecatedBizStage).active
    }
    if (formData.bizStage === 'deprecated' && formData.deprecatedBizStage === 'bank_account_open') {
      typeSteps.value = [0, 1]
    }
    if (formData.openBankAccountFlag === null) {
      formData.openBankAccountFlag = false
    }
    // 将处理好后的formData数据备用一份
    formDataBase.value = cloneDeep(formData)

    //删除详情中的股东信息表格操作列
    if (disabledSteps.value.includes(1)) {
      const index = option.value.findIndex(item => item.prop === 'action')
      if (index > 0) {
        option.value.splice(index, 1)
      }
    }

    // 默认地址 填写 步骤二中的产权证明(在编辑页面中 如果是详情就不需要)
    if (!disabledSteps.value.includes(3) && Array.isArray(formData.addressFileList) && !formData.addressFileList.length) {
      formData.addressFileList = formData.ownershipCertificateFileList.map(item => {
        return {
          ...item,
          id: undefined
        }
      })
    }
  })
}

const onHandleClose = () => {
  emit('ok')
  formRef.value.resetFields()
  visible.value = false
}

const collectionFormRef = ref()
const validateShareholderInfoList = async () => {
  // 框选的情况下检验股东信息
  if (formData.shareholderInfoListFlag) {
    let result
    if (!collectionFormRef.value) {
      result = await formTableRef.value.handleValidate()
    } else {
      result = await collectionFormRef.value.getFormTableRef().handleValidate()
    }
    // const result = await formTableRef.value.handleValidate()
    // formTableRef
    if (!result) {
      // proxy.$message.warning('打勾后必须上传完整附件或输入文本!')
      return false
    } else {
      return true
    }
  } else {
    return true
  }
}

// 检验步骤二 表单的检验方法
const collectionFormValidate = async formDataTemp => {
  let baseValidate = true

  // for (const key in formData_step_1) {
  //   if (formDataTemp[`${key}Bool`] && (!formDataTemp[key] || !formDataTemp[key].length)) {
  //     // proxy.$message.warning('打勾后必须上传完整附件或输入文本!')
  //     // return false
  //     baseValidate = false
  //   }
  //   // 针对法人身份证 和 监事身份证
  //   if (formData.legalIdentityFileList.checked && !formData.legalIdentityFileList.length) {
  //     // proxy.$message.warning('打勾后必须上传完整附件或输入文本!')
  //     // return false
  //     baseValidate = false
  //   }
  //   if (formData.supervisorIdentityFileList.checked && !formData.supervisorIdentityFileList.length) {
  //     // proxy.$message.warning('打勾后必须上传完整附件或输入文本!')
  //     // return false
  //     baseValidate = false
  //   }
  // }

  // 检验股东信息表格
  const valResult = await validateShareholderInfoList()

  const strs = ['legalPhone', 'supervisorPhone']
  // 校验 法人手机号 操作手机号 邮箱格式是否正确
  let valResult1 = true,
    valResult2 = true
  if (formData.legalPhone) {
    valResult1 = await formRef.value.validateField('legalPhone')
  }
  if (formData.supervisorPhone) {
    valResult2 = await formRef.value.validateField('supervisorPhone')
  }
  // if (!baseValidate) {
  //   proxy.$message.warning('打勾后必须上传完整附件或输入文本!')
  //   return false
  // }
  if (!valResult) {
    return false
  } else {
    // 如果检验成功
    formDataTemp.shareholderInfoList = formData.shareholderInfoList
  }

  if (!valResult1 || !valResult2) {
    return false
  }

  return true
}

// 步骤二和步骤三(工商办证-新注册-资料收集和执照办理步骤下的内容保存和提交检验)
// 只需要打勾就可以保存、提交（不需要填写内容和上传文件）

//保存原来的逻辑是1.不打勾也可以保存，2.如果打勾了就必须上传文件或者输入内容，现在就是2条件修改成保存的时候打勾了不用上传文件或者输入内容，1条件还是保留

// 保存逻辑
const collectionOrHandleStepValidateSave = () => {}
// 提交逻辑
const collectionOrHandleStepValidateSubmit = data => {
  // 处理步骤二的上传文件 和 手机号
  const map = {
    legalPhone: '',
    supervisorPhone: '',
    ownershipCertificateFileList: [],
    // newRegistrationInformationConfirmationDocumentFileList: [] // 新注册信息确认单
    newRegistrationFileList: [] // 新注册信息确认单
  }
  // 如果是外资新增三个字段名
  if (data.bizType === 'foreign_business_registration') {
    Object.assign(map, {
      foreignEnterpriseCertificateFileList: [],
      translatedDocumentFileList: [],
      foreignPassportFileList: []
    })
  }

  // 针对法人身份证 和 监事身份证

  if (!data.legalIdentityFileListFlag) {
    proxy.$message.warning('打*项请打上勾再进行提交!')
    return false
  }

  if (!data.supervisorIdentityFileListFlag) {
    proxy.$message.warning('打*项请打上勾再进行提交!')
    return false
  }
  for (const key in map) {
    if (!data[`${key}Flag`]) {
      proxy.$message.warning('打*项请打上勾再进行提交!')
      return false
    }
  }
  return true
}

// 股东信息数据做处理，去除存在股东名称、手机号以及文件都不存在的行
const translateShareholderInfoList = list => {
  return list.filter(
    item => item.shareholderName || item.shareholderPhone || (item.shareholderFileList && item.shareholderFileList.length)
  )
}

// 注意：以后别写这种switch,可读性和维护性太差，请使用策略模式优化
const handleSave = async () => {
  formRef.value.clearValidate()
  switch (active.value) {
    case 1:
      {
        const legalIdentityFileList = formData.legalIdentityFileList.map(item => {
          return {
            ...item,
            id: undefined
          }
        })
        const supervisorIdentityFileList = formData.supervisorIdentityFileList.map(item => {
          return {
            ...item,
            id: undefined
          }
        })
        // 股东信息数据做处理

        const formDataTemp = Object.assign({}, formData, {
          legalIdentityFileList,
          supervisorIdentityFileList,
          taskId: formData.taskId_temp
        })

        //collectionFormValidate
        const result = await collectionFormValidate(formDataTemp)

        formDataTemp.shareholderInfoList = translateShareholderInfoList(formData.shareholderInfoList)

        if (!result) return
        bizDataCollection(formDataTemp).then(res => {
          if (res.code === 200) {
            proxy.$message.success(res.msg || res.message)
            onGetDetail()
          }
        })
      }
      break
    case 2:
      {
        // 将formDataBase中在步骤二的表单数据取出
        const {
          legalIdentityFileList,
          legalIdentityFileListFlag,
          supervisorIdentityFileList,
          supervisorIdentityFileListFlag,
          legalPhone,
          legalPhoneFlag,
          supervisorPhone,
          supervisorPhoneFlag,
          ownershipCertificateFileList,
          ownershipCertificateFileListFlag,
          consultationMaterialFileList,
          consultationMaterialFileListFlag,
          foreignEnterpriseCertificateFileList,
          foreignEnterpriseCertificateFileListFlag,
          translatedDocumentFileList,
          translatedDocumentFileListFlag,
          foreignPassportFileList,
          foreignPassportFileListFlag,
          newRegistrationInformationConfirmationDocumentFileList,
          newRegistrationFileListFlag,
          shareholderInfoList,
          shareholderInfoListFlag
        } = formDataBase.value
        const formDataTemp = Object.assign(
          {},
          formData,
          { taskId: formData.taskId_temp, thirdFlag: true },
          {
            legalIdentityFileList,
            legalIdentityFileListFlag,
            supervisorIdentityFileList,
            supervisorIdentityFileListFlag,
            legalPhone,
            legalPhoneFlag,
            supervisorPhone,
            supervisorPhoneFlag,
            ownershipCertificateFileList,
            ownershipCertificateFileListFlag,
            consultationMaterialFileList,
            consultationMaterialFileListFlag,
            foreignEnterpriseCertificateFileList,
            foreignEnterpriseCertificateFileListFlag,
            translatedDocumentFileList,
            translatedDocumentFileListFlag,
            foreignPassportFileList,
            foreignPassportFileListFlag,
            newRegistrationInformationConfirmationDocumentFileList,
            newRegistrationFileListFlag,
            shareholderInfoList,
            shareholderInfoListFlag
          }
        )
        formDataTemp.stageNameList = [
          formDataTemp.stageNameList_0 ? '提交' : undefined,
          formDataTemp.stageNameList_1 ? '驳回' : undefined,
          formDataTemp.stageNameList_2 ? '五方会审' : undefined,
          formDataTemp.stageNameList_3 ? '完成' : undefined,
          formDataTemp.stageNameList_4 ? '办税' : undefined
        ]
        //collectionFormValidate
        // const result = await collectionFormValidate(formDataTemp)
        const result = await collectionFormRef.value.collectionFormValidate()
        if (!result) return
        formDataTemp.shareholderInfoList = translateShareholderInfoList(shareholderInfoList)
        // 步骤二的接口也要调用
        bizDataCollection(formDataTemp).then(res => {
          if (res.code === 200) {
            // proxy.$message.success(res.msg || res.message)
            bizProcessReport(formDataTemp).then(res => {
              if (res.code === 200) {
                proxy.$message.success(res.msg || res.message)
                onGetDetail()
              }
            })
          }
        })
      }
      break
    case 3:
      {
        // if (
        //   (!formData['identityDocumentFileList_0']?.urls && formData['identityDocumentFileList_1']?.urls) ||
        //   (formData['identityDocumentFileList_0']?.urls && !formData['identityDocumentFileList_1']?.urls)
        // ) {
        //   proxy.$message.warning('请上传完整身份证件!')
        //   return
        // }
        // const identityDocumentFileList =
        //   formData['identityDocumentFileList_0']?.urls && formData['identityDocumentFileList_1']?.urls
        //     ? [formData['identityDocumentFileList_0'], formData['identityDocumentFileList_1']]
        //     : []
        // identityDocumentFileList.forEach(item => {
        //   item.id = undefined
        // })
        // if (!formData.identityDocumentFileList?.length) {
        //   proxy.$message.warning('请上传完整身份证件!')
        //   return
        // }
        const identityDocumentFileList = formData.identityDocumentFileList?.map(item => {
          return {
            ...item,
            id: undefined
          }
        })
        const formDataTemp = Object.assign({}, formData, {
          identityDocumentFileList,
          taskId: formData.taskId_temp
        })
        bizDataUpload(formDataTemp).then(res => {
          if (res.code === 200) {
            proxy.$message.success(res.msg || res.message)
          }
        })
      }
      break
    case 4:
      {
        formRef.value.validateField('bankBookingTime', valid => {
          if (formData.stageNameList_bank_0 && !valid) return
          const formDataTemp = Object.assign({}, { taskId: formData.taskId_temp })
          Object.keys(formData_step_4).forEach(key => {
            formDataTemp[key] = cloneDeep(formData[key])
          })
          formDataTemp.stageNameList = [
            formData.stageNameList_bank_0 ? '银行开户预约' : undefined,
            formData.stageNameList_bank_1 ? '银行开户' : undefined
          ]
          formDataTemp.bankBookingTime = formData.bankBookingTime?.replace('T', ' ')
          formDataTemp.bankBaseName = (Array.isArray(formData.bankBaseName) && formData.bankBaseName.join(',')) || ''
          formDataTemp.bankInfoList.forEach(item => {
            item.commonBankName = (Array.isArray(item.commonBankName) && item.commonBankName.join(',')) || ''
          })
          bizBankAccountOpen(formDataTemp).then(res => {
            if (res.code === 200) {
              proxy.$message.success(res.msg || res.message)
              onGetDetail()
            }
          })
        })
      }
      break
  }
}

// 注释：类似这种多选择条件的逻辑处理 请使用 策略模式进行优化，以下代码没有可读性、维护性
const handleSubmit = async () => {
  switch (active.value) {
    case 0:
      {
        formRef.value.validate(valid => {
          if (!valid) return
          const formDataTemp = Object.assign({}, formData, { taskId: formData.taskId_temp })
          console.log('formDataTemp', formDataTemp)
          bizAssign(formDataTemp).then(res => {
            if (res.code === 200) {
              proxy.$message.success(res.msg || res.message)
              if (formDataTemp.userId === userStore.user.userId) {
                onGetDetail()
                // if (formData.bizType === 'bank_account_open') {
                //   active.value = 4
                //   flowActive.value = 4
                // } else {
                //   active.value++
                //   flowActive.value++
                // }
                // formData.bizStage = bizStageArr.find(item => item.active === active.value).value
                // finishSteps.value = bizStageArr.find(item => item.value === formData.bizStage).finishSteps
                // disabledSteps.value = bizStageArr.find(item => item.value === formData.bizStage).disabledSteps
                // couldSteps.value = bizStageArr.find(item => item.value === formData.bizStage).couldSteps
              } else {
                onHandleClose()
              }
            }
          })
        })
      }
      break
    case 1:
      {
        // formRef.value.validate(async valid => {
        //   if (!valid) return
        // })
        const legalIdentityFileList = formData.legalIdentityFileList.map(item => {
          return {
            ...item,
            id: undefined
          }
        })
        const supervisorIdentityFileList = formData.supervisorIdentityFileList.map(item => {
          return {
            ...item,
            id: undefined
          }
        })

        const formDataTemp = Object.assign({}, formData, {
          legalIdentityFileList,
          supervisorIdentityFileList,
          taskId: formData.taskId_temp
        })
        const valid = collectionOrHandleStepValidateSubmit(formData)
        if (!valid) return
        // 提交时对股东信息表格进行检验
        const valResult = await validateShareholderInfoList()

        if (!valResult) {
          return
        } else {
          // 如果检验成功
          formDataTemp.shareholderInfoList = formData.shareholderInfoList
        }
        formDataTemp.shareholderInfoList = translateShareholderInfoList(formData.shareholderInfoList)
        bizDataCollectionComplete(formDataTemp).then(res => {
          if (res.code === 200) {
            proxy.$message.success(res.msg || res.message)
            onGetDetail()
            // active.value++
            // flowActive.value++
            // formData.bizStage = bizStageArr.find(item => item.active === active.value).value
            // finishSteps.value = bizStageArr.find(item => item.value === formData.bizStage).finishSteps
            // disabledSteps.value = bizStageArr.find(item => item.value === formData.bizStage).disabledSteps
            // couldSteps.value = bizStageArr.find(item => item.value === formData.bizStage).couldSteps
          }
        })
      }
      break
    case 2:
      {
        // 提交同时还要检验 collectionForm组件中的表单
        const valResult = await collectionFormRef.value.validateForm()
        if (!valResult) return

        // 同时校验提交、完成和办税这三个星号复选框被勾选
        const map = {
          stageNameList_0: '',
          stageNameList_3: '',
          stageNameList_4: ''
        }
        for (const key in map) {
          if (!formData[key]) {
            proxy.$message.warning('打*项请打上勾再进行提交!')
            return false
          }
        }
        // 将formDataBase中在步骤二的表单数据取出
        const {
          legalIdentityFileList,
          legalIdentityFileListFlag,
          supervisorIdentityFileList,
          supervisorIdentityFileListFlag,
          legalPhone,
          legalPhoneFlag,
          supervisorPhone,
          supervisorPhoneFlag,
          ownershipCertificateFileList,
          ownershipCertificateFileListFlag,
          consultationMaterialFileList,
          consultationMaterialFileListFlag,
          foreignEnterpriseCertificateFileList,
          foreignEnterpriseCertificateFileListFlag,
          translatedDocumentFileList,
          translatedDocumentFileListFlag,
          foreignPassportFileList,
          foreignPassportFileListFlag,
          newRegistrationInformationConfirmationDocumentFileList,
          newRegistrationFileListFlag,
          shareholderInfoList,
          shareholderInfoListFlag
        } = formDataBase.value
        const formDataTemp = Object.assign(
          {},
          formData,
          {
            taskId: formData.taskId_temp,
            openBankAccountFlag: true,
            thirdFlag: true
          },
          {
            legalIdentityFileList,
            legalIdentityFileListFlag,
            supervisorIdentityFileList,
            supervisorIdentityFileListFlag,
            legalPhone,
            legalPhoneFlag,
            supervisorPhone,
            supervisorPhoneFlag,
            ownershipCertificateFileList,
            ownershipCertificateFileListFlag,
            consultationMaterialFileList,
            consultationMaterialFileListFlag,
            foreignEnterpriseCertificateFileList,
            foreignEnterpriseCertificateFileListFlag,
            translatedDocumentFileList,
            translatedDocumentFileListFlag,
            foreignPassportFileList,
            foreignPassportFileListFlag,
            newRegistrationInformationConfirmationDocumentFileList,
            newRegistrationFileListFlag,
            shareholderInfoList,
            shareholderInfoListFlag
          }
        )
        formDataTemp.stageNameList = [
          formDataTemp.stageNameList_0 ? '提交' : undefined,
          formDataTemp.stageNameList_1 ? '驳回' : undefined,
          formDataTemp.stageNameList_2 ? '五方会审' : undefined,
          formDataTemp.stageNameList_3 ? '完成' : undefined,
          formDataTemp.stageNameList_4 ? '办税' : undefined
        ]
        formDataTemp.shareholderInfoList = translateShareholderInfoList(shareholderInfoList)
        // 步骤二的提交接口也要调用
        bizDataCollectionComplete(formDataTemp).then(res => {
          if (res.code === 200) {
            bizProcessReportComplete(formDataTemp).then(res => {
              if (res.code === 200) {
                proxy.$message.success(res.msg || res.message)
                onGetDetail()
              }
            })
          }
        })
      }
      break
    case 3:
      {
        formRef.value.validate(valid => {
          if (!valid) return
          // const identityDocumentFileList =
          //   formData['identityDocumentFileList_0']?.urls && formData['identityDocumentFileList_1']?.urls
          //     ? [formData['identityDocumentFileList_0'], formData['identityDocumentFileList_1']]
          //     : []
          // identityDocumentFileList.forEach(item => {
          //   item.id = undefined
          // })
          const identityDocumentFileList = formData.identityDocumentFileList
          const formDataTemp = Object.assign({}, formData, {
            identityDocumentFileList,
            taskId: formData.taskId_temp
          })
          console.log('formDataTemp', formDataTemp)
          bizDataUploadComplete(formDataTemp).then(res => {
            if (res.code === 200) {
              proxy.$message.success(res.msg || res.message)
              formData.customerNametemp = formData.customerName
              if (formDataTemp.openBankAccountAssignUserId === userStore.user.userId) {
                onGetDetail()
                // active.value++
                // flowActive.value++
                // formData.bizStage = bizStageArr.find(item => item.active === active.value).value
                // finishSteps.value = bizStageArr.find(item => item.value === formData.bizStage).finishSteps
                // disabledSteps.value = bizStageArr.find(item => item.value === formData.bizStage).disabledSteps
                // couldSteps.value = bizStageArr.find(item => item.value === formData.bizStage).couldSteps
              } else {
                onHandleClose() // 指派给别人，或者选择不办理开户
                if (
                  !formDataTemp.openBankAccountFlag &&
                  formDataTemp.outsourcedAccountingBizFlag &&
                  ['domestic_business_registration', 'foreign_business_registration'].includes(formDataTemp.bizType)
                ) {
                  onHandleNotice()
                }
              }
            }
          })
        })
      }
      break
    case 4:
      {
        formRef.value.validate(valid => {
          if (!valid) return
          const formDataTemp = Object.assign({}, { taskId: formData.taskId_temp })
          Object.keys(formData_step_4).forEach(key => {
            formDataTemp[key] = cloneDeep(formData[key])
          })
          formDataTemp.outsourcedAccountingBizFlag = formData.outsourcedAccountingBizFlag
          formDataTemp.bizType = formData.bizType
          formDataTemp.stageNameList = [
            formData.stageNameList_bank_0 ? '银行开户预约' : undefined,
            formData.stageNameList_bank_1 ? '银行开户' : undefined
          ]
          formDataTemp.bankBookingTime = formData.bankBookingTime?.replace('T', ' ')
          formDataTemp.bankBaseName = (Array.isArray(formData.bankBaseName) && formData.bankBaseName.join(',')) || ''
          formDataTemp.bankInfoList.forEach(item => {
            item.commonBankName = (Array.isArray(item.commonBankName) && item.commonBankName.join(',')) || ''
          })
          console.log('formDataTemp', formDataTemp.bankInfoList)
          console.log('formData', formData.bankInfoList)
          bizBankAccountOpenComplete(formDataTemp).then(res => {
            if (res.code === 200) {
              proxy.$message.success(res.msg || res.message)
              onHandleClose()
              if (
                formDataTemp.outsourcedAccountingBizFlag &&
                ['domestic_business_registration', 'foreign_business_registration'].includes(formDataTemp.bizType)
              ) {
                onHandleNotice()
              }
            }
          })
        })
      }
      break
  }
}

const transferFormModalRef = ref(null)
function handleTransfer() {
  transferFormModalRef.value.onShow({ taskId: formData.taskId_temp })
}

function handleDel() {
  proxy.$modal
    .confirm('是否确认作废本次业务办理？')
    .then(function () {
      bizDeprecate({ id: formData.taskId_temp }).then(res => {
        if (res.code === 200) {
          proxy.$message.success('作废成功')
          onHandleClose()
        }
      })
      return
    })
    .catch(() => {})
}

const noticeFormModalRef = ref(null)
function onHandleNotice() {
  noticeFormModalRef.value.onShow({ taskId: formData.taskId_temp })
}

// 预览功能
const previewUrl = ref('')
const previewShow = ref(false)
const downloadFile = file => {
  if (file && file?.urls) {
    previewShow.value = true
    previewUrl.value = file.urls
  }
}

const handleChange = node => {}

const bank_list = ref([])
function onGetBasicData() {
  getBankTreeList({ enable: 1 }).then(res => {
    bank_list.value = res.data
  })
}
onGetBasicData()

defineExpose({
  onShow
})

// legalIdentityFileList
// supervisorIdentityFileList
// ownershipCertificateFileList
// foreignEnterpriseCertificateFileList
// translatedDocumentFileList
// foreignPassportFileList
// newRegistrationInformationConfirmationDocumentFileList

// watch(
//   () => formData.legalIdentityFileList_0,
//   () => {
//     if (formRef.value && active.value === 1 && formData.legalIdentityFileList_0?.urls) {
//       formRef.value.validateField('legalIdentityFileList_0')
//     }
//   }
// )
// watch(
//   () => formData.legalIdentityFileList_1,
//   () => {
//     if (formRef.value && active.value === 1 && formData.legalIdentityFileList_1?.urls) {
//       formRef.value.validateField('legalIdentityFileList_1')
//     }
//   }
// )
// watch(
//   () => formData.supervisorIdentityFileList_0,
//   () => {
//     if (formRef.value && active.value === 1 && formData.supervisorIdentityFileList_0?.urls) {
//       formRef.value.validateField('supervisorIdentityFileList_0')
//     }
//   }
// )
// watch(
//   () => formData.supervisorIdentityFileList_1,
//   () => {
//     if (formRef.value && active.value === 1 && formData.supervisorIdentityFileList_1?.urls) {
//       formRef.value.validateField('supervisorIdentityFileList_1')
//     }
//   }
// )

// watch
// watch(
//   () => formData.shareholderInfoList,
//   val => {
//     // if (val.length) {
//     //   formData.shareholderInfoList.checked = true
//     // }
//     if (formData.shareholderInfoListFlag && !val.length) {
//       console.log('handleAddRow')
//       handleAddRow()
//     }
//   }
// )
watch(
  () => formData.ownershipCertificateFileList,
  () => {
    if (formRef.value && active.value === 1 && formData.ownershipCertificateFileList[0]?.urls) {
      formRef.value.validateField('ownershipCertificateFileList')
    }
  }
)
watch(
  () => formData.foreignEnterpriseCertificateFileList,
  () => {
    if (formRef.value && active.value === 1 && formData.foreignEnterpriseCertificateFileList[0]?.urls) {
      formRef.value.validateField('foreignEnterpriseCertificateFileList')
    }
  }
)
watch(
  () => formData.translatedDocumentFileList,
  () => {
    if (formRef.value && active.value === 1 && formData.translatedDocumentFileList[0]?.urls) {
      formRef.value.validateField('translatedDocumentFileList')
    }
  }
)
watch(
  () => formData.foreignPassportFileList,
  () => {
    if (formRef.value && active.value === 1 && formData.foreignPassportFileList[0]?.urls) {
      formRef.value.validateField('foreignPassportFileList')
    }
  }
)
watch(
  () => formData.newRegistrationInformationConfirmationDocumentFileList,
  () => {
    if (formRef.value && active.value === 1 && formData.newRegistrationInformationConfirmationDocumentFileList[0]?.urls) {
      formRef.value.validateField('newRegistrationInformationConfirmationDocumentFileList')
    }
  }
)

// businessLicenseFileList
// taxProcessingDocumentFileList
// handoverDocumentFileList
// registrationInformationFileList
// businessConstitutionFileList
// shareholderCommitteeResolutionFileList
// addressFileList
// otherDocumentFileList
// identityDocumentFileList
watch(
  () => formData.businessLicenseFileList,
  () => {
    if (formRef.value && active.value === 3 && formData.businessLicenseFileList[0]?.urls) {
      formRef.value.validateField('businessLicenseFileList')
    }
  }
)
watch(
  () => formData.taxProcessingDocumentFileList,
  () => {
    if (formRef.value && active.value === 3 && formData.taxProcessingDocumentFileList[0]?.urls) {
      formRef.value.validateField('taxProcessingDocumentFileList')
    }
  }
)
watch(
  () => formData.handoverDocumentFileList,
  () => {
    if (formRef.value && active.value === 3 && formData.handoverDocumentFileList[0]?.urls) {
      formRef.value.validateField('handoverDocumentFileList')
    }
  }
)
watch(
  () => formData.registrationInformationFileList,
  () => {
    if (formRef.value && active.value === 3 && formData.registrationInformationFileList[0]?.urls) {
      formRef.value.validateField('registrationInformationFileList')
    }
  }
)
watch(
  () => formData.businessConstitutionFileList,
  () => {
    if (formRef.value && active.value === 3 && formData.businessConstitutionFileList[0]?.urls) {
      formRef.value.validateField('businessConstitutionFileList')
    }
  }
)
watch(
  () => formData.shareholderCommitteeResolutionFileList,
  () => {
    if (formRef.value && active.value === 3 && formData.shareholderCommitteeResolutionFileList[0]?.urls) {
      formRef.value.validateField('shareholderCommitteeResolutionFileList')
    }
  }
)
watch(
  () => formData.addressFileList,
  () => {
    if (formRef.value && active.value === 3 && formData.addressFileList[0]?.urls) {
      formRef.value.validateField('addressFileList')
    }
  }
)
watch(
  () => formData.otherDocumentFileList,
  () => {
    if (formRef.value && active.value === 3 && formData.otherDocumentFileList[0]?.urls) {
      formRef.value.validateField('otherDocumentFileList')
    }
  }
)
watch(
  () => formData.identityDocumentFileList_0,
  () => {
    if (formRef.value && active.value === 3 && formData.identityDocumentFileList_0?.urls) {
      formRef.value.validateField('identityDocumentFileList_0')
    }
  }
)
watch(
  () => formData.identityDocumentFileList_1,
  () => {
    if (formRef.value && active.value === 3 && formData.identityDocumentFileList_1?.urls) {
      formRef.value.validateField('identityDocumentFileList_1')
    }
  }
)

const goodTime = dayjs()
  .add(dayjs().day() === 5 ? 3 : 1, 'day')
  .hour(8)
  .minute(0)
  .second(0)

// 查看其他附件
const fileShow = ref(false)
const showOtherFiles = () => {
  fileShow.value = true
}
// changeClear
const changeClear = value => {
  if (value !== '卡') {
    formData.receiptCardPassword = ''
  }
  if (value !== '账号密码') {
    formData.receiptCardAccount = ''
    formData.receiptCardPassword = ''
  }

  if (value !== '自动获取') {
    formData.receiptCardAccount = ''
    formData.receiptCardPassword = ''
  }
}
</script>

<style lang="scss" scoped>
.my-container {
  font-family: AlibabaPuHuiTi_2_55_Regular;
}
.top {
  .line1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-con {
      font-size: 18px;
      line-height: 18px;
      font-family: AlibabaPuHuiTi_2_65_Medium;
      display: flex;
      align-items: center;
      img {
        vertical-align: middle;
        font-size: 18px;
        line-height: 18px;
      }
      .tit {
        margin: 0 5px;
      }
    }
  }
  .line2 {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .item {
      width: 33.33%;
      height: 40px;
      line-height: 40px;
      .t1 {
        font-size: 14px;
        color: #6a7697;
        line-height: 20px;
      }
      .t2 {
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        font-family: AlibabaPuHuiTi_2_65_Medium;
      }
    }
    .item-s {
      width: 100%;
    }
  }
}
.middle {
  // margin-top: 18px;
  // border-top: 1px solid #e8e8e8;
  // border-bottom: 1px solid #e8e8e8;
}

.step-list {
  margin-top: 20px;
  margin-bottom: 25px;
  border-top: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
  :deep(.el-step) {
    cursor: pointer;
  }
}

.main {
  margin-top: 20px;
  .title-con {
    font-size: 16px;
    line-height: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    img {
      vertical-align: middle;
      font-size: 16px;
      line-height: 16px;
    }
    .tit {
      margin: 0 5px;
    }
  }
  .time {
    margin-left: 20px;
    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    color: #b2b5b9;
    line-height: 20px;
  }
}
.el-button--primary.is-plain {
  --el-button-text-color: var(--el-color-primary);
  --el-button-bg-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-primary);
  }
}
.el-button--danger.is-plain {
  --el-button-text-color: var(--el-color-danger);
  --el-button-bg-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
  &:hover {
    color: #fff;
    background-color: var(--el-color-danger);
  }
}
:deep(.el-checkbox) {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #333;
  }
}
:deep(.upload-file) {
  width: 100%;
}
:deep(.component-upload-image) {
  width: 100%;
  .el-upload-list--picture-card {
    width: 100%;
    .el-upload--picture-card,
    .el-upload-list__item {
      width: 100%;
    }
  }
}
:deep(.select-class) {
  width: 100%;
}
.my-required-label {
  :deep(.el-checkbox__label) {
    &:before {
      content: '*';
      color: var(--el-color-danger);
      margin-right: 4px;
    }
  }
}
.my-required-label-extra-hide {
  :deep(.el-checkbox__label) {
    &:before {
      content: '';
    }
  }
}
.my-required-div {
  margin-bottom: 8px;
  line-height: 22px;
  font-weight: 700;
  &:before {
    content: '*';
    color: var(--el-color-danger);
    margin-right: 4px;
  }
}
.my-required-div-extra-hide {
  &:before {
    content: '';
  }
}

:deep(.form-rap) {
  .el-form-item {
    margin-bottom: 18px;
  }
}
.flex-column {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: start;
  }
}

.invisible-star {
  :deep(.validate-icon::before) {
    content: '';
    display: none;
  }
}
</style>
