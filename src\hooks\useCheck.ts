import { getCustomerById } from '@/api/customer/file'

import { checkContractDetailIsPermission } from '@/api/contract/contract'
import { financeReviewGetById } from '@/api/finance-review/finance-review.js'
import { useRouter } from 'vue-router'
import { setMessageIsRead } from '@/api/message-center/message-center'
import { ElMessage } from 'element-plus'
export const useCheck = (useCommon: any) => {
  const router = useRouter()
  // 跳转客户管理查看客户档案详情
  const jumpCheckCustomerDetail = async (row: any) => {
    console.log('extraDataVO', row, row.extraDataVO)
    // 先判断合同是否删除
    try {
      const result: any = await getCustomerById(row.extraDataVO.id)
      if (result.code === 200) {
        // useCommon.id = row.extraDataVO.id
        useCommon.setId(row.extraDataVO.id)
        useCommon.setBizType(row.bizType)
        router.push({
          path: '/customer/customer-file'
        })
        console.log('useCommon', useCommon)
      }
    } catch (error) {
      console.log('error', error)
    }
  }

  // 跳转合同台账查看合同台账详情
  const jumpCheckContractDetail = async (row: any) => {
    try {
      const { data } = await checkContractDetailIsPermission(row.extraDataVO.id)
      if (data) {
        useCommon.setId(row.extraDataVO.id)
        useCommon.setBizType(row.bizType)
        router.push({
          path: '/contract-manage/contract-list'
        })
      }
    } catch (error) {
      console.log('error', error)
    }
  }

  // 跳转合同评审查看我申请的详情
  const jumpCheckReviewMyCreateDetail = (row: any) => {
    router.push({
      path: '/contract-manage/contract-judge'
    })
    useCommon.setId(row.extraDataVO.id)
    useCommon.setBizType(row.bizType)
  }

  // 跳转借阅管理之我的申请的详情
  const jumpCheckBorrowDetail = (row: any) => {
    router.push({
      path: '/contract-manage/contract-borrowing'
    })
    useCommon.setId(row.extraDataVO.id)
    useCommon.setBizType(row.bizType)
  }

  // 跳转工单管理查看工单详情
  const jumpCheckOrderDetail = (row: any) => {
    router.push({
      path: '/work-manage/my-deal'
    })
    useCommon.setId(row.extraDataVO.id)
    useCommon.setBizType(row.bizType)
  }

  // 跳转线索管理查看线索详情
  const jumpCheckCusClueDetail = (row: any) => {
    console.log('extraDataVO', row.extraDataVO)
    router.push({
      path: '/material-manage/clue-manage'
    })
    useCommon.setId(row.extraDataVO.id)
    useCommon.setBizType(row.bizType)
  }

  // 跳转客户管理查看客户详情
  const jumpCheckCusDetail = (row: any) => {
    router.push({
      path: '/material-manage/client-manage'
    })
    useCommon.setId(row.extraDataVO.id)
    useCommon.setBizType(row.bizType)
  }

  // 跳转工商办证 - 我的任务 - 详情
  const jumpLicenseDetail = (row: any) => {
    router.push({
      path: '/certificate/my-list'
    })
    useCommon.setId(row.extraDataVO.id)
    useCommon.setBizType(row.bizType)
  }

  // 跳转财务审核-整改办理-办理
  const jumpPaymentReviewRecordDetail = async (row: any) => {
    try {
      const { data } = await financeReviewGetById({ id: row.extraDataVO.id })
      if (data) {
        router.push({
          path: '/finance-review/rectify'
        })
        useCommon.setId(row.extraDataVO.id)
        useCommon.setBizType(row.bizType)
      } else {
        ElMessage({
          message: '该条记录已删除!',
          type: 'error'
        })
      }
    } catch (error) {
      console.log('error', error)
    }
  }
  // 跳转工商注销
  const jumpBusiCancelDetail = (row: any) => {
    router.push({
      path: '/certificate/my-list'
    })
    useCommon.setId(row.extraDataVO.id)
    useCommon.setBizType(row.bizType)
  }

  const jumpBankCancelDetail = (row: any) => {
    router.push({
      path: '/certificate/my-list'
    })
    useCommon.setId(row.extraDataVO.id)
    useCommon.setBizType(row.bizType)
  }

  const jumpBusinessChangeDetail = (row: any) => {
    router.push({
      path: '/certificate/my-list'
    })
    useCommon.setId(row.extraDataVO.id)
    useCommon.setBizType(row.bizType)
  }
  const titleMap = {
    customer: jumpCheckCustomerDetail, // 包括 关联客户、客户信息变更以及客户关联人员变更
    contract: jumpCheckContractDetail,
    review: jumpCheckReviewMyCreateDetail,
    borrow: jumpCheckBorrowDetail,
    order: jumpCheckOrderDetail,
    clue: jumpCheckCusClueDetail, // 线索详情
    cus: jumpCheckCusDetail, // 客户详情
    accountant_notify: jumpCheckCustomerDetail,
    business_license_register: jumpLicenseDetail,
    bank_account_open: jumpLicenseDetail,
    license_permit: jumpLicenseDetail,
    payment_review_record: jumpPaymentReviewRecordDetail,
    business_cancellation: jumpBusiCancelDetail,
    bank_cancellation: jumpBankCancelDetail,
    business_change: jumpBusinessChangeDetail
  }
  const routerCheckDetail = (message: any) => {
    console.log('message', message)
    try {
      if (message.bizType && titleMap[message.bizType as keyof typeof titleMap]) {
        titleMap[message.bizType as keyof typeof titleMap](message)
      }
      // 触发已读接口
      setMessageIsRead([message.id])
    } catch (error) {
      console.log('error', error)
    }
  }
  return {
    routerCheckDetail
  }
}
