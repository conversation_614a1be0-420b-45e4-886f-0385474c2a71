<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
    <Collapse title="基础信息">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="客户名称" prop="companyName">
            <el-input v-model="formData.companyName" maxlength="20" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="手机号" prop="contactPhone">
            <el-input v-model="formData.contactPhone" maxlength="20" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户来源" prop="sourceId">
            <el-tree-select
              filterable
              v-model="formData.sourceId"
              :data="clue_source"
              check-strictly
              :render-after-expand="false"
              default-expand-all
              placeholder="请选择"
              :props="{
                value: 'id',
                label: 'name',
                children: 'child',
                disabled: (data, node) => {
                  return data.child
                }
              }"
              clearable
              @current-change="(node, nodeData) => handleSelectChange(node, nodeData)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="formData.sourceId === '3'">
          <el-form-item label="介绍来源" prop="introductionCustomerName">
            <el-input
              @click="handleListSelectShow"
              readonly
              v-model="formData.introductionCustomerName"
              maxlength="50"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户等级" prop="level">
            <el-select v-model="formData.level" clearable placeholder="">
              <el-option :label="item.label" :value="item.value" v-for="(item, index) in customer_level" :key="index" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" maxlength="1000" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="税务性质" prop="taxNature">
            <el-select v-model="formData.taxNature" clearable placeholder="">
              <el-option v-for="(item, index) in customer_property" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="业务类型" prop="productId">
            <el-tree-select
              v-model="formData.productId"
              filterable
              :data="productTreeData"
              check-strictly
              :props="{ value: 'id', label: 'name' }"
              node-key="id"
              :render-after-expand="false"
              @node-click="nodeClick"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>

    <Collapse title="更多信息">
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="标签" prop="tags">
            <el-select v-model="formData.tags" multiple placeholder="请选择">
              <el-option
                v-for="(item, index) in tagsList"
                :disabled="item.status === '0'"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="formData.phone" maxlength="20" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="行业" prop="industry">
            <el-select
              v-model="formData.industry"
              clearable
              placeholder=""
              filterable
              allow-create
              default-first-option
              @change="handleChange"
            >
              <el-option v-for="(item, index) in industry" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="地区" prop="area">
            <el-input v-model="formData.area" maxlength="20" />
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="formData.address" maxlength="100" />
          </el-form-item>
        </el-col>
      </el-row>
    </Collapse>
    <tableModal
      v-if="listSelectShow"
      rowKey="customerId"
      title="介绍来源"
      :columns="columns"
      :request-api="getCustomers"
      @on-close="listSelectShow = false"
      @on-select="handleSelect"
    />
  </el-form>
</template>
<script setup lang="jsx">
import Collapse from '@/components/Collapse'
import { getClueTagList } from '@/api/material-manage/tag'
import { FormValidators } from '@/utils/validate'
import { getBusinessList } from '@/api/business/business'
import { useDic } from '@/hooks/useDic'
import { useSetDic } from '@/hooks/useSetDic'
import { cusSourceTree } from '@/api/material-manage/source'
import tableModal from '@/components/tableModal'
import { getCustomers } from '@/api/customer/file'
const { setDic } = useSetDic()
const { getDic } = useDic()
const handleChange = value => {
  setDic('industry', value, value, industry)
}

const { proxy } = getCurrentInstance()
const { customer_level } = proxy.useDict('customer_level')
const { customer_property } = proxy.useDict('customer_property')
// const { clue_source } = proxy.useDict('clue_source')
const clue_source = ref([])
cusSourceTree({ enable: 1 }).then(res => {
  clue_source.value = res.data
})
const { industry } = proxy.useDict('industry')
const formData = ref({
  contactName: '',
  contactPhone: '',
  sourceId: '',
  sourceName: '',
  introductionCustomerId: '',
  introductionCustomerName: '',
  companyName: '',
  remark: '',
  sex: '0',
  post: '',
  industry: '',
  phone: '',
  level: '',
  email: '',
  wx: '',
  mainContactId: '',
  qq: '',
  birthday: '',
  area: '',
  address: '',
  seaId: '',
  firstFollowTime: '',
  taxNature: '',
  productId: '',
  id: undefined
})
const tagsList = ref([])
const getTagsList = async () => {
  const { data } = await getClueTagList({
    pageSize: 10000,
    pageNum: 1
  })
  tagsList.value = data.records || []
}
getTagsList()
const rules = {
  companyName: [
    {
      required: true,
      message: '请输入',
      trigger: ['blur']
    }
  ],

  contactPhone: [
    {
      required: false,
      validator: FormValidators.mobilePhone,
      trigger: ['blur']
    }
  ],
  phone: [
    {
      required: false,
      message: '请输入正确的电话格式',
      validator: FormValidators.allPhone,
      trigger: ['blur']
    }
  ],
  sourceId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  introductionCustomerName: [
    {
      required: true,
      message: '请选择',
      trigger: ['change', 'blur']
    }
  ],
  level: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  remark: [
    {
      required: false,
      message: '请输入',
      trigger: ['blur']
    }
  ],
  taxNature: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ],
  productId: [
    {
      required: true,
      message: '请选择',
      trigger: ['change']
    }
  ]
}
const productTreeData = ref([])
const getAllProducts = async () => {
  const { data } = await getBusinessList({
    pageNum: 1,
    pageSize: 10000
  })
  // 将后端传回的数据结构进行转换
  const revertData = []
  data.forEach(item => {
    const obj = {
      name: item.typeName,
      id: item.id,
      type: '业务类型',
      children: []
    }
    revertData.push(obj)
    if (Array.isArray(item.child) && item.child.length) {
      item.child.forEach(child => {
        obj.children.push({
          name: child.productName,
          types:
            item.contractType?.split(',')?.map(item => {
              return {
                label: item,
                value: item
              }
            }) || [],
          type: '产品类型',
          quotation: child.quotation, // 合同报价
          isInContract: child.isInContract, // 是否在合同中定义
          feeType: child.feeType, //收费类型
          id: child.id // 产品类型id
        })
      })
    }
  })

  console.log('revertData', revertData)
  productTreeData.value = revertData || []
}

const nodeClick = node => {
  formData.productName = node.productName
}
getAllProducts()
// 表单检验方法
const formRef = ref()

const getFormRef = () => {
  return formRef.value
}
/** 选择线索来源 */
function handleSelectChange(node, nodeData) {
  // console.log('handleSelectChange', node)
  formData.value.sourceName = node.name
}

/** 客户列表弹窗显示 */
const listSelectShow = ref(false)
const handleListSelectShow = () => {
  listSelectShow.value = true
}
const handleSelect = data => {
  console.log('data', data)
  formData.value.introductionCustomerName = data.customerName
  formData.value.introductionCustomerId = data.customerId
}
const columns = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'customerName',
    label: '客户名称',
    minWidth: '300',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'customerNo',
    label: '客户编号',
    width: '150',
    isColShow: false,
    search: { el: 'input' },
    render: scope => {
      return <span>{scope.row.customerNo || '--'}</span>
    }
  },
  {
    prop: 'industry',
    label: '从事行业',
    enum: getDic('industry'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.industry || '--'}</span>
    }
  },

  {
    prop: 'customerStatus',
    label: '客户状态',
    enum: getDic('customer_status'),
    width: '150',
    render: scope => {
      return <span>{scope.row.customerStatus || '--'}</span>
    },
    search: { el: 'select' }
  },
  {
    prop: 'customerProperty',
    label: '客户性质',
    // enum: customerProperty,
    enum: getDic('customer_property'),
    width: '150',
    search: { el: 'select' },
    render: scope => {
      return <span>{scope.row.customerProperty || '--'}</span>
    }
  },
  {
    prop: 'manger',
    width: '150',
    label: '财税顾问'
  }
]

defineExpose({
  formData,
  getFormRef
})
</script>
<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
