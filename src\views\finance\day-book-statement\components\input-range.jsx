import { defineComponent, nextTick, ref, watch } from 'vue'
// import { ElInput } from 'element-plus'
import NumberInput from '@/components/NumberInput/index.vue'

export default defineComponent({
  props: {
    modelValue: {
      type: Array,
      default: () => [null, null]
    }
  },
  setup(props, { emit }) {
    const txtArr = ref([])
    const txtLeft = ref('')
    const txtRight = ref('')
    watch(
      () => props.modelValue,
      val => {
        console.log('===incomeAmount-jsx-watch===', val, val && JSON.parse(JSON.stringify(val)))
        txtArr.value = val
        txtLeft.value = val[0]
        txtRight.value = val[1]
      },
      { deep: true, immediate: true }
    )

    const handleChangeLeft = (node, row) => {
      // console.log('handleChangeLeft', node, row)
      console.log('handleChangeLeft', txtArr.value)
      nextTick(() => {
        txtArr.value[0] = txtLeft.value
        console.log('handleSelectChange', node, txtArr.value)
        emit('update:modelValue', txtArr.value)
      })
    }

    const handleChangeRight = (node, row) => {
      nextTick(() => {
        txtArr.value[1] = txtRight.value
        console.log('handleSelectChange', node, txtArr.value)
        emit('update:modelValue', txtArr.value)
      })
    }

    return {
      txtLeft,
      txtRight,
      handleChangeLeft,
      handleChangeRight
    }
  },
  render() {
    return (
      <div style="display:flex;">
        <NumberInput style="width:40%" vModel={this.txtLeft} onChange={this.handleChangeLeft} />
        <div style="width:20%;text-align:center;">~</div>
        <NumberInput style="width:40%" vModel={this.txtRight} onChange={this.handleChangeRight} />
      </div>
    )
  }
})
