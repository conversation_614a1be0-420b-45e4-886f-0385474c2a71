<!--
 * @Description: 关联合同组件 
 * @Author: thb
 * @Date: 2023-05-26 13:21:07
 * @LastEditTime: 2024-02-21 15:47:33
 * @LastEditors: thb
-->
<template>
  <el-button type="primary" class="add-btn" @click="addContract">新增合同</el-button>
  <FormTable :formData="formData" :option="option">
    <template #index="{ $index }">
      {{ $index + 1 }}
    </template>
    <template #contractNo="{ row }">
      <span class="blue-text" @click="handleShowContractDetail(row)">{{ row.contractNo }}</span>
    </template>
    <template #contractStatus="{ row }">
      <el-tag :type="statusMap[row.contractStatus]"> {{ contractStatusMap[row.contractStatus] }}</el-tag>
    </template>
    <!-- 新增归档号 -->
    <template #documentNo="{ row }">
      <span :class="[row.documentNo ? '' : 'blue-text']" @click="editDocumentNo(row)">{{ row.documentNo || '归档' }} </span>
    </template>
    <template #action="{ row }" v-if="disabled">
      <el-button type="primary" text v-if="row.contractStatus === '4'" @click="handleChange(row.contractId, row.type)"
        >变更</el-button
      >
      <el-button type="primary" text v-else-if="row.contractStatus === '6'" @click="handleTranslate(row.contractId, row.type)"
        >转为正式合同</el-button
      >
      <span v-else>--</span>
    </template>
  </FormTable>
  <Pagination v-if="total > 0" :total="total" v-model:page="pages" @pagination="handlePageChange" />
  <contract v-if="contractShow" @on-close="contractShow = false" @on-next="handelNext" />
  <revivification :detail="props.modelValue" v-if="revShow" @on-close="revShow = false" @on-success="handleSuccess" />

  <normalCreate v-if="normalShow" :associated="detail" @on-close="normalShow = false" @on-success="getList" />
  <templateCreate
    v-if="templateShow"
    :associated="detail"
    :id="contractId"
    @on-close="templateShow = false"
    @on-success="getList"
  />

  <changeContract v-if="changeShow" :id="contractId" @on-close="changeShow = false" />
  <translateContract v-if="translateShow" :id="contractId" @on-close="translateShow = false" @on-success="getList" />

  <!-- 合同详情 -->
  <contractDetail
    v-if="detailShow"
    :isChange="isChange"
    :id="contractId"
    @on-close="detailShow = false"
    @on-change="handleChange"
    @edit-documnetNo="getCurrentPages"
  />
</template>
<script setup>
import FormTable from '@/components/FormTable/index.vue'
import contract from './contract.vue'
import { ElMessageBox } from 'element-plus'
import normalCreate from '@/views/contract-manage/contract-list/components/normal-create.vue'
import templateCreate from '@/views/contract-manage/contract-list/components/template-create.vue'
import revivification from './revivification.vue'
import changeContract from '@/views/contract-manage/contract-list/components/change-contract.vue'
import translateContract from '@/views/contract-manage/contract-list/components/translate-contract.vue'
import { getCustomerAssociatedContract } from '@/api/contract/contract.js'
import { getCustomerById } from '@/api/customer/file'
import Pagination from '@/components/Pagination'
import { useDialog } from '@/hooks/useDialog'
import { useContract } from '@/hooks/useContract'
import documentNoForm from '@/views/contract-manage/contract-list/components/documentNo-form'
import { checkContractDetailIsPermission, updateDocumentNo } from '@/api/contract/contract'
// import { useRemote } from '@/hooks/useRemote'
const { handleShowContractDetail, detailShow, contractId, isChange, contractDetail } = useContract()
const disabled = inject('disabled')
const formData = ref({
  tableData: [],
  rules: {}
})

const props = defineProps({
  modelValue: Object
})

const statusMap = {
  4: '',
  5: 'warning',
  3: 'danger',
  6: ''
}
const contractStatusMap = {
  4: '执行中',
  5: '已到期',
  3: '已终止',
  6: '意向合同'
}
const option = ref([
  {
    prop: 'index',
    label: '序号'
  },
  {
    prop: 'contractNo',
    label: '合同编号'
  },
  {
    prop: 'contractTypeName',
    label: '合同类型'
  },
  {
    prop: 'totalCost',
    label: '合同金额(元)'
  },
  {
    prop: 'createTime',
    width: '150',
    label: '签订日期'
  },
  {
    prop: 'contractStatus',
    label: '合同状态'
  },
  {
    prop: 'manager',
    fixed: 'right',
    width: 180,
    label: '财税顾问'
  },
  {
    prop: 'documentNo',
    fixed: 'right',
    width: 100,
    label: '归档号'
  }
])
watch(
  disabled,
  () => {
    if (disabled.value) {
      option.value = [
        {
          prop: 'index',
          label: '序号'
        },
        {
          prop: 'contractNo',
          width: '125',
          label: '合同编号'
        },
        {
          prop: 'contractTypeName',
          width: '125',
          label: '合同类型'
        },
        {
          prop: 'totalCost',
          width: '100',
          label: '合同金额(元)'
        },
        {
          prop: 'createTime',
          width: '150',
          label: '签订日期'
        },
        {
          prop: 'contractStatus',
          width: '100',
          label: '合同状态'
        },
        {
          prop: 'manager',
          label: '财税顾问'
        },
        {
          prop: 'productName',
          width: '200',
          label: '办理业务'
        },
        {
          prop: 'documentNo',
          fixed: 'right',
          width: 100,
          label: '归档号'
        },
        {
          prop: 'action',
          fixed: 'right',
          width: 136,
          label: '操作'
        }
      ]
    }
  },
  {
    immediate: true
  }
)

// 新增合同
const contractShow = ref(false)
const revShow = ref(false)
const addContract = () => {
  if (props.modelValue.discard === 0 || !props.modelValue.discard) {
    // 不是废弃的情况下
    contractShow.value = true
  } else {
    // 废弃的情况下
    ElMessageBox.confirm('请注意该客户已被废弃,是否继续与合同进行关联', '注意', {
      confirmButtonText: '继续关联',
      cancelButtonText: '还原客户',
      type: 'warning'
    })
      .then(() => {
        // 继续关联
        contractShow.value = true
      })
      .catch(action => {
        if (action === 'cancel') {
          // 还原客户
          revShow.value = true
        }
      })
  }
}
const emits = defineEmits(['on-success'])
const handleSuccess = () => {
  console.log('handleSuccess', 'on-success')
  emits('on-success')
  revShow.value = false
}

// 手动新建的弹窗显示标志
const normalShow = ref(false)
const templateShow = ref(false)

// 下一步
const handelNext = createType => {
  // '0'代表 普通新建
  // '1'代表 模板新建
  if (createType === '0') {
    normalShow.value = true
  }
  if (createType === '1') {
    contractId.value = undefined
    templateShow.value = true
  }
}

// getList
const total = ref(0)
const pages = ref(1)
const getList = async () => {
  console.log('getList')
  const { data } = await getCustomerAssociatedContract({
    ciId: props.modelValue?.customerId,
    pageNum: 1,
    pageSize: 10
  })
  pages.value = Number(data.current)
  formData.value.tableData = data.records || []
  total.value = Number(data.total) || 0
}
// 获取实际客户详情，而不是modelValue 表单
const detail = ref({})
const getDetail = async () => {
  const { data } = await getCustomerById(props.modelValue?.customerId)
  detail.value = data
}
// 切换页数或者页码
// <!-- emit('pagination', { page: val, limit: pageSize.value }) -->
const handlePageChange = async ({ page, limit }) => {
  const { data } = await getCustomerAssociatedContract({
    ciId: props.modelValue?.customerId,
    pageNum: page,
    pageSize: limit
  })
  formData.value.tableData = data.records || []
  total.value = Number(data.total) || 0
}

// 变更操作
const changeShow = ref(false)
// const contractId = ref()
const handleChange = (id, type) => {
  if (type && type === '1') {
    contractId.value = id
    templateShow.value = true
  } else {
    contractId.value = id
    changeShow.value = true
  }
}

// 转为正式合同
const translateShow = ref(false)
const handleTranslate = (id, type) => {
  if (type && type === '1') {
    contractId.value = id
    templateShow.value = true
  } else {
    contractId.value = id
    translateShow.value = true
  }
}

// editDocumentNo 编辑归档号
const { showDialog } = useDialog()

const getCurrentPages = async () => {
  const { data } = await getCustomerAssociatedContract({
    ciId: props.modelValue?.customerId,
    pageNum: pages.value,
    pageSize: 10
  })
  pages.value = Number(data.current)
  formData.value.tableData = data.records || []
  total.value = Number(data.total) || 0
}
const editDocumentNo = row => {
  showDialog({
    title: '合同归档',
    component: documentNoForm,
    customClass: 'document-dialog',
    getApi: checkContractDetailIsPermission,
    requestParams: row.contractId,
    submitApi: updateDocumentNo,
    submitCallback: async () => {
      getCurrentPages()
    }
    // handleRevertParams
  })
}
onMounted(() => {
  getDetail()
  getList()
})
</script>
<style lang="scss" scoped>
.add-btn {
  margin-bottom: 12px;
}
.pagination-container {
  height: 35px;
}
:deep(.el-form-item--default) {
  margin-bottom: 0;
}
:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
