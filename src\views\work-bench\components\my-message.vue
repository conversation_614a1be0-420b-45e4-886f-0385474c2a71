<!--
 * @Description: 我的消息
 * @Author: thb
 * @Date: 2023-07-20 09:58:06
 * @LastEditTime: 2024-03-13 09:42:33
 * @LastEditors: thb
-->
<template>
  <div class="table-wrap">
    <ProTable
      ref="proTable"
      title="通知公告"
      :init-param="initParam"
      :columns="columns"
      :request-api="getMyMessageList"
      :dataCallback="dataCallback"
      :pagination="false"
      :border="false"
      @row-click="handleDetail"
      :row-style="{ cursor: 'pointer' }"
    >
      <template #title="{ row }">
        <el-badge style="margin-left: 10px" :is-dot="row.readStatus === 0"> {{ row.title }}</el-badge>
      </template>
      <template #content="{ row }">
        <span>{{ row.content }}</span>
      </template>
      <template #receiveTime="{ row }">
        <span>{{ row.receiveTime }}</span>
      </template>
    </ProTable>
  </div>
  <messageDetail v-if="messageShow" :id="notificationId" :messageId="messageId" @on-close="handleClose" />
</template>
<script setup lang="tsx">
import { inject, ref, watch } from 'vue'
import ProTable from '@/components/ProTable/index.vue'
import { ColumnProps } from '@/components/ProTable/interface'
import { getMyMessageList, setMessageIsRead } from '@/api/message-center/message-center'
// import { checkContractDetailIsPermission } from '@/api/contract/contract'
import messageDetail from '@/views/message-center/my-message/components/message-detail.vue'
// import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
// import { getCustomerById } from '@/api/customer/file'
import useCommonStore from '@/store/modules/common'
const useCommon = useCommonStore()
import { useCheck } from '@/hooks/useCheck'
const { routerCheckDetail } = useCheck(useCommon)

const initParam = reactive({ readStatus: 0, pageSize: 3 })
// 表格配置项
const columns: ColumnProps<any>[] = [
  // {
  //   prop: 'operation',
  //   width: 200,
  //   label: '操作',
  //   align: 'left'
  // },
  {
    prop: 'title',
    label: '标题',
    width: 250,
    align: 'left'
  },

  {
    prop: 'content',
    label: '消息内容',
    align: 'left'
  },
  {
    prop: 'receiveTime',
    width: 200,
    label: '发送时间',
    align: 'left'
  }
]
const messageShow = ref(false)
const notificationId = ref()
const messageId = ref()

const handleDetail = (row: any) => {
  if (row.messageType === '提醒') {
    try {
      routerCheckDetail(row)
      // if (row.bizType && titleMap[row.bizType as keyof typeof titleMap]) {
      //   titleMap[row.bizType as keyof typeof titleMap](row)
      // }
      // // 触发已读接口
      // setMessageIsRead([row.id])
    } catch (error) {}
  } else {
    // 公告的详情

    notificationId.value = row.notificationId
    messageId.value = row.id
    messageShow.value = true
  }
}

// watch 监听公告消息
watch(
  () => useCommon.notObj,
  () => {
    const { id, notificationId: noteId } = useCommon.notObj
    if (id && notificationId) {
      // 公告的详情
      notificationId.value = noteId
      messageId.value = id
      messageShow.value = true
      // clear useCommon.notObj
      useCommon.notObj = {
        id: '',
        notificationId: ''
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
)

// 设置标记为已读
const { proxy } = getCurrentInstance()
const setIsRead = () => {
  ElMessageBox.confirm('确定要标记全部消息为已读?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const result: any = await setMessageIsRead()
      if (result.data) {
        // 标记成功
        getList()
        proxy.$modal.msgSuccess(`标记成功!`)
      }
    })
    .catch(() => {})
}

// handleMark 标记为已读
const handleMark = async row => {
  // 触发已读接口
  const result = await setMessageIsRead([row.id])
  if (result.code === 200) {
    proxy.$modal.msgSuccess(`标记成功!`)
    getList()
  } else {
    proxy.$modal.msgError(`标记失败!`)
  }
}
const proTable = ref()
const getList = () => {
  proTable.value?.getTableList()
}

const handleClose = () => {
  messageShow.value = false
  getList()
}

// 组件销毁时需要删除useCommonStore中的存储数据
// onUnmounted(() => {
//   useCommon.id = ''
// })
const dataCallback = (data: any) => {
  if (data) {
    return data.records
  }
}
</script>
<style lang="scss" scoped>
.table-wrap {
  // min-height: 140px;
  min-height: 380px;
  display: flex;
}
:deep(.el-badge__content.el-badge__content--danger.is-fixed.is-dot) {
  left: -20px;
  right: none;
  top: 10px;
}
</style>
