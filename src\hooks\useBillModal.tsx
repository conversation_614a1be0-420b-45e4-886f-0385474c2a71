/*
 * @Description:关联账单
 * @Author: thb
 * @Date: 2023-11-30 11:14:59
 * @LastEditTime: 2023-11-30 15:55:08
 * @LastEditors: thb
 */
import tableModalBill from '@/components/tableModal'
import { ColumnProps } from '@/components/ProTable/interface'
import { getBusinessList } from '@/api/business/business'
import { financePaymentList } from '@/api/finance/accounts-receivable'
import { receiveStatusArr } from '@/utils/constants'
import { reactive, toRefs } from 'vue'

const columnsBill: ColumnProps<any>[] = [
  { type: 'selection', fixed: 'left', width: 80 },
  {
    prop: 'paymentNo',
    label: '账单编号',
    isColShow: false,
    search: { el: 'input' }
  },
  {
    prop: 'feeType',
    label: '费用类别',
    isColShow: false,
    enum: (() => {
      return () => {
        return new Promise(async (resolve, reject) => {
          const { data } = await getBusinessList({
            pageNum: 1,
            pageSize: 10000
          })
          if (data) {
            const revertData = (data || []).map((item: any) => {
              return {
                ...item,
                label: item.typeName,
                value: item.id
              }
            })
            resolve({
              data: revertData
            })
          } else {
            resolve({
              data: []
            })
          }
        })
      }
    })()
  },
  {
    prop: 'receiveStatus',
    label: '收款情况',
    enum: receiveStatusArr,
    search: { el: 'select' }
  },
  {
    prop: 'paymentAmount',
    label: '账款金额', // 账款金额->账款金额总计
    isColShow: false
  },
  {
    prop: 'allReceiptAmount',
    label: '已付款金额', // 已收款->已付款金额
    isColShow: false
  },
  {
    prop: 'allReceivableAmount',
    label: '应待收款',
    isColShow: false
  }
]
export const useBillModal = () => {
  const state = reactive({
    listShowBill: false
  })

  return {
    ...toRefs(state),
    tableModalBill,
    financePaymentList,
    columnsBill
  }
}
