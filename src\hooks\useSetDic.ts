import { addData } from '@/api/system/dict/data'
import useDictStore from '@/store/modules/dict'
import { getDicts } from '@/api/system/dict/data'
export const useSetDic = () => {
  const setDic = async (dictType: string, dictLabel: string, dictValue: string, dicts: any) => {
    if ((dicts.value || []).map((item: any) => item.label).includes(dictLabel) || !dictLabel) return
    const result: any = await addData({
      dictType,
      dictLabel,
      dictValue,
      dictSort: 0,
      listClass: 'default',
      status: '0'
    })
    if (result.code === 200) {
      useDictStore().removeDict(dictType)
      // 获取最新的dictType下的字典数据
      const result1: any = await getDicts(dictType)
      if (result1.code === 200) {
        const revertData = result1.data.map((p: any) => ({
          label: p.dictLabel,
          value: p.dictValue,
          elTagType: p.listClass,
          elTagClass: p.cssClass
        }))
        useDictStore().setDict(dictType, revertData)
        dicts.value = revertData
      }
    }
  }
  return {
    setDic
  }
}
